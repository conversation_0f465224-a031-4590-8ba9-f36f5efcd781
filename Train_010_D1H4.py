import os
import pandas as pd
import numpy as np
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import lightgbm as lgb
from sklearn.metrics import accuracy_score, f1_score
import mplfinance as mpf

# ====================================================================
# Configuration
# ====================================================================
TEST_GROUPS = {
    "M60": [
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    ]
}
OUTPUT_DIR = "Test_Data"
HORIZONS = [10, 20]
TARGETS = ['Target_Buy', 'Target_Sell']

os.makedirs(OUTPUT_DIR, exist_ok=True)

# ====================================================================
# Feature Engineering Helper Functions
# ====================================================================

# --- EMA Calculation (Classic, seeded with SMA) ---
def classic_ema(series, span):
    sma = series.rolling(window=span, min_periods=span).mean()
    ema = pd.Series(index=series.index, dtype=float)
    alpha = 2 / (span + 1)

    for i in range(len(series)):
        if i < span-1:
            ema.iloc[i] = np.nan
        elif i == span-1:
            ema.iloc[i] = sma.iloc[i]
        else:
            ema.iloc[i] = alpha * series.iloc[i] + (1 - alpha) * ema.iloc[i-1]
    return ema

def classic_macd_ema(series: pd.Series, span: int):
    s = pd.to_numeric(series, errors='coerce').copy()
    alpha = 2.0 / (span + 1.0)
    out = pd.Series(index=s.index, dtype='float64')

    # หาตำแหน่งเริ่มต้นที่มี value จริง
    valid_idx = s.first_valid_index()
    if valid_idx is None:
        return out

    start = s.index.get_loc(valid_idx)
    n = len(s)

    # ถ้ามีข้อมูลมากพอสำหรับ seed SMA
    if n - start >= span:
        # seed position at index start+span-1
        seed_pos = start + span - 1
        seed = s.iloc[start: start + span].mean()
        out.iloc[seed_pos] = seed
        # recursive
        for i in range(seed_pos + 1, n):
            out.iloc[i] = alpha * s.iloc[i] + (1 - alpha) * out.iloc[i - 1]
        # ข้อดี: ค่าก่อน seed_pos จะยังคง NaN
    else:
        # fallback: ถ้าไม่พอข้อมูล ให้ใช้ pandas ewm (จะ seed ด้วยค่าที่มี)
        out = s.ewm(span=span, adjust=False).mean()

    return out

def mt5_like_macd_seeded(price: pd.Series, fast=12, slow=26, signal=9):
    price = pd.to_numeric(price, errors='coerce')
    ema_fast = classic_macd_ema(price, fast)
    ema_slow = classic_macd_ema(price, slow)
    macd_line = ema_fast - ema_slow

    # สำหรับ signal line เรามักจะใช้ EMA บน macd_line (และสามารถ seed ด้วย SMA ของ macd ส่วนเริ่ม)
    signal_line = classic_macd_ema(macd_line, signal)
    hist = macd_line - signal_line
    return ema_fast, ema_slow, macd_line, signal_line, hist

# ====================================================================
# Data Processing Functions
# ====================================================================

def load_and_clean_data(symbol, timeframe, file_path: str) -> pd.DataFrame:
    """Loads data from a CSV file, cleans it, and standardizes the DateTime column."""
    print(f"\n--- Loading and Cleaning {os.path.basename(file_path)} ---")
    
    df = pd.read_csv(file_path)
    print(f"Initial rows: {len(df)}")
    
    if "Date" not in df.columns and "Time" not in df.columns:
        df = df.iloc[1:].reset_index(drop=True)

    if 'Date' in df.columns and 'Time' in df.columns:
        df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'], errors='coerce')
        df.drop(['Date', 'Time'], axis=1, inplace=True)
        print(f"DateTime column created. Date range: {df['DateTime'].min()} to {df['DateTime'].max()}")
    else:
        print("⚠️ 'Date' or 'Time' column not found.")

    for col in ['Open', 'High', 'Low', 'Close']:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    df = df.iloc[1:].reset_index(drop=True)
    drop_cols = ["Vol","Col_8"]
    df = df.drop(columns=drop_cols, errors="ignore")

    if 'TickVol' in df.columns:
        df = df.rename(columns={'TickVol': 'Volume'})

    # Ensure Volume is numeric after potential rename
    if 'Volume' in df.columns:
        df['Volume'] = pd.to_numeric(df['Volume'], errors='coerce')

    # ====================================================================
    new_file_name = f"{timeframe}_{symbol}_01_Load_Clean.csv"
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
    df.to_csv(new_file_path, index=False)
    print(f"\n✅ Saved cleaned file to: {new_file_path}")
    # ====================================================================

    return df

def create_resampled_df(symbol, timeframe, df: pd.DataFrame, rule: str, prefix: str) -> pd.DataFrame:
    """
    Resamples OHLCV data to a new timeframe, adding a prefix to columns.
    The input DataFrame `df` must have a DatetimeIndex.
    """

    # ====================================================================
    # new_file_name = f"{timeframe}_{symbol}_03a_MTF_{prefix}.csv"
    # new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
    # df.to_csv(new_file_path, index=True) # True ต้องการแสดงคอลัมน์เวลาในไฟล์ CSV
    # print(f"\n✅ Saved cleaned file to: {new_file_path}")
    # ====================================================================

    resampled_df = pd.DataFrame()
    resampled_df[f'{prefix}_Open'] = df['Open'].resample(rule).first()
    resampled_df[f'{prefix}_High'] = df['High'].resample(rule).max()
    resampled_df[f'{prefix}_Low'] = df['Low'].resample(rule).min()
    resampled_df[f'{prefix}_Close'] = df['Close'].resample(rule).last()
    resampled_df[f'{prefix}_Volume'] = df['Volume'].resample(rule).sum()
    resampled_df.dropna(inplace=True)

    # ====================================================================
    new_file_name = f"{timeframe}_{symbol}_03b_MTF_{prefix}.csv"
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
    resampled_df.to_csv(new_file_path, index=True) # True ต้องการแสดงคอลัมน์เวลาในไฟล์ CSV
    print(f"\n✅ Saved cleaned file to: {new_file_path}")
    # ====================================================================

    return resampled_df

def create_features_mtf(symbol, timeframe, df: pd.DataFrame, prefix: str) -> pd.DataFrame:
    
    df_feat = df.copy()

    df_feat[f"{prefix}_Bar_CL"] = 0.0
    df_feat.loc[df_feat[f'{prefix}_Close'] > df_feat[f'{prefix}_Open'], f"{prefix}_Bar_CL"] = 1.0
    df_feat.loc[df_feat[f'{prefix}_Close'] < df_feat[f'{prefix}_Open'], f"{prefix}_Bar_CL"] = -1.0

    ema12, ema26, macd_line, macd_signal, macd_hist = mt5_like_macd_seeded(df[f'{prefix}_Close'])
    df_feat[f'{prefix}_EMA12']       = ema12
    df_feat[f'{prefix}_EMA26']       = ema26
    df_feat[f'{prefix}_MACD']        = macd_line
    df_feat[f'{prefix}_MACD_SIGNAL'] = macd_signal
    df_feat[f'{prefix}_MACD_HIST']   = macd_hist

    # ====================================================================
    new_file_name = f"{timeframe}_{symbol}_04_{prefix}_Features.csv"
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
    df_feat.to_csv(new_file_path, index=False)
    print(f"✅ Saved final merged features to: {new_file_path}")
    # ====================================================================

    return df_feat

def create_features(symbol, timeframe, df: pd.DataFrame) -> pd.DataFrame:
    """
    Creates technical indicators and price action features, including MTF features
    from H4 and D1 timeframes, and merges them correctly.
    """
    print(f"\n--- Creating Features ---")

    # --- 1. Base Feature Engineering (on original timeframe) ---
    df_feat = df.copy()
    df_feat['DateTime'] = pd.to_datetime(df_feat['DateTime'])

    df_feat["Bar_CL"] = 0.0
    df_feat.loc[df_feat['Close'] > df_feat['Open'], "Bar_CL"] = 1.0
    df_feat.loc[df_feat['Close'] < df_feat['Open'], "Bar_CL"] = -1.0
    df_feat['Price_Range'] = df_feat["High"] - df_feat["Low"]
    df_feat['Price_Move'] = df_feat["Close"] - df_feat["Open"]
    df_feat['EMA50'] = classic_ema(df_feat['Close'], 50)
    df_feat['EMA100'] = classic_ema(df_feat['Close'], 100)
    df_feat['EMA200'] = classic_ema(df_feat['Close'], 200)
    
    ema12, ema26, macd_line, macd_signal, macd_hist = mt5_like_macd_seeded(df['Close'])
    df_feat['MACD'] = macd_line
    df_feat['MACD_SIGNAL'] = macd_signal
    df_feat['MACD_HIST'] = macd_hist

    # ====================================================================
    new_file_name = f"{timeframe}_{symbol}_02a_Features.csv"
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
    df_feat.to_csv(new_file_path, index=False)
    print(f"✅ Saved final merged features to: {new_file_path}")
    # ====================================================================

    df_feat.dropna(inplace=True)
    df_feat.reset_index(drop=True, inplace=True)

    # ====================================================================
    new_file_name = f"{timeframe}_{symbol}_02b_Dropna.csv"
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
    df_feat.to_csv(new_file_path, index=False)
    print(f"✅ Saved final merged features to: {new_file_path}")
    # ====================================================================

    # --- 2. MTF Feature Creation and Merging ---
    base_df = df_feat.copy()
    base_df.set_index('DateTime', inplace=True)
    base_df.sort_index(inplace=True)

    # ====================================================================
    new_file_name = f"{timeframe}_{symbol}_02c_Resample.csv"
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
    base_df.to_csv(new_file_path, index=True)
    print(f"✅ Saved final merged features to: {new_file_path}")
    # ====================================================================

    # print("--- Resampling to H4 and D1 timeframes ---")
    df_d1 = create_resampled_df(symbol, timeframe, base_df, rule='1D', prefix='D1')
    df_h4 = create_resampled_df(symbol, timeframe, base_df, rule='4H', prefix='H4')
    
    df_d1_feature = create_features_mtf(symbol, timeframe, df_d1, prefix='D1')
    df_h4_feature = create_features_mtf(symbol, timeframe, df_h4, prefix='H4')

    # ====================================================================

    # print("--- Merging MTF features ---")
    # df_merged = pd.merge_asof(base_df, df_h4, left_index=True, right_index=True, direction='backward')
    # df_merged = pd.merge_asof(df_merged, df_d1, left_index=True, right_index=True, direction='backward')

    # print("--- Merging MTF features ---")
    # df_merged = pd.merge_asof(base_df, df_h4, left_index=True, right_index=True, direction='backward')
    # df_merged = pd.merge_asof(df_merged, df_d1, left_index=True, right_index=True, direction='backward')

    # ====================================================================

    print("--- Merging MTF features ---")
    # Shift H4 and D1 data to get previous bar's values
    # เพื่อให้ได้ข้อมูลย้อนหลัง 1 แท่ง (previous bar) จาก Timeframe ที่สูงกว่า (H4, D1) คุณสามารถใช้เมธอด .shift(1) กับ DataFrame
    # df_d1_shifted = df_d1.shift(1)
    # df_h4_shifted = df_h4.shift(1)

    df_d1_shifted = df_d1_feature.shift(1)
    df_h4_shifted = df_h4_feature.shift(1)

    print("--- Merging MTF features ---")
    df_merged = pd.merge_asof(base_df, df_h4_shifted, left_index=True, right_index=True, direction='backward')
    df_merged = pd.merge_asof(df_merged, df_d1_shifted, left_index=True, right_index=True, direction='backward')

    # ====================================================================

    mtf_cols = [col for col in df_merged.columns if col.startswith('H4_') or col.startswith('D1_')]
    df_merged[mtf_cols] = df_merged[mtf_cols].fillna(method='ffill')

    df_merged.dropna(inplace=True)
    df_merged.reset_index(inplace=True)
    
    print(f"✅ Features created successfully. DF shape: {df_merged.shape}")
    
    # ====================================================================
    new_file_name = f"{timeframe}_{symbol}_05_MTF_Merged.csv"
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
    df_merged.to_csv(new_file_path, index=False)
    print(f"✅ Saved final merged features to: {new_file_path}")
    # ====================================================================

    return df_merged

def preprocess_for_training(symbol, timeframe, df: pd.DataFrame) -> pd.DataFrame:
    """Prepares the dataframe for training by cleaning and adding scenario features."""
    print(f"\n--- Preprocessing for Training ---")
    
    df_processed = df.dropna().reset_index(drop=True)
    print(f"Rows after dropping NaN: {len(df_processed)}")

    drop_cols = [
        # "Open", "High", "Low", "Close",
        "Vol", "Col_8", "Bar_CLp",
        "H4_Open", "H4_High", "H4_Low", "H4_Close",
        "D1_Open", "D1_High", "D1_Low", "D1_Close",
        ] 
    df_processed = df_processed.drop(columns=drop_cols, errors="ignore")

    if 'TickVol' in df_processed.columns:
        df_processed = df_processed.rename(columns={'TickVol': 'Volume'})

    cols_to_convert = ['Volume']
    for col in cols_to_convert:
        if col in df_processed.columns:
            df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce').fillna(0)

    conditions = [
        (df_processed['Close'] > df_processed['EMA200']) & (df_processed['Low'] > df_processed['EMA200']) & (df_processed['MACD'] > df_processed['MACD_SIGNAL']),
        (df_processed['Close'] < df_processed['EMA200']) & (df_processed['High'] < df_processed['EMA200']) & (df_processed['MACD'] < df_processed['MACD_SIGNAL']),
    ]
    choices = ['Uptrend', 'Downtrend']
    df_processed['Market_Scenario'] = np.select(conditions, choices, default='Natural')
    df_processed = pd.get_dummies(df_processed, columns=['Market_Scenario'], prefix='Scenario')

    # ====================================================================
    new_file_name = f"{timeframe}_{symbol}_06_Processed.csv"
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
    df_processed.to_csv(new_file_path, index=False)
    print(f"\n✅ Saved cleaned file to: {new_file_path}")
    # ====================================================================

    print("Preprocessing complete.")
    return df_processed

# ====================================================================
# Model Training and Evaluation Function
# ====================================================================

def train_evaluate_and_save(df: pd.DataFrame, symbol: str, timeframe, group_name: str):
    """Loops through horizons and targets to train, evaluate, and save models and scalers."""
    print(f"\n{'='*60}")
    print(f"STARTING TRAINING FOR: {symbol} - {group_name}")
    print(f"{'='*60}")

    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # ต้องการสรุปการวิเคราะห์และเปรียบเทียบผลลัพธ์ Buy+Sell
    all_horizon_results = []

    for horizon in HORIZONS:
        print(f"\n--- Processing Horizon: {horizon} bars ---")
        
        df_horizon = df.copy()
        df_horizon['Next_Close'] = df_horizon['Close'].shift(-horizon)
        df_horizon['Target_Buy'] = ((df_horizon['Close'] > df_horizon['Open']) & (df_horizon['Next_Close'] > df_horizon['Close'])).astype(int)
        df_horizon['Target_Sell'] = ((df_horizon['Close'] < df_horizon['Open']) & (df_horizon['Next_Close'] < df_horizon['Close'])).astype(int)
        df_horizon.dropna(subset=['Next_Close', 'Target_Buy', 'Target_Sell'], inplace=True)

        # ====================================================================
        new_file_name = f"{timeframe}_{symbol}_07_Target.csv"
        new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
        df_horizon.to_csv(new_file_path, index=False)
        print(f"\n✅ Saved cleaned file to: {new_file_path}")
        # ====================================================================

        # ต้องการสรุปการวิเคราะห์และเปรียบเทียบผลลัพธ์ Buy+Sell
        results_for_this_horizon = {}

        for target_col in TARGETS:
            print(f"\n🎯 Training for Target: {target_col} (Horizon: {horizon})")

            drop_cols = [
                'DateTime', 
                'Open', 'High', 'Low', 'Close',
                'Next_Close', 
                'Target_Buy', 'Target_Sell'
                ]

            feature_columns = [col for col in df_horizon.columns if col not in drop_cols]

            print(f"\n🛠️ Features used for training ({len(feature_columns)} total):")
            print(feature_columns)

            X = df_horizon[feature_columns].copy()
            y = df_horizon[target_col].copy()

            # ====================================================================
            new_file_name = f"{timeframe}_{symbol}_{target_col}_08_df.csv"
            new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
            df_horizon.to_csv(new_file_path, index=False)
            
            new_file_name = f"{timeframe}_{symbol}_{target_col}_09_X.csv"
            new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
            X.to_csv(new_file_path, index=False)

            new_file_name = f"{timeframe}_{symbol}_{target_col}_09_y.csv"
            new_file_path = os.path.join(OUTPUT_DIR, new_file_name)
            y.to_csv(new_file_path, index=False)

            print(f"\n✅ Saved cleaned file to: {new_file_path}")
            # ====================================================================

            if y.nunique() < 2:
                print(f"⚠️ Skipping {target_col} for Horizon {horizon} due to single class in target.")
                continue

            X_train, X_temp, y_train, y_temp = train_test_split(X, y, test_size=0.4, random_state=42, stratify=y) # Train = 60%, temp = 40%
            X_val, X_test, y_val, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp) # Temp แบ่ง Validation = 50%, Test = 50% สรุปเป็น Train = 60%, Validation = 20%, Test = 20%

            scaler = StandardScaler()
            numeric_features = X_train.select_dtypes(include=np.number).columns.tolist()
            
            X_train = X_train.copy()
            X_val = X_val.copy()
            X_test = X_test.copy()
            
            scaler.fit(X_train[numeric_features])
            
            scaler_filename = f"{symbol}_{group_name}_{target_col}_h{horizon}_scaler.joblib"
            scaler_path = os.path.join(OUTPUT_DIR, scaler_filename)
            joblib.dump(scaler, scaler_path)
            print(f"💾 Saved Scaler to: {scaler_path}")

            X_train.loc[:, numeric_features] = scaler.transform(X_train[numeric_features])
            X_val.loc[:, numeric_features] = scaler.transform(X_val[numeric_features])
            X_test.loc[:, numeric_features] = scaler.transform(X_test[numeric_features])

            print("\nTraining model with validation set for early stopping...")
            model = lgb.LGBMClassifier(objective='binary', random_state=42)

            # เทรนโมเดล
            model.fit(X_train, y_train,
                    eval_set=[(X_val, y_val)],
                    eval_metric='logloss',
                    callbacks=[lgb.early_stopping(10, verbose=True), lgb.log_evaluation(period=10)])

            print("\n--- Model Evaluation on Validation Set ---")
            from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

            # สร้างการทำนาย
            y_pred = model.predict(X_test)

            # คำนวณ metrics
            accuracy = accuracy_score(y_test, y_pred)
            print(f"Accuracy: {accuracy:.4f}")

            print("Classification Report:")
            print(classification_report(y_test, y_pred))

            print("Confusion Matrix:")
            print(confusion_matrix(y_test, y_pred))

            f1 = f1_score(y_test, y_pred, average='weighted')
            print(f"📊 Test Set Accuracy: {accuracy:.4f}, F1-Score: {f1:.4f}")

            report_dict = classification_report(y_test, y_pred, output_dict=True)
            
            # results_for_this_horizon[target_col] = {
            #     "accuracy": accuracy,
            #     "f1_weighted": f1,
            #     "precision_for_1": report_dict.get('1', {}).get('precision', 0),
            #     "recall_for_1": report_dict.get('1', {}).get('recall', 0),
            #     "f1_for_1": report_dict.get('1', {}).get('f1-score', 0)
            # }
            
            # ต้องการสรุปการวิเคราะห์และเปรียบเทียบผลลัพธ์ 0 และ 1
            results_for_this_horizon[target_col] = {
                "accuracy": accuracy,
                "f1_weighted": f1,

                # class 0
                "precision_for_0": report_dict.get('0', {}).get('precision', 0),
                "recall_for_0": report_dict.get('0', {}).get('recall', 0),
                "f1_for_0": report_dict.get('0', {}).get('f1-score', 0),

                # class 1
                "precision_for_1": report_dict.get('1', {}).get('precision', 0),
                "recall_for_1": report_dict.get('1', {}).get('recall', 0),
                "f1_for_1": report_dict.get('1', {}).get('f1-score', 0),
            }

            model_filename = f"{symbol}_{group_name}_{target_col}_h{horizon}_model.joblib"
            model_path = os.path.join(OUTPUT_DIR, model_filename)
            joblib.dump(model, model_path)
            print(f"\n💾 Saved Model to: {model_path}")

            if 'DateTime' not in df_horizon.columns:
                print("⚠️ DateTime not found in df_horizon, skipping visualization.")
                continue

            test_and_visualize_model(df_horizon, symbol, group_name, target_col, horizon, X_val, X_test)
        
        all_horizon_results.append({'horizon': horizon, 'results': results_for_this_horizon})

    print(f"\n\n{'='*30} OVERALL PERFORMANCE SUMMARY {'='*30}")
    # for res in all_horizon_results:
    #     h = res['horizon']
    #     buy_res = res['results'].get('Target_Buy', {})
    #     sell_res = res['results'].get('Target_Sell', {})

    #     print(f"\n----- Horizon: {h} bars -----")
    #     print(f"{'Metric':<20} | {'Target_Buy':<15} | {'Target_Sell':<15}")
    #     print(f"{'-'*20} | {'-'*15} | {'-'*15}")
    #     print(f"{'Accuracy':<20} | {buy_res.get('accuracy', 0.0):<15.4f} | {sell_res.get('accuracy', 0.0):<15.4f}")
    #     print(f"{'F1 Score (Weighted)':<20} | {buy_res.get('f1_weighted', 0.0):<15.4f} | {sell_res.get('f1_weighted', 0.0):<15.4f}")
    #     print(f"{'Precision (Signal)':<20} | {buy_res.get('precision_for_1', 0.0):<15.4f} | {sell_res.get('precision_for_1', 0.0):<15.4f}")
    #     print(f"{'Recall (Signal)':<20} | {buy_res.get('recall_for_1', 0.0):<15.4f} | {sell_res.get('recall_for_1', 0.0):<15.4f}")
    #     print(f"{'F1 Score (Signal)':<20} | {buy_res.get('f1_for_1', 0.0):<15.4f} | {sell_res.get('f1_for_1', 0.0):<15.4f}")

    # ✅ NEW: รวมผลลัพธ์ทุก Horizon
    combined_buy = {"accuracy": 0, "f1_weighted": 0, "precision_for_1": 0, "recall_for_1": 0, "f1_for_1": 0}
    combined_sell = {"accuracy": 0, "f1_weighted": 0, "precision_for_1": 0, "recall_for_1": 0, "f1_for_1": 0}
    count = len(all_horizon_results)

    for res in all_horizon_results:
        buy_res = res['results'].get('Target_Buy', {})
        sell_res = res['results'].get('Target_Sell', {})
        for k in combined_buy.keys():
            combined_buy[k] += buy_res.get(k, 0.0)
            combined_sell[k] += sell_res.get(k, 0.0)

    # หาค่าเฉลี่ย
    for k in combined_buy.keys():
        combined_buy[k] /= count
        combined_sell[k] /= count

    print(f"\n----- Horizon: COMBINED ({'+'.join(map(str,HORIZONS))} bars) -----")

    print(f"{'Metric':<20} | {'Target_Buy':<15} | {'Target_Sell':<15}")
    print(f"{'-'*20} | {'-'*15} | {'-'*15}")
    print(f"{'Accuracy':<20} | {combined_buy['accuracy']:<15.4f} | {combined_sell['accuracy']:<15.4f}")
    print(f"{'F1 Score (Weighted)':<20} | {combined_buy['f1_weighted']:<15.4f} | {combined_sell['f1_weighted']:<15.4f}")
    print(f"{'Precision (Signal)':<20} | {combined_buy['precision_for_1']:<15.4f} | {combined_sell['precision_for_1']:<15.4f}")
    print(f"{'Recall (Signal)':<20} | {combined_buy['recall_for_1']:<15.4f} | {combined_sell['recall_for_1']:<15.4f}")
    print(f"{'F1 Score (Signal)':<20} | {combined_buy['f1_for_1']:<15.4f} | {combined_sell['f1_for_1']:<15.4f}")

    print(f"\n{'='*82}\n")

    # ============================== OVERALL PERFORMANCE SUMMARY =======================
    # ----- Horizon: 10 bars -----
    # Metric               | Target_Buy      | Target_Sell
    # -------------------- | --------------- | ---------------
    # Accuracy             | 0.8283          | 0.8349
    # F1 Score (Weighted)  | 0.8280          | 0.8290
    # Precision (Signal)   | 0.6627          | 0.6811
    # Recall (Signal)      | 0.6555          | 0.5593
    # F1 Score (Signal)    | 0.6591          | 0.6142
    # ----- Horizon: 20 bars -----
    # Metric               | Target_Buy      | Target_Sell
    # -------------------- | --------------- | ---------------
    # Accuracy             | 0.8654          | 0.8752
    # F1 Score (Weighted)  | 0.8666          | 0.8739
    # Precision (Signal)   | 0.7294          | 0.7512
    # Recall (Signal)      | 0.7690          | 0.7069
    # F1 Score (Signal)    | 0.7487          | 0.7284
    # ==================================================================================
    
    # การดูค่า Accuracy (ความแม่นยำโดยรวม) เพียงอย่างเดียว อาจทำให้เข้าใจผิดได้ เพราะข้อมูลมักจะไม่มีความสมดุล (Imbalanced Data) 
    # คือมีช่วงเวลาที่ "ไม่มีสัญญาณ" (class 0) เยอะกว่าช่วงที่ "มีสัญญาณ" (class 1) มากๆ
    
    # ให้ความสำคัญกับค่าเหล่านี้แทน (โดยดูที่แถวของ class 1 ใน Classification Report):
    # 1. Precision (Signal): สำคัญที่สุด
    # คืออะไร: ในบรรดาสัญญาณทั้งหมดที่โมเดลสร้างขึ้น (เช่น ทายว่าเป็น Buy) มีกี่เปอร์เซ็นต์ที่ถูกต้องจริงๆ
    # ทำไมสำคัญ: ค่านี้บอกถึง "คุณภาพ" ของสัญญาณ ยิ่ง Precision สูง หมายความว่า สัญญาณหลอก (False Signals) ยิ่งน้อย ซึ่งสำคัญมากในการเทรดจริง เพราะทุกครั้งที่เข้าเทรดตามสัญญาณหลอกคือการขาดทุน
    
    # 2. Recall (Signal):
    # คืออะไร: ในบรรดา "โอกาส" ในการเข้าเทรดทั้งหมดที่มีในข้อมูล โมเดลของคุณสามารถค้นหาเจอได้กี่เปอร์เซ็นต์
    # ทำไมสำคัญ: ค่านี้บอกถึง "ปริมาณ" ของสัญญาณ ถ้า Recall ต่ำเกินไป หมายความว่าโมเดลของคุณพลาดโอกาสดีๆ ไปเยอะ
    
    # 3. F1-Score (Signal):
    # คืออะไร: เป็นค่าเฉลี่ยแบบ Harmonic ของ Precision และ Recall ใช้เป็นตัวเลขเดียวเพื่อวัดผลโดยรวมของสัญญาณ
    # ทำไมสำคัญ: เป็นค่าที่ดีที่สุดในการเปรียบเทียบโมเดลแต่ละเวอร์ชัน ถ้าคุณปรับแก้โมเดลแล้วค่า F1-Score (ของ class 1) สูงขึ้น ถือว่าโมเดลมีประสิทธิภาพดีขึ้น เพราะมันหมายถึงโมเดลมีความสมดุลที่ดีขึ้นระหว่างการสร้างสัญญาณที่แม่นยำ (Precision) และการไม่พลาดโอกาส (Recall)
    
    # สรุปง่ายๆ: เวลาคุณปรับแก้ Feature หรือ Parameter ต่างๆ ให้ดูที่ตารางสรุปผลที่ผมเพิ่มให้ และเปรียบเทียบค่าต่างๆ โดยให้ความสำคัญตามลำดับนี้:
    # F1 Score (Signal): สูงขึ้นคือดีขึ้น (เป็นการวัดผลแบบองค์รวมที่ดีที่สุด)
    # Precision (Signal): พยายามทำให้สูงที่สุดเท่าที่เป็นไปได้ เพื่อลดการขาดทุนจากสัญญาณหลอก
    # Recall (Signal): ดูประกอบกัน ไม่ควรต่ำจนเกินไป

    # ==================================================================================

# ====================================================================
# Model Testing and Visualization Function
# ====================================================================

def test_and_visualize_model(df_horizon, symbol, group_name, target_col, horizon, X_val, X_test):
    """
    Loads all models for a given horizon, makes predictions, and visualizes
    the combined buy/sell signals on a single chart.
    This function is triggered only for the last target in the training loop.
    """
    if target_col != TARGETS[-1]:
        print(f"--- Skipping plot for {target_col}, will be combined with {TARGETS[-1]}. ---")
        return

    print(f"\n--- Generating Combined Visualization for h{horizon} ---")

    try:
        # This DataFrame contains only the feature columns for prediction.
        X_for_prediction = pd.concat([X_val, X_test]).sort_index()

        # This is the base DataFrame for plotting. It contains ALL original columns
        # (including OHLC and DateTime) for the rows in our visualization set.
        plot_df = df_horizon.loc[X_for_prediction.index].copy()

        for t_col in TARGETS:
            print(f"--- Loading model and predicting for {t_col} ---")
            try:
                model_filename = f"{symbol}_{group_name}_{t_col}_h{horizon}_model.joblib"
                model_path = os.path.join(OUTPUT_DIR, model_filename)
                model = joblib.load(model_path)

                scaler_filename = f"{symbol}_{group_name}_{t_col}_h{horizon}_scaler.joblib"
                scaler_path = os.path.join(OUTPUT_DIR, scaler_filename)
                scaler = joblib.load(scaler_path)

                print(f"✅ Loaded model and scaler for {t_col}")

                # IMPORTANT: Predict using the original features-only DataFrame (X_for_prediction)
                numeric_features = X_for_prediction.select_dtypes(include=np.number).columns.tolist()
                X_scaled = X_for_prediction.copy()
                X_scaled.loc[:, numeric_features] = scaler.transform(X_for_prediction[numeric_features])

                predictions = model.predict(X_scaled)
                # Add the prediction column to our complete plot_df
                plot_df[f'Prediction_{t_col}'] = predictions

            except FileNotFoundError:
                print(f"⚠️ Could not find model/scaler for {t_col}. It will be skipped in the plot.")
                plot_df[f'Prediction_{t_col}'] = 0

        # Set DateTime as index for plotting
        plot_df['DateTime'] = pd.to_datetime(plot_df['DateTime'])
        plot_df.set_index('DateTime', inplace=True)
        plot_df = plot_df.tail(200)

        # This part should now work correctly as plot_df has 'Low' and 'High'
        buy_signals = pd.Series(np.nan, index=plot_df.index)
        sell_signals = pd.Series(np.nan, index=plot_df.index)

        if 'Prediction_Target_Buy' in plot_df.columns:
            buy_mask = plot_df['Prediction_Target_Buy'] == 1
            buy_signals.loc[buy_mask] = plot_df.loc[buy_mask, 'Low'] * 0.98

        if 'Prediction_Target_Sell' in plot_df.columns:
            sell_mask = plot_df['Prediction_Target_Sell'] == 1
            sell_signals.loc[sell_mask] = plot_df.loc[sell_mask, 'High'] * 1.02

        add_plots = []
        if not buy_signals.dropna().empty:
            add_plots.append(mpf.make_addplot(buy_signals, type='scatter', marker='^', color='lime', markersize=100))
        if not sell_signals.dropna().empty:
            add_plots.append(mpf.make_addplot(sell_signals, type='scatter', marker='v', color='red', markersize=100))

        chart_title = f"{symbol} {group_name} - Combined Signals (h{horizon})"
        save_path = os.path.join(OUTPUT_DIR, f"{symbol}_{group_name}_h{horizon}_prediction_chart_COMBINED.png")

        # This plot call will now work as plot_df has OHLC columns
        mpf.plot(plot_df, 
                type='candle', 
                style='yahoo', 
                title=chart_title, 
                ylabel='Price', 
                addplot=add_plots if add_plots else None,
                volume=True, 
                panel_ratios=(3, 1),
                savefig=save_path)

        print(f"📈 Saved combined prediction chart to: {save_path}")

    except FileNotFoundError as e:
        print(f"❌ Error loading a critical file for visualization: {e}. Skipping plot.")
    except Exception as e:
        print(f"❌ An error occurred during visualization: {e}")
        import traceback
        traceback.print_exc()

# ====================================================================
# Main Execution
# ====================================================================

def main():
    """Main function to orchestrate the data processing and model training pipeline."""
    for group_name, group_files in TEST_GROUPS.items():
        for file_path in group_files:
            
            file_name = os.path.basename(file_path)
            symbol = file_name.split('_')[0]
            timeframe = group_name
            print(f"symbol {symbol} timeframe {timeframe}")

            try:
                df_clean = load_and_clean_data(symbol, timeframe, file_path)
                df_with_features = create_features(symbol, timeframe, df_clean)
                df_ready_to_train = preprocess_for_training(symbol, timeframe, df_with_features)
                train_evaluate_and_save(df_ready_to_train, symbol, timeframe, group_name)

            except Exception as e:
                print(f"❌ An error occurred while processing {file_path}: {e}")
                import traceback
                traceback.print_exc()

if __name__ == "__main__":
    main()