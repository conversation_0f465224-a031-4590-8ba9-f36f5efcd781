import os
import pandas as pd
import numpy as np
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import lightgbm as lgb
from sklearn.metrics import accuracy_score, f1_score
import mplfinance as mpf

# ====================================================================
# Configuration
# ====================================================================
TEST_GROUPS = {
    "M60": [
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    ]
}
OUTPUT_DIR = "LightGBM_Data"
HORIZONS = [10, 20]
TARGETS = ['Target_Buy', 'Target_Sell']

# ====================================================================
# Feature Engineering Helper Functions
# ====================================================================

# --- EMA Calculation (Classic, seeded with SMA) ---
def classic_ema(series, span):
    sma = series.rolling(window=span, min_periods=span).mean()
    ema = pd.Series(index=series.index, dtype=float)
    alpha = 2 / (span + 1)

    for i in range(len(series)):
        if i < span-1:
            ema.iloc[i] = np.nan
        elif i == span-1:
            ema.iloc[i] = sma.iloc[i]
        else:
            ema.iloc[i] = alpha * series.iloc[i] + (1 - alpha) * ema.iloc[i-1]
    return ema

def classic_macd_ema(series: pd.Series, span: int):
    s = pd.to_numeric(series, errors='coerce').copy()
    alpha = 2.0 / (span + 1.0)
    out = pd.Series(index=s.index, dtype='float64')

    # หาตำแหน่งเริ่มต้นที่มี value จริง
    valid_idx = s.first_valid_index()
    if valid_idx is None:
        return out

    start = s.index.get_loc(valid_idx)
    n = len(s)

    # ถ้ามีข้อมูลมากพอสำหรับ seed SMA
    if n - start >= span:
        # seed position at index start+span-1
        seed_pos = start + span - 1
        seed = s.iloc[start: start + span].mean()
        out.iloc[seed_pos] = seed
        # recursive
        for i in range(seed_pos + 1, n):
            out.iloc[i] = alpha * s.iloc[i] + (1 - alpha) * out.iloc[i - 1]
        # ข้อดี: ค่าก่อน seed_pos จะยังคง NaN
    else:
        # fallback: ถ้าไม่พอข้อมูล ให้ใช้ pandas ewm (จะ seed ด้วยค่าที่มี)
        out = s.ewm(span=span, adjust=False).mean()

    return out

def mt5_like_macd_seeded(price: pd.Series, fast=12, slow=26, signal=9):
    price = pd.to_numeric(price, errors='coerce')
    ema_fast = classic_macd_ema(price, fast)
    ema_slow = classic_macd_ema(price, slow)
    macd_line = ema_fast - ema_slow

    # สำหรับ signal line เรามักจะใช้ EMA บน macd_line (และสามารถ seed ด้วย SMA ของ macd ส่วนเริ่ม)
    signal_line = classic_macd_ema(macd_line, signal)
    hist = macd_line - signal_line
    return ema_fast, ema_slow, macd_line, signal_line, hist

# ====================================================================
# Data Processing Functions
# ====================================================================

# -> pd.DataFrame: คือ type hint ของ Python
# df: pd.DataFrame → บอกว่า parameter df ควรเป็น pandas.DataFrame
# -> pd.DataFrame → บอกว่า function จะ return DataFrame กลับมา
# editor/IDE (เช่น VS Code, PyCharm, mypy) จะช่วยเช็ก type ถ้าส่ง type ผิด จะเตือนตอนเขียนโค้ด (static analysis)
# คนอื่นเห็นฟังก์ชันก็รู้ทันทีว่า input / output เป็นอะไร

def load_and_clean_data(symbol, timeframe, file_path: str) -> pd.DataFrame:
    """Loads data from a CSV file, cleans it, and standardizes the DateTime column."""
    print(f"\n--- Loading and Cleaning {os.path.basename(file_path)} ---")
    
    # อ่านไฟล์
    # import pandas as pd
    # file_path = "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    # df = pd.read_csv(file_path)

    df = pd.read_csv(file_path)
    print(f"Initial rows: {len(df)}")
    
    # Drop the first row if it's not header-related
    if "Date" not in df.columns and "Time" not in df.columns:
        df = df.iloc[1:].reset_index(drop=True)

    # Combine Date and Time into a single DateTime column
    if 'Date' in df.columns and 'Time' in df.columns:
        df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'], errors='coerce')

        # กรณีต้องการนำออก 'Date', 'Time' ไม่ใช่ข้อมูลตัวเลข (non-numeric) ทำให้ตอนเทรนมีปันหา
        # df.drop(['Date', 'Time'], axis=1, inplace=True)

        # กรณีต้องการให้ DateTime อยู่แถวแรกของข้อมูล
        # Reorder columns to have DateTime first
        # cols = ['DateTime'] + [col for col in df.columns if col != 'DateTime']
        # df = df[cols]

        print(f"DateTime column created. Date range: {df['DateTime'].min()} to {df['DateTime'].max()}")
    else:
        print("⚠️ 'Date' or 'Time' column not found.")

    # Ensure numeric types for price columns
    for col in ['Open', 'High', 'Low', 'Close']:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # ตรวจสอบว่า row แรกมีปัญหา
    # print(df.head(5))

    # ถ้ารู้ว่าแถวแรกไม่ใช่ข้อมูลที่ต้องใช้:
    df = df.iloc[1:].reset_index(drop=True)

    # รายชื่อคอลัมน์ที่ไม่ใช้ ต้องการลบออก
    drop_cols = ["Vol","Col_8"]
    df = df.drop(columns=drop_cols, errors="ignore")

    # เปลี่ยนชื่อคอลัมน์ 'TickVol' เป็น 'Volume'
    if 'TickVol' in df.columns:
        df = df.rename(columns={'TickVol': 'Volume'})

    # ตรวจสอบว่า row แรกมีปัญหา
    # print(df.head(5))

    # ==============================================
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_data = "01_Data"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_data}.csv"        # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name) # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

    # ✅ บันทึกไฟล์ CSV
    df.to_csv(new_file_path, index=False)

    print(f"\n✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    return df

def create_features(symbol, timeframe, df: pd.DataFrame) -> pd.DataFrame:
    """Creates technical indicators and price action features."""
    print(f"\n--- Creating Features ---")
    df_feat = df.copy()

    # Price Action
    df_feat["Bar_CL"] = 0.0
    df_feat.loc[df_feat['Close'] > df_feat['Open'], "Bar_CL"] = 1.0
    df_feat.loc[df_feat['Close'] < df_feat['Open'], "Bar_CL"] = -1.0

    df_feat['Price_Range'] = df_feat["High"] - df_feat["Low"]
    df_feat['Price_Move'] = df_feat["Close"] - df_feat["Open"]

    # เรียกใช้จาก pandas ที่มีอยู่แล้ว SMA EMA
    # df_feat['EMA50'] = df_feat['Close'].ewm(span=50, adjust=False).mean() # ต้องการ EMA50
    # df_feat['SMA50'] = df_feat['Close'].rolling(window=50).mean() # ต้องการ SMA50

    # adjust=True (ค่า default)
    # → คำนวณ EMA แบบใช้ weights ของทุกค่าในอดีต
    # → สูตรเหมือน "ค่าเฉลี่ยเลขชี้กำลังเต็มรูป"
    # adjust=False
    # → คำนวณ EMA แบบ recursive (ใช้ค่า EMA ก่อนหน้า)
    # → เป็น สูตรที่ใช้ในวงการการเงิน/การเทรดจริง (เช่นใน MT4/MT5, TradingView)
    # ถ้าไม่ใส่ adjust เลย pandas จะใช้ค่า default = True ครับ

    # EMAs จากฟังชั่น classic_ema
    df_feat['EMA50'] = classic_ema(df_feat['Close'], 50)
    df_feat['EMA100'] = classic_ema(df_feat['Close'], 100)
    df_feat['EMA200'] = classic_ema(df_feat['Close'], 200)

    # เรียกใช้จาก pandas ไม่มีฟังก์ชัน MACD
    # # คำนวณ EMA12 และ EMA26
    # df_feat['EMA12'] = df_feat['Close'].ewm(span=12, adjust=False).mean()
    # df_feat['EMA26'] = df_feat['Close'].ewm(span=26, adjust=False).mean()
    # # MACD Line
    # df_feat['MACD'] = df_feat['EMA12'] - df_feat['EMA26']
    # # Signal Line (EMA9 ของ MACD)
    # df_feat['Signal'] = df_feat['MACD'].ewm(span=9, adjust=False).mean()
    # # Histogram
    # df_feat['Histogram'] = df_feat['MACD'] - df_feat['Signal']

    # MACD จากฟังชั่น mt5_like_macd_seeded
    ema12, ema26, macd_line, macd_signal, macd_hist = mt5_like_macd_seeded(df['Close'])

    df_feat['EMA12'] = ema12
    df_feat['EMA26'] = ema26
    df_feat['MACD'] = macd_line
    df_feat['MACD_SIGNAL'] = macd_signal
    df_feat['MACD_HIST'] = macd_hist

    # แสดงข้อมูลทั้งหมด
    print(f"\n✅ แสดงข้อมูล 5 แถวแรกทั้งหมด")
    print(df_feat.head())
    print(f"\n✅ แสดงข้อมูล 5 แถวท้ายทั้งหมด")
    print(df_feat.tail())
    print(f"\n✅ แสดงโครงสร้าง DataFrame")
    print(df_feat.info())

    # แสดงเฉพาะบางคอลัมน์
    print(f"\n✅ แสดงเฉพาะ Open Close Price_Range 5 แถวแรก")
    print(df_feat[['Open','Close','Price_Range']].head())

    # รายชื่อคอลัมน์ที่ไม่ใช้ ต้องการลบออก
    drop_cols = ["EMA12","EMA26"]
    df_feat = df_feat.drop(columns=drop_cols, errors="ignore")

    # ==============================================
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_features = "02a_Features"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_features}.csv"        # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name) # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

    # ✅ บันทึกไฟล์ CSV
    df_feat.to_csv(new_file_path, index=False)

    print(f"\n✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    # ลบแถวที่มี NaN
    df_feat = df_feat.dropna().reset_index(drop=True)

    # ==============================================
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_dropna = "02b_Dropna"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_dropna}.csv"          # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name) # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

    # ✅ บันทึกไฟล์ CSV
    df_feat.to_csv(new_file_path, index=False)

    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    print(f"\n Features created. DF shape: {df_feat.shape}")
    return df_feat

def preprocess_for_training(symbol, timeframe, df: pd.DataFrame) -> pd.DataFrame:
    """Prepares the dataframe for training by cleaning and adding scenario features."""
    print(f"\n--- Preprocessing for Training ---")
    
    # 1. Drop rows with NaN values from feature creation
    print(f"Rows before dropping NaN: {len(df)}")
    df_processed = df.dropna().reset_index(drop=True)
    print(f"Rows after dropping NaN: {len(df_processed)}")

    # 2. Drop unnecessary columns
    drop_cols = ["Vol", "Col_8", "Bar_CLp"] # Assuming Bar_CLp might exist from old code
    df_processed = df_processed.drop(columns=drop_cols, errors="ignore")

    # 3. Rename and ensure numeric types for volume
    if 'TickVol' in df_processed.columns:
        df_processed = df_processed.rename(columns={'TickVol': 'Volume'})

    # if 'Date' in df_processed.columns:
    #     df_processed['Date'] = pd.to_numeric(df_processed['Date'], errors='coerce').fillna(0)
    # if 'Time' in df_processed.columns:
    #     df_processed['Time'] = pd.to_numeric(df_processed['Time'], errors='coerce').fillna(0)
    # if 'Volume' in df_processed.columns:
    #     df_processed['Volume'] = pd.to_numeric(df_processed['Volume'], errors='coerce').fillna(0)

    cols_to_convert = ['Date', 'Time', 'Volume'] # เขียนให้กระชับขึ้น
    for col in cols_to_convert:
        if col in df_processed.columns:
            df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce').fillna(0)

    # 4. Create Market Scenario features
    conditions = [
        (df_processed['Close'] > df_processed['EMA200']) & (df_processed['Low'] > df_processed['EMA200']) & (df_processed['MACD'] > df_processed['MACD_SIGNAL']),
        (df_processed['Close'] < df_processed['EMA200']) & (df_processed['High'] < df_processed['EMA200']) & (df_processed['MACD'] < df_processed['MACD_SIGNAL']),
    ]
    choices = ['Uptrend', 'Downtrend']
    df_processed['Market_Scenario'] = np.select(conditions, choices, default='Natural') # np.select(เงื่อนไข, ทางเลือก, ค่าเริ่มต้น)
    df_processed = pd.get_dummies(df_processed, columns=['Market_Scenario'], prefix='Scenario')
    
    # ==============================================
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_process = "03_Process"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_process}.csv"        # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name) # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

    # ✅ บันทึกไฟล์ CSV
    df_processed.to_csv(new_file_path, index=False)

    print(f"\n✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    print("Preprocessing complete.")
    return df_processed

# ====================================================================
# Model Training and Evaluation Function
# ====================================================================

def train_evaluate_and_save(df: pd.DataFrame, symbol: str, group_name: str):
    """Loops through horizons and targets to train, evaluate, and save models and scalers."""
    print(f"\n{'='*60}")
    print(f"STARTING TRAINING FOR: {symbol} - {group_name}")
    print(f"{'='*60}")

    os.makedirs(OUTPUT_DIR, exist_ok=True)

    for horizon in HORIZONS:
        print(f"\n--- Processing Horizon: {horizon} bars ---")
        
        # 1. Create Targets for the current horizon
        df_horizon = df.copy()
        df_horizon['Next_Close'] = df_horizon['Close'].shift(-horizon)
        df_horizon['Target_Buy'] = ((df_horizon['Close'] > df_horizon['Open']) & (df_horizon['Next_Close'] > df_horizon['Close'])).astype(int)
        df_horizon['Target_Sell'] = ((df_horizon['Close'] < df_horizon['Open']) & (df_horizon['Next_Close'] < df_horizon['Close'])).astype(int)
        df_horizon.dropna(subset=['Next_Close', 'Target_Buy', 'Target_Sell'], inplace=True)

        for target_col in TARGETS:
            print(f"\n🎯 Training for Target: {target_col} (Horizon: {horizon})")

            # 2. Define Features (X) and Target (y)
            feature_columns = [col for col in df_horizon.columns if col not in ['DateTime', 'Next_Close', 'Target_Buy', 'Target_Sell']]
            # df_horizon.columns = รายชื่อทุกคอลัมน์ใน DataFrame
            # list comprehension จะวนไปทีละคอลัมน์ แล้วเก็บไว้ถ้าไม่ใช่ ['DateTime', 'Next_Close', 'Target_Buy', 'Target_Sell'] 
            # ผลลัพธ์ = list ของคอลัมน์ที่เอาไว้ใช้เป็น feature (ตัวแปรอิสระ)
            X = df_horizon[feature_columns] 
            # เลือกเฉพาะคอลัมน์ที่เป็น feature และ
            # X จึงกลายเป็น DataFrame ที่เก็บเฉพาะค่าตัวแปรอิสระ (ข้อมูลอินพุตที่จะใช้ train โมเดล)
            y = df_horizon[target_col]
            # เลือกเฉพาะคอลัมน์ที่เป็นเป้าหมาย (Target) ตามชื่อที่เก็บใน target_col และ
            # y คือ series/เวกเตอร์ของ label ที่จะใช้เป็น output ของโมเดล

            if y.nunique() < 2:
                print(f"⚠️ Skipping {target_col} for Horizon {horizon} due to single class in target.")
                continue

            # 3. Split Data (Train 60%, Validation 20%, Test 20%)
            # First, split into training (60%) and a temporary set (40%)
            X_train, X_temp, y_train, y_temp = train_test_split(X, y, test_size=0.4, random_state=42, stratify=y)
            # X = features (อินพุตของโมเดล)
            # y = target (ค่าที่โมเดลต้องการทำนาย)
            # test_size=0.4 หมายถึง ใช้ 40% ของข้อมูลทั้งหมด ไปอยู่ในชุดทดสอบ (X_temp, y_temp) ที่เหลือ 60% เป็นชุดฝึก (X_train, y_train)
            # random_state=42 หมายถึง ใช้เป็น seed สำหรับการสุ่ม เพื่อให้ผลการแบ่งข้อมูล เหมือนเดิมทุกครั้ง (reproducible)
            # stratify=y หมายถึง ทำให้การแบ่ง train/test รักษาสัดส่วนของ class ใน y เช่น ถ้า y มีค่า 1 = 70% และ 0 = 30% ถ้าไม่ใส่ stratify แล้ว y ไม่บาลานซ์ อาจได้ชุด train/test ที่ bias

            # Now, split the temporary set into validation (50% of 40% = 20%) and test (50% of 40% = 20%)
            X_val, X_test, y_val, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp)

            # 4. Scale Features and Save Scaler
            scaler = StandardScaler()
            numeric_features = X_train.select_dtypes(include=np.number).columns.tolist()
            
            X_train = X_train.copy()
            X_val = X_val.copy()
            X_test = X_test.copy()
            
            scaler.fit(X_train[numeric_features])
            
            scaler_filename = f"{symbol}_{group_name}_{target_col}_h{horizon}_scaler.joblib"
            scaler_path = os.path.join(OUTPUT_DIR, scaler_filename)
            joblib.dump(scaler, scaler_path)
            print(f"💾 Saved Scaler to: {scaler_path}")

            X_train.loc[:, numeric_features] = scaler.transform(X_train[numeric_features])
            X_val.loc[:, numeric_features] = scaler.transform(X_val[numeric_features])
            X_test.loc[:, numeric_features] = scaler.transform(X_test[numeric_features])

            # 5. Train Model
            print("\nTraining model with validation set for early stopping...")
            model = lgb.LGBMClassifier(objective='binary', random_state=42)

            # model.fit(X_train, y_train,
            #         eval_set=[(X_val, y_val)],
            #         eval_metric='logloss',
            #         callbacks=[lgb.early_stopping(10, verbose=False)])

            # model.fit(X_train, y_train, ...)
            # เป็นคำสั่ง ฝึกโมเดล (train model) โดยใช้
            # X_train = features ของชุดฝึก
            # y_train = target ของชุดฝึก

            # eval_set=[(X_val, y_val)]
            # ส่งชุด validation ให้ LightGBM เพื่อติดตาม performance ระหว่าง training
            # โมเดลจะดูว่า train ไปเรื่อย ๆ แล้ว ผลลัพธ์บน validation ดีขึ้นหรือแย่ลง

            # eval_metric='logloss'
            # กำหนด metric ที่ใช้วัดคุณภาพโมเดลระหว่างการฝึก
            # 'logloss' (logarithmic loss) = metric สำหรับ classification
            # ค่ายิ่ง ต่ำ = โมเดลยิ่งดี
            # logloss จะ penalize การทำนายที่มั่นใจผิด (confidence สูงแต่ผิด)

            # callbacks=[lgb.early_stopping(10, verbose=False)]
            # ใช้ early stopping เพื่อตัดการฝึกก่อนครบจำนวนรอบ (iterations) ถ้า performance ไม่ดีขึ้น
            # (10) = ถ้า validation ไม่ดีขึ้นภายใน 10 รอบติดต่อกัน → หยุดการฝึก
            # verbose=False = ไม่ต้องพิมพ์ log ของ early stopping

            # model.fit(X_train, y_train)
            # ฝึกโมเดล ตรงไปตรงมา
            # ใช้ข้อมูล X_train, y_train เพียงอย่างเดียว
            # ไม่มี validation set ตรวจ performance ระหว่าง training
            # ไม่มี early stopping → โมเดลจะ train จนครบจำนวนรอบ (n_estimators)
            # ไม่มีการ log progress ให้เห็นว่าค่า loss/metric เป็นอย่างไรในแต่ละรอบ

            # model.fit(X_train, y_train,
            #         eval_set=[(X_val, y_val)],
            #         eval_metric='logloss',
            #         callbacks=[lgb.early_stopping(10, verbose=False)])
            # eval_set=[(X_val, y_val)]
            # + ส่ง validation set ให้โมเดลตรวจสอบ performance ระหว่าง train
            # + ทำให้เห็นว่าโมเดล เรียนรู้ดีขึ้นหรือแย่ลงบนชุด unseen
            # eval_metric='logloss'
            # + ใช้วัดความถูกต้องระหว่าง training/validation
            # + logloss ต่ำ = โมเดลดียิ่งขึ้น
            # callbacks=[lgb.early_stopping(10, verbose=True)]
            # + ถ้า validation metric ไม่ดีขึ้น 10 รอบติดต่อกัน → หยุด training
            # + ช่วยลด overfitting และประหยัดเวลา

            # กรณีต้องการติดตามผลการเทรน ระหว่างการเทรน (During Training) 
            # ปับค่า verbose จาก Fasle > True จะแสดงค่า logloss ของ validation set ทุกๆ 10 รอบ
            model.fit(X_train, y_train,
                    eval_set=[(X_val, y_val)],
                    eval_metric='logloss',
                    callbacks=[lgb.early_stopping(10, verbose=True), lgb.log_evaluation(period=10)])
            # lgb.log_evaluation(period=10)
            # + พิมพ์ค่า metric ทุก 10 รอบ → เห็น trend การฝึก

            # 6. Evaluate Model on Test Set
            print("\n--- Model Evaluation on Validation Set ---")
            from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

            y_pred = model.predict(X_test)

            # Calculate and print metrics
            accuracy = accuracy_score(y_test, y_pred)

            print(f"Accuracy: {accuracy:.4f}")
            # Accuracy: 0.8147  (หรือ 81.47%)
            # หมายถึง โมเดลทำนายถูกต้อง 81% ของข้อมูลทั้งหมด
            # แต่! ต้องระวัง ถ้า class ไม่บาลานซ์ (เช่น class 0 เยอะกว่ามาก) ค่า accuracy สูงอาจไม่ได้หมายความว่าดีจริง

            print("Classification Report:")
            print(classification_report(y_val, y_pred))

            #                   precision   recall  f1-score   support
            #            0      0.76        0.81    0.79       10967
            #            1      0.24        0.19    0.21       3396
            #     accuracy                          0.66       14363
            #    macro avg      0.50        0.50    0.50       14363
            # weighted avg      0.64        0.66    0.65       14363

            # Class 0
            # precision = 0.76 → เวลาทำนายว่า "0" โมเดลถูกต้อง 76%
            # recall = 0.81 → โมเดลจับ class 0 ได้ครบ 81%
            # f1 = 0.79 → ค่ากลางระหว่าง precision/recall → ถือว่าดี

            # Class 1 (น้อยกว่า class 0)
            # precision = 0.24 → เวลาทำนายว่า "1" ถูกจริงแค่ 24%
            # recall = 0.19 → จาก class 1 ทั้งหมด โมเดลจับได้แค่ 19%
            # f1 = 0.21 → ค่อนข้างต่ำ แปลว่าโมเดลไม่เก่งในการทำนาย class 1

            # accuracy (รวม) = 0.66 (66%)
            # macro avg (ค่าเฉลี่ยไม่ถ่วงน้ำหนักระหว่าง class) = 0.50 → บอกตรง ๆ ว่าโมเดล balanced ไม่ดี
            # weighted avg (ค่าเฉลี่ยถ่วงตามจำนวนข้อมูลแต่ละ class) = 0.65 → ถูกดึงขึ้นเพราะ class 0 มีเยอะ
            # 📌 แปลว่า: โมเดลเอียงไปทาง class 0 มาก ๆ (bias ต่อ class ที่มีเยอะกว่า)

            print("Confusion Matrix:")
            print(confusion_matrix(y_val, y_pred))

            # [[8899 2068]
            #  [2758  638]]
            # อ่านเป็นตาราง:
            #           ทำนาย 0	ทำนาย 1
            # จริง 0	 8899	  2068
            # จริง 1     2758	  638

            # 8899 → ทำนายถูกต้องว่าเป็น class 0
            # 2068 → ทำนายผิด → จริง ๆ เป็น 0 แต่โมเดลทำนายเป็น 1
            # 2758 → ทำนายผิด → จริง ๆ เป็น 1 แต่โมเดลทำนายเป็น 0 (เยอะมาก)
            # 638  → ทำนายถูกต้องว่าเป็น class 1
            # 📌 สรุป: โมเดลทำนาย class 0 ได้ดี แต่แทบไม่สามารถจับ class 1 ได้ (recall ต่ำมาก)

            f1 = f1_score(y_test, y_pred, average='weighted')
            print(f"📊 Test Set Accuracy: {accuracy:.4f}, F1-Score: {f1:.4f}")

            # 7. Save Model
            model_filename = f"{symbol}_{group_name}_{target_col}_h{horizon}_model.joblib"
            model_path = os.path.join(OUTPUT_DIR, model_filename)
            joblib.dump(model, model_path)
            print(f"\n💾 Saved Model to: {model_path}")

            # ==============================================
            # กำหนด suffix ที่ต้องการเติมท้าย
            suffix_visualize = "04_Visualize"

            # สร้างชื่อไฟล์ใหม่
            new_file_name = f"{group_name}_{symbol}_{suffix_visualize}.csv"        # 'GOLD_H1_FIXED_cleaned.csv'
            new_file_path = os.path.join(OUTPUT_DIR, new_file_name) # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

            # ✅ บันทึกไฟล์ CSV
            df_horizon.to_csv(new_file_path, index=False)

            print(f"\n✅ Saved cleaned file to: {new_file_path}")
            # ==============================================
            
            # 8. Test and Visualize
            test_and_visualize_model(df_horizon, symbol, group_name, target_col, horizon, X_val, X_test)

# ====================================================================
# Model Testing and Visualization Function
# ====================================================================

def test_and_visualize_model(df_horizon, symbol, group_name, target_col, horizon, X_val, X_test):
    """
    Loads all models for a given horizon, makes predictions, and visualizes
    the combined buy/sell signals on a single chart.
    This function is triggered only for the last target in the training loop.
    """
    # Only generate the combined plot once, when called for the last target.
    if target_col != TARGETS[-1]:
        print(f"--- Skipping plot for {target_col}, will be combined with {TARGETS[-1]}. ---")
        return

    print(f"\n--- Generating Combined Visualization for h{horizon} ---")

    try:
        # 1. Use the current X_val and X_test as the base for visualization
        X_vis = pd.concat([X_val, X_test]).sort_index()
        plot_df = X_vis.copy()

        # 2. Load models and scalers for ALL targets to get predictions
        for t_col in TARGETS:
            print(f"--- Loading model and predicting for {t_col} ---")
            try:
                # Load Model and Scaler
                model_filename = f"{symbol}_{group_name}_{t_col}_h{horizon}_model.joblib"
                model_path = os.path.join(OUTPUT_DIR, model_filename)
                model = joblib.load(model_path)

                scaler_filename = f"{symbol}_{group_name}_{t_col}_h{horizon}_scaler.joblib"
                scaler_path = os.path.join(OUTPUT_DIR, scaler_filename)
                scaler = joblib.load(scaler_path)
                print(f"✅ Loaded model and scaler for {t_col}")

                # Scale features using the correct scaler for the model
                numeric_features = X_vis.select_dtypes(include=np.number).columns.tolist()
                X_vis_scaled = X_vis.copy()
                X_vis_scaled.loc[:, numeric_features] = scaler.transform(X_vis[numeric_features])

                # Make Predictions and add to the plot_df
                predictions = model.predict(X_vis_scaled)
                plot_df[f'Prediction_{t_col}'] = predictions

            except FileNotFoundError:
                print(f"⚠️ Could not find model/scaler for {t_col}. It will be skipped in the plot.")
                plot_df[f'Prediction_{t_col}'] = 0 # Ensure column exists to prevent errors

        # 3. Prepare data for plotting
        plot_df['DateTime'] = df_horizon.loc[plot_df.index, 'DateTime']
        plot_df['DateTime'] = pd.to_datetime(plot_df['DateTime'])
        plot_df.set_index('DateTime', inplace=True)
        plot_df = plot_df.tail(200)

        # 4. Create plot markers for buy and sell signals
        buy_signals = pd.Series(np.nan, index=plot_df.index)
        sell_signals = pd.Series(np.nan, index=plot_df.index)

        if 'Prediction_Target_Buy' in plot_df.columns:
            buy_mask = plot_df['Prediction_Target_Buy'] == 1
            buy_signals.loc[buy_mask] = plot_df.loc[buy_mask, 'Low'] * 0.98

        if 'Prediction_Target_Sell' in plot_df.columns:
            sell_mask = plot_df['Prediction_Target_Sell'] == 1
            sell_signals.loc[sell_mask] = plot_df.loc[sell_mask, 'High'] * 1.02

        add_plots = []
        if not buy_signals.dropna().empty:
            add_plots.append(mpf.make_addplot(buy_signals, type='scatter', marker='^', color='lime', markersize=100))
        if not sell_signals.dropna().empty:
            add_plots.append(mpf.make_addplot(sell_signals, type='scatter', marker='v', color='red', markersize=100))

        # 5. Generate and save the single combined plot
        chart_title = f"{symbol} {group_name} - Combined Signals (h{horizon})"
        save_path = os.path.join(OUTPUT_DIR, f"{symbol}_{group_name}_h{horizon}_prediction_chart_COMBINED.png")

        mpf.plot(plot_df, 
                type='candle', 
                style='yahoo', 
                title=chart_title, 
                ylabel='Price', 
                addplot=add_plots if add_plots else None,
                volume=True, 
                panel_ratios=(3, 1),
                savefig=save_path)

        print(f"📈 Saved combined prediction chart to: {save_path}")

    except FileNotFoundError as e:
        print(f"❌ Error loading a critical file for visualization: {e}. Skipping plot.")
    except Exception as e:
        print(f"❌ An error occurred during visualization: {e}")
        import traceback
        traceback.print_exc()

# ====================================================================
# Main Execution
# ====================================================================

def main():
    """Main function to orchestrate the data processing and model training pipeline."""
    for group_name, group_files in TEST_GROUPS.items():
        for file_path in group_files:
            
            # import os
            file_name = os.path.basename(file_path)  # GOLD_H1_FIXED.csv
            symbol = file_name.split('_')[0] # GOLD
            timeframe = group_name # M60
            print(f"symbol {symbol} timeframe {timeframe}")

            try:
                # Step 1: Load and clean the raw data
                df_clean = load_and_clean_data(symbol, timeframe, file_path)
                
                # Step 2: Create technical features
                df_with_features = create_features(symbol, timeframe, df_clean)
                
                # Step 3: Final preprocessing before training
                df_ready_to_train = preprocess_for_training(symbol, timeframe, df_with_features)
                
                # Step 4: Train, evaluate, and save models for all horizons
                train_evaluate_and_save(df_ready_to_train, symbol, group_name)

            except Exception as e:
                print(f"❌ An error occurred while processing {file_path}: {e}")
                import traceback
                traceback.print_exc()

if __name__ == "__main__":
    main()
