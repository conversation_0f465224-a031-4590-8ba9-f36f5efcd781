"""
🔧 Auto Parameter Loader for LightGBM_10_3.py
=============================================

ระบบโหลดพารามิเตอร์อัตโนมัติสำหรับ LightGBM_10_3.py
ดึงค่าจากผลการทดสอบ Multi-Asset Optimization
"""

import json
import os
import glob
from datetime import datetime

def auto_load_parameters_for_training(symbol, timeframe, show_details=True):
    """
    โหลดพารามิเตอร์อัตโนมัติสำหรับการเทรนโมเดล
    
    Args:
        symbol: ชื่อสินทรัพย์ (เช่น 'GOLD', 'EURUSD')
        timeframe: ช่วงเวลา (เช่น 30, 60, 'M30', 'H1')
        show_details: แสดงรายละเอียดการทดสอบ
    
    Returns:
        dict: พารามิเตอร์ที่ปรับปรุงแล้ว
    """
    
    # แปลง timeframe format
    timeframe_str = normalize_timeframe_for_search(timeframe)
    
    print(f"\n🔍 กำลังค้นหาพารามิเตอร์ที่เหมาะสมสำหรับ {symbol} {timeframe_str}...")
    
    # หาไฟล์ผลลัพธ์ล่าสุด
    multi_asset_files = glob.glob("multi_asset_results_*.json")
    
    if not multi_asset_files:
        print("⚠️ ไม่พบไฟล์ผลการทดสอบ Multi-Asset")
        return get_default_parameters()
    
    # เลือกไฟล์ล่าสุด
    latest_file = max(multi_asset_files, key=os.path.getmtime)
    file_date = datetime.fromtimestamp(os.path.getmtime(latest_file))
    
    print(f"📁 ใช้ไฟล์: {os.path.basename(latest_file)} (Modified: {file_date.strftime('%Y-%m-%d %H:%M')})")
    
    try:
        with open(latest_file, 'r') as f:
            data = json.load(f)
        
        # ค้นหาพารามิเตอร์เฉพาะสินทรัพย์
        asset_params = find_asset_specific_parameters(data, symbol, timeframe_str)
        
        if asset_params:
            if show_details:
                display_optimization_results(asset_params, f"{symbol}_{timeframe_str} specific")
            return convert_to_lightgbm_params(asset_params['parameters'])
        
        # ถ้าไม่พบ ใช้พารามิเตอร์ที่ดีที่สุด
        print(f"⚠️ ไม่พบพารามิเตอร์เฉพาะสำหรับ {symbol}_{timeframe_str}")
        print("🔄 ใช้พารามิเตอร์จากสินทรัพย์ที่ดีที่สุด...")
        
        best_params = find_best_parameters(data)
        if best_params:
            if show_details:
                display_optimization_results(best_params, "Best performing asset")
            return convert_to_lightgbm_params(best_params['parameters'])
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการอ่านไฟล์: {e}")
    
    # Fallback ไปใช้ค่า default
    print("🔄 ใช้พารามิเตอร์ default...")
    return get_default_parameters()

def normalize_timeframe_for_search(timeframe):
    """แปลง timeframe ให้เป็นรูปแบบที่ใช้ในการค้นหา"""
    if isinstance(timeframe, str):
        timeframe = timeframe.upper()
        if timeframe in ['H1', '60', 'M60']:
            return 'M60'
        elif timeframe in ['M30', '30']:
            return 'M30'
        return timeframe
    elif isinstance(timeframe, int):
        if timeframe == 60:
            return 'M60'
        elif timeframe == 30:
            return 'M30'
        return f'M{timeframe}'
    return str(timeframe)

def find_asset_specific_parameters(data, symbol, timeframe_str):
    """ค้นหาพารามิเตอร์เฉพาะสินทรัพย์"""
    for result in data['results']:
        if result['symbol'] == symbol and result['timeframe'] == timeframe_str:
            best_result = result['best_result']
            metrics = best_result['results']
            
            return {
                'symbol': result['symbol'],
                'timeframe': result['timeframe'],
                'score': best_result['performance_score'],
                'win_rate': metrics.get('win_rate', 0),
                'total_profit': metrics.get('total_profit', 0),
                'total_trades': metrics.get('total_trades', 0),
                'expectancy': metrics.get('expectancy', 0),
                'max_drawdown': metrics.get('max_drawdown', 0),
                'parameters': best_result['parameters']
            }
    return None

def find_best_parameters(data):
    """ค้นหาพารามิเตอร์ที่ดีที่สุด"""
    try:
        best_result_data = max(data['results'], key=lambda x: x['best_result']['performance_score'])
        best_result = best_result_data['best_result']
        metrics = best_result['results']
        
        return {
            'symbol': best_result_data['symbol'],
            'timeframe': best_result_data['timeframe'],
            'score': best_result['performance_score'],
            'win_rate': metrics.get('win_rate', 0),
            'total_profit': metrics.get('total_profit', 0),
            'total_trades': metrics.get('total_trades', 0),
            'expectancy': metrics.get('expectancy', 0),
            'max_drawdown': metrics.get('max_drawdown', 0),
            'parameters': best_result['parameters']
        }
    except:
        return None

def display_optimization_results(asset_data, source_type):
    """แสดงผลการทดสอบแบบละเอียด"""
    print("=" * 70)
    print("🎯 PARAMETER OPTIMIZATION RESULTS")
    print("=" * 70)
    print(f"📊 Source: {source_type}")
    print(f"")
    print(f"🔸 {asset_data['symbol']}_{asset_data['timeframe']}")
    print(f"   Score: {asset_data['score']:.2f}")
    print(f"   Win Rate: {asset_data['win_rate']:.1f}%")
    print(f"   Total Profit: ${asset_data['total_profit']:,.0f}")
    print(f"   Total Trades: {asset_data['total_trades']}")
    print(f"   Expectancy: {asset_data['expectancy']:.2f}")
    print(f"   Max Drawdown: ${asset_data['max_drawdown']:,.0f}")
    print(f"   ")
    print(f"   Best Parameters:")
    params = asset_data['parameters']
    print(f"     SL ATR: {params.get('input_stop_loss_atr', 'N/A')}")
    print(f"     TP Ratio: {params.get('input_take_profit', 'N/A')}")
    print(f"     RSI Level: {params.get('input_rsi_level_in', 'N/A')}")
    print(f"     Volume Spike: {params.get('input_volume_spike', 'N/A')}")
    if 'input_initial_nbar_sl' in params:
        print(f"     nBars SL: {params['input_initial_nbar_sl']}")
    print("=" * 70)

def convert_to_lightgbm_params(optimization_params):
    """แปลงพารามิเตอร์จากการทดสอบให้เป็นรูปแบบที่ LightGBM_10_3.py ใช้"""
    lightgbm_params = {}
    
    # แมปพารามิเตอร์
    param_mapping = {
        'input_stop_loss_atr': 'input_stop_loss_atr',
        'input_take_profit': 'input_take_profit', 
        'input_rsi_level_in': 'input_rsi_level_in',
        'input_volume_spike': 'input_volume_spike',
        'input_initial_nbar_sl': 'input_initial_nbar_sl'
    }
    
    for opt_key, lgb_key in param_mapping.items():
        if opt_key in optimization_params:
            lightgbm_params[lgb_key] = optimization_params[opt_key]
    
    # เพิ่มพารามิเตอร์ที่ไม่เปลี่ยนแปลง
    default_params = get_default_parameters()
    for key, value in default_params.items():
        if key not in lightgbm_params:
            lightgbm_params[key] = value
    
    return lightgbm_params

def get_default_parameters():
    """ค่าพารามิเตอร์ default"""
    return {
        'input_volume_spike': 1.5,
        'input_rsi_level_in': 40,
        'input_rsi_level_over': 70,
        'input_rsi_level_out': 35,
        'input_stop_loss_atr': 1.25,
        'input_take_profit': 3.0,
        'input_pull_back': 0.45,
        'input_initial_nbar_sl': 4
    }

def apply_parameters_to_globals(params, globals_dict, show_changes=True):
    """
    นำพารามิเตอร์ไปใช้กับ global variables
    
    Args:
        params: พารามิเตอร์ที่ได้จาก auto_load_parameters_for_training()
        globals_dict: globals() จาก script หลัก
        show_changes: แสดงการเปลี่ยนแปลง
    """
    
    updated_params = []
    
    for param_key, param_value in params.items():
        if param_key in globals_dict:
            old_value = globals_dict[param_key]
            globals_dict[param_key] = param_value
            
            if show_changes:
                if old_value != param_value:
                    updated_params.append(f"{param_key}: {old_value} → {param_value}")
                else:
                    updated_params.append(f"{param_key}: {param_value} (unchanged)")
    
    if show_changes and updated_params:
        print(f"\n🔄 Updated Global Parameters:")
        for param in updated_params:
            print(f"   {param}")
        print()
    
    return len(updated_params)

def get_available_assets():
    """แสดงสินทรัพย์ที่มีผลการทดสอบ"""
    multi_asset_files = glob.glob("multi_asset_results_*.json")
    
    if not multi_asset_files:
        print("❌ ไม่พบไฟล์ผลการทดสอบ Multi-Asset")
        return []
    
    latest_file = max(multi_asset_files, key=os.path.getmtime)
    
    try:
        with open(latest_file, 'r') as f:
            data = json.load(f)
        
        assets = []
        for result in data['results']:
            assets.append({
                'symbol': result['symbol'],
                'timeframe': result['timeframe'],
                'score': result['best_result']['performance_score'],
                'win_rate': result['best_result']['results'].get('win_rate', 0)
            })
        
        # เรียงตามคะแนน
        assets.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"📊 Available Assets (from {os.path.basename(latest_file)}):")
        print(f"{'Rank':<4} {'Asset':<12} {'Score':<8} {'Win Rate':<10}")
        print("-" * 40)
        
        for i, asset in enumerate(assets, 1):
            print(f"{i:<4} {asset['symbol']}_{asset['timeframe']:<12} {asset['score']:<8.2f} {asset['win_rate']:<10.1f}%")
        
        return assets
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        return []

# ตัวอย่างการใช้งาน
if __name__ == "__main__":
    print("🔧 Auto Parameter Loader for LightGBM_10_3.py")
    print("=" * 50)
    
    # แสดงสินทรัพย์ที่มี
    assets = get_available_assets()
    
    if assets:
        print(f"\n🧪 ทดสอบโหลดพารามิเตอร์:")
        
        # ทดสอบกับ GOLD M30
        print(f"\n--- ทดสอบ GOLD M30 ---")
        params = auto_load_parameters_for_training("GOLD", "M30")
        
        # ทดสอบกับ GOLD M60
        print(f"\n--- ทดสอบ GOLD M60 ---")
        params = auto_load_parameters_for_training("GOLD", 60)
        
        # ทดสอบกับสินทรัพย์ที่ไม่มี
        print(f"\n--- ทดสอบสินทรัพย์ที่ไม่มี ---")
        params = auto_load_parameters_for_training("BTCUSD", "M15")
