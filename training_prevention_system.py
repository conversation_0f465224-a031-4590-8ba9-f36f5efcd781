
# ==============================================
# Training Prevention System
# ==============================================

import os
import json
from datetime import datetime, timedelta

class TrainingPreventionSystem:
    def __init__(self):
        self.training_log = "training_attempts.log"
        self.max_daily_attempts = 3
        self.cooldown_hours = 6
        
    def can_train_now(self, symbol, timeframe):
        """ตรวจสอบว่าสามารถเทรนได้หรือไม่"""
        
        today = datetime.now().date()
        recent_attempts = self._get_recent_attempts(symbol, timeframe, today)
        
        if len(recent_attempts) >= self.max_daily_attempts:
            print(f"⚠️ เทรนเกินจำนวนที่กำหนดแล้ววันนี้ ({len(recent_attempts)}/{self.max_daily_attempts})")
            return False, "daily_limit_exceeded"
        
        # ตรวจสอบ cooldown
        if recent_attempts:
            last_attempt = max(recent_attempts)
            time_since_last = datetime.now() - last_attempt
            
            if time_since_last < timedelta(hours=self.cooldown_hours):
                remaining_time = timedelta(hours=self.cooldown_hours) - time_since_last
                print(f"⚠️ ยังอยู่ในช่วง cooldown อีก {remaining_time}")
                return False, f"cooldown_{remaining_time}"
        
        return True, "allowed"
    
    def log_training_attempt(self, symbol, timeframe, result):
        """บันทึกการพยายามเทรน"""
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            'timeframe': timeframe,
            'result': result
        }
        
        # อ่าน log เดิม
        attempts = []
        if os.path.exists(self.training_log):
            with open(self.training_log, 'r') as f:
                attempts = [json.loads(line) for line in f if line.strip()]
        
        # เพิ่ม attempt ใหม่
        attempts.append(log_entry)
        
        # เก็บเฉพาะ 30 วันล่าสุด
        cutoff_date = datetime.now() - timedelta(days=30)
        attempts = [a for a in attempts if datetime.fromisoformat(a['timestamp']) > cutoff_date]
        
        # บันทึกกลับ
        with open(self.training_log, 'w') as f:
            for attempt in attempts:
                f.write(json.dumps(attempt) + '\n')
        
        print(f"📝 บันทึกการเทรน: {symbol}_{timeframe} → {result}")
    
    def _get_recent_attempts(self, symbol, timeframe, date):
        """ดึงการพยายามเทรนล่าสุด"""
        
        if not os.path.exists(self.training_log):
            return []
        
        attempts = []
        with open(self.training_log, 'r') as f:
            for line in f:
                if line.strip():
                    attempt = json.loads(line)
                    attempt_date = datetime.fromisoformat(attempt['timestamp']).date()
                    
                    if (attempt['symbol'] == symbol and 
                        attempt['timeframe'] == timeframe and 
                        attempt_date == date):
                        attempts.append(datetime.fromisoformat(attempt['timestamp']))
        
        return attempts

# การใช้งานใน LightGBM_10_4.py
# prevention_system = TrainingPreventionSystem()

# ก่อนเริ่มเทรน
# can_train, reason = prevention_system.can_train_now(symbol, timeframe)

# if not can_train:
#     print(f"🚫 ไม่สามารถเทรนได้: {reason}")
#     print("💡 กรุณารอหรือใช้โมเดลเดิม")
#     return  # ออกจากการเทรน
# else:
#     print(f"✅ สามารถเทรนได้: {reason}")

#     # เทรนโมเดล...

#     # บันทึกผลการเทรน
#     prevention_system.log_training_attempt(symbol, timeframe, "completed")
