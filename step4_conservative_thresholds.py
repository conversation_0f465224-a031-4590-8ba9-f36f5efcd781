
# ==============================================
# Conservative Threshold Management
# ==============================================

CONSERVATIVE_THRESHOLDS = {
    'min_trades_for_optimization': 100,
    'max_threshold_reduction': 0.3,  # ลดได้ไม่เกิน 30%
    'quality_over_quantity': True,
    'min_win_rate': 0.4,  # Win rate ขั้นต่ำ 40%
    'min_expectancy': 0,   # Expectancy ต้องเป็นบวก
    'max_drawdown': 0.2    # Max drawdown ไม่เกิน 20%
}

def conservative_threshold_optimization(trade_results, current_threshold=0.5):
    """ปรับ threshold แบบ conservative"""
    
    if len(trade_results) < CONSERVATIVE_THRESHOLDS['min_trades_for_optimization']:
        print(f"⚠️ ข้อมูลไม่เพียงพอ ({len(trade_results)} trades) - ใช้ threshold เดิม")
        return current_threshold
    
    # คำนวณ performance metrics
    win_rate = len([t for t in trade_results if t['profit'] > 0]) / len(trade_results)
    total_profit = sum([t['profit'] for t in trade_results])
    expectancy = total_profit / len(trade_results)
    
    # ตรวจสอบเกณฑ์ขั้นต่ำ
    if win_rate < CONSERVATIVE_THRESHOLDS['min_win_rate']:
        print(f"⚠️ Win Rate ต่ำ ({win_rate:.2%}) - เพิ่ม threshold")
        new_threshold = min(current_threshold * 1.1, 0.8)
        return new_threshold
    
    if expectancy < CONSERVATIVE_THRESHOLDS['min_expectancy']:
        print(f"⚠️ Expectancy ติดลบ ({expectancy:.2f}) - เพิ่ม threshold")
        new_threshold = min(current_threshold * 1.2, 0.8)
        return new_threshold
    
    # ถ้าผ่านเกณฑ์ทั้งหมด อาจลด threshold เล็กน้อย
    if win_rate > 0.6 and expectancy > 50:
        max_reduction = CONSERVATIVE_THRESHOLDS['max_threshold_reduction']
        new_threshold = max(current_threshold * (1 - max_reduction), 0.2)
        print(f"✅ Performance ดี - ลด threshold เป็น {new_threshold:.3f}")
        return new_threshold
    
    print(f"✅ Performance พอใช้ - ใช้ threshold เดิม {current_threshold:.3f}")
    return current_threshold

def validate_model_quality(performance_metrics):
    """ตรวจสอบคุณภาพโมเดล"""
    
    checks = []
    
    # Win Rate
    win_rate = performance_metrics.get('win_rate', 0)
    if win_rate >= CONSERVATIVE_THRESHOLDS['min_win_rate']:
        checks.append(('Win Rate', True, f"{win_rate:.2%}"))
    else:
        checks.append(('Win Rate', False, f"{win_rate:.2%} < {CONSERVATIVE_THRESHOLDS['min_win_rate']:.2%}"))
    
    # Expectancy
    expectancy = performance_metrics.get('expectancy', -999)
    if expectancy >= CONSERVATIVE_THRESHOLDS['min_expectancy']:
        checks.append(('Expectancy', True, f"{expectancy:.2f}"))
    else:
        checks.append(('Expectancy', False, f"{expectancy:.2f} < {CONSERVATIVE_THRESHOLDS['min_expectancy']}"))
    
    # Max Drawdown
    max_drawdown = performance_metrics.get('max_drawdown_pct', 1)
    if max_drawdown <= CONSERVATIVE_THRESHOLDS['max_drawdown']:
        checks.append(('Max Drawdown', True, f"{max_drawdown:.2%}"))
    else:
        checks.append(('Max Drawdown', False, f"{max_drawdown:.2%} > {CONSERVATIVE_THRESHOLDS['max_drawdown']:.2%}"))
    
    # สรุปผล
    passed_checks = sum([1 for _, passed, _ in checks if passed])
    total_checks = len(checks)
    
    print(f"\n📊 Model Quality Check: {passed_checks}/{total_checks} passed")
    for check_name, passed, value in checks:
        status = "✅" if passed else "❌"
        print(f"   {status} {check_name}: {value}")
    
    # ต้องผ่านอย่างน้อย 2 ใน 3 เกณฑ์
    quality_passed = passed_checks >= 2
    
    return quality_passed, checks
