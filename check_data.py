import pandas as pd

# ตรวจสอบไฟล์ข้อมูล
df = pd.read_csv('CSV_Files_Fixed/GOLD_H1_FIXED.csv')
print('📊 ข้อมูลไฟล์ GOLD_H1:')
print(f'   Shape: {df.shape}')
print(f'   Columns: {list(df.columns)}')

# ตรวจสอบ data types
print(f'\n📋 Data Types:')
for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
    if col in df.columns:
        print(f'   {col}: {df[col].dtype}')
        print(f'   Sample values: {df[col].head(3).tolist()}')

# ตรวจสอบ sample data
print(f'\n📊 Sample Data:')
print(df.head())
