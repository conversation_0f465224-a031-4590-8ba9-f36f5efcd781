#!/usr/bin/env python3
"""
ทดสอบการส่งข้อมูลไปยัง WebRequest Server แบบง่าย
"""

import requests
import json
import time

def create_simple_test_data(bars_count=250):
    """สร้างข้อมูลทดสอบแบบง่าย"""
    data = []
    base_time = int(time.time()) - (bars_count * 3600)  # เริ่มจากเวลาย้อนหลัง
    base_price = 2650.0
    
    for i in range(bars_count):
        timestamp = base_time + (i * 3600)  # ทุกชั่วโมง
        
        # สร้างข้อมูลราคาแบบง่าย
        open_price = base_price + (i * 0.1)  # เพิ่มขึ้นเรื่อยๆ
        high_price = open_price + 5.0
        low_price = open_price - 3.0
        close_price = open_price + 2.0
        volume = 1000 + (i * 10)
        
        data.append({
            'time': timestamp,
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'tick_volume': volume
        })
        
        base_price = close_price
    
    return data

def test_server():
    """ทดสอบเซิร์ฟเวอร์"""
    url = "http://127.0.0.1:54321/data"
    
    # สร้างข้อมูลทดสอบ
    test_data = create_simple_test_data(250)  # 250 bars
    
    # เตรียม payload
    payload = {
        'symbol': 'GOLD#',
        'timeframe_str': 'PERIOD_H1',
        'bars_count': len(test_data),
        'bars_data': test_data
    }
    
    print(f"🚀 ส่งข้อมูลทดสอบไปยัง {url}")
    print(f"📊 Symbol: {payload['symbol']}")
    print(f"📊 Timeframe: {payload['timeframe_str']}")
    print(f"📊 Bars: {payload['bars_count']}")
    print(f"📊 First bar time: {test_data[0]['time']}")
    print(f"📊 Last bar time: {test_data[-1]['time']}")
    
    try:
        # ส่ง POST request
        print(f"\n⏳ กำลังส่งข้อมูล...")
        response = requests.post(url, json=payload, timeout=120)
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n✅ Response Status: {response.status_code}")
            
            # แสดงข้อมูลสำคัญ
            if 'signal' in result:
                print(f"\n📊 Signal Results:")
                print(f"   🎯 Signal: {result.get('signal', 'N/A')}")
                print(f"   🎯 Confidence: {result.get('confidence', 'N/A')}")
                print(f"   🎯 Threshold: {result.get('threshold', 'N/A')}")
                print(f"   🎯 Scenario Used: {result.get('scenario_used', 'N/A')}")
                
                # แสดงค่า confidence ทั้ง 4 เงื่อนไข
                print(f"\n🔍 Confidence Values:")
                print(f"   📈 Trend Following BUY: {result.get('trend_following_buy_confidence', 'N/A')}")
                print(f"   📉 Trend Following SELL: {result.get('trend_following_sell_confidence', 'N/A')}")
                print(f"   📈 Counter Trend BUY: {result.get('counter_trend_buy_confidence', 'N/A')}")
                print(f"   📉 Counter Trend SELL: {result.get('counter_trend_sell_confidence', 'N/A')}")
                
                # ตรวจสอบว่าค่า confidence ไม่เป็น 0.0 ทั้งหมด
                confidence_values = [
                    result.get('trend_following_buy_confidence', 0),
                    result.get('trend_following_sell_confidence', 0),
                    result.get('counter_trend_buy_confidence', 0),
                    result.get('counter_trend_sell_confidence', 0)
                ]
                
                non_zero_count = sum(1 for v in confidence_values if v != 0.0)
                print(f"\n📈 Confidence Analysis:")
                print(f"   ✅ Non-zero confidence values: {non_zero_count}/4")
                print(f"   📊 Values: {confidence_values}")
                
                if non_zero_count >= 2:
                    print(f"   🎉 SUCCESS: คำนวณ confidence ได้อย่างน้อย 2 เงื่อนไข!")
                else:
                    print(f"   ⚠️ WARNING: คำนวณ confidence ได้น้อยเกินไป")
                
            else:
                print(f"⚠️ No signal data in response")
                print(f"Response keys: {list(result.keys())}")
                
        else:
            print(f"❌ Error Status: {response.status_code}")
            print(f"❌ Error Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print(f"⏰ Request timeout (120 seconds)")
    except requests.exceptions.ConnectionError:
        print(f"🔌 Connection error - Is the server running?")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🧪 Testing WebRequest Server - Simple Request")
    print("=" * 60)
    test_server()
    print("=" * 60)
    print("🏁 Test completed")
