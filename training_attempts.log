{"timestamp": "2025-09-25T13:05:15.432192", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:09:31.942015", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:10:33.306739", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:11:39.509189", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:12:35.478171", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:13:41.892536", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:33:27.870135", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:34:46.544181", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:34:52.997155", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:35:01.348453", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T13:35:08.429474", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.1018 (-10.91%)"}
{"timestamp": "2025-09-25T13:35:30.105469", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.3705 (-44.54%)"}
{"timestamp": "2025-09-25T14:39:09.199451", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - accuracy: -0.0397 (-4.06%), f1: -0.0201 (-2.04%), recall: -0.0397 (-4.06%)"}
{"timestamp": "2025-09-25T14:40:54.886415", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T14:41:16.163576", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.1887 (-20.22%)"}
{"timestamp": "2025-09-25T14:41:56.968079", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T15:08:37.486497", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - accuracy: -0.0397 (-4.06%), f1: -0.0201 (-2.04%), recall: -0.0397 (-4.06%)"}
{"timestamp": "2025-09-25T15:10:23.702886", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-25T15:10:38.194444", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.1887 (-20.22%)"}
{"timestamp": "2025-09-25T15:11:23.554380", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T17:11:22.529518", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - accuracy: -0.0397 (-4.06%), f1: -0.0201 (-2.04%), recall: -0.0397 (-4.06%)"}
{"timestamp": "2025-09-25T17:12:55.940916", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-25T17:13:09.349470", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.1675 (-17.95%)"}
{"timestamp": "2025-09-25T17:13:47.384115", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:22:03.119876", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:26:45.911502", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:27:38.716530", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:28:39.118394", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:29:28.477896", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:30:28.819803", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:58:18.260688", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:58:34.216682", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:58:41.076925", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.4500 (-50.35%)"}
{"timestamp": "2025-09-25T19:58:49.895922", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T19:58:57.500536", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.2731 (-29.23%)"}
{"timestamp": "2025-09-25T19:59:26.565205", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.1039 (-12.48%)"}
{"timestamp": "2025-09-25T20:35:56.925949", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.1325 (-13.73%)"}
{"timestamp": "2025-09-25T20:36:12.353019", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-25T20:36:25.846307", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.2409 (-25.78%)"}
{"timestamp": "2025-09-25T20:36:58.156832", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.3426 (-41.17%)"}
{"timestamp": "2025-09-25T21:04:18.355028", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.1325 (-13.73%)"}
{"timestamp": "2025-09-25T21:04:35.175665", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-25T21:04:49.874157", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.2409 (-25.78%)"}
{"timestamp": "2025-09-25T21:05:25.700094", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.3426 (-41.17%)"}
{"timestamp": "2025-09-25T22:03:16.917502", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-25T22:07:35.377538", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-25T22:08:27.303390", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-25T22:09:22.669941", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-25T22:10:05.551622", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-25T22:10:59.069441", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:12:07.898823", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:16:21.036332", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:17:14.926286", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:18:13.812622", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:18:59.410577", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:19:57.330438", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:32:11.245022", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:35:44.540014", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:36:03.983392", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:37:03.161037", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:37:14.190753", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T08:38:15.122651", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:13:12.810058", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:13:39.980807", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:13:56.441941", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:14:13.998742", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:14:24.367858", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:15:12.316604", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:28:36.148265", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:32:02.676186", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:32:22.263323", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:33:14.074313", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:33:25.311171", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:34:13.807514", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:44:07.837675", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:47:51.716471", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:48:12.155489", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:49:06.731807", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:49:18.004913", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T09:50:07.851027", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_blocked_by_protection_first_model_low_win_rate"}
{"timestamp": "2025-09-26T12:18:32.923162", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T12:22:04.771954", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-26T12:22:23.756547", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T12:23:14.524466", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)"}
{"timestamp": "2025-09-26T12:23:26.088633", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T12:24:14.932791", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0512 (-5.99%)"}
{"timestamp": "2025-09-26T13:29:24.744813", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T13:32:52.129526", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T13:33:11.462824", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T13:34:04.526670", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)"}
{"timestamp": "2025-09-26T13:34:21.497795", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T13:35:09.907211", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0512 (-5.99%)"}
{"timestamp": "2025-09-26T15:21:11.496219", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T15:25:02.137507", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T15:25:24.958005", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T15:26:23.553918", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)"}
{"timestamp": "2025-09-26T15:26:35.976582", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T15:27:31.317537", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0512 (-5.99%)"}
{"timestamp": "2025-09-26T16:25:04.843710", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T16:28:40.613315", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T16:28:59.540878", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T16:29:52.253136", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)"}
{"timestamp": "2025-09-26T16:30:03.280149", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T16:30:52.857034", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0512 (-5.99%)"}
{"timestamp": "2025-09-26T16:39:52.546281", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T16:43:55.871224", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T16:44:16.232878", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T16:45:13.993217", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)"}
{"timestamp": "2025-09-26T16:45:26.028458", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T16:46:20.371324", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0512 (-5.99%)"}
{"timestamp": "2025-09-26T18:34:11.409712", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T18:37:56.932093", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T18:38:16.944666", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T18:39:10.932933", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)"}
{"timestamp": "2025-09-26T18:39:22.489931", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T18:40:13.336018", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0512 (-5.99%)"}
{"timestamp": "2025-09-26T19:04:16.731137", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T19:08:45.834407", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T19:09:09.816288", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T19:10:16.428277", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)"}
{"timestamp": "2025-09-26T19:10:30.967323", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T19:11:36.467335", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 - auc: -0.0512 (-5.99%)"}
{"timestamp": "2025-09-26T20:08:29.817529", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-26T20:13:04.273865", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-26T20:14:08.660088", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-26T20:15:10.864494", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-26T20:16:04.859753", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-26T20:17:06.325998", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-26T20:44:22.211437", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-26T20:44:41.021437", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e1c\u0e48\u0e32\u0e19\u0e40\u0e01\u0e13\u0e11\u0e4c\u0e04\u0e38\u0e13\u0e20\u0e32\u0e1e\u0e02\u0e31\u0e49\u0e19\u0e15\u0e48\u0e33: AUC 0.1134 < 0.408 (FLEXIBLE)"}
{"timestamp": "2025-09-26T20:44:48.029577", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e1c\u0e48\u0e32\u0e19\u0e40\u0e01\u0e13\u0e11\u0e4c\u0e04\u0e38\u0e13\u0e20\u0e32\u0e1e\u0e02\u0e31\u0e49\u0e19\u0e15\u0e48\u0e33: AUC 0.1764 < 0.408 (FLEXIBLE)"}
{"timestamp": "2025-09-26T20:45:02.613576", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 (FLEXIBLE) - auc: -0.1295 (-13.85%), f1: -0.8769 (-88.75%), precision: -0.9130 (-92.74%), recall: -0.7417 (-74.79%)"}
{"timestamp": "2025-09-26T20:45:31.410678", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 (FLEXIBLE) - auc: -0.3116 (-37.71%)"}
{"timestamp": "2025-09-26T21:04:38.755510", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 (FLEXIBLE) - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T21:04:58.618652", "symbol": "GOLD", "timeframe": "M60", "result": "saved_successfully"}
{"timestamp": "2025-09-26T21:05:16.595416", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 (FLEXIBLE) - auc: -0.2575 (-27.55%)"}
{"timestamp": "2025-09-26T21:05:52.514018", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 (FLEXIBLE) - auc: -0.2896 (-35.04%)"}
{"timestamp": "2025-09-26T21:45:59.853574", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 (FLEXIBLE) - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T21:46:19.755772", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 (FLEXIBLE) - \u0e44\u0e21\u0e48\u0e21\u0e35\u0e01\u0e32\u0e23\u0e1b\u0e23\u0e31\u0e1a\u0e1b\u0e23\u0e38\u0e07\u0e17\u0e35\u0e48\u0e2a\u0e33\u0e04\u0e31\u0e0d"}
{"timestamp": "2025-09-26T21:46:36.846623", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 (FLEXIBLE) - auc: -0.2575 (-27.55%)"}
{"timestamp": "2025-09-26T21:47:08.729778", "symbol": "GOLD", "timeframe": "M60", "result": "rejected_\u0e42\u0e21\u0e40\u0e14\u0e25\u0e44\u0e21\u0e48\u0e14\u0e35\u0e02\u0e36\u0e49\u0e19 (FLEXIBLE) - auc: -0.2896 (-35.04%)"}
