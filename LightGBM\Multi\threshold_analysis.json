{"analysis_date": "2025-09-26T21:48:22.990282", "total_models_analyzed": 4, "current_thresholds": {"min_accuracy": 0.45, "min_auc": 0.48, "min_f1": 0.08, "min_precision": 0.08, "min_recall": 0.3, "min_win_rate": 0.35, "min_expectancy": 10.0, "min_trades": 8, "improvement_threshold": 0.008, "max_decline_threshold": -0.03}, "recommended_thresholds": {"accuracy": {"threshold": 0.98697762914223, "mean": 0.9908377980110494, "std": 0.008905062560909947, "min": 0.9764572293716881, "max": 0.9991349480968859, "count": 4}, "auc": {"threshold": 0.6422636503281665, "mean": 0.7107297829288808, "std": 0.11750213168665652, "min": 0.5367965367965368, "max": 0.8489180403156347, "count": 4}, "f1": {"threshold": 0.9900638988382647, "mean": 0.9936688475510123, "std": 0.005213199807156087, "min": 0.9862184357321772, "max": 0.9987026093049788, "count": 4}, "precision": {"threshold": 0.99518470460993, "mean": 0.9965502927611001, "std": 0.002780285957102091, "min": 0.9922085098822204, "max": 0.9995452471344466, "count": 4}, "recall": {"threshold": 0.98697762914223, "mean": 0.9908377980110494, "std": 0.008905062560909947, "min": 0.9764572293716881, "max": 0.9991349480968859, "count": 4}, "win_rate": {"threshold": 0.65, "mean": 0.65, "std": 0.0, "min": 0.65, "max": 0.65, "count": 4}, "expectancy": {"threshold": 49.27893626367681, "mean": 49.5502043549655, "std": 0.3950182462581915, "min": 48.98476557643486, "max": 49.94810062900615, "count": 4}}, "new_thresholds": {"min_accuracy": 0.98697762914223, "min_auc": 0.6422636503281665, "min_f1": 0.9900638988382647, "min_precision": 0.99518470460993, "min_recall": 0.98697762914223, "min_win_rate": 0.006500000000000001, "min_expectancy": 49.27893626367681, "min_trades": 8, "improvement_threshold": 0.008, "max_decline_threshold": -0.03}, "detailed_results": {"GOLD_M60_trend_following": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following", "metrics": [{"accuracy": 0.9764572293716881, "auc": 0.779785199764642, "f1": 0.9862184357321772, "precision": 0.9961767695191666, "recall": 0.9764572293716881}], "trading_stats": [{"win_rate": 0.65, "expectancy": 48.98476557643486, "num_trades": 0}]}, "GOLD_M60_counter_trend": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend", "metrics": [{"accuracy": 0.990484429065744, "auc": 0.8489180403156347, "f1": 0.9913457198736273, "precision": 0.9922085098822204, "recall": 0.990484429065744}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.37699315942413, "num_trades": 0}]}, "GOLD_M60_trend_following_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_Sell", "metrics": [{"accuracy": 0.9972745855098796, "auc": 0.6774193548387096, "f1": 0.9984086252932659, "precision": 0.9995452471344466, "recall": 0.9972745855098796}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.890958054996865, "num_trades": 0}]}, "GOLD_M60_counter_trend_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend_Sell", "metrics": [{"accuracy": 0.9991349480968859, "auc": 0.5367965367965368, "f1": 0.9987026093049788, "precision": 0.9982706445085668, "recall": 0.9991349480968859}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.94810062900615, "num_trades": 0}]}}}