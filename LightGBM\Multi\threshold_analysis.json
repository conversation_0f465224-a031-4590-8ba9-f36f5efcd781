{"analysis_date": "2025-09-26T09:54:21.839495", "total_models_analyzed": 6, "current_thresholds": {"min_accuracy": 0.5, "min_auc": 0.5, "min_f1": 0.1, "min_precision": 0.1, "min_recall": 0.5, "min_win_rate": 0.3, "min_expectancy": 25.0, "min_trades": 10, "improvement_threshold": 0.01, "max_decline_threshold": -0.02}, "recommended_thresholds": {"accuracy": {"threshold": 0.9777992277992278, "mean": 0.9701681364040521, "std": 0.03206803153616768, "min": 0.9071915215745647, "max": 0.9928435114503816, "count": 5}, "auc": {"threshold": 0.8140000614276233, "mean": 0.8671726062336537, "std": 0.05435577977699706, "min": 0.804648862512364, "max": 0.9286719787516601, "count": 6}, "f1": {"threshold": 0.891116987200357, "mean": 0.9158186828797618, "std": 0.0958997704819117, "min": 0.7382894064031218, "max": 0.989595040939691, "count": 5}, "precision": {"threshold": 0.8789636841415259, "mean": 0.903981867354846, "std": 0.10597390195033697, "min": 0.7071920368737575, "max": 0.9863677583531074, "count": 5}, "recall": {"threshold": 0.9071915215745647, "mean": 0.932393757972234, "std": 0.07848218805453296, "min": 0.7889273356401384, "max": 0.9928435114503816, "count": 5}, "win_rate": {"threshold": 0.65, "mean": 0.65, "std": 0.0, "min": 0.65, "max": 0.65, "count": 6}, "expectancy": {"threshold": 48.74083987679494, "mean": 48.35746314600117, "std": 1.6859064233943162, "min": 45.06018746711911, "max": 49.59451503179186, "count": 5}}, "new_thresholds": {"min_accuracy": 0.9777992277992278, "min_auc": 0.8140000614276233, "min_f1": 0.891116987200357, "min_precision": 0.8789636841415259, "min_recall": 0.9071915215745647, "min_win_rate": 0.006500000000000001, "min_expectancy": 48.74083987679494, "min_trades": 10, "improvement_threshold": 0.01, "max_decline_threshold": -0.02}, "detailed_results": {"GOLD_M60_trend_following": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following", "metrics": [{"accuracy": 0.9071915215745647, "auc": 0.9062646330227626, "f1": 0.891116987200357, "precision": 0.8789636841415259, "recall": 0.9071915215745647}], "trading_stats": [{"win_rate": 0.65, "expectancy": 45.06018746711911, "num_trades": 0}]}, "GOLD_M60_counter_trend": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend", "metrics": [{"accuracy": 0.7889273356401384, "auc": 0.8267250873712951, "f1": 0.7382894064031218, "precision": 0.7071920368737575, "recall": 0.7889273356401384}], "trading_stats": [{"win_rate": 0.65, "expectancy": 37.78708810490979, "num_trades": 0}]}, "GOLD_M60_trend_following_Buy": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_Buy", "metrics": [{"accuracy": 0.9924050632911392, "auc": 0.9286719787516601, "f1": 0.9890949447509368, "precision": 0.9858068342808135, "recall": 0.9924050632911392}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.553791915057566, "num_trades": 0}]}, "GOLD_M60_counter_trend_Buy": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend_Buy", "metrics": [{"accuracy": 0.9777992277992278, "auc": 0.8097583861130659, "f1": 0.08, "precision": 0.25, "recall": 0.047619047619047616}], "trading_stats": [{"win_rate": 0.65, "expectancy": 48.74083987679494, "num_trades": 0}]}, "GOLD_M60_trend_following_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_Sell", "metrics": [{"accuracy": 0.9928435114503816, "auc": 0.9269666896307743, "f1": 0.989595040939691, "precision": 0.9863677583531074, "recall": 0.9928435114503816}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.59451503179186, "num_trades": 0}]}, "GOLD_M60_counter_trend_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend_Sell", "metrics": [{"accuracy": 0.9806013579049466, "auc": 0.804648862512364, "f1": 0.9709970351047024, "precision": 0.9615790231250253, "recall": 0.9806013579049466}], "trading_stats": [{"win_rate": 0.65, "expectancy": 48.83798143924244, "num_trades": 0}]}}}