"""
🔧 Training Degradation Fix System
==================================

แก้ไขปัญหาการเทรนที่ให้ผลลัพธ์แย่ลงเมื่อเทรนหลายครั้ง
ทีละขั้นตอนตามแนวทางที่วิเคราะห์ได้
"""

import os
import json
import pickle
import pandas as pd
import numpy as np
from datetime import datetime
import shutil

class TrainingDegradationFix:
    def __init__(self):
        self.backup_dir = "model_backups"
        self.performance_history = []
        self.best_model_info = None
        
    def step1_prevent_overfitting(self):
        """ขั้นตอนที่ 1: ป้องกัน Overfitting"""
        
        print("🔧 Step 1: ป้องกัน Overfitting")
        print("="*50)
        
        # สร้างไฟล์ config สำหรับป้องกัน overfitting
        overfitting_config = {
            'early_stopping_rounds': 50,
            'validation_size': 0.2,
            'use_cross_validation': True,
            'max_retrain_attempts': 3,
            'min_improvement_threshold': 0.01,
            'patience_rounds': 10
        }
        
        with open('overfitting_prevention_config.json', 'w') as f:
            json.dump(overfitting_config, f, indent=4)
        
        print("✅ สร้างไฟล์ overfitting_prevention_config.json")
        
        # สร้างโค้ดสำหรับเพิ่มเข้า LightGBM_10_4.py
        overfitting_code = '''
# ==============================================
# Overfitting Prevention System
# ==============================================

def load_overfitting_config():
    """โหลดการตั้งค่าป้องกัน overfitting"""
    try:
        with open('overfitting_prevention_config.json', 'r') as f:
            config = json.load(f)
        return config
    except:
        # Default config
        return {
            'early_stopping_rounds': 50,
            'validation_size': 0.2,
            'use_cross_validation': True,
            'max_retrain_attempts': 3,
            'min_improvement_threshold': 0.01,
            'patience_rounds': 10
        }

def improved_model_training(X_train, y_train, X_val, y_val, config):
    """การเทรนโมเดลที่ป้องกัน overfitting"""
    
    # LightGBM parameters with overfitting prevention
    lgb_params = {
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,  # ลดลงเพื่อป้องกัน overfitting
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'min_data_in_leaf': 20,  # เพิ่มเพื่อป้องกัน overfitting
        'lambda_l1': 0.1,        # L1 regularization
        'lambda_l2': 0.1,        # L2 regularization
        'early_stopping_rounds': config['early_stopping_rounds']
    }
    
    model = lgb.LGBMClassifier(**lgb_params)
    
    # เทรนด้วย early stopping
    model.fit(
        X_train, y_train,
        eval_set=[(X_val, y_val)],
        eval_metric='auc',
        verbose=False
    )
    
    return model

# เพิ่มในส่วน main function
overfitting_config = load_overfitting_config()
'''
        
        with open('step1_overfitting_prevention.py', 'w', encoding='utf-8') as f:
            f.write(overfitting_code)
        
        print("✅ สร้างไฟล์ step1_overfitting_prevention.py")
        
        return overfitting_config
    
    def step2_fix_data_leakage(self):
        """ขั้นตอนที่ 2: แก้ไข Data Leakage"""
        
        print("\n🔧 Step 2: แก้ไข Data Leakage")
        print("="*50)
        
        data_leakage_fixes = '''
# ==============================================
# Data Leakage Prevention System
# ==============================================

def create_safe_features(df):
    """สร้าง features ที่ปลอดภัยจาก data leakage"""
    
    print("🔍 สร้าง safe features...")
    
    # ใช้ .shift(1) สำหรับทุก feature ที่ใช้ข้อมูลปัจจุบัน
    df['Volume_MA20_safe'] = df['Volume'].rolling(20).mean().shift(1)
    df['Close_MA5_safe'] = df['Close'].rolling(5).mean().shift(1)
    df['Close_MA10_safe'] = df['Close'].rolling(10).mean().shift(1)
    df['Close_MA20_safe'] = df['Close'].rolling(20).mean().shift(1)
    
    # RSI ที่ปลอดภัย
    df['RSI14_safe'] = df['RSI14'].shift(1)
    
    # MACD ที่ปลอดภัย
    df['MACD_safe'] = df['MACD_12_26_9'].shift(1)
    df['MACD_signal_safe'] = df['MACDs_12_26_9'].shift(1)
    
    # ATR ที่ปลอดภัย
    df['ATR_safe'] = df['ATR'].shift(1)
    
    # EMA ที่ปลอดภัย
    df['EMA50_safe'] = df['EMA50'].shift(1)
    df['EMA100_safe'] = df['EMA100'].shift(1)
    df['EMA200_safe'] = df['EMA200'].shift(1)
    
    # ลบ features ที่อาจมี look-ahead bias
    dangerous_features = [
        'Close_Lag_1', 'High_Lag_1', 'Low_Lag_1', 'Open_Lag_1',
        'Volume_Lag_1', 'RSI14_Lag_1', 'EMA50_Lag_1'
    ]
    
    for feature in dangerous_features:
        if feature in df.columns:
            df = df.drop(feature, axis=1)
            print(f"⚠️ ลบ feature ที่อันตราย: {feature}")
    
    # ลบ rows ที่มี NaN จาก shift
    df = df.dropna()
    
    print(f"✅ สร้าง safe features เสร็จสิ้น ({len(df)} rows)")
    
    return df

def validate_no_data_leakage(X_train, X_val, X_test):
    """ตรวจสอบว่าไม่มี data leakage"""
    
    print("🔍 ตรวจสอบ data leakage...")
    
    # ตรวจสอบ index
    train_max_idx = X_train.index.max()
    val_min_idx = X_val.index.min()
    val_max_idx = X_val.index.max()
    test_min_idx = X_test.index.min()
    
    if train_max_idx >= val_min_idx:
        raise ValueError("❌ Data Leakage: Training data overlaps with validation data!")
    
    if val_max_idx >= test_min_idx:
        raise ValueError("❌ Data Leakage: Validation data overlaps with test data!")
    
    print("✅ ไม่พบ data leakage")
    
    return True
'''
        
        with open('step2_data_leakage_prevention.py', 'w', encoding='utf-8') as f:
            f.write(data_leakage_fixes)
        
        print("✅ สร้างไฟล์ step2_data_leakage_prevention.py")
    
    def step3_model_versioning(self):
        """ขั้นตอนที่ 3: Model Versioning และ Performance Tracking"""
        
        print("\n🔧 Step 3: Model Versioning และ Performance Tracking")
        print("="*50)
        
        # สร้างระบบ backup models
        os.makedirs(self.backup_dir, exist_ok=True)
        
        model_versioning_code = '''
# ==============================================
# Model Versioning และ Performance Tracking
# ==============================================

import os
import json
import pickle
import shutil
from datetime import datetime

class ModelVersionManager:
    def __init__(self, backup_dir="model_backups"):
        self.backup_dir = backup_dir
        self.performance_file = os.path.join(backup_dir, "performance_history.json")
        os.makedirs(backup_dir, exist_ok=True)
        
    def save_model_version(self, model, scaler, features, performance_metrics, symbol, timeframe, version_note=""):
        """บันทึกโมเดลพร้อม version"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        version_dir = os.path.join(self.backup_dir, f"{symbol}_{timeframe}_{timestamp}")
        os.makedirs(version_dir, exist_ok=True)
        
        # บันทึกโมเดล
        model_path = os.path.join(version_dir, "model.pkl")
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        
        # บันทึก scaler
        scaler_path = os.path.join(version_dir, "scaler.pkl")
        with open(scaler_path, 'wb') as f:
            pickle.dump(scaler, f)
        
        # บันทึก features
        features_path = os.path.join(version_dir, "features.pkl")
        with open(features_path, 'wb') as f:
            pickle.dump(features, f)
        
        # บันทึก performance metrics
        version_info = {
            'timestamp': timestamp,
            'symbol': symbol,
            'timeframe': timeframe,
            'performance_metrics': performance_metrics,
            'version_note': version_note,
            'model_path': model_path,
            'scaler_path': scaler_path,
            'features_path': features_path
        }
        
        info_path = os.path.join(version_dir, "version_info.json")
        with open(info_path, 'w') as f:
            json.dump(version_info, f, indent=4)
        
        # อัปเดต performance history
        self._update_performance_history(version_info)
        
        print(f"✅ บันทึกโมเดล version: {timestamp}")
        return version_dir
    
    def _update_performance_history(self, version_info):
        """อัปเดต performance history"""
        
        history = []
        if os.path.exists(self.performance_file):
            with open(self.performance_file, 'r') as f:
                history = json.load(f)
        
        history.append(version_info)
        
        with open(self.performance_file, 'w') as f:
            json.dump(history, f, indent=4)
    
    def get_best_model(self, symbol, timeframe, metric='total_profit'):
        """หาโมเดลที่ดีที่สุด"""
        
        if not os.path.exists(self.performance_file):
            return None
        
        with open(self.performance_file, 'r') as f:
            history = json.load(f)
        
        # กรองตาม symbol และ timeframe
        filtered_history = [
            h for h in history 
            if h['symbol'] == symbol and h['timeframe'] == timeframe
        ]
        
        if not filtered_history:
            return None
        
        # หาโมเดลที่ดีที่สุดตาม metric
        best_model = max(filtered_history, key=lambda x: x['performance_metrics'].get(metric, -999999))
        
        return best_model
    
    def should_save_model(self, current_performance, symbol, timeframe, min_improvement=0.05):
        """ตัดสินใจว่าควรบันทึกโมเดลหรือไม่"""
        
        best_model = self.get_best_model(symbol, timeframe)
        
        if not best_model:
            print("✅ ไม่มีโมเดลก่อนหน้า - บันทึกโมเดลนี้")
            return True, "first_model"
        
        current_profit = current_performance.get('total_profit', 0)
        best_profit = best_model['performance_metrics'].get('total_profit', 0)
        
        improvement = (current_profit - best_profit) / abs(best_profit) if best_profit != 0 else 0
        
        if improvement > min_improvement:
            print(f"✅ โมเดลดีขึ้น {improvement:.2%} - บันทึกโมเดลนี้")
            return True, f"improved_by_{improvement:.2%}"
        else:
            print(f"⚠️ โมเดลไม่ดีขึ้น ({improvement:.2%}) - ไม่บันทึก")
            return False, f"not_improved_{improvement:.2%}"

# การใช้งานใน main function
version_manager = ModelVersionManager()

# ก่อนบันทึกโมเดล
should_save, reason = version_manager.should_save_model(
    current_performance={'total_profit': total_profit, 'win_rate': win_rate},
    symbol=symbol,
    timeframe=timeframe
)

if should_save:
    version_manager.save_model_version(
        model=model,
        scaler=scaler,
        features=selected_features,
        performance_metrics={'total_profit': total_profit, 'win_rate': win_rate},
        symbol=symbol,
        timeframe=timeframe,
        version_note=reason
    )
'''
        
        with open('step3_model_versioning.py', 'w', encoding='utf-8') as f:
            f.write(model_versioning_code)
        
        print("✅ สร้างไฟล์ step3_model_versioning.py")
        print(f"✅ สร้างโฟลเดอร์ backup: {self.backup_dir}")
    
    def step4_conservative_thresholds(self):
        """ขั้นตอนที่ 4: Conservative Threshold Management"""
        
        print("\n🔧 Step 4: Conservative Threshold Management")
        print("="*50)
        
        threshold_code = '''
# ==============================================
# Conservative Threshold Management
# ==============================================

CONSERVATIVE_THRESHOLDS = {
    'min_trades_for_optimization': 100,
    'max_threshold_reduction': 0.3,  # ลดได้ไม่เกิน 30%
    'quality_over_quantity': True,
    'min_win_rate': 0.4,  # Win rate ขั้นต่ำ 40%
    'min_expectancy': 0,   # Expectancy ต้องเป็นบวก
    'max_drawdown': 0.2    # Max drawdown ไม่เกิน 20%
}

def conservative_threshold_optimization(trade_results, current_threshold=0.5):
    """ปรับ threshold แบบ conservative"""
    
    if len(trade_results) < CONSERVATIVE_THRESHOLDS['min_trades_for_optimization']:
        print(f"⚠️ ข้อมูลไม่เพียงพอ ({len(trade_results)} trades) - ใช้ threshold เดิม")
        return current_threshold
    
    # คำนวณ performance metrics
    win_rate = len([t for t in trade_results if t['profit'] > 0]) / len(trade_results)
    total_profit = sum([t['profit'] for t in trade_results])
    expectancy = total_profit / len(trade_results)
    
    # ตรวจสอบเกณฑ์ขั้นต่ำ
    if win_rate < CONSERVATIVE_THRESHOLDS['min_win_rate']:
        print(f"⚠️ Win Rate ต่ำ ({win_rate:.2%}) - เพิ่ม threshold")
        new_threshold = min(current_threshold * 1.1, 0.8)
        return new_threshold
    
    if expectancy < CONSERVATIVE_THRESHOLDS['min_expectancy']:
        print(f"⚠️ Expectancy ติดลบ ({expectancy:.2f}) - เพิ่ม threshold")
        new_threshold = min(current_threshold * 1.2, 0.8)
        return new_threshold
    
    # ถ้าผ่านเกณฑ์ทั้งหมด อาจลด threshold เล็กน้อย
    if win_rate > 0.6 and expectancy > 50:
        max_reduction = CONSERVATIVE_THRESHOLDS['max_threshold_reduction']
        new_threshold = max(current_threshold * (1 - max_reduction), 0.2)
        print(f"✅ Performance ดี - ลด threshold เป็น {new_threshold:.3f}")
        return new_threshold
    
    print(f"✅ Performance พอใช้ - ใช้ threshold เดิม {current_threshold:.3f}")
    return current_threshold

def validate_model_quality(performance_metrics):
    """ตรวจสอบคุณภาพโมเดล"""
    
    checks = []
    
    # Win Rate
    win_rate = performance_metrics.get('win_rate', 0)
    if win_rate >= CONSERVATIVE_THRESHOLDS['min_win_rate']:
        checks.append(('Win Rate', True, f"{win_rate:.2%}"))
    else:
        checks.append(('Win Rate', False, f"{win_rate:.2%} < {CONSERVATIVE_THRESHOLDS['min_win_rate']:.2%}"))
    
    # Expectancy
    expectancy = performance_metrics.get('expectancy', -999)
    if expectancy >= CONSERVATIVE_THRESHOLDS['min_expectancy']:
        checks.append(('Expectancy', True, f"{expectancy:.2f}"))
    else:
        checks.append(('Expectancy', False, f"{expectancy:.2f} < {CONSERVATIVE_THRESHOLDS['min_expectancy']}"))
    
    # Max Drawdown
    max_drawdown = performance_metrics.get('max_drawdown_pct', 1)
    if max_drawdown <= CONSERVATIVE_THRESHOLDS['max_drawdown']:
        checks.append(('Max Drawdown', True, f"{max_drawdown:.2%}"))
    else:
        checks.append(('Max Drawdown', False, f"{max_drawdown:.2%} > {CONSERVATIVE_THRESHOLDS['max_drawdown']:.2%}"))
    
    # สรุปผล
    passed_checks = sum([1 for _, passed, _ in checks if passed])
    total_checks = len(checks)
    
    print(f"\n📊 Model Quality Check: {passed_checks}/{total_checks} passed")
    for check_name, passed, value in checks:
        status = "✅" if passed else "❌"
        print(f"   {status} {check_name}: {value}")
    
    # ต้องผ่านอย่างน้อย 2 ใน 3 เกณฑ์
    quality_passed = passed_checks >= 2
    
    return quality_passed, checks
'''
        
        with open('step4_conservative_thresholds.py', 'w', encoding='utf-8') as f:
            f.write(threshold_code)
        
        print("✅ สร้างไฟล์ step4_conservative_thresholds.py")
    
    def step5_safe_oversampling(self):
        """ขั้นตอนที่ 5: Safe Data Balancing"""
        
        print("\n🔧 Step 5: Safe Data Balancing")
        print("="*50)
        
        balancing_code = '''
# ==============================================
# Safe Data Balancing System
# ==============================================

from imblearn.over_sampling import SMOTE
from collections import Counter

def check_imbalance_severity(y):
    """ตรวจสอบความรุนแรงของ imbalance"""
    
    counter = Counter(y)
    minority_class = min(counter.values())
    majority_class = max(counter.values())
    
    imbalance_ratio = minority_class / majority_class
    
    print(f"📊 Class distribution: {dict(counter)}")
    print(f"📊 Imbalance ratio: {imbalance_ratio:.3f}")
    
    return imbalance_ratio

def safe_oversampling(X, y, min_imbalance_ratio=0.3):
    """Safe oversampling ที่ระมัดระวัง"""
    
    imbalance_ratio = check_imbalance_severity(y)
    
    # ถ้า imbalance ไม่รุนแรงมาก ไม่ต้อง oversample
    if imbalance_ratio > min_imbalance_ratio:
        print(f"✅ Imbalance ไม่รุนแรง ({imbalance_ratio:.3f}) - ไม่ต้อง oversample")
        return X, y
    
    print(f"⚠️ Imbalance รุนแรง ({imbalance_ratio:.3f}) - ใช้ SMOTE")
    
    # ใช้ SMOTE พร้อมควบคุม parameters
    smote = SMOTE(
        sampling_strategy='auto',  # สมดุลอัตโนมัติ
        k_neighbors=min(5, len(y[y == y.value_counts().idxmin()]) - 1),  # ปรับตามข้อมูล minority
        random_state=42
    )
    
    try:
        X_balanced, y_balanced = smote.fit_resample(X, y)
        
        print(f"📊 Original: {Counter(y)}")
        print(f"📊 Balanced: {Counter(y_balanced)}")
        
        return X_balanced, y_balanced
        
    except Exception as e:
        print(f"⚠️ SMOTE ล้มเหลว: {e}")
        print("✅ ใช้ข้อมูลเดิม")
        return X, y

def use_class_weights_instead(y):
    """ใช้ class weights แทน oversampling"""
    
    from sklearn.utils.class_weight import compute_class_weight
    
    classes = np.unique(y)
    class_weights = compute_class_weight(
        'balanced', 
        classes=classes, 
        y=y
    )
    
    class_weight_dict = dict(zip(classes, class_weights))
    
    print(f"📊 Class weights: {class_weight_dict}")
    
    return class_weight_dict
'''
        
        with open('step5_safe_oversampling.py', 'w', encoding='utf-8') as f:
            f.write(balancing_code)
        
        print("✅ สร้างไฟล์ step5_safe_oversampling.py")
    
    def create_integration_guide(self):
        """สร้างคู่มือการนำไปใช้"""
        
        print("\n📋 สร้างคู่มือการนำไปใช้")
        print("="*50)
        
        integration_guide = '''
# 🔧 คู่มือการแก้ไขปัญหาการเทรนที่ให้ผลลัพธ์แย่ลง
# =====================================================

## 📊 สาเหตุหลักที่พบ:
1. **Data Leakage** - Model Accuracy 98%+ แต่ Win Rate ต่ำ
2. **Overfitting** - โมเดลจำข้อมูลแทนที่จะเรียนรู้ pattern
3. **ไม่มี Model Versioning** - เทรนทับโมเดลดีด้วยโมเดลแย่
4. **Threshold ไม่เหมาะสม** - ปรับ threshold โดยไม่ระมัดระวัง
5. **Data Imbalance** - ข้อมูลไม่สมดุลทำให้โมเดลเรียนรู้ผิด

## 🔧 วิธีการแก้ไขทีละขั้นตอน:

### Step 1: เพิ่มโค้ดป้องกัน Overfitting
```python
# เพิ่มในส่วนต้นของ LightGBM_10_4.py
from step1_overfitting_prevention import load_overfitting_config, improved_model_training

# ในฟังก์ชัน main()
overfitting_config = load_overfitting_config()
```

### Step 2: แก้ไข Data Leakage
```python
# เพิ่มในส่วน feature engineering
from step2_data_leakage_prevention import create_safe_features, validate_no_data_leakage

# ใช้แทน create_features()
df_safe = create_safe_features(df)

# ตรวจสอบก่อนเทรน
validate_no_data_leakage(X_train, X_val, X_test)
```

### Step 3: เพิ่ม Model Versioning
```python
# เพิ่มในส่วนต้นของ LightGBM_10_4.py
from step3_model_versioning import ModelVersionManager

# ในฟังก์ชัน main()
version_manager = ModelVersionManager()

# ก่อนบันทึกโมเดล
should_save, reason = version_manager.should_save_model(
    current_performance={'total_profit': total_profit, 'win_rate': win_rate},
    symbol=symbol,
    timeframe=timeframe
)

if should_save:
    version_manager.save_model_version(
        model=model, scaler=scaler, features=selected_features,
        performance_metrics={'total_profit': total_profit, 'win_rate': win_rate},
        symbol=symbol, timeframe=timeframe, version_note=reason
    )
else:
    print("⚠️ โมเดลไม่ดีขึ้น - ไม่บันทึก")
```

### Step 4: ใช้ Conservative Thresholds
```python
# เพิ่มในส่วน threshold optimization
from step4_conservative_thresholds import conservative_threshold_optimization, validate_model_quality

# แทนที่การปรับ threshold เดิม
new_threshold = conservative_threshold_optimization(trade_results, current_threshold)

# ตรวจสอบคุณภาพก่อนใช้โมเดล
quality_passed, checks = validate_model_quality(performance_metrics)
if not quality_passed:
    print("❌ โมเดลไม่ผ่านเกณฑ์คุณภาพ - ไม่ใช้โมเดลนี้")
```

### Step 5: Safe Data Balancing
```python
# เพิ่มในส่วน data preprocessing
from step5_safe_oversampling import safe_oversampling, use_class_weights_instead

# แทนที่ SMOTE เดิม
X_balanced, y_balanced = safe_oversampling(X_train, y_train)

# หรือใช้ class weights
class_weights = use_class_weights_instead(y_train)
```

## ⚠️ สิ่งสำคัญที่ต้องทำ:

### 1. หยุดการเทรนซ้ำทันที
- ไม่เทรนซ้ำจนกว่าจะแก้ไขปัญหาหลัก
- ใช้โมเดลจากครั้งที่ 2 ที่ให้ผลดี (+$5,940)

### 2. ตรวจสอบ Data Leakage
- ตรวจสอบทุก feature ที่ใช้ข้อมูลปัจจุบัน
- ใช้ .shift(1) สำหรับ features ที่จำเป็น
- ลบ features ที่อันตราย (Lag_1, Lag_2)

### 3. ใช้ Model Versioning
- บันทึกโมเดลทุกครั้งพร้อม performance metrics
- เปรียบเทียบกับโมเดลก่อนหน้า
- บันทึกเฉพาะโมเดลที่ดีขึ้น

### 4. ตั้งเกณฑ์คุณภาพ
- Win Rate > 40%
- Expectancy > 0
- Max Drawdown < 20%
- F1 Score > 0.3
- AUC > 0.6

### 5. ทดสอบอย่างเข้มงวด
- ใช้ Out-of-Sample Testing
- ใช้ Walk-Forward Validation
- ทดสอบใน Market Conditions ต่างๆ

## 🎯 เป้าหมายที่ต้องบรรลุ:
- ไม่มี Data Leakage (Model Accuracy < 90%)
- Win Rate > 40%
- Total Profit เป็นบวกอย่างสม่ำเสมอ
- F1 Score > 0 (โมเดลสามารถทำนาย positive class ได้)
- Performance ไม่แย่ลงเมื่อเทรนใหม่

## 📋 Checklist ก่อนการเทรน:
- [ ] ตรวจสอบไม่มี Data Leakage
- [ ] ใช้ Safe Features เท่านั้น
- [ ] ตั้งค่า Early Stopping
- [ ] เปิดใช้ Model Versioning
- [ ] ตรวจสอบ Data Balance
- [ ] กำหนดเกณฑ์คุณภาพ
- [ ] เตรียม Out-of-Sample Data
'''
        
        with open('INTEGRATION_GUIDE.md', 'w', encoding='utf-8') as f:
            f.write(integration_guide)
        
        print("✅ สร้างไฟล์ INTEGRATION_GUIDE.md")
    
    def run_all_steps(self):
        """รันทุกขั้นตอน"""
        
        print("🚀 เริ่มแก้ไขปัญหาการเทรนที่ให้ผลลัพธ์แย่ลง")
        print("="*60)
        
        # รันทุกขั้นตอน
        self.step1_prevent_overfitting()
        self.step2_fix_data_leakage()
        self.step3_model_versioning()
        self.step4_conservative_thresholds()
        self.step5_safe_oversampling()
        self.create_integration_guide()
        
        print("\n🎉 สร้างไฟล์แก้ไขปัญหาเสร็จสมบูรณ์!")
        print("="*60)
        
        print("\n📁 ไฟล์ที่สร้าง:")
        files_created = [
            "overfitting_prevention_config.json",
            "step1_overfitting_prevention.py",
            "step2_data_leakage_prevention.py", 
            "step3_model_versioning.py",
            "step4_conservative_thresholds.py",
            "step5_safe_oversampling.py",
            "INTEGRATION_GUIDE.md"
        ]
        
        for file in files_created:
            if os.path.exists(file):
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file}")
        
        print(f"\n📂 โฟลเดอร์ backup: {self.backup_dir}")
        
        print("\n🎯 ขั้นตอนต่อไป:")
        print("1. อ่าน INTEGRATION_GUIDE.md")
        print("2. เพิ่มโค้ดเข้า LightGBM_10_4.py ทีละขั้นตอน")
        print("3. ทดสอบแต่ละขั้นตอนก่อนไปขั้นตอนต่อไป")
        print("4. ใช้โมเดลจากครั้งที่ 2 ที่ให้ผลดี (+$5,940)")
        print("5. หยุดการเทรนซ้ำจนกว่าจะแก้ไขปัญหาหลัก")

if __name__ == "__main__":
    fixer = TrainingDegradationFix()
    fixer.run_all_steps()
