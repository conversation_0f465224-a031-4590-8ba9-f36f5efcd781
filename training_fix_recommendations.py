"""
🔧 Training Fix Recommendations
==============================

แนวทางแก้ไขปัญหาการเทรนที่ให้ผลลัพธ์แย่ลง
พร้อมโค้ดสำหรับปรับปรุง LightGBM_10_3.py
"""

import os
import json
from datetime import datetime

class TrainingFixRecommendations:
    def __init__(self):
        self.fixes = []
        self.code_modifications = []
    
    def generate_comprehensive_fixes(self):
        """สร้างแนวทางแก้ไขครบถ้วน"""
        
        print("🔧 Training Fix Recommendations")
        print("="*50)
        
        # 1. Data Leakage และ Overfitting
        self._fix_data_leakage()
        
        # 2. Win Rate และ Trading Performance
        self._fix_win_rate_issues()
        
        # 3. Data Imbalance
        self._fix_data_imbalance()
        
        # 4. Model Performance
        self._fix_model_performance()
        
        # 5. Parameter Optimization
        self._fix_parameter_issues()
        
        # 6. Training Process
        self._fix_training_process()
        
        # แสดงผลการแก้ไข
        self._display_fixes()
        
        # สร้างโค้ดสำหรับแก้ไข
        self._generate_fix_code()
    
    def _fix_data_leakage(self):
        """แก้ไขปัญหา Data Leakage"""
        
        self.fixes.append({
            'category': '🚨 Data Leakage & Overfitting',
            'priority': 'CRITICAL',
            'fixes': [
                "ตรวจสอบ Features ที่อาจมี Look-ahead Bias",
                "ใช้ TimeSeriesSplit แทน StratifiedKFold",
                "ตรวจสอบการสร้าง Target Variable",
                "เพิ่ม Validation Set แยกจาก Test Set",
                "ใช้ Early Stopping ใน LightGBM"
            ]
        })
        
        # โค้ดสำหรับแก้ไข
        self.code_modifications.append({
            'file': 'LightGBM_10_3.py',
            'section': 'Cross Validation',
            'code': '''
# แก้ไข Cross Validation
from sklearn.model_selection import TimeSeriesSplit

def improved_time_series_cv(X, y, n_splits=5):
    """Time Series CV ที่ป้องกัน Data Leakage"""
    
    # ใช้ TimeSeriesSplit
    tscv = TimeSeriesSplit(n_splits=n_splits, test_size=None)
    
    cv_scores = []
    for train_idx, val_idx in tscv.split(X):
        # แยกข้อมูลตามเวลา
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        # ตรวจสอบว่าไม่มี Data Leakage
        if X_train.index.max() >= X_val.index.min():
            print("⚠️ Warning: Potential data leakage detected!")
        
        # เทรนและทดสอบ
        model = lgb.LGBMClassifier(
            objective='binary',
            metric='auc',
            boosting_type='gbdt',
            num_leaves=31,
            learning_rate=0.05,
            feature_fraction=0.9,
            bagging_fraction=0.8,
            bagging_freq=5,
            verbose=-1,
            random_state=42,
            early_stopping_rounds=50  # เพิ่ม Early Stopping
        )
        
        model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            eval_metric='auc',
            verbose=False
        )
        
        y_pred = model.predict_proba(X_val)[:, 1]
        score = roc_auc_score(y_val, y_pred)
        cv_scores.append(score)
    
    return cv_scores
'''
        })
    
    def _fix_win_rate_issues(self):
        """แก้ไขปัญหา Win Rate"""
        
        self.fixes.append({
            'category': '🎯 Win Rate & Trading Performance',
            'priority': 'HIGH',
            'fixes': [
                "ปรับ Entry Conditions ให้เข้มงวดขึ้น",
                "ใช้ Multiple Timeframe Analysis",
                "เพิ่ม Market Condition Filters",
                "ปรับ Stop Loss และ Take Profit Ratios",
                "ใช้ Dynamic Position Sizing"
            ]
        })
        
        self.code_modifications.append({
            'file': 'LightGBM_10_3.py',
            'section': 'Entry Conditions',
            'code': '''
def improved_entry_conditions(df):
    """ปรับปรุง Entry Conditions เพื่อเพิ่ม Win Rate"""
    
    # เงื่อนไขเข้มงวดขึ้น
    strong_trend = (
        (df['EMA_diff_50_200'] > df['ATR'] * 0.5) |  # Trend แรง
        (df['EMA_diff_50_200'] < -df['ATR'] * 0.5)
    )
    
    # Volume Confirmation
    volume_confirm = df['Volume_Spike'] > 1.2
    
    # RSI ไม่ Overbought/Oversold
    rsi_safe = (df['RSI14'] > 25) & (df['RSI14'] < 75)
    
    # Market Hours Filter
    market_hours = df['Hour'].isin([8, 9, 10, 11, 12, 13, 14, 15, 16])
    
    # รวมเงื่อนไข
    entry_filter = strong_trend & volume_confirm & rsi_safe & market_hours
    
    return entry_filter

def dynamic_stop_loss_take_profit(df, atr_multiplier=1.5):
    """Dynamic SL/TP based on volatility"""
    
    # SL/TP ปรับตาม ATR
    df['Dynamic_SL'] = df['ATR'] * atr_multiplier
    df['Dynamic_TP'] = df['ATR'] * (atr_multiplier * 2)  # RR 1:2
    
    # ปรับตาม Market Condition
    high_vol_condition = df['ATR'] > df['ATR'].rolling(20).mean() * 1.2
    df.loc[high_vol_condition, 'Dynamic_SL'] *= 1.3
    df.loc[high_vol_condition, 'Dynamic_TP'] *= 1.3
    
    return df
'''
        })
    
    def _fix_data_imbalance(self):
        """แก้ไขปัญหา Data Imbalance"""
        
        self.fixes.append({
            'category': '⚖️ Data Imbalance',
            'priority': 'HIGH',
            'fixes': [
                "ใช้ SMOTE สำหรับ Oversampling",
                "ใช้ Class Weights ใน LightGBM",
                "ใช้ Stratified Sampling",
                "สร้าง Synthetic Data สำหรับ Minority Class",
                "ใช้ Ensemble Methods"
            ]
        })
        
        self.code_modifications.append({
            'file': 'LightGBM_10_3.py',
            'section': 'Data Balancing',
            'code': '''
from imblearn.over_sampling import SMOTE
from collections import Counter

def balance_training_data(X, y, method='smote'):
    """Balance training data"""
    
    print(f"Original distribution: {Counter(y)}")
    
    if method == 'smote':
        # ใช้ SMOTE
        smote = SMOTE(random_state=42, k_neighbors=3)
        X_balanced, y_balanced = smote.fit_resample(X, y)
        
    elif method == 'class_weights':
        # ใช้ Class Weights
        from sklearn.utils.class_weight import compute_class_weight
        
        classes = np.unique(y)
        class_weights = compute_class_weight(
            'balanced', 
            classes=classes, 
            y=y
        )
        
        class_weight_dict = dict(zip(classes, class_weights))
        return X, y, class_weight_dict
    
    print(f"Balanced distribution: {Counter(y_balanced)}")
    return X_balanced, y_balanced, None

# ใช้ใน Model Training
def train_balanced_model(X_train, y_train, X_val, y_val):
    """Train model with balanced data"""
    
    # Balance data
    X_balanced, y_balanced, class_weights = balance_training_data(
        X_train, y_train, method='smote'
    )
    
    # Model parameters
    params = {
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'early_stopping_rounds': 50,
        'is_unbalance': True  # LightGBM auto-balance
    }
    
    # Add class weights if available
    if class_weights:
        params['class_weight'] = class_weights
    
    model = lgb.LGBMClassifier(**params)
    
    model.fit(
        X_balanced, y_balanced,
        eval_set=[(X_val, y_val)],
        eval_metric='auc',
        verbose=False
    )
    
    return model
'''
        })
    
    def _fix_model_performance(self):
        """แก้ไขปัญหา Model Performance"""
        
        self.fixes.append({
            'category': '🤖 Model Performance',
            'priority': 'HIGH',
            'fixes': [
                "ใช้ Ensemble Methods (Voting, Stacking)",
                "ปรับ Hyperparameters ให้เหมาะสม",
                "เพิ่ม Feature Engineering",
                "ใช้ Feature Selection",
                "ใช้ Model Validation ที่เข้มงวด"
            ]
        })
        
        self.code_modifications.append({
            'file': 'LightGBM_10_3.py',
            'section': 'Model Ensemble',
            'code': '''
from sklearn.ensemble import VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier

def create_ensemble_model(X_train, y_train, X_val, y_val):
    """Create ensemble model for better performance"""
    
    # Base models
    lgb_model = lgb.LGBMClassifier(
        objective='binary',
        metric='auc',
        num_leaves=31,
        learning_rate=0.05,
        feature_fraction=0.9,
        random_state=42,
        verbose=-1
    )
    
    rf_model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1
    )
    
    lr_model = LogisticRegression(
        random_state=42,
        max_iter=1000
    )
    
    # Ensemble
    ensemble = VotingClassifier(
        estimators=[
            ('lgb', lgb_model),
            ('rf', rf_model),
            ('lr', lr_model)
        ],
        voting='soft'  # Use probabilities
    )
    
    # Train ensemble
    ensemble.fit(X_train, y_train)
    
    # Validate
    y_pred_proba = ensemble.predict_proba(X_val)[:, 1]
    auc_score = roc_auc_score(y_val, y_pred_proba)
    
    print(f"Ensemble AUC: {auc_score:.4f}")
    
    return ensemble

def advanced_feature_selection(X, y, n_features=50):
    """Advanced feature selection"""
    
    from sklearn.feature_selection import SelectKBest, f_classif
    from sklearn.feature_selection import RFE
    
    # Statistical selection
    selector_stats = SelectKBest(f_classif, k=n_features)
    X_selected_stats = selector_stats.fit_transform(X, y)
    
    # RFE with LightGBM
    lgb_model = lgb.LGBMClassifier(random_state=42, verbose=-1)
    selector_rfe = RFE(lgb_model, n_features_to_select=n_features)
    X_selected_rfe = selector_rfe.fit_transform(X, y)
    
    # Get selected features
    selected_features_stats = X.columns[selector_stats.get_support()]
    selected_features_rfe = X.columns[selector_rfe.get_support()]
    
    # Combine selections
    combined_features = list(set(selected_features_stats) | set(selected_features_rfe))
    
    print(f"Selected {len(combined_features)} features")
    
    return X[combined_features], combined_features
'''
        })
    
    def _fix_parameter_issues(self):
        """แก้ไขปัญหาพารามิเตอร์"""
        
        self.fixes.append({
            'category': '⚙️ Parameter Optimization',
            'priority': 'MEDIUM',
            'fixes': [
                "ใช้ Bayesian Optimization สำหรับ Hyperparameters",
                "ปรับ Threshold แบบ Dynamic",
                "ใช้ Walk-Forward Analysis",
                "ปรับ Time Filters ตาม Market Conditions",
                "ใช้ Adaptive Parameters"
            ]
        })
    
    def _fix_training_process(self):
        """แก้ไขกระบวนการเทรน"""
        
        self.fixes.append({
            'category': '🔄 Training Process',
            'priority': 'MEDIUM',
            'fixes': [
                "ใช้ Model Versioning และ Comparison",
                "เพิ่ม Data Quality Checks",
                "ใช้ Automated Model Validation",
                "สร้าง Training Pipeline",
                "ใช้ Monitoring และ Alerting"
            ]
        })
    
    def _display_fixes(self):
        """แสดงผลการแก้ไข"""
        
        print("\n💡 แนวทางแก้ไขปัญหาการเทรน:")
        print("="*60)
        
        for fix_group in self.fixes:
            priority_emoji = {
                'CRITICAL': '🚨',
                'HIGH': '⚠️',
                'MEDIUM': '📊',
                'LOW': 'ℹ️'
            }.get(fix_group['priority'], '📝')
            
            print(f"\n{priority_emoji} {fix_group['category']} [{fix_group['priority']}]")
            print("-" * 50)
            
            for i, fix in enumerate(fix_group['fixes'], 1):
                print(f"   {i}. {fix}")
    
    def _generate_fix_code(self):
        """สร้างไฟล์โค้ดสำหรับแก้ไข"""
        
        fix_code = """
# =============================================================================
# 🔧 TRAINING FIXES FOR LightGBM_10_3.py
# =============================================================================
# Generated: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """
#
# วิธีใช้:
# 1. เพิ่มโค้ดเหล่านี้เข้าใน LightGBM_10_3.py
# 2. เรียกใช้ฟังก์ชันในส่วนที่เหมาะสม
# 3. ทดสอบการทำงานก่อนใช้งานจริง
# =============================================================================

"""
        
        for mod in self.code_modifications:
            fix_code += f"""
# -----------------------------------------------------------------------------
# {mod['section']} - {mod['file']}
# -----------------------------------------------------------------------------
{mod['code']}

"""
        
        # บันทึกไฟล์
        with open('training_fixes_code.py', 'w', encoding='utf-8') as f:
            f.write(fix_code)
        
        print(f"\n✅ สร้างไฟล์โค้ดแก้ไข: training_fixes_code.py")
    
    def create_training_checklist(self):
        """สร้าง Checklist สำหรับการเทรน"""
        
        checklist = """
🔍 TRAINING QUALITY CHECKLIST
============================

ก่อนการเทรน:
□ ตรวจสอบข้อมูลไม่มี Missing Values
□ ตรวจสอบ Data Distribution
□ ตรวจสอบ Feature Correlation
□ ตรวจสอบ Target Variable Balance
□ ตรวจสอบ Time Series Order

ระหว่างการเทรน:
□ ใช้ TimeSeriesSplit สำหรับ CV
□ ใช้ Early Stopping
□ ตรวจสอบ Validation Score
□ ตรวจสอบ Feature Importance
□ ตรวจสอบ Model Complexity

หลังการเทรน:
□ ตรวจสอบ Out-of-Sample Performance
□ ตรวจสอบ Win Rate > 40%
□ ตรวจสอบ Expectancy > 0
□ ตรวจสอบ Max Drawdown < 20%
□ เปรียบเทียบกับ Baseline Model

เงื่อนไขการยอมรับโมเดล:
□ AUC > 0.6
□ F1 Score > 0.3
□ Win Rate > 40%
□ Expectancy > 0
□ Max Drawdown < 20%
□ Profit Factor > 1.2

หากไม่ผ่านเงื่อนไข:
□ หยุดการเทรนซ้ำ
□ วิเคราะห์สาเหตุ
□ แก้ไขปัญหาก่อนเทรนใหม่
□ ปรับ Parameters
□ เพิ่ม/ลด Features
"""
        
        with open('training_quality_checklist.txt', 'w', encoding='utf-8') as f:
            f.write(checklist)
        
        print(f"✅ สร้าง Training Checklist: training_quality_checklist.txt")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🔧 Training Fix Recommendations Generator")
    print("="*50)
    
    fixer = TrainingFixRecommendations()
    
    # สร้างแนวทางแก้ไข
    fixer.generate_comprehensive_fixes()
    
    # สร้าง Checklist
    fixer.create_training_checklist()
    
    print(f"\n🎯 สรุปการแก้ไข:")
    print("1. ✅ วิเคราะห์ปัญหาและสร้างแนวทางแก้ไข")
    print("2. ✅ สร้างโค้ดสำหรับแก้ไขปัญหา")
    print("3. ✅ สร้าง Training Quality Checklist")
    
    print(f"\n📁 ไฟล์ที่สร้าง:")
    print("   - training_fixes_code.py")
    print("   - training_quality_checklist.txt")
    
    print(f"\n⚠️ คำแนะนำสำคัญ:")
    print("1. หยุดการเทรนซ้ำจนกว่าจะแก้ไขปัญหาหลัก")
    print("2. ใช้ Checklist ทุกครั้งก่อนการเทรน")
    print("3. ทดสอบโค้ดแก้ไขก่อนใช้งานจริง")
    print("4. ตรวจสอบผลลัพธ์อย่างละเอียด")

if __name__ == "__main__":
    main()
