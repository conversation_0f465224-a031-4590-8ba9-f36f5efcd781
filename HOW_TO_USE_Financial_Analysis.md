# 🚀 วิธีการใช้งานระบบวิเคราะห์ทางการเงิน

## 📋 **ขั้นตอนการใช้งานแบบ Step-by-Step**

### **ขั้นตอนที่ 1: ตรวจสอบไฟล์ที่จำเป็น**

ตรวจสอบว่ามีไฟล์เหล่านี้ในโฟลเดอร์เดียวกับ `LightGBM_09_MM.py`:

```
✅ financial_analysis_system.py      # ระบบวิเคราะห์หลัก
✅ financial_integration.py          # การผสานรวม
✅ LightGBM_09_MM.py                 # ไฟล์หลัก (แก้ไขแล้ว)
```

### **ขั้นตอนที่ 2: ตั้งค่าการใช้งาน**

แก้ไขใน `LightGBM_09_MM.py` บรรทัดประมาณ 14245-14247:

```python
# เปิดใช้งานการวิเคราะห์ทางการเงิน (เปลี่ยนเป็น False หากไม่ต้องการ)
ENABLE_FINANCIAL_ANALYSIS = True    # 🔄 เปลี่ยนเป็น True/False
ACCOUNT_BALANCE = 1000              # 🔄 ปรับยอดเงินในบัญชี
```

### **ขั้นตอนที่ 3: ตั้งค่า TEST_GROUPS**

แก้ไขใน `LightGBM_09_MM.py` บรรทัดประมาณ 298-303:

```python
TEST_GROUPS = {
    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
}
```

### **ขั้นตอนที่ 4: อัปเดตราคาปัจจุบัน**

แก้ไขใน `financial_integration.py` บรรทัดประมาณ 18-26:

```python
CURRENT_PRICES = {
    'GOLD': 2650.0,      # 🔄 อัปเดตราคาปัจจุบัน
    'EURUSD': 1.0850,    # 🔄 อัปเดตราคาปัจจุบัน
    'GBPUSD': 1.2650,    # 🔄 อัปเดตราคาปัจจุบัน
    'AUDUSD': 0.6750,    # 🔄 อัปเดตราคาปัจจุบัน
    'NZDUSD': 0.6150,    # 🔄 อัปเดตราคาปัจจุบัน
    'USDCAD': 1.3550,    # 🔄 อัปเดตราคาปัจจุบัน
    'USDJPY': 148.50     # 🔄 อัปเดตราคาปัจจุบัน
}
```

### **ขั้นตอนที่ 5: รันการวิเคราะห์**

```bash
python LightGBM_09_MM.py
```

## 📊 **ผลลัพธ์ที่จะได้รับ**

### **1. ระหว่างการรัน**

จะเห็นข้อความแบบนี้สำหรับแต่ละสัญลักษณ์:

```
💰 เริ่มการวิเคราะห์ทางการเงินสำหรับ EURUSD M30
🔄 กำลังประมวลผล EURUSD M30...
💾 บันทึกการวิเคราะห์ EURUSD M30 ที่: Financial_Analysis_Results/EURUSD_M30_financial_analysis.json
✅ วิเคราะห์ทางการเงิน EURUSD M30 สำเร็จ
```

### **2. ไฟล์ที่จะถูกสร้าง**

```
📁 Financial_Analysis_Results/
├── 📄 complete_financial_analysis.json          # ข้อมูลการวิเคราะห์ทั้งหมด
├── 📊 risk_management_table.csv                # ตารางการจัดการความเสี่ยง
├── 📝 financial_analysis_report.txt            # รายงานสรุป
├── 📈 trading_performance_analysis.png         # กราฟผลการเทรด
├── 📋 AUDUSD_M30_financial_analysis.json       # ข้อมูลแต่ละสัญลักษณ์
├── 📋 AUDUSD_M60_financial_analysis.json
├── 📋 EURUSD_M30_financial_analysis.json
├── 📋 EURUSD_M60_financial_analysis.json
├── 📋 GBPUSD_M30_financial_analysis.json
├── 📋 GBPUSD_M60_financial_analysis.json
├── 📋 GOLD_M30_financial_analysis.json
├── 📋 GOLD_M60_financial_analysis.json
├── 📋 NZDUSD_M30_financial_analysis.json
├── 📋 NZDUSD_M60_financial_analysis.json
├── 📋 USDCAD_M30_financial_analysis.json
├── 📋 USDCAD_M60_financial_analysis.json
├── 📋 USDJPY_M30_financial_analysis.json
└── 📋 USDJPY_M60_financial_analysis.json
```

### **3. ผลสรุปที่จะแสดง**

```
================================================================================
💰 เริ่มการวิเคราะห์ทางการเงินรวม
================================================================================
🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์
📊 พบข้อมูลการเทรด: 672 รายการ
📊 บันทึกกราฟที่: Financial_Analysis_Results/trading_performance_analysis.png

============================================================
📈 FINANCIAL ANALYSIS SUMMARY
============================================================
💰 ยอดเงินในบัญชี: $1,000.00
📊 จำนวนการเทรดทั้งหมด: 672
💵 กำไรรวม (1.0 lot): $20,676.66
📉 Drawdown สูงสุด (1.0 lot): $3,747.22
🎯 ขนาดล็อตที่แนะนำ: 0.0053
⚠️ ความเสี่ยงสูงสุด: 2.00%
============================================================
```

## 🔧 **การปรับแต่งเพิ่มเติม**

### **1. ปิดการใช้งานการวิเคราะห์ทางการเงิน**

```python
ENABLE_FINANCIAL_ANALYSIS = False
```

### **2. เปลี่ยนยอดเงินในบัญชี**

```python
ACCOUNT_BALANCE = 5000  # เปลี่ยนเป็น $5,000
```

### **3. เปลี่ยน Leverage**

แก้ไขใน `financial_analysis_system.py` บรรทัด 25:

```python
def __init__(self, base_currency='USD', leverage=1000):  # เปลี่ยนเป็น 1:1000
```

## 🎯 **การอ่านผลลัพธ์**

### **1. ตารางการจัดการความเสี่ยง**

| Risk % | Risk Amount | Max Lot Size | Status |
|--------|-------------|--------------|---------|
| 0.1%   | $1.00       | 0.0003       | Safe    |
| 2.0%   | $20.00      | 0.0053       | Safe    |
| 5.0%   | $50.00      | 0.0133       | Moderate|
| 10.0%  | $100.00     | 0.0267       | High Risk|

**คำแนะนำ**: ใช้ขนาดล็อตในระดับ "Safe" (≤ 2%)

### **2. กราฟผลการเทรด**

- **กราฟบน**: Cumulative Profit ตามเวลา
- **กราฟล่าง**: Drawdown ตามเวลา

### **3. ไฟล์ JSON แต่ละสัญลักษณ์**

```json
{
  "symbol": "EURUSD",
  "timeframe": "M30",
  "total_trades": 48,
  "total_profit_usd": 1234.56,
  "max_drawdown_usd": 234.56,
  "margin_per_trade": 21.70,
  "pips_value_per_pip": 1.0
}
```

## ❗ **ข้อควรระวัง**

1. **ราคาปัจจุบัน**: อัปเดตใน `CURRENT_PRICES` ให้เป็นราคาล่าสุด
2. **ไฟล์ CSV**: ตรวจสอบว่ามีไฟล์ CSV ในโฟลเดอร์ `CSV_Files_Fixed/`
3. **เวลาการรัน**: อาจใช้เวลานานสำหรับข้อมูลจำนวนมาก
4. **หน่วยความจำ**: ตรวจสอบ RAM เพียงพอสำหรับการประมวลผล

## 🆘 **การแก้ไขปัญหา**

### **ปัญหา: ไม่พบไฟล์ financial_analysis_system.py**

```
❌ ไม่พบไฟล์ financial_analysis_system.py - ปิดการใช้งานการวิเคราะห์ทางการเงิน
```

**วิธีแก้**: ตรวจสอบว่าไฟล์อยู่ในโฟลเดอร์เดียวกับ `LightGBM_09_MM.py`

### **ปัญหา: ไม่มีข้อมูลการเทรด**

```
ℹ️ ไม่ได้เปิดใช้งานการวิเคราะห์ทางการเงิน หรือไม่มีข้อมูลสำหรับวิเคราะห์
```

**วิธีแก้**: 
1. ตรวจสอบ `ENABLE_FINANCIAL_ANALYSIS = True`
2. ตรวจสอบว่า `create_trade_cycles_with_model()` สร้างข้อมูลการเทรด

### **ปัญหา: ข้อผิดพลาดในการคำนวณ**

**วิธีแก้**: ตรวจสอบราคาใน `CURRENT_PRICES` ว่าเป็นตัวเลขที่ถูกต้อง

---

**🎉 พร้อมใช้งาน!** หากมีปัญหาเพิ่มเติม สามารถดูรายละเอียดใน log ที่แสดงระหว่างการรัน
