+ ถ้าต้องการทดสอบ TEST_GROUPS ทั้งหมด เพื่อหาการตั้งค่าที่ดีที่สุด

TEST_GROUPS = {
    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
}

+ วิธีเรียกดูผลการทดสอบอย่างไร มีการบันทึกหรือไม่ ต้องการให้มีการบันทึกเป็นไฟล .txt เพื่อดูง่ายขึ้น

+ การนำไปใช้กับ LightGBM_10_3.py และ WebRequest_Server_05.py ต้องเพิ่มเงื่อนไขการดึงข้อมูลทดสอบมาใช้อย่างไร

+++

+ ช่วยตรวจสอบการจัดการ ข้อมูลที่ใช้ทดสอบ เพื่อป้องกันไม่ให้ overfit หรือ Data Leakage มีการจัดการอย่างไร

+ ช่วยสรุปวิธีการใช้งาน การทดสอบ แบบเป็นขั้นตอน

+++

