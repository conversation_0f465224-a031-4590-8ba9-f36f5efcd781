"""
🔍 Training Degradation Analyzer
===============================

วิเคราะห์สาเหตุที่ผลลัพธ์การเทรนแย่ลงเมื่อเทรนหลายครั้ง
และแนะนำแนวทางแก้ไข
"""

import json
import os
import pandas as pd
from datetime import datetime
import glob

class TrainingDegradationAnalyzer:
    def __init__(self):
        self.performance_history = []
        self.issues_found = []
        self.recommendations = []
    
    def analyze_log_file(self, log_file_path="LightGBM/Log_Train.txt"):
        """วิเคราะห์ไฟล์ log เพื่อหาสาเหตุปัญหา"""
        
        print("🔍 วิเคราะห์ไฟล์ Log การเทรน...")
        print("=" * 50)
        
        if not os.path.exists(log_file_path):
            print(f"❌ ไม่พบไฟล์ {log_file_path}")
            return
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                log_content = f.read()
            
            # วิเคราะห์ปัญหาต่างๆ
            self._analyze_win_rate_issues(log_content)
            self._analyze_data_quality_issues(log_content)
            self._analyze_model_performance_issues(log_content)
            self._analyze_overfitting_issues(log_content)
            self._analyze_parameter_issues(log_content)
            
            # สรุปผลการวิเคราะห์
            self._generate_recommendations()
            self._display_analysis_results()
            
        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดในการอ่านไฟล์: {e}")
    
    def _analyze_win_rate_issues(self, log_content):
        """วิเคราะห์ปัญหา Win Rate"""
        
        print("\n🎯 วิเคราะห์ Win Rate Issues:")
        
        # ค้นหา Win Rate ที่ต่ำ
        if "win_rate': 0.0" in log_content:
            self.issues_found.append({
                'type': 'CRITICAL',
                'category': 'Win Rate',
                'issue': 'Win Rate = 0% สำหรับ Buy trades',
                'impact': 'ไม่มี Buy trade ที่ทำกำไร',
                'severity': 'HIGH'
            })
            print("   ❌ CRITICAL: Win Rate = 0% สำหรับ Buy trades")
        
        if "win_rate': 15.151515151515152" in log_content:
            self.issues_found.append({
                'type': 'WARNING',
                'category': 'Win Rate',
                'issue': 'Win Rate = 15.15% สำหรับ Sell trades',
                'impact': 'Win Rate ต่ำมาก ทำให้ขาดทุนโดยรวม',
                'severity': 'HIGH'
            })
            print("   ⚠️ WARNING: Win Rate = 15.15% สำหรับ Sell trades")
        
        # ตรวจสอบ Time Filters
        if "'days': [], 'hours': []" in log_content:
            self.issues_found.append({
                'type': 'WARNING',
                'category': 'Time Filters',
                'issue': 'Time Filters ว่าง',
                'impact': 'ไม่มีการกรองเวลาที่เหมาะสม',
                'severity': 'MEDIUM'
            })
            print("   ⚠️ WARNING: Time Filters ว่าง")
    
    def _analyze_data_quality_issues(self, log_content):
        """วิเคราะห์ปัญหาคุณภาพข้อมูล"""
        
        print("\n📊 วิเคราะห์ Data Quality Issues:")
        
        # ตรวจสอบจำนวน trades
        if "Total trades: 19" in log_content and "Total trades: 165" in log_content:
            self.issues_found.append({
                'type': 'WARNING',
                'category': 'Data Imbalance',
                'issue': 'ข้อมูลไม่สมดุล: Buy 19 trades vs Sell 165 trades',
                'impact': 'โมเดลเรียนรู้ Sell มากกว่า Buy',
                'severity': 'HIGH'
            })
            print("   ⚠️ WARNING: ข้อมูลไม่สมดุล (Buy 19 vs Sell 165)")
        
        # ตรวจสอบ Features
        if "features : 20" in log_content:
            self.issues_found.append({
                'type': 'INFO',
                'category': 'Features',
                'issue': 'ใช้ Features เพียง 20 ตัว',
                'impact': 'อาจไม่เพียงพอสำหรับการเรียนรู้',
                'severity': 'MEDIUM'
            })
            print("   ℹ️ INFO: ใช้ Features เพียง 20 ตัว")
    
    def _analyze_model_performance_issues(self, log_content):
        """วิเคราะห์ปัญหาประสิทธิภาพโมเดล"""
        
        print("\n🤖 วิเคราะห์ Model Performance Issues:")
        
        # ตรวจสอบ AUC
        if "AUC         | 0.5000    | 0.7820" in log_content:
            self.issues_found.append({
                'type': 'WARNING',
                'category': 'Model Performance',
                'issue': 'CV AUC = 0.5 (random performance)',
                'impact': 'โมเดลไม่สามารถเรียนรู้ได้ดีใน CV',
                'severity': 'HIGH'
            })
            print("   ⚠️ WARNING: CV AUC = 0.5 (random performance)")
        
        # ตรวจสอบ F1 Score
        if "F1 Score    | 0.0000    | 0.0000" in log_content:
            self.issues_found.append({
                'type': 'CRITICAL',
                'category': 'Model Performance',
                'issue': 'F1 Score = 0 ทั้ง CV และ Test',
                'impact': 'โมเดลไม่สามารถทำนาย positive class ได้',
                'severity': 'CRITICAL'
            })
            print("   ❌ CRITICAL: F1 Score = 0")
        
        # ตรวจสอบการเปรียบเทียบกับโมเดลก่อนหน้า
        if "โมเดลไม่ดีขึ้น" in log_content:
            self.issues_found.append({
                'type': 'WARNING',
                'category': 'Model Degradation',
                'issue': 'โมเดลไม่ดีขึ้นเมื่อเทรนใหม่',
                'impact': 'ประสิทธิภาพลดลงเมื่อเทรนหลายครั้ง',
                'severity': 'HIGH'
            })
            print("   ⚠️ WARNING: โมเดลไม่ดีขึ้นเมื่อเทรนใหม่")
    
    def _analyze_overfitting_issues(self, log_content):
        """วิเคราะห์ปัญหา Overfitting"""
        
        print("\n📈 วิเคราะห์ Overfitting Issues:")
        
        # ตรวจสอบความแตกต่างระหว่าง Train และ Test
        if "Accuracy        0.500      0.997        0.997      +99.4%" in log_content:
            self.issues_found.append({
                'type': 'CRITICAL',
                'category': 'Overfitting',
                'issue': 'Accuracy เพิ่มขึ้น 99.4% (น่าสงสัย)',
                'impact': 'อาจเกิด overfitting หรือ data leakage',
                'severity': 'CRITICAL'
            })
            print("   ❌ CRITICAL: Accuracy เพิ่มขึ้น 99.4% (น่าสงสัย)")
        
        # ตรวจสอบ Model Accuracy ที่สูงผิดปกติ
        if "model_accuracy': 0.9827429761722086" in log_content:
            self.issues_found.append({
                'type': 'WARNING',
                'category': 'Overfitting',
                'issue': 'Model Accuracy = 98.27% (สูงผิดปกติ)',
                'impact': 'อาจเกิด overfitting',
                'severity': 'MEDIUM'
            })
            print("   ⚠️ WARNING: Model Accuracy = 98.27% (สูงผิดปกติ)")
    
    def _analyze_parameter_issues(self, log_content):
        """วิเคราะห์ปัญหาพารามิเตอร์"""
        
        print("\n⚙️ วิเคราะห์ Parameter Issues:")
        
        # ตรวจสอบการใช้ default threshold
        if "ใช้ค่า default: 0.3" in log_content:
            self.issues_found.append({
                'type': 'INFO',
                'category': 'Parameters',
                'issue': 'ใช้ default threshold = 0.3',
                'impact': 'อาจไม่เหมาะสมกับข้อมูล',
                'severity': 'MEDIUM'
            })
            print("   ℹ️ INFO: ใช้ default threshold = 0.3")
        
        # ตรวจสอบการเปลี่ยนแปลงพารามิเตอร์
        if "input_rsi_level_in: 35 → 25" in log_content:
            self.issues_found.append({
                'type': 'INFO',
                'category': 'Parameters',
                'issue': 'เปลี่ยน RSI Level จาก 35 → 25',
                'impact': 'อาจทำให้สัญญาณเข้มงวดเกินไป',
                'severity': 'LOW'
            })
            print("   ℹ️ INFO: เปลี่ยน RSI Level จาก 35 → 25")
    
    def _generate_recommendations(self):
        """สร้างคำแนะนำการแก้ไข"""
        
        # จัดกลุ่มปัญหาตามความรุนแรง
        critical_issues = [i for i in self.issues_found if i['severity'] == 'CRITICAL']
        high_issues = [i for i in self.issues_found if i['severity'] == 'HIGH']
        medium_issues = [i for i in self.issues_found if i['severity'] == 'MEDIUM']
        
        # สร้างคำแนะนำ
        if critical_issues:
            self.recommendations.extend([
                "🚨 CRITICAL FIXES:",
                "1. ตรวจสอบ Data Leakage - Model Accuracy 98%+ ผิดปกติ",
                "2. แก้ไข F1 Score = 0 - โมเดลไม่สามารถทำนาย positive class",
                "3. ตรวจสอบ Target Variable - อาจมีปัญหาในการสร้าง labels",
                "4. ใช้ Stratified Split เพื่อรักษาสัดส่วน positive/negative"
            ])
        
        if high_issues:
            self.recommendations.extend([
                "\n⚠️ HIGH PRIORITY FIXES:",
                "1. แก้ไขปัญหา Data Imbalance - ใช้ SMOTE หรือ class weights",
                "2. ปรับ Win Rate ให้สูงขึ้น - ตรวจสอบ entry conditions",
                "3. ใช้ Time Series CV แทน Random CV",
                "4. ตรวจสอบ Feature Engineering - อาจต้องเพิ่ม features"
            ])
        
        if medium_issues:
            self.recommendations.extend([
                "\n📊 MEDIUM PRIORITY FIXES:",
                "1. ปรับ Threshold ให้เหมาะสมกับข้อมูล",
                "2. เพิ่มจำนวน Features ที่ใช้ในโมเดล",
                "3. ใช้ Time Filters ที่เหมาะสม",
                "4. ตรวจสอบ Hyperparameter Tuning"
            ])
        
        # คำแนะนำทั่วไป
        self.recommendations.extend([
            "\n🔧 GENERAL RECOMMENDATIONS:",
            "1. หยุดการเทรนซ้ำๆ จนกว่าจะแก้ไขปัญหาหลัก",
            "2. ใช้ Early Stopping เพื่อป้องกัน Overfitting",
            "3. ตรวจสอบข้อมูลก่อนการเทรนทุกครั้ง",
            "4. บันทึก Model Performance เพื่อเปรียบเทียบ",
            "5. ใช้ Cross-Validation ที่เหมาะสมกับ Time Series"
        ])
    
    def _display_analysis_results(self):
        """แสดงผลการวิเคราะห์"""
        
        print("\n" + "="*70)
        print("📋 สรุปการวิเคราะห์ปัญหาการเทรน")
        print("="*70)
        
        # สรุปปัญหาที่พบ
        print(f"\n🔍 ปัญหาที่พบทั้งหมด: {len(self.issues_found)}")
        
        severity_counts = {}
        for issue in self.issues_found:
            severity = issue['severity']
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        for severity, count in severity_counts.items():
            emoji = {"CRITICAL": "🚨", "HIGH": "⚠️", "MEDIUM": "📊", "LOW": "ℹ️"}.get(severity, "📝")
            print(f"   {emoji} {severity}: {count} ปัญหา")
        
        # แสดงปัญหาที่สำคัญ
        critical_and_high = [i for i in self.issues_found if i['severity'] in ['CRITICAL', 'HIGH']]
        
        if critical_and_high:
            print(f"\n🚨 ปัญหาที่ต้องแก้ไขด่วน:")
            for i, issue in enumerate(critical_and_high, 1):
                emoji = "🚨" if issue['severity'] == 'CRITICAL' else "⚠️"
                print(f"   {i}. {emoji} {issue['issue']}")
                print(f"      Impact: {issue['impact']}")
        
        # แสดงคำแนะนำ
        print(f"\n💡 คำแนะนำการแก้ไข:")
        for rec in self.recommendations:
            print(f"   {rec}")
        
        print("\n" + "="*70)
    
    def save_analysis_report(self, output_file="training_analysis_report.txt"):
        """บันทึกรายงานการวิเคราะห์"""
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("🔍 TRAINING DEGRADATION ANALYSIS REPORT\n")
                f.write("="*60 + "\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write(f"📊 ISSUES SUMMARY:\n")
                f.write(f"Total Issues Found: {len(self.issues_found)}\n\n")
                
                for issue in self.issues_found:
                    f.write(f"[{issue['severity']}] {issue['category']}: {issue['issue']}\n")
                    f.write(f"Impact: {issue['impact']}\n\n")
                
                f.write("💡 RECOMMENDATIONS:\n")
                for rec in self.recommendations:
                    f.write(f"{rec}\n")
            
            print(f"✅ บันทึกรายงานการวิเคราะห์: {output_file}")
            
        except Exception as e:
            print(f"❌ ไม่สามารถบันทึกรายงาน: {e}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🔍 Training Degradation Analyzer")
    print("="*50)
    print("วิเคราะห์สาเหตุที่ผลลัพธ์การเทรนแย่ลงเมื่อเทรนหลายครั้ง")
    
    analyzer = TrainingDegradationAnalyzer()
    
    # วิเคราะห์ไฟล์ log
    analyzer.analyze_log_file()
    
    # บันทึกรายงาน
    analyzer.save_analysis_report()
    
    print(f"\n🎯 สรุปหลัก:")
    print("1. ปัญหาหลักคือ Data Leakage และ Overfitting")
    print("2. Win Rate ต่ำมาก (0% Buy, 15% Sell)")
    print("3. Model Accuracy สูงผิดปกติ (98%+)")
    print("4. F1 Score = 0 แสดงว่าโมเดลไม่สามารถทำนาย positive class")
    print("5. ข้อมูลไม่สมดุล (Buy 19 vs Sell 165)")
    
    print(f"\n💡 แนะนำ:")
    print("1. หยุดการเทรนซ้ำจนกว่าจะแก้ไขปัญหาหลัก")
    print("2. ตรวจสอบ Data Leakage ในการสร้าง Features")
    print("3. ใช้ Stratified Time Series Split")
    print("4. ปรับ Entry Conditions ให้ Win Rate สูงขึ้น")
    print("5. ใช้ SMOTE หรือ Class Weights สำหรับ Data Imbalance")

if __name__ == "__main__":
    main()
