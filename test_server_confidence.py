#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการส่งข้อมูลไป WebRequest Server และตรวจสอบ confidence values
"""

import requests
import json
import time

def test_server_confidence():
    """ทดสอบการส่งข้อมูลไป server และตรวจสอบ confidence values"""
    
    print("🔍 ทดสอบ WebRequest Server - Confidence Values")
    
    # URL ของ server
    server_url = "http://127.0.0.1:54321/data"
    
    # ข้อมูลทดสอบ (จำลองข้อมูลจาก MT5) - เพิ่มข้อมูลที่ขาดหาย
    test_data = {
        "symbol": "GOLD",
        "timeframe_str": "PERIOD_H1",
        "time": 1726574400.0,  # 2024-09-17 10:00:00
        "open": 3679.13,
        "high": 3685.25,
        "low": 3675.01,
        "close": 3679.16,
        "volume": 10295,
        "spread": 25,
        # เพิ่มข้อมูลที่อาจจะต้องการ
        "tick_volume": 10295,
        "real_volume": 0
    }
    
    print(f"📊 ข้อมูลทดสอบ:")
    print(f"   Symbol: {test_data['symbol']}")
    print(f"   Timeframe: {test_data['timeframe_str']}")
    print(f"   Close: {test_data['close']}")
    print(f"   Volume: {test_data['volume']}")
    
    try:
        print(f"\n🔄 ส่งข้อมูลไป server...")
        
        # ส่งข้อมูลไป server
        response = requests.post(
            server_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ Server ตอบกลับสำเร็จ!")
            print(f"📊 Response Status: {result.get('status', 'Unknown')}")
            print(f"📊 Signal: {result.get('signal', 'Unknown')}")
            print(f"📊 Confidence: {result.get('confidence', 'Unknown')}")
            print(f"📊 Scenario Used: {result.get('scenario_used', 'Unknown')}")
            print(f"📊 Market Condition: {result.get('market_condition', 'Unknown')}")
            
            # ตรวจสอบ confidence values ของแต่ละ scenario
            print(f"\n🔍 Confidence Values ของแต่ละ Scenario:")
            
            tf_buy_conf = result.get('trend_following_buy_confidence', 'N/A')
            tf_sell_conf = result.get('trend_following_sell_confidence', 'N/A')
            ct_buy_conf = result.get('counter_trend_buy_confidence', 'N/A')
            ct_sell_conf = result.get('counter_trend_sell_confidence', 'N/A')
            
            print(f"   Trend Following:")
            print(f"      BUY: {tf_buy_conf}")
            print(f"      SELL: {tf_sell_conf}")
            print(f"   Counter Trend:")
            print(f"      BUY: {ct_buy_conf}")
            print(f"      SELL: {ct_sell_conf}")
            
            # ตรวจสอบความสอดคล้อง
            print(f"\n🔍 ตรวจสอบความสอดคล้อง:")
            
            main_confidence = result.get('confidence', 0.0)
            scenario_used = result.get('scenario_used', 'unknown')
            signal = result.get('signal', 'HOLD')
            
            print(f"   Main Signal: {signal}")
            print(f"   Main Confidence: {main_confidence}")
            print(f"   Scenario Used: {scenario_used}")
            
            # ตรวจสอบว่า confidence ตรงกับ scenario ที่ใช้หรือไม่
            if scenario_used == 'trend_following':
                if signal == 'BUY':
                    expected_conf = tf_buy_conf
                    print(f"   Expected Confidence (TF BUY): {expected_conf}")
                elif signal == 'SELL':
                    expected_conf = tf_sell_conf
                    print(f"   Expected Confidence (TF SELL): {expected_conf}")
                else:
                    expected_conf = max(tf_buy_conf, tf_sell_conf) if isinstance(tf_buy_conf, (int, float)) and isinstance(tf_sell_conf, (int, float)) else 'N/A'
                    print(f"   Expected Confidence (TF MAX): {expected_conf}")
            elif scenario_used == 'counter_trend':
                if signal == 'BUY':
                    expected_conf = ct_buy_conf
                    print(f"   Expected Confidence (CT BUY): {expected_conf}")
                elif signal == 'SELL':
                    expected_conf = ct_sell_conf
                    print(f"   Expected Confidence (CT SELL): {expected_conf}")
                else:
                    expected_conf = max(ct_buy_conf, ct_sell_conf) if isinstance(ct_buy_conf, (int, float)) and isinstance(ct_sell_conf, (int, float)) else 'N/A'
                    print(f"   Expected Confidence (CT MAX): {expected_conf}")
            else:
                expected_conf = 'N/A'
                print(f"   Expected Confidence: {expected_conf} (Unknown scenario)")
            
            # ตรวจสอบความสอดคล้อง
            if isinstance(expected_conf, (int, float)) and isinstance(main_confidence, (int, float)):
                if abs(expected_conf - main_confidence) < 0.0001:
                    print(f"   ✅ Confidence สอดคล้องกัน!")
                else:
                    print(f"   ❌ Confidence ไม่สอดคล้อง! (ต่าง {abs(expected_conf - main_confidence):.4f})")
            else:
                print(f"   ⚠️ ไม่สามารถตรวจสอบความสอดคล้องได้")
            
            # แสดงข้อมูลเพิ่มเติม
            print(f"\n📋 ข้อมูลเพิ่มเติม:")
            print(f"   Entry Price: {result.get('entry_price', 'N/A')}")
            print(f"   SL Price: {result.get('sl_price', 'N/A')}")
            print(f"   TP Price: {result.get('tp_price', 'N/A')}")
            print(f"   Threshold: {result.get('threshold', 'N/A')}")
            print(f"   nBars_SL: {result.get('nBars_SL', 'N/A')}")
            print(f"   Time Filters: {result.get('time_filters', 'N/A')}")
            
            # แสดง JSON response ทั้งหมด (สำหรับ debug)
            print(f"\n🔍 Full JSON Response:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
        else:
            print(f"❌ Server error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ ไม่สามารถเชื่อมต่อ server ได้ (ตรวจสอบว่า server ทำงานอยู่หรือไม่)")
    except requests.exceptions.Timeout:
        print(f"❌ Server timeout (ใช้เวลานานเกินไป)")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

if __name__ == "__main__":
    # รอให้ server เริ่มทำงาน
    print("⏳ รอ server เริ่มทำงาน...")
    time.sleep(3)
    
    test_server_confidence()
