#!/usr/bin/env python3
"""
Financial Analysis System for Trading Performance
ระบบวิเคราะห์ทางการเงินสำหรับประสิทธิภาพการเทรด

Features:
- คำนวณ pips value และ margin สำหรับแต่ละสัญลักษณ์
- วิเคราะห์ DDmax, Profit, Risk Management
- สร้างกราฟแสดงผลการเทรด
- คำนวณทุนที่ต้องใช้และความเสี่ยงที่เหมาะสม
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class FinancialAnalysisSystem:
    """ระบบวิเคราะห์ทางการเงินสำหรับการเทรด"""
    
    def __init__(self, base_currency='USD', leverage=500):
        self.base_currency = base_currency
        self.leverage = leverage
        
        # กำหนดข้อมูลสัญลักษณ์
        self.symbol_specs = {
            'GOLD': {'point': 0.01, 'contract_size': 100, 'type': 'metal'},
            'EURUSD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_quote'},
            'GBPUSD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_quote'},
            'AUDUSD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_quote'},
            'NZDUSD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_quote'},
            'USDCAD': {'point': 0.00001, 'contract_size': 100000, 'type': 'major_usd_base'},
            'USDJPY': {'point': 0.001, 'contract_size': 100000, 'type': 'major_usd_base'}
        }
        
        # เก็บข้อมูลการเทรดทั้งหมด
        self.all_trades = []
        self.symbol_data = {}
        
        # สร้างโฟลเดอร์สำหรับบันทึกผล
        self.results_folder = "Financial_Analysis_Results"
        os.makedirs(self.results_folder, exist_ok=True)
        
    def calculate_pips_value_and_margin(self, symbol: str, current_price: float, lot_size: float = 0.01) -> Dict:
        """
        คำนวณ pips value และ margin สำหรับสัญลักษณ์
        
        Args:
            symbol: สัญลักษณ์ (เช่น GOLD, EURUSD)
            current_price: ราคาปัจจุบัน
            lot_size: ขนาดล็อต (default: 0.01)
            
        Returns:
            Dict: {'pips_value_per_point', 'pips_value_per_pip', 'margin_required'}
        """
        if symbol not in self.symbol_specs:
            raise ValueError(f"Symbol {symbol} not supported")
            
        spec = self.symbol_specs[symbol]
        point = spec['point']
        contract_size = spec['contract_size']
        symbol_type = spec['type']
        
        # คำนวณ pips value
        if symbol_type == 'metal':  # GOLD
            pips_value_per_point = (point / current_price) * current_price * contract_size * lot_size
            margin_required = (lot_size * contract_size * current_price) / self.leverage
            
        elif symbol_type == 'major_usd_quote':  # EURUSD, GBPUSD, etc.
            pips_value_per_point = (point / current_price) * current_price * contract_size * lot_size
            margin_required = (lot_size * contract_size * current_price) / self.leverage
            
        elif symbol_type == 'major_usd_base':  # USDJPY, USDCAD
            pips_value_per_point = (point / current_price) * contract_size * lot_size
            margin_required = (lot_size * contract_size) / self.leverage
            
        else:
            raise ValueError(f"Unknown symbol type: {symbol_type}")
        
        # คำนวณ pips value per pip (10 points = 1 pip สำหรับส่วนใหญ่)
        pips_per_point_multiplier = 10 if point == 0.00001 or point == 0.001 else 1
        pips_value_per_pip = pips_value_per_point * pips_per_point_multiplier
        
        return {
            'pips_value_per_point': pips_value_per_point,
            'pips_value_per_pip': pips_value_per_pip,
            'margin_required': margin_required,
            'point': point,
            'contract_size': contract_size
        }
    
    def process_trade_cycle(self, symbol: str, timeframe: str, trade_data: pd.DataFrame, 
                           current_prices: Dict[str, float]) -> Dict:
        """
        ประมวลผลข้อมูลการเทรดสำหรับสัญลักษณ์หนึ่ง
        
        Args:
            symbol: สัญลักษณ์
            timeframe: ไทม์เฟรม (M30, M60)
            trade_data: DataFrame ที่มีข้อมูลการเทรด
            current_prices: Dict ของราคาปัจจุบันแต่ละสัญลักษณ์
            
        Returns:
            Dict: ข้อมูลการวิเคราะห์ทางการเงิน
        """
        if symbol not in current_prices:
            print(f"⚠️ ไม่มีราคาปัจจุบันสำหรับ {symbol}")
            return None
            
        # คำนวณ pips value และ margin
        financial_data = self.calculate_pips_value_and_margin(symbol, current_prices[symbol], 1.0)
        
        # ประมวลผลข้อมูลการเทรด
        trades_processed = []
        
        for idx, trade in trade_data.iterrows():
            # คำนวณกำไร/ขาดทุนในสกุลเงินหลัก
            pips_profit = trade.get('pips_profit', 0)  # กำไรในหน่วย pips
            usd_profit = pips_profit * financial_data['pips_value_per_pip']
            
            trade_info = {
                'symbol': symbol,
                'timeframe': timeframe,
                'open_time': trade.get('open_time'),
                'close_time': trade.get('close_time'),
                'direction': trade.get('direction', 'BUY'),
                'open_price': trade.get('open_price', 0),
                'close_price': trade.get('close_price', 0),
                'pips_profit': pips_profit,
                'usd_profit': usd_profit,
                'margin_required': financial_data['margin_required'],
                'pips_value_per_pip': financial_data['pips_value_per_pip'],
                'lot_size': 1.0  # ขนาดล็อตคงที่
            }
            
            trades_processed.append(trade_info)
        
        # คำนวณสถิติ
        if trades_processed:
            profits = [t['usd_profit'] for t in trades_processed]
            cumulative_profit = np.cumsum(profits)
            
            # คำนวณ Drawdown
            running_max = np.maximum.accumulate(cumulative_profit)
            drawdown = cumulative_profit - running_max
            max_drawdown = abs(min(drawdown)) if len(drawdown) > 0 else 0
            
            analysis_result = {
                'symbol': symbol,
                'timeframe': timeframe,
                'total_trades': len(trades_processed),
                'total_profit_usd': sum(profits),
                'max_drawdown_usd': max_drawdown,
                'margin_per_trade': financial_data['margin_required'],
                'pips_value_per_pip': financial_data['pips_value_per_pip'],
                'trades': trades_processed,
                'financial_specs': financial_data
            }
            
            # บันทึกข้อมูลสำหรับสัญลักษณ์นี้
            self.symbol_data[f"{symbol}_{timeframe}"] = analysis_result
            
            return analysis_result
        
        return None
    
    def save_individual_analysis(self, symbol: str, timeframe: str, analysis_data: Dict):
        """บันทึกผลการวิเคราะห์แต่ละสัญลักษณ์"""
        filename = f"{self.results_folder}/{symbol}_{timeframe}_financial_analysis.json"
        
        # แปลง datetime objects เป็น string สำหรับ JSON
        data_to_save = analysis_data.copy()
        for trade in data_to_save.get('trades', []):
            if 'open_time' in trade and hasattr(trade['open_time'], 'isoformat'):
                trade['open_time'] = trade['open_time'].isoformat()
            if 'close_time' in trade and hasattr(trade['close_time'], 'isoformat'):
                trade['close_time'] = trade['close_time'].isoformat()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data_to_save, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"💾 บันทึกการวิเคราะห์ {symbol} {timeframe} ที่: {filename}")
    
    def combine_all_analyses(self) -> Dict:
        """รวมการวิเคราะห์ทั้งหมดเข้าด้วยกัน"""
        all_trades = []
        total_profit = 0
        total_margin_required = 0
        
        # รวมข้อมูลจากทุกสัญลักษณ์
        for key, data in self.symbol_data.items():
            all_trades.extend(data['trades'])
            total_profit += data['total_profit_usd']
            total_margin_required += data['margin_per_trade']
        
        # เรียงตามเวลาเปิดการเทรด
        all_trades.sort(key=lambda x: x['open_time'] if x['open_time'] else datetime.min)
        
        # คำนวณสถิติรวม
        if all_trades:
            profits = [t['usd_profit'] for t in all_trades]
            cumulative_profit = np.cumsum(profits)
            
            # คำนวณ Drawdown รวม
            running_max = np.maximum.accumulate(cumulative_profit)
            drawdown = cumulative_profit - running_max
            max_drawdown = abs(min(drawdown)) if len(drawdown) > 0 else 0
            
            combined_analysis = {
                'total_trades': len(all_trades),
                'total_profit_usd': total_profit,
                'max_drawdown_usd': max_drawdown,
                'total_margin_required': total_margin_required,
                'trades': all_trades,
                'symbols_analyzed': list(self.symbol_data.keys()),
                'analysis_date': datetime.now().isoformat()
            }
            
            return combined_analysis
        
        return None

    def create_performance_plots(self, combined_analysis: Dict):
        """สร้างกราฟแสดงผลการเทรด"""
        if not combined_analysis or not combined_analysis['trades']:
            print("⚠️ ไม่มีข้อมูลการเทรดสำหรับสร้างกราฟ")
            return

        trades = combined_analysis['trades']

        # เตรียมข้อมูลสำหรับกราฟ
        dates = []
        cumulative_profits = []
        drawdowns = []

        cumulative = 0
        running_max = 0

        for trade in trades:
            if trade['close_time']:
                try:
                    if isinstance(trade['close_time'], str):
                        close_time = datetime.fromisoformat(trade['close_time'].replace('Z', '+00:00'))
                    else:
                        close_time = trade['close_time']

                    dates.append(close_time)
                    cumulative += trade['usd_profit']
                    cumulative_profits.append(cumulative)

                    # คำนวณ drawdown
                    running_max = max(running_max, cumulative)
                    drawdown = cumulative - running_max
                    drawdowns.append(drawdown)

                except Exception as e:
                    print(f"⚠️ Error processing date: {e}")
                    continue

        if not dates:
            print("⚠️ ไม่มีข้อมูลวันที่ที่ถูกต้องสำหรับสร้างกราฟ")
            return

        # สร้างกราฟ
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # กราฟกำไรสะสม
        ax1.plot(dates, cumulative_profits, 'b-', linewidth=2, label='Cumulative Profit')
        ax1.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax1.set_title('Cumulative Profit Over Time (USD)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Profit (USD)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Format x-axis
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

        # กราฟ Drawdown
        ax2.fill_between(dates, drawdowns, 0, color='red', alpha=0.3, label='Drawdown')
        ax2.plot(dates, drawdowns, 'r-', linewidth=2)
        ax2.set_title('Drawdown Over Time (USD)', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Date', fontsize=12)
        ax2.set_ylabel('Drawdown (USD)', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # Format x-axis
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        # บันทึกกราฟ
        plot_filename = f"{self.results_folder}/trading_performance_analysis.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 บันทึกกราฟที่: {plot_filename}")

    def calculate_risk_management(self, combined_analysis: Dict, account_balance: float = 1000) -> Dict:
        """
        คำนวณการจัดการความเสี่ยงและขนาดการเทรดที่เหมาะสม

        Args:
            combined_analysis: ผลการวิเคราะห์รวม
            account_balance: ยอดเงินในบัญชี (USD)

        Returns:
            Dict: ข้อมูลการจัดการความเสี่ยง
        """
        if not combined_analysis:
            return None

        max_dd_usd = combined_analysis['max_drawdown_usd']
        total_margin = combined_analysis['total_margin_required']

        # กำหนดระดับความเสี่ยง
        risk_levels = {
            'daily_risk_2pct': account_balance * 0.02,      # 2% ต่อวัน
            'weekly_risk_5pct': account_balance * 0.05,     # 5% ต่อสัปดาห์
            'total_risk_10pct': account_balance * 0.10      # 10% ของทั้งหมด
        }

        # คำนวณขนาดการเทรดที่เหมาะสม (lot size multiplier)
        safe_multipliers = {}

        if max_dd_usd > 0:
            # คำนวณตาม drawdown
            safe_multipliers['based_on_daily_risk'] = risk_levels['daily_risk_2pct'] / max_dd_usd
            safe_multipliers['based_on_weekly_risk'] = risk_levels['weekly_risk_5pct'] / max_dd_usd
            safe_multipliers['based_on_total_risk'] = risk_levels['total_risk_10pct'] / max_dd_usd

        # คำนวณตาม margin requirement
        if total_margin > 0:
            safe_multipliers['based_on_margin'] = account_balance / (total_margin * 2)  # ใช้ 50% ของเงินสำหรับ margin

        # เลือกค่าที่ปลอดภัยที่สุด
        if safe_multipliers:
            recommended_multiplier = min(safe_multipliers.values())
            recommended_lot_size = recommended_multiplier * 1.0  # base lot size = 1.0
        else:
            recommended_multiplier = 0.01
            recommended_lot_size = 0.01

        risk_analysis = {
            'account_balance': account_balance,
            'max_drawdown_at_1lot': max_dd_usd,
            'total_margin_required': total_margin,
            'risk_levels': risk_levels,
            'safe_multipliers': safe_multipliers,
            'recommended_multiplier': recommended_multiplier,
            'recommended_lot_size': recommended_lot_size,
            'max_risk_percentage': (max_dd_usd * recommended_multiplier / account_balance) * 100
        }

        return risk_analysis

    def create_risk_table(self, risk_analysis: Dict) -> pd.DataFrame:
        """สร้างตารางแสดงความเสี่ยงในระดับต่างๆ"""
        if not risk_analysis:
            return None

        # สร้างตารางสำหรับระดับความเสี่ยงต่างๆ
        risk_percentages = [0.1, 0.25, 0.5, 1.0, 2.0, 5.0, 10.0]  # เปอร์เซ็นต์ความเสี่ยง
        account_balance = risk_analysis['account_balance']
        max_dd_at_1lot = risk_analysis['max_drawdown_at_1lot']

        table_data = []

        for risk_pct in risk_percentages:
            risk_amount = account_balance * (risk_pct / 100)

            if max_dd_at_1lot > 0:
                max_lot_size = risk_amount / max_dd_at_1lot
                max_risk_actual = (max_dd_at_1lot * max_lot_size / account_balance) * 100
            else:
                max_lot_size = 0
                max_risk_actual = 0

            table_data.append({
                'Risk_Percentage': f"{risk_pct}%",
                'Risk_Amount_USD': f"${risk_amount:.2f}",
                'Max_Lot_Size': f"{max_lot_size:.4f}",
                'Actual_Risk_Pct': f"{max_risk_actual:.2f}%",
                'Status': 'Safe' if risk_pct <= 2.0 else 'Moderate' if risk_pct <= 5.0 else 'High Risk'
            })

        df = pd.DataFrame(table_data)
        return df

    def save_complete_analysis(self, combined_analysis: Dict, risk_analysis: Dict, risk_table: pd.DataFrame):
        """บันทึกการวิเคราะห์ทั้งหมด"""

        # บันทึกข้อมูลรวม JSON
        summary_filename = f"{self.results_folder}/complete_financial_analysis.json"

        summary_data = {
            'analysis_summary': {
                'total_trades': combined_analysis['total_trades'],
                'total_profit_usd': combined_analysis['total_profit_usd'],
                'max_drawdown_usd': combined_analysis['max_drawdown_usd'],
                'total_margin_required': combined_analysis['total_margin_required'],
                'symbols_analyzed': combined_analysis['symbols_analyzed']
            },
            'risk_management': risk_analysis,
            'analysis_date': combined_analysis['analysis_date']
        }

        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, indent=2, ensure_ascii=False, default=str)

        # บันทึกตารางความเสี่ยง CSV
        risk_table_filename = f"{self.results_folder}/risk_management_table.csv"
        risk_table.to_csv(risk_table_filename, index=False)

        # บันทึกรายงานข้อความ
        report_filename = f"{self.results_folder}/financial_analysis_report.txt"

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("FINANCIAL ANALYSIS REPORT\n")
            f.write("=" * 80 + "\n\n")

            f.write(f"Analysis Date: {combined_analysis['analysis_date']}\n")
            f.write(f"Account Balance: ${risk_analysis['account_balance']:,.2f}\n")
            f.write(f"Leverage: 1:{self.leverage}\n\n")

            f.write("TRADING PERFORMANCE SUMMARY:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Total Trades: {combined_analysis['total_trades']}\n")
            f.write(f"Total Profit (1.0 lot): ${combined_analysis['total_profit_usd']:,.2f}\n")
            f.write(f"Max Drawdown (1.0 lot): ${combined_analysis['max_drawdown_usd']:,.2f}\n")
            f.write(f"Total Margin Required: ${combined_analysis['total_margin_required']:,.2f}\n\n")

            f.write("RISK MANAGEMENT RECOMMENDATIONS:\n")
            f.write("-" * 40 + "\n")
            f.write(f"Recommended Lot Size: {risk_analysis['recommended_lot_size']:.4f}\n")
            f.write(f"Max Risk Percentage: {risk_analysis['max_risk_percentage']:.2f}%\n\n")

            f.write("SYMBOLS ANALYZED:\n")
            f.write("-" * 40 + "\n")
            for symbol in combined_analysis['symbols_analyzed']:
                f.write(f"- {symbol}\n")

            f.write("\n" + "=" * 80 + "\n")

        print(f"💾 บันทึกการวิเคราะห์สมบูรณ์:")
        print(f"   📄 Summary: {summary_filename}")
        print(f"   📊 Risk Table: {risk_table_filename}")
        print(f"   📝 Report: {report_filename}")

    def run_complete_analysis(self, account_balance: float = 1000):
        """รันการวิเคราะห์ทั้งหมด"""
        print("🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์")

        # รวมข้อมูลทั้งหมด
        combined_analysis = self.combine_all_analyses()
        if not combined_analysis:
            print("❌ ไม่มีข้อมูลสำหรับการวิเคราะห์")
            return

        print(f"📊 พบข้อมูลการเทรด: {combined_analysis['total_trades']} รายการ")

        # สร้างกราฟ
        self.create_performance_plots(combined_analysis)

        # คำนวณความเสี่ยง
        risk_analysis = self.calculate_risk_management(combined_analysis, account_balance)

        # สร้างตารางความเสี่ยง
        risk_table = self.create_risk_table(risk_analysis)

        # บันทึกผลการวิเคราะห์
        self.save_complete_analysis(combined_analysis, risk_analysis, risk_table)

        # แสดงผลสรุป
        print("\n" + "=" * 60)
        print("📈 FINANCIAL ANALYSIS SUMMARY")
        print("=" * 60)
        print(f"💰 Account Balance: ${account_balance:,.2f}")
        print(f"📊 Total Trades: {combined_analysis['total_trades']}")
        print(f"💵 Total Profit (1.0 lot): ${combined_analysis['total_profit_usd']:,.2f}")
        print(f"📉 Max Drawdown (1.0 lot): ${combined_analysis['max_drawdown_usd']:,.2f}")
        print(f"🎯 Recommended Lot Size: {risk_analysis['recommended_lot_size']:.4f}")
        print(f"⚠️ Max Risk: {risk_analysis['max_risk_percentage']:.2f}%")
        print("=" * 60)

        return {
            'combined_analysis': combined_analysis,
            'risk_analysis': risk_analysis,
            'risk_table': risk_table
        }
