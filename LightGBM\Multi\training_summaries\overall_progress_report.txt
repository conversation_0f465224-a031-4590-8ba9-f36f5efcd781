🌟 รายงานภาพรวมระบบการเทรน
================================================================================

📊 สถิติรวมทั้งระบบ:
   จำนวนโมเดลทั้งหมด: 1
   คะแนนเฉลี่ย: 79.09/100
   Win Rate เฉลี่ย (Test): 62.37%
   Expectancy เฉลี่ย (Test): 4.83

🏆 อันดับโมเดลที่ดีที่สุด (Top 5):
   1. GOLD M060: Score 79.1, Test W% 62.4%, Test Exp 4.83

🏗️ เปรียบเทียบตาม Architecture:
   multi_model: 1 โมเดล, Score 79.1, W% 62.4%, Exp 4.83

💰 เปรียบเทียบตาม Symbol:
   GOLD: 1 timeframes, Score 79.1, W% 62.4%

📈 แนวโน้มการพัฒนาระบบ:
   📉 แย่ลงเฉลี่ย 4.7 คะแนน
   📊 โมเดลที่ดีขึ้น: 0/1 (0.0%)

💡 คำแนะนำสำหรับระบบ:
   ✅ ระบบมีประสิทธิภาพดี - พร้อมใช้งานจริง

📅 อัปเดตล่าสุด: 2025-09-26 09:52:31
================================================================================