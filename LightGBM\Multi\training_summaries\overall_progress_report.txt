🌟 รายงานภาพรวมระบบการเทรน
================================================================================

📊 สถิติรวมทั้งระบบ:
   จำนวนโมเดลทั้งหมด: 1
   คะแนนเฉลี่ย: 79.84/100
   Win Rate เฉลี่ย (Test): 63.92%
   Expectancy เฉลี่ย (Test): 5.46

🏆 อันดับโมเดลที่ดีที่สุด (Top 5):
   1. GOLD M060: Score 79.8, Test W% 63.9%, Test Exp 5.46

🏗️ เปรียบเทียบตาม Architecture:
   multi_model: 1 โมเดล, Score 79.8, W% 63.9%, Exp 5.46

💰 เปรียบเทียบตาม Symbol:
   GOLD: 1 timeframes, Score 79.8, W% 63.9%

📈 แนวโน้มการพัฒนาระบบ:
   📉 แย่ลงเฉลี่ย 3.9 คะแนน
   📊 โมเดลที่ดีขึ้น: 0/1 (0.0%)

💡 คำแนะนำสำหรับระบบ:
   ✅ ระบบมีประสิทธิภาพดี - พร้อมใช้งานจริง

📅 อัปเดตล่าสุด: 2025-09-26 19:15:00
================================================================================