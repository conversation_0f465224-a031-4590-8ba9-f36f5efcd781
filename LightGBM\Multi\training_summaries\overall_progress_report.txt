🌟 รายงานภาพรวมระบบการเทรน
================================================================================

📊 สถิติรวมทั้งระบบ:
   จำนวนโมเดลทั้งหมด: 1
   คะแนนเฉลี่ย: 77.09/100
   Win Rate เฉลี่ย (Test): 65.66%
   Expectancy เฉลี่ย (Test): 5.52

🏆 อันดับโมเดลที่ดีที่สุด (Top 5):
   1. GOLD M060: Score 77.1, Test W% 65.7%, Test Exp 5.52

🏗️ เปรียบเทียบตาม Architecture:
   multi_model: 1 โมเดล, Score 77.1, W% 65.7%, Exp 5.52

💰 เปรียบเทียบตาม Symbol:
   GOLD: 1 timeframes, Score 77.1, W% 65.7%

📈 แนวโน้มการพัฒนาระบบ:
   📉 แย่ลงเฉลี่ย 7.0 คะแนน
   📊 โมเดลที่ดีขึ้น: 0/1 (0.0%)

💡 คำแนะนำสำหรับระบบ:
   ✅ ระบบมีประสิทธิภาพดี - พร้อมใช้งานจริง

📅 อัปเดตล่าสุด: 2025-09-26 21:47:29
================================================================================