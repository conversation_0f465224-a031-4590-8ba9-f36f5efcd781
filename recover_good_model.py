
# ==============================================
# Model Recovery Script
# ==============================================

import os
import shutil
from datetime import datetime

def recover_good_model():
    """กู้คืนโมเดลที่ดีจากการเทรนครั้งที่ 2"""
    
    print("🔄 เริ่มกู้คืนโมเดลที่ดี...")
    
    # สำรองโมเดลปัจจุบันก่อน
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_before_recovery_{timestamp}"
    
    if os.path.exists("LightGBM/Multi"):
        shutil.copytree("LightGBM/Multi", backup_dir)
        print(f"✅ สำรองโมเดลปัจจุบัน: {backup_dir}")
    
    # ค้นหาโมเดลที่ดีที่สุด
    best_model_dir = None
    
    # ค้นหาใน backup folders
    for item in os.listdir("."):
        if os.path.isdir(item) and "backup" in item.lower():
            # ตรวจสอบว่ามีโมเดลใน backup นี้หรือไม่
            models_path = os.path.join(item, "models")
            if os.path.exists(models_path):
                print(f"📁 พบโมเดลใน: {item}")
                best_model_dir = item
                break
    
    if best_model_dir:
        # กู้คืนโมเดล
        source_models = os.path.join(best_model_dir, "models")
        target_models = "LightGBM/Multi/models"
        
        if os.path.exists(target_models):
            shutil.rmtree(target_models)
        
        shutil.copytree(source_models, target_models)
        print(f"✅ กู้คืนโมเดล: {source_models} → {target_models}")
        
        # กู้คืน thresholds
        source_thresholds = os.path.join(best_model_dir, "thresholds")
        target_thresholds = "LightGBM/Multi/thresholds"
        
        if os.path.exists(source_thresholds):
            if os.path.exists(target_thresholds):
                shutil.rmtree(target_thresholds)
            shutil.copytree(source_thresholds, target_thresholds)
            print(f"✅ กู้คืน thresholds: {source_thresholds} → {target_thresholds}")
        
        print("🎉 กู้คืนโมเดลเสร็จสิ้น!")
        print("💡 ตอนนี้ระบบใช้โมเดลที่ดีจากการเทรนครั้งที่ 2")
        
    else:
        print("❌ ไม่พบโมเดลที่ดีในระบบ backup")
        print("💡 กรุณาตรวจสอบโฟลเดอร์ backup ด้วยตนเอง")

if __name__ == "__main__":
    recover_good_model()
