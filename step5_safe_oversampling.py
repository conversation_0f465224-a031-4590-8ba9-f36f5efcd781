
# ==============================================
# Safe Data Balancing System
# ==============================================

from imblearn.over_sampling import SMOTE
from collections import Counter

def check_imbalance_severity(y):
    """ตรวจสอบความรุนแรงของ imbalance"""
    
    counter = Counter(y)
    minority_class = min(counter.values())
    majority_class = max(counter.values())
    
    imbalance_ratio = minority_class / majority_class
    
    print(f"📊 Class distribution: {dict(counter)}")
    print(f"📊 Imbalance ratio: {imbalance_ratio:.3f}")
    
    return imbalance_ratio

def safe_oversampling(X, y, min_imbalance_ratio=0.3):
    """Safe oversampling ที่ระมัดระวัง"""
    
    imbalance_ratio = check_imbalance_severity(y)
    
    # ถ้า imbalance ไม่รุนแรงมาก ไม่ต้อง oversample
    if imbalance_ratio > min_imbalance_ratio:
        print(f"✅ Imbalance ไม่รุนแรง ({imbalance_ratio:.3f}) - ไม่ต้อง oversample")
        return X, y
    
    print(f"⚠️ Imbalance รุนแรง ({imbalance_ratio:.3f}) - ใช้ SMOTE")
    
    # ใช้ SMOTE พร้อมควบคุม parameters
    smote = SMOTE(
        sampling_strategy='auto',  # สมดุลอัตโนมัติ
        k_neighbors=min(5, len(y[y == y.value_counts().idxmin()]) - 1),  # ปรับตามข้อมูล minority
        random_state=42
    )
    
    try:
        X_balanced, y_balanced = smote.fit_resample(X, y)
        
        print(f"📊 Original: {Counter(y)}")
        print(f"📊 Balanced: {Counter(y_balanced)}")
        
        return X_balanced, y_balanced
        
    except Exception as e:
        print(f"⚠️ SMOTE ล้มเหลว: {e}")
        print("✅ ใช้ข้อมูลเดิม")
        return X, y

def use_class_weights_instead(y):
    """ใช้ class weights แทน oversampling"""
    
    from sklearn.utils.class_weight import compute_class_weight
    
    classes = np.unique(y)
    class_weights = compute_class_weight(
        'balanced', 
        classes=classes, 
        y=y
    )
    
    class_weight_dict = dict(zip(classes, class_weights))
    
    print(f"📊 Class weights: {class_weight_dict}")
    
    return class_weight_dict
