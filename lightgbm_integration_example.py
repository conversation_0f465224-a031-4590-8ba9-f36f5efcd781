"""
🔧 LightGBM_10_3.py Integration Example
======================================

ตัวอย่างการเพิ่มโค้ดใน LightGBM_10_3.py เพื่อโหลดพารามิเตอร์อัตโนมัติ
"""

# ==============================================
# เพิ่มโค้ดนี้ที่ต้นไฟล์ LightGBM_10_3.py (หลัง imports)
# ==============================================

def load_optimized_parameters_for_current_symbol():
    """
    โหลดพารามิเตอร์ที่ปรับปรุงแล้วสำหรับ symbol และ timeframe ปัจจุบัน
    """
    try:
        # Import ระบบโหลดพารามิเตอร์
        from auto_parameter_loader import auto_load_parameters_for_training, apply_parameters_to_globals
        
        # ดึงค่า symbol และ timeframe จาก global variables
        current_symbol = globals().get('symbol', 'GOLD')
        current_timeframe = globals().get('timeframe', 30)
        
        print(f"\n🎯 กำลังโหลดพารามิเตอร์ที่ปรับปรุงแล้วสำหรับ {current_symbol} M{current_timeframe}...")
        
        # โหลดพารามิเตอร์
        optimized_params = auto_load_parameters_for_training(
            symbol=current_symbol, 
            timeframe=current_timeframe,
            show_details=True
        )
        
        # นำไปใช้กับ global variables
        changes_count = apply_parameters_to_globals(optimized_params, globals(), show_changes=True)
        
        if changes_count > 0:
            print(f"✅ อัปเดตพารามิเตอร์สำเร็จ: {changes_count} parameters")
        else:
            print("ℹ️ ไม่มีพารามิเตอร์ที่ต้องอัปเดต")
        
        return True
        
    except ImportError:
        print("⚠️ ไม่พบไฟล์ auto_parameter_loader.py - ใช้พารามิเตอร์ default")
        return False
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการโหลดพารามิเตอร์: {e}")
        return False

# ==============================================
# เพิ่มโค้ดนี้ในฟังก์ชัน main() ก่อนเริ่มการเทรน
# ==============================================

def main():
    """ฟังก์ชันหลักของ LightGBM_10_3.py"""
    
    # ... โค้ดเดิมของ main() ...
    
    # เพิ่มส่วนนี้ก่อนเริ่มการเทรน
    print("🔧 กำลังตรวจสอบและโหลดพารามิเตอร์ที่ปรับปรุงแล้ว...")
    
    # โหลดพารามิเตอร์ที่ปรับปรุงแล้ว
    parameter_loaded = load_optimized_parameters_for_current_symbol()
    
    if parameter_loaded:
        print("✅ ใช้พารามิเตอร์ที่ปรับปรุงแล้วจากการทดสอบ Multi-Asset")
    else:
        print("ℹ️ ใช้พารามิเตอร์ default ที่กำหนดไว้ในโค้ด")
    
    # แสดงพารามิเตอร์ปัจจุบันเพื่อการตรวจสอบ
    print(f"\n📋 พารามิเตอร์ปัจจุบันที่จะใช้ในการเทรน:")
    print(f"   Symbol: {globals().get('symbol', 'N/A')}")
    print(f"   Timeframe: M{globals().get('timeframe', 'N/A')}")
    print(f"   Volume Spike: {globals().get('input_volume_spike', 'N/A')}")
    print(f"   RSI Level In: {globals().get('input_rsi_level_in', 'N/A')}")
    print(f"   Stop Loss ATR: {globals().get('input_stop_loss_atr', 'N/A')}")
    print(f"   Take Profit: {globals().get('input_take_profit', 'N/A')}")
    print(f"   Initial nBar SL: {globals().get('input_initial_nbar_sl', 'N/A')}")
    
    # ถามผู้ใช้ว่าต้องการดำเนินการต่อหรือไม่
    if parameter_loaded:
        print(f"\n⚠️ กรุณาตรวจสอบพารามิเตอร์ข้างต้นก่อนเริ่มการเทรน")
        confirm = input("ต้องการดำเนินการต่อด้วยพารามิเตอร์นี้? (y/n): ").strip().lower()
        
        if confirm != 'y':
            print("❌ ยกเลิกการเทรน")
            return
        
        print("✅ เริ่มการเทรนด้วยพารามิเตอร์ที่ปรับปรุงแล้ว...")
    
    # ... ส่วนที่เหลือของโค้ด main() เดิม ...

# ==============================================
# ตัวอย่างการใช้งานแบบ Manual
# ==============================================

def manual_parameter_loading_example():
    """ตัวอย่างการโหลดพารามิเตอร์แบบ manual"""
    
    from auto_parameter_loader import auto_load_parameters_for_training, get_available_assets
    
    print("🔧 Manual Parameter Loading Example")
    print("=" * 40)
    
    # แสดงสินทรัพย์ที่มี
    print("📊 สินทรัพย์ที่มีผลการทดสอบ:")
    assets = get_available_assets()
    
    if not assets:
        print("❌ ไม่พบข้อมูลการทดสอบ")
        return
    
    # เลือกสินทรัพย์ที่ต้องการ
    print(f"\n🎯 ตัวอย่างการโหลดพารามิเตอร์:")
    
    # ตัวอย่าง 1: GOLD M30
    print(f"\n--- GOLD M30 ---")
    params_gold_m30 = auto_load_parameters_for_training("GOLD", "M30")
    
    # ตัวอย่าง 2: GOLD M60
    print(f"\n--- GOLD M60 ---")
    params_gold_m60 = auto_load_parameters_for_training("GOLD", 60)
    
    # ตัวอย่าง 3: สินทรัพย์ที่ดีที่สุด (ไม่ระบุ symbol/timeframe)
    print(f"\n--- Best Performing Asset ---")
    params_best = auto_load_parameters_for_training(None, None)
    
    return {
        'gold_m30': params_gold_m30,
        'gold_m60': params_gold_m60,
        'best': params_best
    }

# ==============================================
# ตัวอย่างการใช้งานใน Script อื่น
# ==============================================

def standalone_usage_example():
    """ตัวอย่างการใช้งานใน script อื่น"""
    
    from auto_parameter_loader import auto_load_parameters_for_training, apply_parameters_to_globals
    
    # กำหนดค่า symbol และ timeframe
    symbol = "GOLD"
    timeframe = 30
    
    print(f"🎯 โหลดพารามิเตอร์สำหรับ {symbol} M{timeframe}")
    
    # โหลดพารามิเตอร์
    params = auto_load_parameters_for_training(symbol, timeframe)
    
    # ใช้พารามิเตอร์
    input_volume_spike = params['input_volume_spike']
    input_rsi_level_in = params['input_rsi_level_in']
    input_stop_loss_atr = params['input_stop_loss_atr']
    input_take_profit = params['input_take_profit']
    
    print(f"\n✅ พารามิเตอร์ที่โหลดได้:")
    print(f"   Volume Spike: {input_volume_spike}")
    print(f"   RSI Level: {input_rsi_level_in}")
    print(f"   Stop Loss ATR: {input_stop_loss_atr}")
    print(f"   Take Profit: {input_take_profit}")
    
    return params

# ==============================================
# การทดสอบระบบ
# ==============================================

if __name__ == "__main__":
    print("🧪 Testing Parameter Loading System")
    print("=" * 50)
    
    # ทดสอบการโหลดพารามิเตอร์
    try:
        # ทดสอบ manual loading
        manual_results = manual_parameter_loading_example()
        
        print(f"\n📊 สรุปผลการทดสอบ:")
        for key, params in manual_results.items():
            print(f"   {key}: {len(params)} parameters loaded")
        
        # ทดสอบ standalone usage
        print(f"\n🔧 ทดสอบ Standalone Usage:")
        standalone_params = standalone_usage_example()
        
        print(f"\n✅ การทดสอบเสร็จสมบูรณ์!")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()

# ==============================================
# คำแนะนำการใช้งาน
# ==============================================

"""
📋 วิธีการใช้งาน:

1. วางไฟล์ auto_parameter_loader.py ในโฟลเดอร์เดียวกับ LightGBM_10_3.py

2. เพิ่มโค้ดต่อไปนี้ที่ต้นไฟล์ LightGBM_10_3.py:

   from auto_parameter_loader import auto_load_parameters_for_training, apply_parameters_to_globals

3. เพิ่มในฟังก์ชัน main() ก่อนเริ่มการเทรน:

   # โหลดพารามิเตอร์ที่ปรับปรุงแล้ว
   params = auto_load_parameters_for_training(symbol, timeframe)
   apply_parameters_to_globals(params, globals())

4. รันการเทรนตามปกติ - ระบบจะใช้พารามิเตอร์ที่ปรับปรุงแล้วโดยอัตโนมัติ

🎯 ผลลัพธ์ที่คาดหวัง:
- แสดงรายละเอียดการทดสอบของสินทรัพย์ที่เลือก
- แสดงพารามิเตอร์ที่จะใช้ในการเทรน
- อัปเดต global variables อัตโนมัติ
- ใช้พารามิเตอร์ที่ดีที่สุดจากการทดสอบ Multi-Asset
"""
