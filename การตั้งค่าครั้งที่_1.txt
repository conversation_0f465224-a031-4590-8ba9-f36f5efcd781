Market Scenarios ที่มี 6 แบบ scenario
'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell', 'trend_following', 'trend_following_Buy', 'trend_following_Sell'

ตำแหน่งไฟล์
LightGBM/Multi
├─ models
│   ├─ counter_trend
│   │   ├─ {timeframe}_{symbol}_features.pkl  >> ตัวอย่างชื่อไฟล์ M60_GOLD_features.pkl
│   │   ├─ {timeframe}_{symbol}_scaler.pkl  >> ตัวอย่างชื่อไฟล์ M60_GOLD_scaler.pkl
│   │   └─ {timeframe}_{symbol}_trained.pkl  >> ตัวอย่างชื่อไฟล์ M60_GOLD_trained.pkl
│   ├─ counter_trend_Buy
│   │   ├─ {timeframe}_{symbol}_features.pkl
│   │   ├─ {timeframe}_{symbol}_scaler.pkl
│   │   └─ {timeframe}_{symbol}_trained.pkl
│   ├─ counter_trend_Sell
│   │   ├─ {timeframe}_{symbol}_features.pkl
│   │   ├─ {timeframe}_{symbol}_scaler.pkl
│   │   └─ {timeframe}_{symbol}_trained.pkl
│   ├─ trend_following
│   │   ├─ {timeframe}_{symbol}_features.pkl
│   │   ├─ {timeframe}_{symbol}_scaler.pkl
│   │   └─ {timeframe}_{symbol}_trained.pkl
│   ├─ trend_following_Buy
│   │   ├─ {timeframe}_{symbol}_features.pkl
│   │   ├─ {timeframe}_{symbol}_scaler.pkl
│   │   └─ {timeframe}_{symbol}_trained.pkl
│   └─ trend_following_Sell
│         ├─ {timeframe}_{symbol}_features.pkl
│         ├─ {timeframe}_{symbol}_scaler.pkl
│         └─ {timeframe}_{symbol}_trained.pkl
└─ thresholds
      ├─ {timeframe}_{symbol}_counter_trend_Buy_optimal_nBars_SL.pkl
      ├─ {timeframe}_{symbol}_counter_trend_Buy_optimal_threshold.pkl
      ├─ {timeframe}_{symbol}_counter_trend_optimal_nBars_SL.pkl >> ตัวอย่างชื่อไฟล์ M60_GOLD_counter_trend_optimal_nBars_SL.pkl
      ├─ {timeframe}_{symbol}_counter_trend_optimal_threshold.pkl >> ตัวอย่างชื่อไฟล์ M60_GOLD_counter_trend_optimal_threshold.pkl
      ├─ {timeframe}_{symbol}_counter_trend_Sell_optimal_nBars_SL.pkl
      ├─ {timeframe}_{symbol}_counter_trend_Sell_optimal_threshold.pkl
      ├─ {timeframe}_{symbol}_nBars_analysis_summary.pkl
      ├─ {timeframe}_{symbol}_threshold_analysis_summary.pkl
      ├─ {timeframe}_{symbol}_time_filters.pkl
      ├─ {timeframe}_{symbol}_trend_following_Buy_optimal_nBars_SL.pkl
      ├─ {timeframe}_{symbol}_trend_following_Buy_optimal_threshold.pkl
      ├─ {timeframe}_{symbol}_trend_following_optimal_nBars_SL.pkl >> ตัวอย่างชื่อไฟล์ M60_GOLD_trend_following_optimal_nBars_SL.pkl
      ├─ {timeframe}_{symbol}_trend_following_optimal_threshold.pkl >> ตัวอย่างชื่อไฟล์ M60_GOLD_trend_following_optimal_threshold.pkl
      ├─ {timeframe}_{symbol}_trend_following_Sell_optimal_nBars_SL.pkl
      └─ {timeframe}_{symbol}_trend_following_Sell_optimal_threshold.pkl

ต้องการให้มีการปรับ การโหลดพารามิเตอร์ ให้คลอบคลุมมากขึ้น จากค่าเริ่มต้น fallback rules
input_initial_threshold = 0.30
input_initial_nbar_sl = 4
เป็นการเตรียมของ เพื่อเตรียมไว้เรียกใช้งาน

1. กรณี ไม่มี 'trend_following' โหลดไม่ได้ หรือไม่มีไฟล์ ให้มีการตั้งค่าเริ่มต้นไว้ เช่น ดึงค่า input_initial_threshold หรือ input_initial_nbar_sl
2. กรณี มี 'trend_following' และ
- กรณี ไม่มี 'trend_following_Buy' ให้ใช้ค่า 'trend_following'
- กรณี ไม่มี 'trend_following_Sell' ให้ใช้ค่า 'trend_following'
3. กรณี ไม่มี 'counter_trend' โหลดไม่ได้ หรือไม่มีไฟล์ ให้มีการตั้งค่าเริ่มต้นไว้ เช่น ดึงค่า input_initial_threshold หรือ input_initial_nbar_sl
4. กรณี มี 'counter_trend' และ
- กรณี ไม่มี 'counter_trend_Buy' ให้ใช้ค่า 'counter_trend'
- กรณี ไม่มี 'counter_trend_Sell' ให้ใช้ค่า 'counter_trend'

// code
            # โหลดพารามิเตอร์ทั้ง 2 ระบบ
            try:
                tf_threshold_data = load_scenario_threshold(cleaned_symbol, f"M{timeframe_int}", 'trend_following')
                tf_threshold = tf_threshold_data['best_threshold']
                ct_threshold_data = load_scenario_threshold(cleaned_symbol, f"M{timeframe_int}", 'counter_trend')
                ct_threshold = ct_threshold_data['best_threshold']

                tf_nbars = load_scenario_nbars(cleaned_symbol, f"M{timeframe_int}", 'trend_following')
                ct_nbars = load_scenario_nbars(cleaned_symbol, f"M{timeframe_int}", 'counter_trend')

                if tf_threshold is not None:
                    response_trend_following_threshold = tf_threshold
                if tf_nbars is not None:
                    response_trend_following_nbars = tf_nbars
                if ct_threshold is not None:
                    response_counter_trend_threshold = ct_threshold
                if ct_nbars is not None:
                    response_counter_trend_nbars = ct_nbars

            except Exception as e:
                print(f"⚠️ Error loading dual system parameters: {e}")