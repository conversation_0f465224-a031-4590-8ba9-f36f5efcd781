"""
🧪 Test Protection System Fix
=============================

ทดสอบการแก้ไขปัญหา KeyError ในระบบป้องกัน
"""

import pandas as pd
import numpy as np
from step2_data_leakage_prevention import create_safe_features, validate_no_data_leakage

def create_test_dataframe():
    """สร้าง DataFrame ทดสอบ"""
    
    print("📊 สร้าง test DataFrame...")
    
    # สร้างข้อมูลทดสอบ
    dates = pd.date_range('2024-01-01', periods=100, freq='H')
    
    df = pd.DataFrame({
        'DateTime': dates,
        'Open': np.random.uniform(2000, 2100, 100),
        'High': np.random.uniform(2050, 2150, 100),
        'Low': np.random.uniform(1950, 2050, 100),
        'Close': np.random.uniform(2000, 2100, 100),
        'Volume': np.random.uniform(1000, 5000, 100)
    })
    
    # เพิ่ม technical indicators บางตัว
    df['EMA50'] = df['Close'].ewm(span=50).mean()
    df['EMA200'] = df['Close'].ewm(span=200).mean()
    
    # เพิ่ม RSI (ถ้ามี)
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI14'] = 100 - (100 / (1 + rs))
    
    print(f"✅ สร้าง DataFrame: {len(df)} rows, {len(df.columns)} columns")
    print(f"📊 Columns: {list(df.columns)}")
    
    return df

def test_create_safe_features():
    """ทดสอบการสร้าง safe features"""
    
    print("\n🧪 ทดสอบ create_safe_features")
    print("="*50)
    
    # สร้าง test data
    df = create_test_dataframe()
    
    try:
        # ทดสอบการสร้าง safe features
        df_safe = create_safe_features(df)
        
        print(f"✅ สำเร็จ! DataFrame หลัง: {len(df_safe)} rows, {len(df_safe.columns)} columns")
        
        # แสดง safe features ที่สร้างได้
        safe_features = [col for col in df_safe.columns if '_safe' in col]
        print(f"📊 Safe features ที่สร้าง: {len(safe_features)}")
        for feature in safe_features:
            print(f"   ✅ {feature}")
        
        return True, df_safe
        
    except Exception as e:
        print(f"❌ ล้มเหลว: {e}")
        return False, None

def test_validate_no_data_leakage():
    """ทดสอบการตรวจสอบ data leakage"""
    
    print("\n🧪 ทดสอบ validate_no_data_leakage")
    print("="*50)
    
    # สร้าง test data
    df = create_test_dataframe()
    
    # แบ่งข้อมูลทดสอบ
    train_size = int(len(df) * 0.6)
    val_size = int(len(df) * 0.2)
    
    X_train = df.iloc[:train_size].drop(['DateTime'], axis=1)
    X_val = df.iloc[train_size:train_size+val_size].drop(['DateTime'], axis=1)
    X_test = df.iloc[train_size+val_size:].drop(['DateTime'], axis=1)
    
    print(f"📊 Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}")
    
    try:
        # ทดสอบการตรวจสอบ data leakage
        result = validate_no_data_leakage(X_train, X_val, X_test)
        
        print(f"✅ สำเร็จ! ผลการตรวจสอบ: {result}")
        return True
        
    except Exception as e:
        print(f"❌ ล้มเหลว: {e}")
        return False

def test_missing_columns():
    """ทดสอบกับข้อมูลที่ขาดคอลัมน์"""
    
    print("\n🧪 ทดสอบกับข้อมูลที่ขาดคอลัมน์")
    print("="*50)
    
    # สร้าง DataFrame ที่ขาดคอลัมน์
    df = pd.DataFrame({
        'DateTime': pd.date_range('2024-01-01', periods=50, freq='H'),
        'Open': np.random.uniform(2000, 2100, 50),
        'Close': np.random.uniform(2000, 2100, 50),
        'Volume': np.random.uniform(1000, 5000, 50)
        # ไม่มี RSI14, MACD, ATR, EMA
    })
    
    print(f"📊 DataFrame ที่ขาดคอลัมน์: {list(df.columns)}")
    
    try:
        # ทดสอบการสร้าง safe features
        df_safe = create_safe_features(df)
        
        print(f"✅ สำเร็จ! จัดการข้อมูลที่ขาดคอลัมน์ได้")
        
        # แสดง safe features ที่สร้างได้
        safe_features = [col for col in df_safe.columns if '_safe' in col]
        print(f"📊 Safe features ที่สร้างได้: {len(safe_features)}")
        for feature in safe_features:
            print(f"   ✅ {feature}")
        
        return True
        
    except Exception as e:
        print(f"❌ ล้มเหลว: {e}")
        return False

def run_all_tests():
    """รันการทดสอบทั้งหมด"""
    
    print("🚀 เริ่มทดสอบระบบป้องกัน Data Leakage")
    print("="*60)
    
    results = []
    
    # ทดสอบ 1: create_safe_features
    success1, df_safe = test_create_safe_features()
    results.append(("create_safe_features", success1))
    
    # ทดสอบ 2: validate_no_data_leakage
    success2 = test_validate_no_data_leakage()
    results.append(("validate_no_data_leakage", success2))
    
    # ทดสอบ 3: missing columns
    success3 = test_missing_columns()
    results.append(("missing_columns_handling", success3))
    
    # สรุปผล
    print("\n📊 สรุปผลการทดสอบ")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ ผ่าน" if success else "❌ ล้มเหลว"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    total = len(results)
    score = passed / total * 100
    
    print(f"\n🎯 คะแนนรวม: {passed}/{total} ({score:.1f}%)")
    
    if score >= 80:
        print("🟢 ระบบทำงานได้ดี!")
        print("💡 สามารถใช้งานได้แล้ว")
    elif score >= 60:
        print("🟡 ระบบทำงานได้บางส่วน")
        print("💡 ควรตรวจสอบปัญหาที่เหลือ")
    else:
        print("🔴 ระบบมีปัญหา")
        print("💡 ต้องแก้ไขก่อนใช้งาน")
    
    return score

if __name__ == "__main__":
    score = run_all_tests()
    
    print("\n" + "="*60)
    print("🎉 การทดสอบเสร็จสิ้น!")
    print(f"📊 คะแนนรวม: {score:.1f}%")
    
    if score >= 80:
        print("\n💡 แนะนำ: ลองรัน LightGBM_10_4.py ได้แล้ว")
    else:
        print("\n💡 แนะนำ: ตรวจสอบและแก้ไขปัญหาก่อน")
