#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ระบบ Time Filter ที่ปรับปรุงใหม่ - มีทางเลือกมากขึ้น
"""

import pandas as pd
import numpy as np
from datetime import datetime
import json

class ImprovedTimeFilterSystem:
    def __init__(self):
        self.min_trades_per_period = 3  # ลดจากเดิม
        self.min_win_rate = 45.0        # ลดจาก 50%
        self.min_expectancy = -0.1      # ยอมให้ติดลบได้บ้าง
        self.min_profit_factor = 0.8    # ลดจากเดิม
        
    def analyze_time_patterns_flexible(self, trade_df, symbol, timeframe):
        """วิเคราะห์ Time Patterns แบบยืดหยุ่น"""
        if trade_df.empty:
            return self._create_default_filters()
        
        # แปลง Entry Time เป็น datetime
        trade_df = trade_df.copy()
        trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time'])
        trade_df['DayOfWeek'] = trade_df['Entry_DateTime'].dt.dayofweek
        trade_df['Hour'] = trade_df['Entry_DateTime'].dt.hour
        
        # วิเคราะห์หลายระดับ
        results = {
            'strict': self._analyze_strict_criteria(trade_df),
            'moderate': self._analyze_moderate_criteria(trade_df),
            'relaxed': self._analyze_relaxed_criteria(trade_df),
            'volume_based': self._analyze_volume_based(trade_df),
            'profit_based': self._analyze_profit_based(trade_df)
        }
        
        # เลือกผลลัพธ์ที่ดีที่สุด
        best_filter = self._select_best_filter(results)
        
        return {
            'selected_filter': best_filter,
            'all_options': results,
            'analysis_summary': self._create_analysis_summary(results)
        }
    
    def _analyze_strict_criteria(self, trade_df):
        """เกณฑ์เข้มงวด (เดิม)"""
        return self._analyze_with_criteria(
            trade_df, 
            min_trades=5, 
            min_win_rate=55.0, 
            min_expectancy=0.05,
            name="Strict"
        )
    
    def _analyze_moderate_criteria(self, trade_df):
        """เกณฑ์ปานกลาง"""
        return self._analyze_with_criteria(
            trade_df, 
            min_trades=3, 
            min_win_rate=50.0, 
            min_expectancy=0.0,
            name="Moderate"
        )
    
    def _analyze_relaxed_criteria(self, trade_df):
        """เกณฑ์ผ่อนปรน"""
        return self._analyze_with_criteria(
            trade_df, 
            min_trades=2, 
            min_win_rate=45.0, 
            min_expectancy=-0.05,
            name="Relaxed"
        )
    
    def _analyze_volume_based(self, trade_df):
        """เลือกตามปริมาณการเทรด"""
        day_stats = self._calculate_day_stats(trade_df)
        hour_stats = self._calculate_hour_stats(trade_df)
        
        # เลือกวันที่มีการเทรดมากที่สุด (top 60%)
        day_counts = day_stats.groupby('DayOfWeek').size().sort_values(ascending=False)
        top_days = day_counts.head(int(len(day_counts) * 0.6)).index.tolist()
        
        # เลือกชั่วโมงที่มีการเทรดมากที่สุด (top 50%)
        hour_counts = hour_stats.groupby('Hour').size().sort_values(ascending=False)
        top_hours = hour_counts.head(int(len(hour_counts) * 0.5)).index.tolist()
        
        return {
            'name': 'Volume-Based',
            'days': sorted(top_days),
            'hours': sorted(top_hours),
            'criteria': 'Top 60% days by volume, Top 50% hours by volume',
            'stats': {
                'total_periods': len(day_counts) + len(hour_counts),
                'selected_days': len(top_days),
                'selected_hours': len(top_hours)
            }
        }
    
    def _analyze_profit_based(self, trade_df):
        """เลือกตามผลกำไร"""
        day_stats = self._calculate_day_stats(trade_df)
        hour_stats = self._calculate_hour_stats(trade_df)
        
        # เลือกวันที่มี expectancy > -0.1
        good_days = day_stats[day_stats['Expectancy'] > -0.1]['DayOfWeek'].unique()
        
        # เลือกชั่วโมงที่มี expectancy > -0.1
        good_hours = hour_stats[hour_stats['Expectancy'] > -0.1]['Hour'].unique()
        
        # ถ้าไม่มีเลย ให้เลือกที่ดีที่สุด
        if len(good_days) == 0:
            good_days = day_stats.nlargest(3, 'Expectancy')['DayOfWeek'].unique()
        
        if len(good_hours) == 0:
            good_hours = hour_stats.nlargest(8, 'Expectancy')['Hour'].unique()
        
        return {
            'name': 'Profit-Based',
            'days': sorted(good_days.tolist()),
            'hours': sorted(good_hours.tolist()),
            'criteria': 'Expectancy > -0.1 or top performers',
            'stats': {
                'avg_day_expectancy': day_stats['Expectancy'].mean(),
                'avg_hour_expectancy': hour_stats['Expectancy'].mean(),
                'selected_days': len(good_days),
                'selected_hours': len(good_hours)
            }
        }
    
    def _analyze_with_criteria(self, trade_df, min_trades, min_win_rate, min_expectancy, name):
        """วิเคราะห์ด้วยเกณฑ์ที่กำหนด"""
        day_stats = self._calculate_day_stats(trade_df)
        hour_stats = self._calculate_hour_stats(trade_df)
        
        # กรองตามเกณฑ์
        good_days = day_stats[
            (day_stats['Count'] >= min_trades) &
            (day_stats['Win_Rate'] >= min_win_rate) &
            (day_stats['Expectancy'] >= min_expectancy)
        ]['DayOfWeek'].unique()
        
        good_hours = hour_stats[
            (hour_stats['Count'] >= min_trades) &
            (hour_stats['Win_Rate'] >= min_win_rate) &
            (hour_stats['Expectancy'] >= min_expectancy)
        ]['Hour'].unique()
        
        return {
            'name': name,
            'days': sorted(good_days.tolist()),
            'hours': sorted(good_hours.tolist()),
            'criteria': f'Min trades: {min_trades}, Min win rate: {min_win_rate}%, Min expectancy: {min_expectancy}',
            'stats': {
                'total_days_analyzed': len(day_stats['DayOfWeek'].unique()),
                'total_hours_analyzed': len(hour_stats['Hour'].unique()),
                'selected_days': len(good_days),
                'selected_hours': len(good_hours)
            }
        }
    
    def _calculate_day_stats(self, trade_df):
        """คำนวณสถิติรายวัน"""
        day_stats = []
        
        for day in range(7):  # 0=Monday, 6=Sunday
            day_trades = trade_df[trade_df['DayOfWeek'] == day]
            
            if len(day_trades) > 0:
                wins = len(day_trades[day_trades['Profit'] > 0])
                losses = len(day_trades[day_trades['Profit'] <= 0])
                total = len(day_trades)
                win_rate = (wins / total) * 100 if total > 0 else 0
                
                total_profit = day_trades['Profit'].sum()
                expectancy = total_profit / total if total > 0 else 0
                
                day_stats.append({
                    'DayOfWeek': day,
                    'Count': total,
                    'Wins': wins,
                    'Losses': losses,
                    'Win_Rate': win_rate,
                    'Total_Profit': total_profit,
                    'Expectancy': expectancy
                })
        
        return pd.DataFrame(day_stats)
    
    def _calculate_hour_stats(self, trade_df):
        """คำนวณสถิติรายชั่วโมง"""
        hour_stats = []
        
        for hour in range(24):
            hour_trades = trade_df[trade_df['Hour'] == hour]
            
            if len(hour_trades) > 0:
                wins = len(hour_trades[hour_trades['Profit'] > 0])
                losses = len(hour_trades[hour_trades['Profit'] <= 0])
                total = len(hour_trades)
                win_rate = (wins / total) * 100 if total > 0 else 0
                
                total_profit = hour_trades['Profit'].sum()
                expectancy = total_profit / total if total > 0 else 0
                
                hour_stats.append({
                    'Hour': hour,
                    'Count': total,
                    'Wins': wins,
                    'Losses': losses,
                    'Win_Rate': win_rate,
                    'Total_Profit': total_profit,
                    'Expectancy': expectancy
                })
        
        return pd.DataFrame(hour_stats)
    
    def _select_best_filter(self, results):
        """เลือก filter ที่ดีที่สุด"""
        # ให้คะแนนแต่ละ filter
        scores = {}
        
        for name, filter_data in results.items():
            if not filter_data or 'days' not in filter_data:
                scores[name] = 0
                continue
            
            # คะแนนจากจำนวนวันและชั่วโมงที่เลือก
            day_score = len(filter_data['days']) * 2  # วันสำคัญกว่า
            hour_score = len(filter_data['hours'])
            
            # โบนัสถ้ามีการเลือกที่สมเหตุสมผล (ไม่น้อยหรือมากเกินไป)
            if 2 <= len(filter_data['days']) <= 5:
                day_score += 5
            if 6 <= len(filter_data['hours']) <= 16:
                hour_score += 3
            
            scores[name] = day_score + hour_score
        
        # เลือกที่ได้คะแนนสูงสุด
        if not scores or max(scores.values()) == 0:
            return self._create_default_filters()
        
        best_name = max(scores, key=scores.get)
        return results[best_name]
    
    def _create_default_filters(self):
        """สร้าง filter เริ่มต้น"""
        return {
            'name': 'Default',
            'days': [0, 1, 2, 3, 4],  # Monday-Friday
            'hours': list(range(8, 18)),  # 8:00-17:00
            'criteria': 'Default business hours',
            'stats': {
                'selected_days': 5,
                'selected_hours': 10
            }
        }
    
    def _create_analysis_summary(self, results):
        """สร้างสรุปการวิเคราะห์"""
        summary = {
            'total_options': len(results),
            'options_with_results': sum(1 for r in results.values() if r and 'days' in r and len(r['days']) > 0),
            'best_option': None,
            'recommendations': []
        }
        
        # หาตัวเลือกที่ดีที่สุด
        best_filter = self._select_best_filter(results)
        summary['best_option'] = best_filter['name'] if best_filter else 'None'
        
        # สร้างคำแนะนำ
        if summary['options_with_results'] == 0:
            summary['recommendations'].append("ไม่มี time filter ที่ผ่านเกณฑ์ - ใช้ default filter")
        elif summary['options_with_results'] == 1:
            summary['recommendations'].append("มี time filter เพียง 1 ตัวเลือก")
        else:
            summary['recommendations'].append(f"มี {summary['options_with_results']} ตัวเลือก - เลือกใช้ {summary['best_option']}")
        
        return summary
    
    def format_time_filter_display(self, filter_data):
        """จัดรูปแบบการแสดงผล time filter"""
        if not filter_data or 'days' not in filter_data:
            return "ไม่มีวัน, ไม่มีชั่วโมง"
        
        days = filter_data['days']
        hours = filter_data['hours']
        
        # แปลงวัน
        day_names = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        if len(days) == 7:
            day_str = "Every Day"
        elif len(days) == 5 and days == [0, 1, 2, 3, 4]:
            day_str = "Weekdays"
        elif len(days) == 2 and days == [5, 6]:
            day_str = "Weekends"
        else:
            day_str = ", ".join([day_names[d] for d in days])
        
        # แปลงชั่วโมง
        if len(hours) == 24:
            hour_str = "All Day"
        elif len(hours) == 0:
            hour_str = "No Hours"
        else:
            # จัดกลุ่มชั่วโมงที่ติดกัน
            hour_ranges = self._group_consecutive_hours(hours)
            hour_str = ", ".join(hour_ranges)
        
        return f"{day_str}, {hour_str}"
    
    def _group_consecutive_hours(self, hours):
        """จัดกลุ่มชั่วโมงที่ติดกัน"""
        if not hours:
            return []
        
        hours = sorted(hours)
        ranges = []
        start = hours[0]
        end = hours[0]
        
        for i in range(1, len(hours)):
            if hours[i] == end + 1:
                end = hours[i]
            else:
                if start == end:
                    ranges.append(f"{start:02d}:00")
                else:
                    ranges.append(f"{start:02d}:00-{end:02d}:00")
                start = end = hours[i]
        
        # เพิ่มช่วงสุดท้าย
        if start == end:
            ranges.append(f"{start:02d}:00")
        else:
            ranges.append(f"{start:02d}:00-{end:02d}:00")
        
        return ranges

# ตัวอย่างการใช้งาน
def example_usage():
    # สร้างข้อมูลตัวอย่าง
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='H')
    
    trade_data = []
    for i in range(200):
        date = np.random.choice(dates)
        profit = np.random.normal(10, 50)  # กำไรเฉลี่ย 10, ส่วนเบี่ยงเบน 50
        
        trade_data.append({
            'Entry Time': date.strftime('%Y.%m.%d %H:%M:%S'),
            'Profit': profit
        })
    
    trade_df = pd.DataFrame(trade_data)
    
    # ทดสอบระบบใหม่
    filter_system = ImprovedTimeFilterSystem()
    result = filter_system.analyze_time_patterns_flexible(trade_df, "GOLD", 60)
    
    print("Selected Filter:", result['selected_filter']['name'])
    print("Days:", result['selected_filter']['days'])
    print("Hours:", result['selected_filter']['hours'])
    print("Display:", filter_system.format_time_filter_display(result['selected_filter']))
    print("\nAll Options:")
    for name, option in result['all_options'].items():
        if option and 'days' in option:
            print(f"  {name}: {len(option['days'])} days, {len(option['hours'])} hours")

if __name__ == "__main__":
    example_usage()
