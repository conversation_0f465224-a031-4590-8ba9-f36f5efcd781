#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Flexible Protection System
ทดสอบระบบป้องกันแบบยืดหยุ่น

การทดสอบ:
1. DISABLED - บันทึกโมเดลทุกตัว
2. PERMISSIVE - เกณฑ์หลวมมาก
3. FLEXIBLE - เกณฑ์ยืดหยุ่น
4. STRICT - เกณฑ์เข้มงวด
"""

import sys
import os

# เพิ่ม path ของโปรเจค
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_protection_modes():
    """ทดสอบโหมดการป้องกันต่างๆ"""
    
    print("="*80)
    print("🧪 ทดสอบระบบป้องกันแบบยืดหยุ่น")
    print("="*80)
    
    # Import functions from main script
    try:
        from LightGBM_10_4 import (
            get_model_quality_thresholds,
            validate_model_performance,
            should_save_model,
            check_model_exists
        )
        print("✅ Import functions สำเร็จ")
    except ImportError as e:
        print(f"❌ ไม่สามารถ import functions: {e}")
        return
    
    # ทดสอบข้อมูลโมเดลจำลอง
    test_cases = [
        {
            'name': 'โมเดลดีมาก',
            'metrics': {
                'accuracy': 0.75,
                'auc': 0.80,
                'f1': 0.65,
                'precision': 0.70,
                'recall': 0.60
            },
            'trading_stats': {
                'win_rate': 0.60,
                'expectancy': 50.0,
                'num_trades': 25
            }
        },
        {
            'name': 'โมเดลปานกลาง',
            'metrics': {
                'accuracy': 0.55,
                'auc': 0.58,
                'f1': 0.35,
                'precision': 0.40,
                'recall': 0.30
            },
            'trading_stats': {
                'win_rate': 0.45,
                'expectancy': 15.0,
                'num_trades': 12
            }
        },
        {
            'name': 'โมเดลแย่',
            'metrics': {
                'accuracy': 0.35,
                'auc': 0.40,
                'f1': 0.15,
                'precision': 0.20,
                'recall': 0.12
            },
            'trading_stats': {
                'win_rate': 0.25,
                'expectancy': -5.0,
                'num_trades': 8
            }
        }
    ]
    
    protection_modes = ["DISABLED", "PERMISSIVE", "FLEXIBLE", "STRICT"]
    
    for mode in protection_modes:
        print(f"\n{'='*60}")
        print(f"🛡️ ทดสอบโหมด: {mode}")
        print(f"{'='*60}")
        
        # ได้เกณฑ์สำหรับโหมดนี้
        thresholds = get_model_quality_thresholds(mode)
        print(f"📊 เกณฑ์สำหรับโหมด {mode}:")
        print(f"   - Accuracy ≥ {thresholds['min_accuracy']:.1%}")
        print(f"   - Win Rate ≥ {thresholds['min_win_rate']:.1%}")
        print(f"   - Expectancy ≥ {thresholds['min_expectancy']:.1f}")
        
        for test_case in test_cases:
            print(f"\n🧪 ทดสอบ: {test_case['name']}")
            
            # ทดสอบการตรวจสอบคุณภาพ
            # Note: ต้องปรับ PROTECTION_MODE ใน LightGBM_10_4.py ก่อนเรียกใช้
            print(f"   📋 Metrics: Acc={test_case['metrics']['accuracy']:.2%}, "
                  f"WR={test_case['trading_stats']['win_rate']:.2%}, "
                  f"Exp={test_case['trading_stats']['expectancy']:.1f}")
            
            # สำหรับการทดสอบจริง จะต้องเรียกใช้ validate_model_performance
            # แต่ต้องปรับ PROTECTION_MODE ใน main script ก่อน
            
        print(f"\n✅ ทดสอบโหมด {mode} เสร็จสิ้น")

def test_first_model_logic():
    """ทดสอบตรรกะโมเดลแรก"""
    
    print(f"\n{'='*60}")
    print(f"🆕 ทดสอบตรรกะโมเดลแรก")
    print(f"{'='*60}")
    
    # ทดสอบการตรวจสอบโมเดลที่มีอยู่
    test_symbols = ['GOLD', 'EURUSD', 'GBPUSD']
    test_timeframes = [60, 240]
    test_scenarios = ['trend_following', 'counter_trend']
    
    try:
        from LightGBM_10_4 import check_model_exists
        
        for symbol in test_symbols:
            for timeframe in test_timeframes:
                for scenario in test_scenarios:
                    result = check_model_exists(symbol, timeframe, scenario)
                    
                    status = "✅ มีอยู่" if result['exists'] else "❌ ไม่มี"
                    print(f"   {symbol} M{timeframe} {scenario}: {status}")
                    
                    if result['exists']:
                        print(f"      - Model: {'✅' if result['model_exists'] else '❌'}")
                        print(f"      - Metrics: {'✅' if result['metrics_exists'] else '❌'}")
    
    except ImportError as e:
        print(f"❌ ไม่สามารถ import check_model_exists: {e}")

def main():
    """ฟังก์ชันหลัก"""
    
    print("🚀 เริ่มการทดสอบระบบป้องกันแบบยืดหยุ่น")
    
    # ทดสอบโหมดการป้องกัน
    test_protection_modes()
    
    # ทดสอบตรรกะโมเดลแรก
    test_first_model_logic()
    
    print(f"\n{'='*80}")
    print("✅ การทดสอบเสร็จสิ้น")
    print("="*80)
    
    print(f"\n💡 คำแนะนำการใช้งาน:")
    print(f"1. แก้ไข PROTECTION_MODE ใน LightGBM_10_4.py")
    print(f"2. รันการเทรนโมเดลเพื่อทดสอบระบบป้องกัน")
    print(f"3. ตรวจสอบ log การบันทึกโมเดล")
    print(f"4. โมเดลแรกจะถูกบันทึกตามเกณฑ์ของแต่ละโหมด")
    print(f"5. โมเดลถัดไปจะถูกเปรียบเทียบกับโมเดลก่อนหน้า")

if __name__ == "__main__":
    main()
