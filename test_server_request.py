#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบส่งข้อมูลไปยัง WebRequest Server เพื่อดู debug messages
"""

import requests
import json
import time

def test_server_request():
    """ส่งข้อมูลทดสอบไปยัง server"""
    
    print("🔍 ทดสอบส่งข้อมูลไปยัง WebRequest Server")
    
    # ข้อมูลทดสอบ (จำลองข้อมูลจาก MT5)
    test_data = {
        "symbol": "USDJPY",
        "timeframe_str": "PERIOD_H1",
        "time": 1726488000.0,  # 2025-09-16 12:00:00
        "open": 146.887,
        "high": 147.001,
        "low": 146.850,
        "close": 146.991,
        "tick_volume": 5231,
        "latest_indicators": {
            "EMA50": 147.33,
            "EMA200": 147.50,
            "RSI14": 36.79,
            "MACD": -0.1305,
            "MACD_signal": -0.0944,
            "MACD_histogram": -0.0361,
            "PullBack_Up": 0.7791,
            "PullBack_Down": 0.2209,
            "Ratio_Buy": 3.5279,
            "Ratio_Sell": 1.5025
        },
        "bars": [
            # Previous bars data (simplified)
            {
                "time": 1726484400.0,
                "open": 146.850,
                "high": 146.920,
                "low": 146.800,
                "close": 146.887,
                "tick_volume": 4500
            }
        ]
    }
    
    server_url = "http://127.0.0.1:5000/data"
    
    try:
        print(f"📤 ส่งข้อมูลไปยัง {server_url}")
        print(f"📊 Symbol: {test_data['symbol']}")
        print(f"📊 Timeframe: {test_data['timeframe_str']}")
        print(f"📊 Close: {test_data['close']}")
        print(f"📊 EMA200: {test_data['latest_indicators']['EMA200']}")
        print(f"📊 Expected scenario: counter_trend (Close < EMA200)")
        
        response = requests.post(
            server_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Server Response:")
            print(f"   Status: {result.get('status')}")
            print(f"   Signal: {result.get('signal')}")
            print(f"   Confidence: {result.get('confidence')}")
            print(f"   Message: {result.get('message')}")
            
            # แสดงข้อมูล confidence แยกตาม scenario
            if 'trend_following_buy_confidence' in result:
                print(f"\n📊 Individual Scenario Confidence:")
                print(f"   TF BUY: {result.get('trend_following_buy_confidence', 0.0):.4f}")
                print(f"   TF SELL: {result.get('trend_following_sell_confidence', 0.0):.4f}")
                print(f"   CT BUY: {result.get('counter_trend_buy_confidence', 0.0):.4f}")
                print(f"   CT SELL: {result.get('counter_trend_sell_confidence', 0.0):.4f}")
                
                total_conf = (result.get('trend_following_buy_confidence', 0.0) + 
                             result.get('trend_following_sell_confidence', 0.0) + 
                             result.get('counter_trend_buy_confidence', 0.0) + 
                             result.get('counter_trend_sell_confidence', 0.0))
                
                if total_conf > 0:
                    print(f"✅ Confidence values are working! (Total: {total_conf:.4f})")
                else:
                    print(f"⚠️ All confidence values are still 0.0")
            
        else:
            print(f"❌ Server Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ ไม่สามารถเชื่อมต่อกับ server ได้")
        print(f"   กรุณาตรวจสอบว่า server ทำงานอยู่หรือไม่")
        print(f"   คำสั่งเริ่ม server: python WebRequest_Server_03_Target.py")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")

if __name__ == "__main__":
    test_server_request()
