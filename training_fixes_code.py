
# =============================================================================
# 🔧 TRAINING FIXES FOR LightGBM_10_3.py
# =============================================================================
# Generated: 2025-09-25 10:11:58
#
# วิธีใช้:
# 1. เพิ่มโค้ดเหล่านี้เข้าใน LightGBM_10_3.py
# 2. เรียกใช้ฟังก์ชันในส่วนที่เหมาะสม
# 3. ทดสอบการทำงานก่อนใช้งานจริง
# =============================================================================


# -----------------------------------------------------------------------------
# Cross Validation - LightGBM_10_3.py
# -----------------------------------------------------------------------------

# แก้ไข Cross Validation
from sklearn.model_selection import TimeSeriesSplit

def improved_time_series_cv(X, y, n_splits=5):
    """Time Series CV ที่ป้องกัน Data Leakage"""
    
    # ใช้ TimeSeriesSplit
    tscv = TimeSeriesSplit(n_splits=n_splits, test_size=None)
    
    cv_scores = []
    for train_idx, val_idx in tscv.split(X):
        # แยกข้อมูลตามเวลา
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        # ตรวจสอบว่าไม่มี Data Leakage
        if X_train.index.max() >= X_val.index.min():
            print("⚠️ Warning: Potential data leakage detected!")
        
        # เทรนและทดสอบ
        model = lgb.LGBMClassifier(
            objective='binary',
            metric='auc',
            boosting_type='gbdt',
            num_leaves=31,
            learning_rate=0.05,
            feature_fraction=0.9,
            bagging_fraction=0.8,
            bagging_freq=5,
            verbose=-1,
            random_state=42,
            early_stopping_rounds=50  # เพิ่ม Early Stopping
        )
        
        model.fit(
            X_train, y_train,
            eval_set=[(X_val, y_val)],
            eval_metric='auc',
            verbose=False
        )
        
        y_pred = model.predict_proba(X_val)[:, 1]
        score = roc_auc_score(y_val, y_pred)
        cv_scores.append(score)
    
    return cv_scores



# -----------------------------------------------------------------------------
# Entry Conditions - LightGBM_10_3.py
# -----------------------------------------------------------------------------

def improved_entry_conditions(df):
    """ปรับปรุง Entry Conditions เพื่อเพิ่ม Win Rate"""
    
    # เงื่อนไขเข้มงวดขึ้น
    strong_trend = (
        (df['EMA_diff_50_200'] > df['ATR'] * 0.5) |  # Trend แรง
        (df['EMA_diff_50_200'] < -df['ATR'] * 0.5)
    )
    
    # Volume Confirmation
    volume_confirm = df['Volume_Spike'] > 1.2
    
    # RSI ไม่ Overbought/Oversold
    rsi_safe = (df['RSI14'] > 25) & (df['RSI14'] < 75)
    
    # Market Hours Filter
    market_hours = df['Hour'].isin([8, 9, 10, 11, 12, 13, 14, 15, 16])
    
    # รวมเงื่อนไข
    entry_filter = strong_trend & volume_confirm & rsi_safe & market_hours
    
    return entry_filter

def dynamic_stop_loss_take_profit(df, atr_multiplier=1.5):
    """Dynamic SL/TP based on volatility"""
    
    # SL/TP ปรับตาม ATR
    df['Dynamic_SL'] = df['ATR'] * atr_multiplier
    df['Dynamic_TP'] = df['ATR'] * (atr_multiplier * 2)  # RR 1:2
    
    # ปรับตาม Market Condition
    high_vol_condition = df['ATR'] > df['ATR'].rolling(20).mean() * 1.2
    df.loc[high_vol_condition, 'Dynamic_SL'] *= 1.3
    df.loc[high_vol_condition, 'Dynamic_TP'] *= 1.3
    
    return df



# -----------------------------------------------------------------------------
# Data Balancing - LightGBM_10_3.py
# -----------------------------------------------------------------------------

from imblearn.over_sampling import SMOTE
from collections import Counter

def balance_training_data(X, y, method='smote'):
    """Balance training data"""
    
    print(f"Original distribution: {Counter(y)}")
    
    if method == 'smote':
        # ใช้ SMOTE
        smote = SMOTE(random_state=42, k_neighbors=3)
        X_balanced, y_balanced = smote.fit_resample(X, y)
        
    elif method == 'class_weights':
        # ใช้ Class Weights
        from sklearn.utils.class_weight import compute_class_weight
        
        classes = np.unique(y)
        class_weights = compute_class_weight(
            'balanced', 
            classes=classes, 
            y=y
        )
        
        class_weight_dict = dict(zip(classes, class_weights))
        return X, y, class_weight_dict
    
    print(f"Balanced distribution: {Counter(y_balanced)}")
    return X_balanced, y_balanced, None

# ใช้ใน Model Training
def train_balanced_model(X_train, y_train, X_val, y_val):
    """Train model with balanced data"""
    
    # Balance data
    X_balanced, y_balanced, class_weights = balance_training_data(
        X_train, y_train, method='smote'
    )
    
    # Model parameters
    params = {
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'early_stopping_rounds': 50,
        'is_unbalance': True  # LightGBM auto-balance
    }
    
    # Add class weights if available
    if class_weights:
        params['class_weight'] = class_weights
    
    model = lgb.LGBMClassifier(**params)
    
    model.fit(
        X_balanced, y_balanced,
        eval_set=[(X_val, y_val)],
        eval_metric='auc',
        verbose=False
    )
    
    return model



# -----------------------------------------------------------------------------
# Model Ensemble - LightGBM_10_3.py
# -----------------------------------------------------------------------------

from sklearn.ensemble import VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier

def create_ensemble_model(X_train, y_train, X_val, y_val):
    """Create ensemble model for better performance"""
    
    # Base models
    lgb_model = lgb.LGBMClassifier(
        objective='binary',
        metric='auc',
        num_leaves=31,
        learning_rate=0.05,
        feature_fraction=0.9,
        random_state=42,
        verbose=-1
    )
    
    rf_model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1
    )
    
    lr_model = LogisticRegression(
        random_state=42,
        max_iter=1000
    )
    
    # Ensemble
    ensemble = VotingClassifier(
        estimators=[
            ('lgb', lgb_model),
            ('rf', rf_model),
            ('lr', lr_model)
        ],
        voting='soft'  # Use probabilities
    )
    
    # Train ensemble
    ensemble.fit(X_train, y_train)
    
    # Validate
    y_pred_proba = ensemble.predict_proba(X_val)[:, 1]
    auc_score = roc_auc_score(y_val, y_pred_proba)
    
    print(f"Ensemble AUC: {auc_score:.4f}")
    
    return ensemble

def advanced_feature_selection(X, y, n_features=50):
    """Advanced feature selection"""
    
    from sklearn.feature_selection import SelectKBest, f_classif
    from sklearn.feature_selection import RFE
    
    # Statistical selection
    selector_stats = SelectKBest(f_classif, k=n_features)
    X_selected_stats = selector_stats.fit_transform(X, y)
    
    # RFE with LightGBM
    lgb_model = lgb.LGBMClassifier(random_state=42, verbose=-1)
    selector_rfe = RFE(lgb_model, n_features_to_select=n_features)
    X_selected_rfe = selector_rfe.fit_transform(X, y)
    
    # Get selected features
    selected_features_stats = X.columns[selector_stats.get_support()]
    selected_features_rfe = X.columns[selector_rfe.get_support()]
    
    # Combine selections
    combined_features = list(set(selected_features_stats) | set(selected_features_rfe))
    
    print(f"Selected {len(combined_features)} features")
    
    return X[combined_features], combined_features


