ครั้งที่ 1 ช่วยแก้ไข และตรวจสอบตามคำแนะนำ

// ช่วยตรวจสอบขั้นตอนการเทรน มีส่วนไหนที่ไม่เหมาะสม หรือควรปรับแก้หรือไม่

เนื่องจากได้เพิ่มการเทรน Target_Buy และ Target_Sell เพื่อใช้เป็น

จากที่ตรวจสอบ ตามประเภทการทดสอบ 
Target_Multiclass มี 5 class 0 12275, 1 826, 2 801, 3 620, 4 106 
Target_Buy มี 2 class 0 13125, 1 105 
Target_Sell มี 2 class 0 13431, 1 122
* ช่วยหาแนวทางการรับมือกับกรณีที่ class ต่างกันมาก
* เนื่องจากมีการแยกเป็น trend_following และ counter_trend

    โครงสร้างไฟล์:
    LightGBM/Multi/results/models/
    ├─ counter_trend/
    │   ├─ 060_SYMBOL_trained.pkl
    │   ├─ 060_SYMBOL_features.pkl
    │   └─ 060_SYMBOL_scaler.pkl
    ├─ counter_trend_Buy/
    │   ├─ 060_SYMBOL_trained.pkl
    │   ├─ 060_SYMBOL_features.pkl
    │   └─ 060_SYMBOL_scaler.pkl
    ├─ counter_trend_Sell/
    │   ├─ 060_SYMBOL_trained.pkl
    │   ├─ 060_SYMBOL_features.pkl
    │   └─ 060_SYMBOL_scaler.pkl
    ├─ trend_following/
    │   ├─ 060_SYMBOL_trained.pkl
    │   ├─ 060_SYMBOL_features.pkl
    │   └─ 060_SYMBOL_scaler.pkl
    ├─ trend_following_Buy/
    │   ├─ 060_SYMBOL_trained.pkl
    │   ├─ 060_SYMBOL_features.pkl
    │   └─ 060_SYMBOL_scaler.pkl
    └─ trend_following_Sell/
        ├─ 060_SYMBOL_trained.pkl
        ├─ 060_SYMBOL_features.pkl
        └─ 060_SYMBOL_scaler.pkl

✅ จุดที่ควรตรวจสอบ/ปรับแก้

1. Imbalance ของ Target_Buy และ Target_Sell
+ Target_Buy: {0: 13125, 1: 105}
+ Target_Sell: {0: 13431, 1: 122}
+ class 1 น้อยมาก (<1%) → โมเดลมีแนวโน้ม bias ไปที่ class 0
+ ข้อเสนอ:
- ใช้ class_weight='balanced' ใน LightGBM
- หรือ oversampling (เช่น SMOTE) สำหรับ minority class
- หรือเปลี่ยน metric ให้เหมาะกับ imbalance เช่น f1, roc_auc แทน accuracy

2. จำนวนข้อมูลที่น้อยในบาง scenario
+ มี check ว่า <50 → skip training
+ แต่ใน multiclass (5 class) distribution คือ class 4 มีแค่ 106 จาก ~14000 → ถ้าสุ่ม train/test split อาจจะหายไปจากชุด test ได้
+ ข้อเสนอ:
- ใช้ StratifiedKFold cross-validation แทน single split
- หรือบังคับให้แต่ละ class ต้องมีอย่างน้อย 1 sample ในทุก split

3.Hyperparameter tuning flag
+ ปัจจุบันใช้ไฟล์ JSON *_tuning_flag.json เพื่อปิด tuning ครั้งถัดไป
+ ถ้า training ครั้งแรก fail → อาจเขียน flag ว่า "ปิด tuning" ทั้งที่ยังไม่ได้หาค่า
+ ข้อเสนอ:
- เขียน flag หลังจาก random_search.best_params_ ได้จริง ๆ เท่านั้น
- หรือเก็บ field "status": "success" / "failed"

4. Scaling
+ คุณใช้ StandardScaler → แต่ LightGBM ไม่จำเป็นต้อง scale (เป็น tree-based model)
+ การ scale ไม่ผิด แต่เป็น ขั้นตอนเกินจำเป็น
+ อาจตัดออกได้ (ทำให้ process เร็วขึ้น และผลลัพธ์ stable กว่า)

5. การคำนวณ Metrics หลัง train
+ ตอนประเมิน AUC มีการ fallback หลายชั้น
+ แนะนำเพิ่ม log ให้ชัดเจนว่า fallback ไปใช้ metric ไหน เพราะตอน backtest อาจเกิด confusion ว่า accuracy/f1/auc ไม่ตรงกัน

6. อย่าเขียน flag_file ว่า "ปิด tuning" จนกว่าจะหา best_params สำเร็จ
ย้ายการเขียน flag_file ให้อยู่หลัง random_search.fit(...) และตรวจสอบ random_search.best_params_ ก่อน:

random_search.fit(X_train, y_train)
if hasattr(random_search, "best_params_") and random_search.best_params_:
    best_params = random_search.best_params_
    best_score = getattr(random_search, "best_score_", None)
    # บันทึก param_file
    with open(param_file, "w") as f:
        json.dump({"best_params": best_params, "best_score": best_score, "tuning_date": pd.Timestamp.now().isoformat()}, f, indent=2)
    # เขียน flag ให้ปิด tuning ครั้งถัดไป
    with open(flag_file, "w") as f:
        json.dump({"do_hyperparameter_tuning": False, "status": "success"}, f)
else:
    print(f"⚠️ RandomizedSearchCV ไม่มี best_params_ หรือ tuning ล้มเหลว; จะไม่เขียน flag")

7. ใช้ StratifiedKFold ใน RandomizedSearchCV (ป้องกัน class หายจาก fold)
แก้การสร้าง random_search แบบนี้:
from sklearn.model_selection import StratifiedKFold
cv_folds = 3
cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
random_search = RandomizedSearchCV(
    lgb_model,
    param_distributions=param_dist,
    n_iter=n_iter,
    cv=cv,
    scoring=scoring_metric,
    random_state=42,
    n_jobs=-1,
    verbose=0
)
(ใช้ StratifiedKFold เหมาะทั้ง binary และ multiclass — แต่ถ้า class น้อยเกินไป อาจยังมีปัญหา ให้ใช้ oversampling ก่อน)

8. จัดการ class imbalance ด้วย Random oversampling แบบง่าย (ไม่ต้องติดตั้ง imblearn)
เรียกก่อนแบ่งหรือก่อน train (ใช้กับ X_train,y_train):

from sklearn.utils import resample
def oversample_minority(X, y, target_min=300):
    df = X.copy()
    df['_target_'] = y.values
    classes = df['_target_'].value_counts().to_dict()
    parts = [df[df['_target_']==cls] for cls in classes.keys()]
    for cls, cnt in classes.items():
        if cnt < target_min:
            need = target_min - cnt
            sample = df[df['_target_']==cls].sample(n=need, replace=True, random_state=42)
            df = pd.concat([df, sample], ignore_index=True)
    y_res = df['_target_']
    X_res = df.drop(columns=['_target_'])
    return X_res, y_res

# ตัวอย่างการใช้งาน:
X_train_res, y_train_res = oversample_minority(X_train, y_train, target_min=200)
# และใช้ X_train_res, y_train_res ในการ fit / RandomizedSearchCV

(ถ้าใช้ oversampling ให้ลด n_iter ใน RandomizedSearchCV หรือจำกัด param space เพื่อประหยัดเวลา)


9. ตรวจสอบก่อนใช้ metrics ที่อาจเป็น None (เช่น best_score, auc)
ตัวอย่างปรับตรงคำนวณ AUC:

best_score = getattr(random_search, "best_score_", None)
if best_score is not None:
    print(f"✅ Best CV score สำหรับ {scenario_name}: {safe_fmt(best_score,4)}")
else:
    print(f"⚠️ ไม่มี Best CV score สำหรับ {scenario_name}")

และก่อนคำนวณ roc_auc_score ให้เช็คว่า y_test มี 2+ classes และ y_pred_proba shape ถูกต้อง (มี logic นี้อยู่แล้ว — แค่ใช้ safe_fmt ใน print)

10. ลดโอกาสเกิดปัญหาเมื่อ dataset เล็ก — ปรับ n_iter ตามจำนวนข้อมูล
ถ้าจำนวนแถวใน X_train น้อย (เช่น < 1000) ลด n_iter:

if len(X_train) < 1000:
    n_iter = min(8, n_iter)

จะช่วยไม่ให้ RandomizedSearchCV ใช้เวลานานและเกิด overfitting โดยไม่จำเป็น

11. ถ้าทำตามขั้นตอนทั้งหมดแล้ว ให้เช็คสิ่งต่อไปนี้เมื่อรันใหม่
ไม่มี unsupported format string ใน log — ถ้ายังมี ให้ค้นหาทุก :.4f/:.3f แล้วเปลี่ยนเป็น safe_fmt()
ดูการแจกแจง class ใน y_train หลัง oversampling — ควรสมเหตุสมผล
RandomizedSearchCV ต้องแสดง Best parameters และ Best CV score (หรือข้อความเตือนว่าล้มเหลว)
ถ้า model ยัง bias ให้ลองเพิ่ม target_min หรือปรับ class_weight + oversample