"""
🔧 LightGBM Parameter Loader
===========================

เครื่องมือสำหรับโหลดพารามิเตอร์ที่ปรับปรุงแล้วไปใช้กับ LightGBM_10_3.py
"""

import json
import os
import glob
from datetime import datetime

def load_optimized_parameters(symbol=None, timeframe=None, show_details=True):
    """
    โหลดพารามิเตอร์ที่ปรับปรุงแล้ว

    Args:
        symbol: ชื่อสินทรัพย์ (เช่น 'GOLD', 'EURUSD')
        timeframe: ช่วงเวลา (เช่น 'M30', 'H1', 'M60')
        show_details: แสดงรายละเอียดการทดสอบ

    Returns:
        dict: พารามิเตอร์ที่ปรับปรุงแล้ว
    """

    # แปลง timeframe format
    if timeframe:
        timeframe = _normalize_timeframe(timeframe)

    # หาไฟล์ผลลัพธ์ล่าสุด
    multi_asset_files = glob.glob("multi_asset_results_*.json")
    phase2_files = glob.glob("phase2_result*.json")
    baseline_files = glob.glob("baseline_result*.json")

    params = None
    source_info = "default"
    asset_details = None

    # ลำดับความสำคัญ: Multi-Asset > Phase2 > Baseline
    if symbol and timeframe and multi_asset_files:
        # ใช้พารามิเตอร์เฉพาะสินทรัพย์
        latest_file = max(multi_asset_files, key=os.path.getmtime)
        params, asset_details = _get_asset_specific_params_with_details(latest_file, symbol, timeframe)
        if params:
            source_info = f"{symbol}_{timeframe} specific"

    if not params and multi_asset_files:
        # ใช้พารามิเตอร์ที่ดีที่สุดจาก multi-asset
        latest_file = max(multi_asset_files, key=os.path.getmtime)
        params, asset_details = _get_best_multi_asset_params_with_details(latest_file)
        if params:
            source_info = "multi-asset best"

    if not params and phase2_files:
        # ใช้พารามิเตอร์จาก Phase 2
        latest_file = max(phase2_files, key=os.path.getmtime)
        params = _get_phase_params(latest_file)
        if params:
            source_info = "phase2"

    if not params and baseline_files:
        # ใช้พารามิเตอร์ baseline
        latest_file = max(baseline_files, key=os.path.getmtime)
        params = _get_baseline_params(latest_file)
        if params:
            source_info = "baseline"

    if not params:
        # ใช้ค่า default
        params = {
            'input_volume_spike': 1.5,
            'input_rsi_level_in': 40,
            'input_stop_loss_atr': 1.25,
            'input_take_profit': 3.0,
            'input_initial_nbar_sl': 4
        }
        source_info = "default values"

    # แสดงผลลัพธ์
    if show_details:
        _display_parameter_details(params, source_info, asset_details, symbol, timeframe)
    else:
        print(f"✅ โหลดพารามิเตอร์สำเร็จ (Source: {source_info})")

    return params

def _normalize_timeframe(timeframe):
    """แปลง timeframe format ให้เป็นมาตรฐาน"""
    if isinstance(timeframe, str):
        timeframe = timeframe.upper()
        if timeframe in ['H1', '60']:
            return 'M60'
        elif timeframe in ['M30', '30']:
            return 'M30'
    elif isinstance(timeframe, int):
        if timeframe == 60:
            return 'M60'
        elif timeframe == 30:
            return 'M30'
    return timeframe

def _display_parameter_details(params, source_info, asset_details, symbol, timeframe):
    """แสดงรายละเอียดพารามิเตอร์และผลการทดสอบ"""
    print("=" * 70)
    print("🎯 OPTIMIZED PARAMETERS LOADED")
    print("=" * 70)

    if asset_details:
        print(f"🔸 {asset_details['symbol']}_{asset_details['timeframe']}")
        print(f"   Score: {asset_details['score']:.2f}")
        print(f"   Win Rate: {asset_details['win_rate']:.1f}%")
        print(f"   Total Profit: ${asset_details['total_profit']:,.0f}")
        print(f"   Total Trades: {asset_details['total_trades']}")
        print(f"   Expectancy: {asset_details['expectancy']:.2f}")
        print(f"   Max Drawdown: ${asset_details['max_drawdown']:,.0f}")
        print(f"   ")
        print(f"   Best Parameters:")
        print(f"     SL ATR: {params.get('input_stop_loss_atr', 'N/A')}")
        print(f"     TP Ratio: {params.get('input_take_profit', 'N/A')}")
        print(f"     RSI Level: {params.get('input_rsi_level_in', 'N/A')}")
        print(f"     Volume Spike: {params.get('input_volume_spike', 'N/A')}")
        if 'input_initial_nbar_sl' in params:
            print(f"     nBars SL: {params['input_initial_nbar_sl']}")
    else:
        print(f"📊 Source: {source_info}")
        print(f"   Parameters:")
        for key, value in params.items():
            print(f"     {key}: {value}")

    print("=" * 70)

def _get_asset_specific_params(file_path, symbol, timeframe):
    """ดึงพารามิเตอร์เฉพาะสินทรัพย์"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)

        for result in data['results']:
            if result['symbol'] == symbol and result['timeframe'] == timeframe:
                return result['best_result']['parameters']

        return None
    except:
        return None

def _get_asset_specific_params_with_details(file_path, symbol, timeframe):
    """ดึงพารามิเตอร์เฉพาะสินทรัพย์พร้อมรายละเอียด"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)

        for result in data['results']:
            if result['symbol'] == symbol and result['timeframe'] == timeframe:
                best_result = result['best_result']
                metrics = best_result['results']

                details = {
                    'symbol': result['symbol'],
                    'timeframe': result['timeframe'],
                    'score': best_result['performance_score'],
                    'win_rate': metrics.get('win_rate', 0),
                    'total_profit': metrics.get('total_profit', 0),
                    'total_trades': metrics.get('total_trades', 0),
                    'expectancy': metrics.get('expectancy', 0),
                    'max_drawdown': metrics.get('max_drawdown', 0)
                }

                return best_result['parameters'], details

        return None, None
    except Exception as e:
        print(f"⚠️ Error loading asset specific params: {e}")
        return None, None

def _get_best_multi_asset_params(file_path):
    """ดึงพารามิเตอร์ที่ดีที่สุดจาก multi-asset"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)

        # หาผลลัพธ์ที่ดีที่สุด
        best_result = max(data['results'], key=lambda x: x['best_result']['performance_score'])
        return best_result['best_result']['parameters']
    except:
        return None

def _get_best_multi_asset_params_with_details(file_path):
    """ดึงพารามิเตอร์ที่ดีที่สุดจาก multi-asset พร้อมรายละเอียด"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)

        # หาผลลัพธ์ที่ดีที่สุด
        best_result_data = max(data['results'], key=lambda x: x['best_result']['performance_score'])
        best_result = best_result_data['best_result']
        metrics = best_result['results']

        details = {
            'symbol': best_result_data['symbol'],
            'timeframe': best_result_data['timeframe'],
            'score': best_result['performance_score'],
            'win_rate': metrics.get('win_rate', 0),
            'total_profit': metrics.get('total_profit', 0),
            'total_trades': metrics.get('total_trades', 0),
            'expectancy': metrics.get('expectancy', 0),
            'max_drawdown': metrics.get('max_drawdown', 0)
        }

        return best_result['parameters'], details
    except Exception as e:
        print(f"⚠️ Error loading best multi-asset params: {e}")
        return None, None

def _get_phase_params(file_path):
    """ดึงพารามิเตอร์จาก phase"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        return data['best_parameters']
    except:
        return None

def _get_baseline_params(file_path):
    """ดึงพารามิเตอร์จาก baseline"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        return data['parameters']
    except:
        return None

def apply_parameters_to_globals(params, globals_dict):
    """
    นำพารามิเตอร์ไปใช้กับ global variables
    
    Args:
        params: พารามิเตอร์ที่ได้จาก load_optimized_parameters()
        globals_dict: globals() จาก script หลัก
    """
    
    # แมป parameter names กับ global variable names
    param_mapping = {
        'input_volume_spike': 'input_volume_spike',
        'input_rsi_level_in': 'input_rsi_level_in', 
        'input_stop_loss_atr': 'input_stop_loss_atr',
        'input_take_profit': 'input_take_profit',
        'input_initial_nbar_sl': 'input_initial_nbar_sl'
    }
    
    updated_params = []
    
    for param_key, global_key in param_mapping.items():
        if param_key in params:
            globals_dict[global_key] = params[param_key]
            updated_params.append(f"{global_key}: {params[param_key]}")
    
    if updated_params:
        print("🔄 อัปเดต global parameters:")
        for param in updated_params:
            print(f"   {param}")
    
    return len(updated_params)

def create_parameter_summary():
    """สร้างสรุปพารามิเตอร์ทั้งหมดที่มี"""
    
    print("📊 Parameter Summary Report")
    print("=" * 40)
    
    # หาไฟล์ทั้งหมด
    multi_files = glob.glob("multi_asset_results_*.json")
    phase_files = glob.glob("phase*_result*.json") 
    baseline_files = glob.glob("baseline_result*.json")
    
    summary_data = []
    
    # Multi-asset results
    for file in multi_files:
        try:
            with open(file, 'r') as f:
                data = json.load(f)
            
            modified = datetime.fromtimestamp(os.path.getmtime(file))
            
            summary_data.append({
                'type': 'Multi-Asset',
                'file': file,
                'date': modified,
                'avg_score': data['summary']['avg_score'],
                'best_score': data['summary']['best_score'],
                'assets_count': len(data['results'])
            })
        except:
            continue
    
    # Phase results
    for file in phase_files:
        try:
            with open(file, 'r') as f:
                data = json.load(f)
            
            modified = datetime.fromtimestamp(os.path.getmtime(file))
            
            summary_data.append({
                'type': f"Phase {data['phase']}",
                'file': file,
                'date': modified,
                'avg_score': data['best_score'],
                'best_score': data['best_score'],
                'assets_count': 1
            })
        except:
            continue
    
    # Baseline results
    for file in baseline_files:
        try:
            with open(file, 'r') as f:
                data = json.load(f)
            
            modified = datetime.fromtimestamp(os.path.getmtime(file))
            
            summary_data.append({
                'type': 'Baseline',
                'file': file,
                'date': modified,
                'avg_score': data['performance_score'],
                'best_score': data['performance_score'],
                'assets_count': 1
            })
        except:
            continue
    
    if summary_data:
        # เรียงตามวันที่
        summary_data.sort(key=lambda x: x['date'], reverse=True)
        
        print(f"{'Type':<12} {'Score':<8} {'Best':<8} {'Assets':<7} {'Date':<16} {'File':<30}")
        print("-" * 85)
        
        for item in summary_data:
            print(f"{item['type']:<12} {item['avg_score']:<8.2f} {item['best_score']:<8.2f} {item['assets_count']:<7} {item['date'].strftime('%Y-%m-%d %H:%M'):<16} {item['file']:<30}")
    
    else:
        print("❌ ไม่พบไฟล์ผลลัพธ์")

# ตัวอย่างการใช้งานใน LightGBM_10_3.py
def example_usage():
    """ตัวอย่างการใช้งาน"""
    
    print("📝 ตัวอย่างการใช้งานใน LightGBM_10_3.py:")
    print("-" * 40)
    
    example_code = '''
# เพิ่มที่ต้นไฟล์ LightGBM_10_3.py
from lightgbm_parameter_loader import load_optimized_parameters, apply_parameters_to_globals

# โหลดพารามิเตอร์ที่ปรับปรุงแล้ว
# สำหรับสินทรัพย์เฉพาะ:
optimized_params = load_optimized_parameters(symbol="GOLD", timeframe="H1")

# หรือใช้พารามิเตอร์ทั่วไป:
# optimized_params = load_optimized_parameters()

# นำไปใช้กับ global variables
apply_parameters_to_globals(optimized_params, globals())

# ตอนนี้ global variables จะถูกอัปเดตแล้ว
print(f"Volume Spike: {input_volume_spike}")
print(f"RSI Level: {input_rsi_level_in}")
print(f"Stop Loss ATR: {input_stop_loss_atr}")
print(f"Take Profit: {input_take_profit}")
'''
    
    print(example_code)

if __name__ == "__main__":
    print("🔧 LightGBM Parameter Loader")
    print("=" * 30)
    
    while True:
        print(f"\n🔧 เลือกการดำเนินการ:")
        print("1. 📊 แสดงสรุปพารามิเตอร์ทั้งหมด")
        print("2. 🎯 ทดสอบโหลดพารามิเตอร์")
        print("3. 📝 ดูตัวอย่างการใช้งาน")
        print("4. 🚪 ออก")
        
        choice = input("\nเลือก (1-4): ").strip()
        
        if choice == '1':
            create_parameter_summary()
        
        elif choice == '2':
            symbol = input("Symbol (เว้นว่างสำหรับทั่วไป): ").strip().upper()
            timeframe = input("Timeframe (เว้นว่างสำหรับทั่วไป): ").strip()
            
            if not symbol:
                symbol = None
            if not timeframe:
                timeframe = None
            
            params = load_optimized_parameters(symbol, timeframe)
            print(f"\n📊 พารามิเตอร์ที่โหลดได้:")
            for key, value in params.items():
                print(f"   {key}: {value}")
        
        elif choice == '3':
            example_usage()
        
        elif choice == '4':
            print("👋 ขอบคุณที่ใช้งาน!")
            break
        
        else:
            print("❌ กรุณาเลือก 1-4")
