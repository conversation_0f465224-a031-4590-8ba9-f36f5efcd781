"""
📊 Step 6: Results Viewer & Analysis
===================================

เครื่องมือสำหรับดูและวิเคราะห์ผลการทดสอบ Parameter Optimization
"""

import json
import os
import pandas as pd
from datetime import datetime
import glob

def list_available_results():
    """แสดงรายการไฟล์ผลลัพธ์ที่มี"""
    print("📁 ไฟล์ผลลัพธ์ที่มีอยู่:")
    print("-" * 40)
    
    # หาไฟล์ JSON ผลลัพธ์
    json_files = glob.glob("*_result*.json") + glob.glob("multi_asset_results_*.json")
    txt_files = glob.glob("multi_asset_optimization_results_*.txt")
    
    if json_files:
        print("📄 JSON Files:")
        for i, file in enumerate(json_files, 1):
            size = os.path.getsize(file)
            modified = datetime.fromtimestamp(os.path.getmtime(file))
            print(f"   {i}. {file} ({size} bytes, {modified.strftime('%Y-%m-%d %H:%M')})")
    
    if txt_files:
        print("\n📝 TXT Files:")
        for i, file in enumerate(txt_files, 1):
            size = os.path.getsize(file)
            modified = datetime.fromtimestamp(os.path.getmtime(file))
            print(f"   {i}. {file} ({size} bytes, {modified.strftime('%Y-%m-%d %H:%M')})")
    
    if not json_files and not txt_files:
        print("   ❌ ไม่พบไฟล์ผลลัพธ์")
        return []
    
    return json_files + txt_files

def view_txt_results(filename):
    """แสดงผลลัพธ์จากไฟล์ TXT"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"\n📝 เนื้อหาจากไฟล์: {filename}")
        print("=" * 60)
        print(content)
        
    except Exception as e:
        print(f"❌ ไม่สามารถอ่านไฟล์ {filename}: {e}")

def view_json_results(filename):
    """แสดงผลลัพธ์จากไฟล์ JSON"""
    try:
        with open(filename, 'r') as f:
            data = json.load(f)
        
        print(f"\n📄 ผลลัพธ์จากไฟล์: {filename}")
        print("=" * 60)
        
        # ตรวจสอบประเภทของไฟล์
        if 'summary' in data and 'results' in data:
            # Multi-asset results
            view_multi_asset_json(data)
        elif 'phase' in data:
            # Phase results
            view_phase_json(data)
        elif 'parameters' in data and 'results' in data:
            # Baseline results
            view_baseline_json(data)
        else:
            # Generic JSON
            print(json.dumps(data, indent=2))
            
    except Exception as e:
        print(f"❌ ไม่สามารถอ่านไฟล์ {filename}: {e}")

def view_multi_asset_json(data):
    """แสดงผลลัพธ์ Multi-Asset"""
    summary = data['summary']
    results = data['results']
    
    print("📊 MULTI-ASSET OPTIMIZATION SUMMARY")
    print("-" * 40)
    print(f"Average Score: {summary['avg_score']:.2f}")
    print(f"Best Score: {summary['best_score']:.2f} ({summary['best_asset']})")
    print(f"Worst Score: {summary['worst_score']:.2f} ({summary['worst_asset']})")
    print(f"Average Win Rate: {summary['avg_win_rate']:.1f}%")
    print(f"Assets with Win Rate > 50%: {summary['good_win_rate_count']}")
    
    print(f"\n🏆 TOP 5 PERFORMING ASSETS:")
    print("-" * 40)
    
    # เรียงลำดับตามคะแนน
    sorted_results = sorted(results, key=lambda x: x['best_result']['performance_score'], reverse=True)
    
    print(f"{'Rank':<4} {'Asset':<12} {'Score':<8} {'Win%':<8} {'Profit':<12} {'Trades':<8}")
    print("-" * 60)
    
    for i, result in enumerate(sorted_results[:5], 1):
        asset = f"{result['symbol']}_{result['timeframe']}"
        best = result['best_result']
        metrics = best['results']
        
        print(f"{i:<4} {asset:<12} {best['performance_score']:<8.2f} {metrics.get('win_rate', 0):<8.1f} ${metrics.get('total_profit', 0):<11.0f} {metrics.get('total_trades', 0):<8}")

def view_phase_json(data):
    """แสดงผลลัพธ์ Phase"""
    print(f"📊 PHASE {data['phase']} RESULTS - {data['focus']}")
    print("-" * 40)
    print(f"Best Score: {data['best_score']:.2f}")
    
    if 'best_win_rate' in data:
        print(f"Best Win Rate: {data['best_win_rate']:.1f}%")
    
    print(f"\nBest Parameters:")
    for key, value in data['best_parameters'].items():
        print(f"   {key}: {value}")

def view_baseline_json(data):
    """แสดงผลลัพธ์ Baseline"""
    print("📊 BASELINE RESULTS")
    print("-" * 20)
    print(f"Score: {data['performance_score']:.2f}")
    
    results = data['results']
    print(f"Win Rate: {results.get('win_rate', 0):.1f}%")
    print(f"Total Profit: ${results.get('total_profit', 0):.0f}")
    print(f"Total Trades: {results.get('total_trades', 0)}")
    print(f"Expectancy: {results.get('expectancy', 0):.2f}")
    
    print(f"\nParameters:")
    for key, value in data['parameters'].items():
        print(f"   {key}: {value}")

def create_comparison_report():
    """สร้างรายงานเปรียบเทียบผลลัพธ์ทั้งหมด"""
    print("\n📊 สร้างรายงานเปรียบเทียบ...")
    
    # หาไฟล์ผลลัพธ์ทั้งหมด
    json_files = glob.glob("*_result*.json") + glob.glob("multi_asset_results_*.json")
    
    if not json_files:
        print("❌ ไม่พบไฟล์ผลลัพธ์")
        return
    
    comparison_data = []
    
    for file in json_files:
        try:
            with open(file, 'r') as f:
                data = json.load(f)
            
            if 'summary' in data:  # Multi-asset
                comparison_data.append({
                    'file': file,
                    'type': 'Multi-Asset',
                    'score': data['summary']['avg_score'],
                    'best_score': data['summary']['best_score'],
                    'win_rate': data['summary']['avg_win_rate'],
                    'assets_count': len(data['results'])
                })
            elif 'phase' in data:  # Phase
                comparison_data.append({
                    'file': file,
                    'type': f"Phase {data['phase']}",
                    'score': data['best_score'],
                    'best_score': data['best_score'],
                    'win_rate': data.get('best_win_rate', 0),
                    'assets_count': 1
                })
            elif 'performance_score' in data:  # Baseline
                comparison_data.append({
                    'file': file,
                    'type': 'Baseline',
                    'score': data['performance_score'],
                    'best_score': data['performance_score'],
                    'win_rate': data['results'].get('win_rate', 0),
                    'assets_count': 1
                })
        except:
            continue
    
    if comparison_data:
        # สร้างรายงาน
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"comparison_report_{timestamp}.txt"
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("📊 PARAMETER OPTIMIZATION COMPARISON REPORT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"{'Type':<15} {'Score':<8} {'Best':<8} {'Win%':<8} {'Assets':<8} {'File':<30}\n")
            f.write("-" * 80 + "\n")
            
            for item in sorted(comparison_data, key=lambda x: x['score'], reverse=True):
                f.write(f"{item['type']:<15} {item['score']:<8.2f} {item['best_score']:<8.2f} {item['win_rate']:<8.1f} {item['assets_count']:<8} {item['file']:<30}\n")
        
        print(f"✅ สร้างรายงานเปรียบเทียบ: {report_filename}")
        return report_filename
    else:
        print("❌ ไม่พบข้อมูลที่สามารถเปรียบเทียบได้")
        return None

def main():
    print("📊 Parameter Optimization Results Viewer")
    print("=" * 50)
    
    while True:
        print(f"\n🔍 เลือกการดำเนินการ:")
        print("1. 📁 ดูรายการไฟล์ผลลัพธ์")
        print("2. 📝 อ่านไฟล์ TXT")
        print("3. 📄 อ่านไฟล์ JSON")
        print("4. 📊 สร้างรายงานเปรียบเทียบ")
        print("5. 🚪 ออก")
        
        choice = input("\nเลือก (1-5): ").strip()
        
        if choice == '1':
            list_available_results()
            
        elif choice == '2':
            files = list_available_results()
            txt_files = [f for f in files if f.endswith('.txt')]
            
            if txt_files:
                print(f"\nเลือกไฟล์ TXT:")
                for i, file in enumerate(txt_files, 1):
                    print(f"{i}. {file}")
                
                try:
                    file_choice = int(input("เลือกไฟล์ (หมายเลข): ")) - 1
                    if 0 <= file_choice < len(txt_files):
                        view_txt_results(txt_files[file_choice])
                    else:
                        print("❌ หมายเลขไม่ถูกต้อง")
                except ValueError:
                    print("❌ กรุณาใส่หมายเลข")
            else:
                print("❌ ไม่พบไฟล์ TXT")
                
        elif choice == '3':
            files = list_available_results()
            json_files = [f for f in files if f.endswith('.json')]
            
            if json_files:
                print(f"\nเลือกไฟล์ JSON:")
                for i, file in enumerate(json_files, 1):
                    print(f"{i}. {file}")
                
                try:
                    file_choice = int(input("เลือกไฟล์ (หมายเลข): ")) - 1
                    if 0 <= file_choice < len(json_files):
                        view_json_results(json_files[file_choice])
                    else:
                        print("❌ หมายเลขไม่ถูกต้อง")
                except ValueError:
                    print("❌ กรุณาใส่หมายเลข")
            else:
                print("❌ ไม่พบไฟล์ JSON")
                
        elif choice == '4':
            create_comparison_report()
            
        elif choice == '5':
            print("👋 ขอบคุณที่ใช้งาน!")
            break
            
        else:
            print("❌ กรุณาเลือก 1-5")

if __name__ == "__main__":
    main()
