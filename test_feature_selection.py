#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข feature selection
"""

import sys
import os
import pandas as pd
import pickle

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_must_have_features_path():
    """ทดสอบ path ของไฟล์ must_have_features"""
    print("🔍 ทดสอบ path ของไฟล์ must_have_features")

    # ทดสอบ path ใหม่หลังแก้ไข model_name
    from LightGBM_07_target import test_folder
    timeframe = 60

    # Path ที่โค้ดจะใช้หลังแก้ไข
    new_path = os.path.join(f'{test_folder}', 'feature_importance', f'M{timeframe}_must_have_features.pkl')
    print(f'✅ Path ที่โค้ดจะใช้: {new_path}')
    print(f'   มีไฟล์อยู่หรือไม่: {os.path.exists(new_path)}')

    if os.path.exists(new_path):
        try:
            with open(new_path, 'rb') as f:
                features = pickle.load(f)
            print(f'📋 Features ในไฟล์: {len(features)} features')
            print('🔍 Features แรก 10 ตัว:')
            for i, feat in enumerate(features[:10], 1):
                print(f'   {i}. {feat}')
        except Exception as e:
            print(f'❌ เกิดข้อผิดพลาดในการอ่านไฟล์: {e}')
    else:
        print('❌ ไฟล์ไม่พบ - ตรวจสอบ path อีกครั้ง')

def test_select_features_simple():
    """ทดสอบฟังก์ชัน select_features แบบง่าย"""
    print("\n🧪 ทดสอบฟังก์ชัน select_features")
    
    try:
        from LightGBM_07_target import select_features
        
        # สร้างข้อมูลทดสอบ
        data = {
            'Close': [100, 101, 102, 103, 104],
            'Volume': [1000, 1100, 1200, 1300, 1400],
            'RSI14': [50, 55, 60, 65, 70],
            'EMA50': [99, 100, 101, 102, 103],
            'Target': [0, 1, 0, 1, 0]
        }
        df = pd.DataFrame(data)
        
        print(f'📊 ข้อมูลทดสอบ: {df.shape}')
        
        # ทดสอบ select_features
        features = select_features(df, 'GOLD', 'M60')
        print(f'✅ ทดสอบสำเร็จ')
        print(f'📋 จำนวน features: {len(features)}')
        print(f'🔍 Features:')
        for i, feat in enumerate(features, 1):
            print(f'   {i}. {feat}')
            
    except Exception as e:
        print(f'❌ เกิดข้อผิดพลาด: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_must_have_features_path()
    test_select_features_simple()
