#+------------------------------------------------------------------+
#|                                     Python Server Endpoint.py    |
#|                      Receives Bar Data from MT5 via HTTP (Flask) |
#+------------------------------------------------------------------+
from flask import Flask, request, jsonify
import json
import time
import datetime
import MetaTrader5 as mt5
import pandas as pd
import threading
import pandas_ta as ta # Make sure pandas_ta is installed
from werkzeug.exceptions import BadRequest # นำเข้า BadRequest เพื่อจับ Error จาก Flask/Werkzeug
import os # Import os for path joining and existence checks
import joblib # Import joblib for loading models and scalers
import numpy as np # Import numpy for numerical operations
import traceback # Import traceback for detailed error logging
import pickle # Import pickle library (สำหรับโหลด features_list ถ้าบันทึกด้วย pickle)
import sys # Import sys
import pytz # Import pytz for timezone handling (recommended for timestamps)
import logging
from logging.handlers import RotatingFileHandler

import requests

Telegram_Open = True

Save_Data_file = False   # บันทึกไฟล์ CSV เพื่อตรวจสอบ ขั้นตอนการเทรนโมเดล

TOKEN = '7553787791:AAE-uAa0hyFQNGhN5edoQq5bFBge0LkMPbY'
CHAT_ID = 6546140292
MESSAGE = "-"

url = f"https://api.telegram.org/bot{TOKEN}/sendMessage"

# กำหนดเงื่อนไขการเทรด
input_volume_spike = 1.5         # Volume Spike สำหรับ entry
input_rsi_level_in = 40          # เพิ่มจาก 40 เป็น 42 - เข้มงวดมากขึ้น
input_rsi_level_over = 70        # พื้นที่ overbought oversold
input_rsi_level_out = 35         # เพิ่มจาก 35 เป็น 38 - ออกเร็วขึ้นเพื่อป้องกันขาดทุน
input_stop_loss_atr = 1.25       # ลดจาก 1.3 เป็น 1.2 - SL แคบขึ้นเพื่อลดความเสี่ยง
input_take_profit = 3.0          # เพิ่มจาก 2.8 เป็น 3.0 เพื่อ Risk:Reward = 1:3.0
input_pull_back = 0.45           # เพิ่มจาก 0.45 เป็น 0.48 เพื่อเลือกสัญญาณที่แข็งแกร่งที่สุด

# การตั้งค่า Time Filters
ENABLE_TIME_FILTERS = False  # True = ใช้ time filters, False = เทรดได้ตลอดเวลา
DEFAULT_TIME_FILTERS = {
    'days': [0, 1, 2, 3, 4],  # จันทร์-ศุกร์ (0=จันทร์, 6=อาทิตย์)
    'hours': list(range(6, 22))  # 06:00-21:59
}

# การตั้งค่าเงื่อนไขทางเทคนิค
ENABLE_TECHNICAL_CONDITIONS = True  # True = ใช้เงื่อนไขทางเทคนิค, False = ใช้เฉพาะ Model Prediction

# การตั้งค่าสำหรับ Target_Buy และ Target_Sell Models
ENABLE_TARGET_CONFIRMATION = True  # True = ใช้โมเดล Target_Buy/Target_Sell ยืนยัน, False = ใช้เฉพาะโมเดลหลัก
input_initial_confirm = 0.00  # threshold สำหรับโมเดลยืนยัน Target_Buy/Target_Sell
reduce_threshold = 0.80  # ตัวคูณสำหรับลด threshold ในการตัดสินใจ

# --- Import necessary components from python_LightGBM.py ---
try:
    # เพิ่ม Path ของโฟลเดอร์ที่เก็บ python_LightGBM.py เข้าไปใน sys.path ชั่วคราว
    # แก้ไข path นี้ให้ชี้ไปยังโฟลเดอร์ที่เก็บไฟล์ python_LightGBM.py ของคุณ
    python_lightgbm_folder = r'D:\test_gold' # *** แก้ Path นี้ ***
    if python_lightgbm_folder not in sys.path:
        sys.path.append(python_lightgbm_folder)
        print(f"Added {python_lightgbm_folder} to sys.path")

    # Import specific functions needed for Multi-Model Architecture
    from LightGBM_06_folder import (

        USE_MULTI_MODEL_ARCHITECTURE, MARKET_SCENARIOS,

        Save_Data_file,

        input_volume_spike,
        input_rsi_level_in, input_rsi_level_over, input_rsi_level_out,
        input_stop_loss_atr, input_take_profit, input_pull_back,

        # Enhanced Multi-Model Functions
        get_applicable_scenarios, select_appropriate_model,
        get_optimal_parameters, predict_with_scenario_model,

        # Feature
        ceiling_price, floor_price,
        mt5_like_macd_seeded, classic_macd_ema,
        create_features,
        create_resampled_df, create_features_mtf, 

        # Backward Compatibility Functions
        load_optimal_threshold_compatible, load_optimal_nbars_compatible,

        # Core Multi-Model Functions
        load_scenario_threshold, load_scenario_nbars, load_time_filters,
        load_scenario_models, detect_market_scenario,
        )

    # Define the model confidence threshold
    # model_confidence_threshold = 0.50 # ใช้ค่า Threshold จากโมเดลของคุณ

    # Define the base path for your models - ปรับให้รองรับ Multi-Model Architecture
    if USE_MULTI_MODEL_ARCHITECTURE:
        MODEL_BASE_PATH = r'D:\test_gold\LightGBM\Multi\models'
        THRESHOLD_BASE_PATH = r'D:\test_gold\LightGBM\Multi'
        FEATURE_IMPORTANCE_PATH = r'D:\test_gold\LightGBM\Multi\feature_importance'
        print(f"🔄 Using Multi-Model Architecture")
        print(f"Model base path set to: {MODEL_BASE_PATH}")
        print(f"Threshold base path set to: {THRESHOLD_BASE_PATH}")
        print(f"Feature importance path: {FEATURE_IMPORTANCE_PATH}")
        print(f"Available scenarios: {list(MARKET_SCENARIOS.keys())}")
    else:
        MODEL_BASE_PATH = r'D:\test_gold\LightGBM\Single\models'
        THRESHOLD_BASE_PATH = r'D:\test_gold\LightGBM\Single'
        FEATURE_IMPORTANCE_PATH = r'D:\test_gold\LightGBM\Single\feature_importance'
        print(f"📊 Using Single Model Architecture")
        print(f"Model base path set to: {MODEL_BASE_PATH}")
        print(f"Threshold base path set to: {THRESHOLD_BASE_PATH}")
        print(f"Feature importance path: {FEATURE_IMPORTANCE_PATH}")

    # กำหนด Timezone ของ MT5 Server (มักจะเป็น UTC)
    MT5_TIMEZONE = pytz.utc # หรือ pytz.timezone('Etc/UTC') หรือ Timezone ของ Server Broker

except ImportError as e:
    print(f"Error: Could not import components from python_LightGBM.py - {e}")
    print("Please ensure python_LightGBM.py is in the specified path or in Python's path.")
    print("Exiting server initialization.")
    exit()
except Exception as e:
    print(f"An unexpected error occurred during import from python_LightGBM.py: {e}")
    traceback.print_exc()
    print("Exiting server initialization.")
    exit()

# --- Configuration ---
HTTP_PORT = 54321
HTTP_HOST = '127.0.0.1'

# --- Add Timeframe Mapping ---
timeframe_map = {
    "PERIOD_M1": mt5.TIMEFRAME_M1,
    "PERIOD_M2": mt5.TIMEFRAME_M2,
    "PERIOD_M3": mt5.TIMEFRAME_M3,
    "PERIOD_M4": mt5.TIMEFRAME_M4,
    "PERIOD_M5": mt5.TIMEFRAME_M5,
    "PERIOD_M6": mt5.TIMEFRAME_M6,
    "PERIOD_M10": mt5.TIMEFRAME_M10,
    "PERIOD_M12": mt5.TIMEFRAME_M12,
    "PERIOD_M15": mt5.TIMEFRAME_M15,
    "PERIOD_M20": mt5.TIMEFRAME_M20,
    "PERIOD_M30": mt5.TIMEFRAME_M30,
    "PERIOD_H1": mt5.TIMEFRAME_H1,
    "PERIOD_H2": mt5.TIMEFRAME_H2,
    "PERIOD_H3": mt5.TIMEFRAME_H3,
    "PERIOD_H4": mt5.TIMEFRAME_H4,
    "PERIOD_H6": mt5.TIMEFRAME_H6,
    "PERIOD_H8": mt5.TIMEFRAME_H8,
    "PERIOD_H12": mt5.TIMEFRAME_H12,
    "PERIOD_D1": mt5.TIMEFRAME_D1,
    "PERIOD_W1": mt5.TIMEFRAME_W1,
    "PERIOD_MN1": mt5.TIMEFRAME_MN1,
}

timeframe_code_map = {
    "PERIOD_M1": 1, "PERIOD_M2": 5, "PERIOD_M3": 15, "PERIOD_M30": 30, "PERIOD_H1": 60, "PERIOD_H4": 240
    # เพิ่ม Timeframe ที่เหลือตาม folder structure ของคุณ
}

# ตั้งค่าข้อมูลคู่เงิน
SYMBOL_INFO = {
    "GOLD":   {"Spread": 25, "Digits": 2, "Points": 0.01, "Swap_Long": 0, "Swap_Short": 0},
    "AUDUSD": {"Spread": 15, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURGBP": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURUSD": {"Spread": 13, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "GBPUSD": {"Spread": 25, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "NZDUSD": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDCAD": {"Spread": 28, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDJPY": {"Spread": 16, "Digits": 3, "Points": 0.001, "Swap_Long": 0, "Swap_Short": 0}
}

# ==============================================

model_name = "LightGBM"
test_csv   = f"{model_name}/Server_Data"
os.makedirs(test_csv, exist_ok=True)

# ==============================================

# Timeframe Information
TIMEFRAME_INFO = {"M30": 30, "M60": 60}

symbol_info = SYMBOL_INFO
timeframe_info = TIMEFRAME_INFO

# --- Global Data Storage ---
market_data_store = {}
data_lock = threading.Lock()

# --- Dictionary to store the latest signal and confidence for each symbol/timeframe ---
# This will be used to send the signal back in the HTTP response.
# Key: (cleaned_symbol, timeframe_int)
# Value: {"signal": str, "confidence": float, "timestamp": datetime}
latest_signals_data = {}
signals_lock = threading.Lock() # Lock for accessing latest_signals_data

# --- Flask App Setup ---
app = Flask(__name__)

# Setup logging
def setup_logging():
    """ตั้งค่า logging system สำหรับ server"""
    # สร้าง logger
    logger = logging.getLogger('trading_server')
    logger.setLevel(logging.INFO)

    # สร้าง formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # File handler สำหรับ log ทั่วไป
    file_handler = RotatingFileHandler(
        'server_trading.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)

    # File handler สำหรับ signals
    signal_handler = RotatingFileHandler(
        'server_signals.log',
        maxBytes=5*1024*1024,   # 5MB
        backupCount=3,
        encoding='utf-8'
    )
    signal_handler.setFormatter(formatter)
    signal_handler.setLevel(logging.INFO)

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)

    # เพิ่ม handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # สร้าง signal logger แยก
    signal_logger = logging.getLogger('trading_signals')
    signal_logger.setLevel(logging.INFO)
    signal_logger.addHandler(signal_handler)
    signal_logger.addHandler(console_handler)

    return logger, signal_logger

# ตั้งค่า logging
server_logger, signal_logger = setup_logging()

# --- Model Loading Cache ---
loaded_models = {}  # สำหรับ Single Model
loaded_scenario_models = {}  # สำหรับ Multi-Model Architecture
model_lock = threading.Lock()

def load_best_entry_condition(symbol, timeframe):
    """
    โหลด entry condition ที่ดีที่สุดจากการทดสอบ
    """
    try:
        # ลองหาในโฟลเดอร์ตาม timeframe ก่อน
        timeframe_folder = f"M{timeframe}" if timeframe in [30, 60] else str(timeframe)
        best_entry_path = f"Test_LightGBM/results/{timeframe_folder}/M{timeframe}_{symbol}_best_entry.pkl"

        if not os.path.exists(best_entry_path):
            # ถ้าไม่พบ ลองหาในโฟลเดอร์หลัก
            best_entry_path = f"Test_LightGBM/results/M{timeframe}_{symbol}_best_entry.pkl"

        if os.path.exists(best_entry_path):
            with open(best_entry_path, 'rb') as f:
                best_entry_info = pickle.load(f)

            entry_name = best_entry_info.get("entry_name", "entry_v1")
            expectancy = best_entry_info.get("expectancy", 0)
            timestamp = best_entry_info.get("timestamp", "unknown")

            print(f"✅ โหลด best_entry จาก: {best_entry_path}")
            print(f"   Entry: {entry_name}, Expectancy: {expectancy:.2f}, Updated: {timestamp}")

            return entry_name
        else:
            print(f"⚠️ ไม่พบไฟล์ best_entry: {best_entry_path}")
            return "entry_v1"  # fallback

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดขณะโหลด best_entry: {e}")
        return "entry_v1"  # fallback

def get_entry_conditions_functions():
    """
    ส่งคืน dictionary ของฟังก์ชัน entry conditions ต่างๆ
    """
    def default_entry_conditions(latest_features_dict_all_i2, input_rsi_level_in, input_pull_back, input_take_profit):
        # เงื่อนไขทางเทคนิคสำหรับ Buy (default)
        tech_signal_buy = (
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
            (latest_features_dict_all_i2['RSI_signal'] > input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Up'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Buy'] > input_take_profit)
        )

        # เงื่อนไขทางเทคนิคสำหรับ Sell (default)
        tech_signal_sell = (
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
            (latest_features_dict_all_i2['RSI_signal'] < -input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Down'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Sell'] > input_take_profit)
        )

        return tech_signal_buy, tech_signal_sell

    def entry_v1_conditions(latest_features_dict_all_i2, input_rsi_level_in, input_pull_back, input_take_profit):
        # เงื่อนไขทางเทคนิคสำหรับ Buy (entry_v1: เพิ่มเงื่อนไข close > ema50)
        tech_signal_buy = (
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['EMA50']) and  # entry_v1: เพิ่มเงื่อนไข close > ema50
            (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
            (latest_features_dict_all_i2['RSI_signal'] > input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Up'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Buy'] > input_take_profit)
        )

        # เงื่อนไขทางเทคนิคสำหรับ Sell (entry_v1: เพิ่มเงื่อนไข close < ema50)
        tech_signal_sell = (
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['EMA50']) and  # entry_v1: เพิ่มเงื่อนไข close < ema50
            (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
            (latest_features_dict_all_i2['RSI_signal'] < -input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Down'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Sell'] > input_take_profit)
        )

        return tech_signal_buy, tech_signal_sell

    def entry_v2_conditions(latest_features_dict_all_i2, input_rsi_level_in, input_pull_back, input_take_profit):
        # เงื่อนไขทางเทคนิคสำหรับ Buy (entry_v2: เพิ่มเงื่อนไข RSI > 50)
        tech_signal_buy = (
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['EMA50']) and
            (latest_features_dict_all_i2['RSI'] > 50) and  # entry_v2: เพิ่มเงื่อนไข RSI > 50
            (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
            (latest_features_dict_all_i2['RSI_signal'] > input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Up'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Buy'] > input_take_profit)
        )

        # เงื่อนไขทางเทคนิคสำหรับ Sell (entry_v2: เพิ่มเงื่อนไข RSI < 50)
        tech_signal_sell = (
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['EMA50']) and
            (latest_features_dict_all_i2['RSI'] < 50) and  # entry_v2: เพิ่มเงื่อนไข RSI < 50
            (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
            (latest_features_dict_all_i2['RSI_signal'] < -input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Down'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Sell'] > input_take_profit)
        )

        return tech_signal_buy, tech_signal_sell

    def entry_v3_conditions(latest_features_dict_all_i2, input_rsi_level_in, input_pull_back, input_take_profit):
        # เงื่อนไขทางเทคนิคสำหรับ Buy (entry_v3: เพิ่มเงื่อนไข MACD > 0)
        tech_signal_buy = (
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] > latest_features_dict_all_i2['EMA50']) and
            (latest_features_dict_all_i2['RSI'] > 50) and
            (latest_features_dict_all_i2['MACD'] > 0) and  # entry_v3: เพิ่มเงื่อนไข MACD > 0
            (latest_features_dict_all_i2['MACD_signal'] == 1.0) and
            (latest_features_dict_all_i2['RSI_signal'] > input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Up'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Buy'] > input_take_profit)
        )

        # เงื่อนไขทางเทคนิคสำหรับ Sell (entry_v3: เพิ่มเงื่อนไข MACD < 0)
        tech_signal_sell = (
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['Open']) and
            (latest_features_dict_all_i2['Close'] < latest_features_dict_all_i2['EMA50']) and
            (latest_features_dict_all_i2['RSI'] < 50) and
            (latest_features_dict_all_i2['MACD'] < 0) and  # entry_v3: เพิ่มเงื่อนไข MACD < 0
            (latest_features_dict_all_i2['MACD_signal'] == -1.0) and
            (latest_features_dict_all_i2['RSI_signal'] < -input_rsi_level_in) and
            (latest_features_dict_all_i2['Volume'] > latest_features_dict_all_i2['Volume_MA20'] * 0.80) and
            (latest_features_dict_all_i2['PullBack_Down'] > input_pull_back) and
            (latest_features_dict_all_i2['Ratio_Sell'] > input_take_profit)
        )

        return tech_signal_buy, tech_signal_sell

def is_high_quality_entry(df_row, symbol):
    """
    ตรวจสอบคุณภาพของ entry point
    """
    try:
        # ตรวจสอบ Volume
        volume_check = df_row.get('Volume', 0) > df_row.get('Volume_MA20', 0) * 1.2

        # ตรวจสอบ ATR (ความผันผวน)
        atr_check = df_row.get('ATR', 0) > 0

        # ตรวจสอบ RSI ไม่อยู่ในโซน extreme
        rsi = df_row.get('RSI', 50)
        rsi_check = 25 < rsi < 75

        # ตรวจสอบ MACD signal
        macd_signal_check = abs(df_row.get('MACD_signal', 0)) > 0

        return volume_check and atr_check and rsi_check and macd_signal_check

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการตรวจสอบคุณภาพ entry: {e}")
        return True  # default ให้ผ่าน

def enhanced_model_decision_buy(scenario_models, prediction_row, symbol, timeframe, threshold_trend, threshold_counter):
    """
    การตัดสินใจ BUY แบบ Enhanced ด้วยโมเดล Target_Buy
    """
    try:
        # --- Step 1: Predict ด้วยโมเดลหลัก (trend_following หรือ counter_trend) ---

        close = prediction_row.get('Close', 0)
        ema200 = prediction_row.get('EMA200', prediction_row.get('ema200', prediction_row.get('EMA_200', close)))

        scenario_base = "trend_following" if close > ema200 else "counter_trend"
        scenario_name = scenario_base  # ใช้ base เป็นโมเดลหลัก

        print(f"\n🔍 BUY Analysis - Close: {close:.5f}, EMA200: {ema200:.5f}, Scenario: {scenario_base}")

        if scenario_name not in scenario_models:
            print(f"⚠️ ไม่มีโมเดลสำหรับ {scenario_name} - ข้าม BUY signal")
            return False, 0.0, "No model available", "none"

        # ใช้โมเดลหลัก
        main_model = scenario_models[scenario_name]['model']
        main_scaler = scenario_models[scenario_name]['scaler']
        main_features = scenario_models[scenario_name]['features']

        # ตรวจสอบ scaler (LightGBM ไม่จำเป็นต้องใช้ scaler)
        if main_scaler is None:
            print(f"ℹ️ Main Scaler เป็น None สำหรับ {scenario_name} (ปกติสำหรับ LightGBM)")
            # ไม่ return False เพราะ LightGBM ไม่จำเป็นต้องใช้ scaler

        # เตรียมข้อมูลสำหรับโมเดลหลัก
        available_features = [f for f in main_features if f in prediction_row.index]
        if len(available_features) == 0:
            print(f"⚠️ ไม่มี features ที่ใช้ได้สำหรับโมเดลหลัก")
            return False, 0.0, "No features available", "none"

        # สร้าง feature array
        feature_values = []
        for feature in main_features:
            if feature in prediction_row.index:
                feature_values.append(prediction_row[feature])
            else:
                feature_values.append(0.0)  # padding สำหรับ missing features

        feature_array = np.array(feature_values).reshape(1, -1)

        # ใช้ scaler ถ้ามี ถ้าไม่มีใช้ข้อมูลดิบ (สำหรับ LightGBM)
        if main_scaler is not None:
            scaled_features = main_scaler.transform(feature_array)
        else:
            scaled_features = feature_array  # ใช้ข้อมูลดิบสำหรับ LightGBM

        # ทำนายด้วยโมเดลหลัก
        prediction_proba = main_model.predict_proba(scaled_features)[0]

        # คำนวณ probability สำหรับ BUY
        if len(prediction_proba) >= 5:
            prob_buy = prediction_proba[3] + prediction_proba[4]  # weak_buy + strong_buy
        else:
            prob_buy = prediction_proba[1] if len(prediction_proba) > 1 else prediction_proba[0]

        # กำหนด threshold
        current_threshold = threshold_trend if scenario_base == "trend_following" else threshold_counter
        buy_threshold = current_threshold * reduce_threshold

        basic_model_decision = (prob_buy > buy_threshold)
        print(f"📊 Main Model BUY - Prob: {prob_buy:.4f}, Threshold: {buy_threshold:.4f}, Decision: {basic_model_decision} reduce {reduce_threshold}")

        # --- Step 2: ตรวจสอบด้วยโมเดล "_Buy" ---

        confirm_pass = True  # default = ผ่าน
        confirm_prob = None

        if ENABLE_TARGET_CONFIRMATION:
            confirm_model_name = f"{scenario_base}_Buy"
            if confirm_model_name in scenario_models:
                try:
                    confirm_model = scenario_models[confirm_model_name]['model']
                    confirm_scaler = scenario_models[confirm_model_name]['scaler']
                    confirm_features = scenario_models[confirm_model_name]['features']

                    # ตรวจสอบ confirm_scaler ก่อนใช้งาน
                    if confirm_scaler is None:
                        print(f"⚠️ Confirm Scaler เป็น None สำหรับ {confirm_model_name}")
                        confirm_pass = True  # ถ้า scaler เป็น None ให้ผ่านไป
                    else:
                        # เตรียมข้อมูลสำหรับโมเดลยืนยัน
                        confirm_feature_values = []
                        for feature in confirm_features:
                            if feature in prediction_row.index:
                                confirm_feature_values.append(prediction_row[feature])
                            else:
                                confirm_feature_values.append(0.0)

                        confirm_feature_array = np.array(confirm_feature_values).reshape(1, -1)
                        scaled_features_c = confirm_scaler.transform(confirm_feature_array)

                        prediction_proba_c = confirm_model.predict_proba(scaled_features_c)[0]

                        if len(prediction_proba_c) >= 5:
                            confirm_prob = prediction_proba_c[3] + prediction_proba_c[4]
                        else:
                            confirm_prob = prediction_proba_c[1] if len(prediction_proba_c) > 1 else prediction_proba_c[0]

                        confirm_pass = confirm_prob > input_initial_confirm * reduce_threshold
                        print(f"✅ Confirm Model BUY ({confirm_model_name}) - Prob: {confirm_prob:.4f}, Pass: {confirm_pass}")

                except Exception as e:
                    print(f"⚠️ เกิดข้อผิดพลาดในโมเดลยืนยัน {confirm_model_name}: {str(e)}")
                    confirm_pass = False
            else:
                print(f"ℹ️ ไม่พบโมเดลยืนยัน {confirm_model_name}")

        # --- Step 3: ตรวจสอบคุณภาพ entry ---
        high_quality_check = is_high_quality_entry(prediction_row, symbol)
        print(f"🔍 High Quality Check: {high_quality_check}")

        # --- Step 4: รวมผลการตัดสินใจ ---
        final_decision = basic_model_decision and confirm_pass and high_quality_check
        final_confidence = prob_buy if confirm_prob is None else (prob_buy + confirm_prob) / 2

        decision_details = f"Main:{basic_model_decision}, Confirm:{confirm_pass}, Quality:{high_quality_check}"

        print(f"🎯 Final BUY Decision: {final_decision} (Confidence: {final_confidence:.4f}) - {decision_details}")

        return final_decision, final_confidence, decision_details, scenario_name

    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดใน enhanced_model_decision_buy: {str(e)}")
        return False, 0.0, f"Error: {str(e)}", "none"

def enhanced_model_decision_sell(scenario_models, prediction_row, symbol, timeframe, threshold_trend, threshold_counter):
    """
    การตัดสินใจ SELL แบบ Enhanced ด้วยโมเดล Target_Sell
    """
    try:
        # --- Step 1: Predict ด้วยโมเดลหลัก (trend_following หรือ counter_trend) ---

        close = prediction_row.get('close', prediction_row.get('Close', 0))
        ema200 = prediction_row.get('EMA200', prediction_row.get('ema200', prediction_row.get('EMA_200', close)))

        # สำหรับ SELL: trend_following เมื่อ close < ema200, counter_trend เมื่อ close >= ema200
        scenario_base = "trend_following" if close < ema200 else "counter_trend"
        scenario_name = scenario_base  # ใช้ base เป็นโมเดลหลัก

        print(f"\n🔍 SELL Analysis - Close: {close:.5f}, EMA200: {ema200:.5f}, Scenario: {scenario_base}")

        if scenario_name not in scenario_models:
            print(f"⚠️ ไม่มีโมเดลสำหรับ {scenario_name} - ข้าม SELL signal")
            return False, 0.0, "No model available", "none"

        # ใช้โมเดลหลัก
        main_model = scenario_models[scenario_name]['model']
        main_scaler = scenario_models[scenario_name]['scaler']
        main_features = scenario_models[scenario_name]['features']

        # ตรวจสอบ scaler (LightGBM ไม่จำเป็นต้องใช้ scaler)
        if main_scaler is None:
            print(f"ℹ️ Main Scaler เป็น None สำหรับ {scenario_name} (ปกติสำหรับ LightGBM)")
            # ไม่ return False เพราะ LightGBM ไม่จำเป็นต้องใช้ scaler

        # เตรียมข้อมูลสำหรับโมเดลหลัก
        available_features = [f for f in main_features if f in prediction_row.index]
        if len(available_features) == 0:
            print(f"⚠️ ไม่มี features ที่ใช้ได้สำหรับโมเดลหลัก")
            return False, 0.0, "No features available", "none"

        # สร้าง feature array
        feature_values = []
        for feature in main_features:
            if feature in prediction_row.index:
                feature_values.append(prediction_row[feature])
            else:
                feature_values.append(0.0)  # padding สำหรับ missing features

        feature_array = np.array(feature_values).reshape(1, -1)

        # ใช้ scaler ถ้ามี ถ้าไม่มีใช้ข้อมูลดิบ (สำหรับ LightGBM)
        if main_scaler is not None:
            scaled_features = main_scaler.transform(feature_array)
        else:
            scaled_features = feature_array  # ใช้ข้อมูลดิบสำหรับ LightGBM

        # ทำนายด้วยโมเดลหลัก
        prediction_proba = main_model.predict_proba(scaled_features)[0]

        # คำนวณ probability สำหรับ SELL
        if len(prediction_proba) >= 5:
            prob_sell = prediction_proba[0] + prediction_proba[1]  # strong_sell + weak_sell
        else:
            prob_sell = prediction_proba[1] if len(prediction_proba) > 1 else prediction_proba[0]

        # กำหนด threshold
        current_threshold = threshold_trend if scenario_base == "trend_following" else threshold_counter
        sell_threshold = current_threshold * reduce_threshold

        basic_model_decision = (prob_sell > sell_threshold)
        print(f"📊 Main Model SELL - Prob: {prob_sell:.4f}, Threshold: {sell_threshold:.4f}, Decision: {basic_model_decision} reduce {reduce_threshold}")

        # --- Step 2: ตรวจสอบด้วยโมเดล "_Sell" ---

        confirm_pass = True  # default = ผ่าน
        confirm_prob = None

        if ENABLE_TARGET_CONFIRMATION:
            confirm_model_name = f"{scenario_base}_Sell"
            if confirm_model_name in scenario_models:
                try:
                    confirm_model = scenario_models[confirm_model_name]['model']
                    confirm_scaler = scenario_models[confirm_model_name]['scaler']
                    confirm_features = scenario_models[confirm_model_name]['features']

                    # ตรวจสอบ confirm_scaler ก่อนใช้งาน
                    if confirm_scaler is None:
                        print(f"⚠️ Confirm Scaler เป็น None สำหรับ {confirm_model_name}")
                        confirm_pass = True  # ถ้า scaler เป็น None ให้ผ่านไป
                    else:
                        # เตรียมข้อมูลสำหรับโมเดลยืนยัน
                        confirm_feature_values = []
                        for feature in confirm_features:
                            if feature in prediction_row.index:
                                confirm_feature_values.append(prediction_row[feature])
                            else:
                                confirm_feature_values.append(0.0)

                        confirm_feature_array = np.array(confirm_feature_values).reshape(1, -1)
                        scaled_features_c = confirm_scaler.transform(confirm_feature_array)

                        prediction_proba_c = confirm_model.predict_proba(scaled_features_c)[0]

                        if len(prediction_proba_c) >= 5:
                            confirm_prob = prediction_proba_c[0] + prediction_proba_c[1]
                        else:
                            confirm_prob = prediction_proba_c[1] if len(prediction_proba_c) > 1 else prediction_proba_c[0]

                        confirm_pass = confirm_prob > input_initial_confirm * reduce_threshold
                        print(f"✅ Confirm Model SELL ({confirm_model_name}) - Prob: {confirm_prob:.4f}, Pass: {confirm_pass}")

                except Exception as e:
                    print(f"⚠️ เกิดข้อผิดพลาดในโมเดลยืนยัน {confirm_model_name}: {str(e)}")
                    confirm_pass = False
            else:
                print(f"ℹ️ ไม่พบโมเดลยืนยัน {confirm_model_name}")

        # --- Step 3: ตรวจสอบคุณภาพ entry ---
        high_quality_check = is_high_quality_entry(prediction_row, symbol)
        print(f"🔍 High Quality Check: {high_quality_check}")

        # --- Step 4: รวมผลการตัดสินใจ ---
        final_decision = basic_model_decision and confirm_pass and high_quality_check
        final_confidence = prob_sell if confirm_prob is None else (prob_sell + confirm_prob) / 2

        decision_details = f"Main:{basic_model_decision}, Confirm:{confirm_pass}, Quality:{high_quality_check}"

        print(f"🎯 Final SELL Decision: {final_decision} (Confidence: {final_confidence:.4f}) - {decision_details}")

        return final_decision, final_confidence, decision_details, scenario_name

    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดใน enhanced_model_decision_sell: {str(e)}")
        return False, 0.0, f"Error: {str(e)}", "none"

# Helper function to get entry conditions - ฟังก์ชันเหล่านี้อยู่ใน class TechnicalAnalysisServer

def load_multi_model_components(symbol, timeframe):
    """
    Loads Multi-Model Architecture components (6 scenarios: trend_following, trend_following_Buy, trend_following_Sell,
    counter_trend, counter_trend_Buy, counter_trend_Sell)
    Uses cache to avoid reloading.
    """
    key = (symbol, timeframe)
    with model_lock:
        if key in loaded_scenario_models:
            print(f"🔄 Loading Multi-Model components from cache for {symbol} M{timeframe}")
            return loaded_scenario_models[key]

        print(f"📊 Loading Multi-Model components for {symbol} M{timeframe}")

        # ใช้ฟังก์ชันจาก LightGBM_07_target.py เพื่อโหลดทั้ง 6 scenarios
        try:
            from LightGBM_07_target import load_scenario_models as load_scenario_models_target
            # ส่ง MODEL_BASE_PATH ที่ถูกต้องไปยัง load_scenario_models
            scenario_models = load_scenario_models_target(symbol, timeframe, base_folder=MODEL_BASE_PATH)
        except ImportError:
            print("⚠️ ไม่สามารถ import load_scenario_models จาก LightGBM_07_target.py - ใช้ fallback")
            scenario_models = load_scenario_models(symbol, timeframe)

        if scenario_models:
            loaded_scenario_models[key] = scenario_models
            print(f"✅ Successfully loaded {len(scenario_models)} scenario models for {symbol} M{timeframe}")

            # แสดงรายละเอียดโมเดลที่โหลดได้
            print(f"📋 Loaded scenarios:")
            for scenario_name, model_info in scenario_models.items():
                features_count = len(model_info.get('features', [])) if model_info.get('features') else 0
                print(f"   - {scenario_name}: {features_count} features")

            return scenario_models
        else:
            print(f"❌ Failed to load Multi-Model components for {symbol} M{timeframe}")
            return None

def load_model_components(symbol, timeframe):
    """
    Loads model components - supports both Single Model and Multi-Model Architecture
    """
    if USE_MULTI_MODEL_ARCHITECTURE:
        return load_multi_model_components(symbol, timeframe)
    else:
        # Original single model loading logic (for backward compatibility)
        key = (symbol, timeframe)
        with model_lock:
            if key in loaded_models:
                return loaded_models[key]

            # Import legacy functions if needed - เปลี่ยนเป็น LightGBM_06_folder
            try:
                # ใช้ฟังก์ชันจาก LightGBM_06_folder แทน python_LightGBM_19_Gemini
                print("📊 Using direct file loading for single model (no legacy functions needed)")
            except ImportError:
                print("❌ Cannot import functions from LightGBM_06_folder")
                return None, None, None

            model_name = "LightGBM"
            model_dir = os.path.join(MODEL_BASE_PATH, f"{str(timeframe).zfill(3)}_{symbol}")

            # ปรับรูปแบบชื่อไฟล์ให้ตรงกับไฟล์ที่มีอยู่จริง
            model_path = os.path.join(model_dir, f"{str(timeframe).zfill(3)}_{symbol}_trained.pkl")
            scaler_path = os.path.join(model_dir, f"{str(timeframe).zfill(3)}_{symbol}_scaler.pkl")
            features_list_path = os.path.join(model_dir, f"{str(timeframe).zfill(3)}_{symbol}_features.pkl")

            print(f"📊 Loading Single Model from: {model_dir}")
            print(f"   Model: {model_path}")
            print(f"   Scaler: {scaler_path}")
            print(f"   Features: {features_list_path}")

            try:
                # โหลดไฟล์โดยตรงแทนการใช้ legacy functions
                if os.path.exists(model_path) and os.path.exists(scaler_path) and os.path.exists(features_list_path):
                    model = joblib.load(model_path)
                    scaler = joblib.load(scaler_path)

                    with open(features_list_path, 'rb') as f:
                        try:
                            features_list = pickle.load(f)
                        except Exception:
                            f.seek(0)
                            features_list = joblib.load(f)

                    loaded_models[key] = (model, scaler, features_list)
                    print(f"✅ Successfully loaded Single Model components")
                    return model, scaler, features_list
                else:
                    missing_files = []
                    if not os.path.exists(model_path):
                        missing_files.append("trained.pkl")
                    if not os.path.exists(scaler_path):
                        missing_files.append("scaler.pkl")
                    if not os.path.exists(features_list_path):
                        missing_files.append("features.pkl")
                    print(f"❌ Missing Single Model files: {', '.join(missing_files)}")
                    return None, None, None

            except Exception as e:
                print(f"❌ Error loading single model components: {e}")
                return None, None, None

def load_single_model_threshold(symbol, timeframe, default=0.5):
    """โหลด threshold สำหรับ Single Model Architecture"""
    threshold_file = os.path.join(THRESHOLD_BASE_PATH, f"{str(timeframe).zfill(3)}_{symbol}_optimal_threshold.pkl")

    try:
        with open(threshold_file, 'rb') as f:
            threshold = pickle.load(f)
        print(f"✅ โหลด Single Model threshold สำเร็จ: {threshold:.4f}")
        return threshold
    except Exception as e:
        print(f"⚠️ ไม่พบไฟล์ Single Model threshold, ใช้ค่า default: {default} ({e})")
        return default

def load_single_model_nbars(symbol, timeframe, default=6):
    """โหลด nBars_SL สำหรับ Single Model Architecture"""
    nbars_file = os.path.join(THRESHOLD_BASE_PATH, f"{str(timeframe).zfill(3)}_{symbol}_optimal_nBars_SL.pkl")

    try:
        with open(nbars_file, 'rb') as f:
            nbars = pickle.load(f)
        print(f"✅ โหลด Single Model nBars_SL สำเร็จ: {nbars}")
        return nbars
    except Exception as e:
        print(f"⚠️ ไม่พบไฟล์ Single Model nBars_SL, ใช้ค่า default: {default} ({e})")
        return default

def load_single_model_time_filters(symbol, timeframe):
    """โหลด time_filters สำหรับ Single Model Architecture"""
    # ตรวจสอบว่าเปิดใช้ time_filters หรือไม่
    if not ENABLE_TIME_FILTERS:
        print(f"⚠️ Time filters ถูกปิดใช้งาน - เทรดได้ตลอดเวลา")
        return {'days': list(range(7)), 'hours': list(range(24))}

    filters_file = os.path.join(THRESHOLD_BASE_PATH, f"{str(timeframe).zfill(3)}_{symbol}_time_filters.pkl")

    try:
        with open(filters_file, 'rb') as f:
            filters = pickle.load(f)
        print(f"✅ โหลด Single Model time_filters สำเร็จ")
        return filters
    except Exception as e:
        print(f"⚠️ ไม่พบไฟล์ Single Model time_filters, ใช้ค่า default: {e}")
        # ใช้ค่า default ที่กำหนดไว้
        return DEFAULT_TIME_FILTERS.copy()

def prepare_analysis_summary(analysis_results):
    """แปลงผลการวิเคราะห์ให้เป็น JSON serializable"""
    if not analysis_results:
        return {}

    summary = {}

    try:
        for scenario in ['trend_following', 'counter_trend']:
            if scenario in analysis_results:
                summary[scenario] = {}
                for action in ['buy', 'sell']:
                    if action in analysis_results[scenario] and analysis_results[scenario][action]:
                        result = analysis_results[scenario][action]
                        # แปลงค่าทั้งหมดให้เป็น Python native types
                        should_trade = result.get('should_trade', False)
                        confidence = result.get('confidence', 0.0)

                        # แปลงเป็น Python native types
                        if hasattr(should_trade, 'item'):  # numpy types
                            should_trade = should_trade.item()
                        if hasattr(confidence, 'item'):  # numpy types
                            confidence = confidence.item()

                        summary[scenario][action] = {
                            'should_trade': bool(should_trade),
                            'confidence': float(confidence),
                            'model_info': {
                                'scenario': str(scenario),
                                'action': str(action),
                                'available': True
                            }
                        }
                    else:
                        summary[scenario][action] = {
                            'should_trade': False,
                            'confidence': 0.0,
                            'model_info': {
                                'scenario': str(scenario),
                                'action': str(action),
                                'available': False
                            }
                        }

        return summary

    except Exception as e:
        print(f"⚠️ Error preparing analysis summary: {e}")
        import traceback
        traceback.print_exc()
        return {
            'error': str(e),
            'trend_following': {'buy': {'should_trade': False, 'confidence': 0.0}, 'sell': {'should_trade': False, 'confidence': 0.0}},
            'counter_trend': {'buy': {'should_trade': False, 'confidence': 0.0}, 'sell': {'should_trade': False, 'confidence': 0.0}}
        }

def get_log_path(symbol, timeframe, log_folder=r"LightGBM\Server_Log"):

    # print(f"symbol {symbol} timeframe {timeframe}")
    tf_map = {
        "PERIOD_M30": "030", "PERIOD_H1": "060",
        "M30": "030", "H1": "060",
        "30": "030", "60": "060"
        }

    # tf_code = tf_map.get(timeframe.upper(), "000")
    tf_code = tf_map.get(str(timeframe).upper(), "000") # แปลง timeframe เป็น string ก่อน

    if tf_code == "000":
        print(f"⚠️ Warning: Unknown timeframe mapping for '{timeframe}'")

    now = datetime.datetime.now()
    year_thai = now.year
    date_prefix = f"{str(year_thai)[-2:]}{now.month:02d}{now.day:02d}"

    log_filename = f"{date_prefix}_{tf_code}_{symbol}_Log.txt"
    log_path = os.path.join(log_folder, log_filename)

    os.makedirs(log_folder, exist_ok=True)
    return log_path

def log_response_payload(response_payload, log_folder="LightGBM/Server_Log"):
    symbol = response_payload.get("symbol", "UNKNOWN")
    timeframe = response_payload.get("timeframe_str", "UNKNOWN")

    log_path = get_log_path(symbol, timeframe, log_folder)

    now = datetime.datetime.now()
    current_time = now.strftime("%Y-%m-%d %H:%M:%S")

    log_entry = f"[{current_time}] Symbol: {symbol}, Timeframe: {timeframe}\n"
    log_entry += json.dumps(response_payload, ensure_ascii=False, indent=2)
    log_entry += "\n" + "-"*80 + "\n"

    with open(log_path, "a", encoding="utf-8") as f:
        f.write(log_entry)

    print(f"✅ Logged to: {log_path}")
    return log_path  # ✅ ส่งกลับ path เพื่อใช้ต่อ

# ==============================================
# DATA PROCESSING (การประมวลผลข้อมูล)
# ==============================================

# --- Data Processing Function (จะเรียกใช้ใน Thread แยก) ---
def process_data_and_trade(symbol, timeframe, latest_indicators=None):
    """
    Calculates indicators, runs model, determines trade signal and confidence.
    This runs in a separate thread.
    """

    # --- Overwrite latest bar with precise values from MT5 (Hybrid Approach) ---
    if latest_indicators and not df_ft.empty:
        print(f"[{datetime.datetime.now()}] 🔧 Applying precise indicators from MT5 for the latest bar...")
        latest_index = df_ft.index[-1]
        
        # สร้าง Series จาก dict ของ latest_indicators
        mt5_values = pd.Series(latest_indicators)
        
        # วนลูปเพื่อเขียนทับค่า
        for indicator_name, value in mt5_values.items():
            # แปลงชื่อ indicator จาก MQL5 (ตัวเล็ก) เป็นชื่อคอลัมน์ใน Python (ตัวพิมพ์ใหญ่)
            column_name = indicator_name.upper()
            if column_name in df_ft.columns:
                # ใช้ .loc เพื่อเขียนทับค่าในแถวและคอลัมน์ที่ต้องการ
                df_ft.loc[latest_index, column_name] = value
                print(f"   Overwrote {column_name}: {df_ft.loc[latest_index, column_name]:.4f} -> {value:.4f}")

    print(f"\n[{datetime.datetime.now()}] 🔄 Starting process_data_and_trade for {symbol} M{timeframe}")

    df = None
    with data_lock:
        key = (symbol, timeframe)
        if key in market_data_store and len(market_data_store[key]) > 0:
            df = market_data_store[key].copy()
            latest_bar_dt = df.index[-1] # Get the timestamp of the latest bar
            print(f"[{datetime.datetime.now()}] 📊 Retrieved {len(df)} bars from data store")
            print(f"[{datetime.datetime.now()}] 🕒 Latest bar time: {latest_bar_dt}")
        else:
            print(f"[{datetime.datetime.now()}] ❌ No sufficient data yet for {symbol} ({timeframe}). Waiting for more bars.")
            return

    # --- แก้ไข: เปลี่ยนชื่อคอลัมน์ให้เป็นตัวพิมพ์ใหญ่เพื่อให้ตรงกับโค้ด Indicator ---
    print(f"[{datetime.datetime.now()}] 🔄 Renaming columns for indicator calculation")
    # คอลัมน์ที่ต้องการเปลี่ยน: 'open', 'high', 'low', 'close', 'volume' ให้เป็น: 'Open', 'High', 'Low', 'Close', 'Volume'
    rename_map = {
        'open': 'Open',
        'high': 'High',
        'low': 'Low',
        'close': 'Close',
        'volume': 'Volume',
        'tick_volume': 'Volume'  # เพิ่มการ map tick_volume เป็น Volume
    }
    # ใช้ .rename() เพื่อเปลี่ยนชื่อคอลัมน์ โดยใช้ errors='ignore' เพื่อไม่ให้ error ถ้าคอลัมน์ไม่มี
    df_ft = df.rename(columns=rename_map, errors='ignore')

    # *** แก้ไขปัญหา duplicate columns ***
    print(f"[{datetime.datetime.now()}] 🔍 Checking for duplicate columns")
    if df_ft.columns.duplicated().any():
        duplicate_cols = df_ft.columns[df_ft.columns.duplicated()].tolist()
        print(f"[{datetime.datetime.now()}] ⚠️ Found duplicate columns: {duplicate_cols}")
        # ลบ duplicate columns โดยเก็บคอลัมน์แรก
        df_ft = df_ft.loc[:, ~df_ft.columns.duplicated()]
        print(f"[{datetime.datetime.now()}] ✅ Removed duplicate columns")

    print(f"[{datetime.datetime.now()}] ✅ Final columns: {list(df_ft.columns)}")

    # ตรวจสอบว่าเปลี่ยนชื่อคอลัมน์หลักสำเร็จหรือไม่
    required_price_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_cols = [col for col in required_price_cols if col not in df_ft.columns]
    if missing_cols:
        print(f"❌ Error: Missing required price columns after renaming: {missing_cols}")
        # Print คอลัมน์ที่มีอยู่เพื่อ Debug
        print(f"Available columns in df_ft: {df_ft.columns.tolist()}")
        return # หยุดการประมวลผลถ้าคอลัมน์หลักไม่ครบ
    print(f"[{datetime.datetime.now()}] ✅ All required price columns present")
    # --- จบการแก้ไข ---

    # --- ตรวจสอบจำนวนข้อมูลที่เพียงพอสำหรับการคำนวณ Indicator ---
    required_bars = 210 # *** ต้องตรงกับ BARS_FOR_PYTHON ใน MQL5 ***
    if len(df_ft) < required_bars:
        print(f"[{datetime.datetime.now()}] Not enough data ({len(df_ft)} bars) for {symbol} (enum: {timeframe}). Minimum required: {required_bars}. Skipping processing.")
        # *** ถ้าข้อมูลน้อยกว่าที่ต้องการ อาจจะคืนค่า Signal เป็น HOLD/ERROR ***

        signal = "HOLD"
        probability_tp_hit = 0.0
        entry_price = 0.0
        sl_price = 0.0 # Default SL
        tp_price = 0.0 # Default TP

        with signals_lock:
            signals_key = (symbol, timeframe)
            latest_signals_data[signals_key] = {
                "symbol": symbol,
                "timeframe_enum": timeframe,
                "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'),
                "signal": signal,
                "confidence": float(probability_tp_hit),
                "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE),
                "sl_price": sl_price, # เพิ่ม SL เข้าไป
                "tp_price": tp_price  # เพิ่ม TP เข้าไป
            }

        print(f"[{datetime.datetime.now()}] Stored default signal due to insufficient data for {symbol} (enum: {timeframe}).")
        return # หยุดการประมวลผล

    signal = "HOLD" # Default signal
    probability_tp_hit = 0.0 # Default confidence
    entry_price = 0.0
    sl_price = 0.0
    tp_price = 0.0

    # --- โหลดโมเดลและพารามิเตอร์ ---
    if USE_MULTI_MODEL_ARCHITECTURE:
        # โหลด Multi-Model components
        scenario_models = load_model_components(symbol, timeframe)
        time_filters = load_time_filters(symbol, timeframe)

        # สำหรับ Multi-Model จะใช้ default values ก่อน แล้วจะปรับตาม scenario ในภายหลัง
        model_confidence_threshold = 0.5  # Default threshold
        num_nBars_SL = 6  # Default nBars_SL

        if not scenario_models:
            print(f"❌ ไม่สามารถโหลด Multi-Model components สำหรับ {symbol} M{timeframe}")
            return

        print(f"✅ โหลด Multi-Model components สำเร็จ: {len(scenario_models)} scenarios")

    else:
        # โหลด Single Model components (ปรับปรุงใหม่)
        model, scaler, features_list = load_model_components(symbol, timeframe)

        # ใช้ฟังก์ชันใหม่สำหรับ Single Model
        time_filters = load_single_model_time_filters(symbol, timeframe)
        model_confidence_threshold = load_single_model_threshold(symbol, timeframe)
        num_nBars_SL = load_single_model_nbars(symbol, timeframe)

        if model is None or scaler is None or features_list is None:
            print(f"❌ ไม่สามารถโหลด Single Model components สำหรับ {symbol} M{timeframe}")
            return

        print(f"✅ โหลด Single Model components สำเร็จ")
        print(f"   Model: {type(model).__name__}")
        print(f"   Features: {len(features_list)} features")
        print(f"   Threshold: {model_confidence_threshold}")
        print(f"   nBars_SL: {num_nBars_SL}")

    try:
        print(f"[{datetime.datetime.now()}] 🔄 Starting indicators calculation")
        # --- 1. คำนวณ Indicators และสร้าง Features ทั้งหมดที่ใช้ในโมเดล ---
        # คัดลอก Logic จาก load_and_process_data ส่วน "3. สร้าง technical indicators" มาที่นี่
        # ใช้ df_ft ที่เปลี่ยนชื่อคอลัมน์เป็นตัวพิมพ์ใหญ่แล้ว

        # แสดงผล
        # print("\n✅ ข้อมูล : df_ft")
        # print(df_ft.info())
        # print(df_ft.head())
        # print(df_ft.tail())

        # --- เพิ่มฟีเจอร์วันและเวลา ---
        # ใน Server, Bar Time มาเป็น datetime index อยู่แล้ว
        df_ft['DateTime'] = df_ft.index

        # แปลง timezone ออก (ถ้ามี) ก่อน
        # df_ft['DateTime'] = df_ft['DateTime'].dt.tz_localize(None)

        # คอลัมน์ Date (YYYY.MM.DD)
        df_ft['Date'] = df_ft['DateTime'].dt.strftime("%Y.%m.%d")

        # คอลัมน์ Time (HH:MM:SS)
        df_ft['Time'] = df_ft['DateTime'].dt.strftime("%H:%M:%S")

        # คอลัมน์ DateTime แบบ dd/MM/yy HH:MM
        # df_ft['DateTime'] = df_ft['DateTime'].dt.strftime("%y/%m/%d %H:%M") # %Y.%m.%d %H:%M:%S
        # df_ft['DateTime'] = pd.to_datetime(df['DateTime'])

        # ✅ จัดลำดับคอลัมน์ใหม่ให้ตรงกับไฟล์ train
        # df_ft = df_ft[['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime']]

        # ==============================================
        if Save_Data_file:
            new_file_name = f"{timeframe}_{symbol}_Server_00_df.csv"
            new_file_path = os.path.join(test_csv, new_file_name)
            df_ft.to_csv(new_file_path, index=False)
            print(f"\n✅ Saved cleaned file to: {new_file_path}")
            # ==============================================
            print(f'🔍 ตรวจสอบ columns ข้อมูล df : จำนวน {len(df_ft.columns)}')
            print(df_ft.columns.tolist())
        # ==============================================

        print(f"ตรวจสอบค่า ก่อนส่ง : symbol {symbol} timeframe {timeframe} {type(timeframe)} num_nBars_SL {num_nBars_SL} {type(num_nBars_SL)}")
        df_ft = create_features(df_ft, symbol, timeframe, num_nBars_SL)

        # ==============================================

        # ตรวจสอบการโหลดโมเดลตาม Architecture ที่ใช้
        if USE_MULTI_MODEL_ARCHITECTURE:
            if scenario_models is None:
                print(f"[{datetime.datetime.now()}] Failed to load Multi-Model components for {symbol} ({timeframe}). Cannot make prediction.")
                return
        else:
            if model is None or scaler is None or features_list is None:
                print(f"[{datetime.datetime.now()}] Failed to load Single Model components for {symbol} ({timeframe}). Cannot make prediction.")
                return

        # --- 3. เตรียมข้อมูล และทำนายตาม Architecture ---
        if USE_MULTI_MODEL_ARCHITECTURE:
            # === Multi-Model Architecture Prediction ===
            try:
                # เตรียมข้อมูลสำหรับการทำนาย (ใช้แท่งก่อนสุดท้าย)
                latest_bar_for_prediction = df_ft.iloc[-2:-1]  # แท่งก่อนสุดท้าย
                latest_bar_for_decision = df_ft.tail(1)  # แท่งสุดท้าย

                # สร้าง Series สำหรับการตรวจจับ scenario
                prediction_row = latest_bar_for_prediction.iloc[0]

                # ตรวจจับสถานการณ์ตลาด
                market_condition = detect_market_scenario(prediction_row)
                print(f"🔍 Market Condition: {market_condition}")

                # *** ปรับปรุงใหม่: ตรวจสอบทั้ง 2 แบบตามสถานการณ์ตลาด ***
                # กรณี uptrend: ตรวจสอบ trend_following (buy) และ counter_trend (sell)
                # กรณี downtrend: ตรวจสอบ trend_following (sell) และ counter_trend (buy)

                # เก็บผลการวิเคราะห์ทั้ง 2 แบบ
                analysis_results = {
                    'trend_following': {'buy': None, 'sell': None},
                    'counter_trend': {'buy': None, 'sell': None}
                }

                # *** คำนวณ confidence สำหรับแต่ละ scenario แยกต่างหาก ***
                print(f"\n🔄 Calculating individual scenario confidence...")
                print(f"🔍 DEBUG - Available scenarios: {list(scenario_models.keys())}")

                # คำนวณ confidence สำหรับ trend_following
                if 'trend_following' in scenario_models:
                    tf_model = scenario_models['trend_following']['model']
                    tf_features = scenario_models['trend_following']['features']
                    tf_scaler = scenario_models['trend_following']['scaler']

                    # เตรียมข้อมูลสำหรับ trend_following
                    tf_feature_values = []
                    for feature in tf_features:
                        if feature in prediction_row.index:
                            tf_feature_values.append(prediction_row[feature])
                        else:
                            tf_feature_values.append(0.0)

                    tf_feature_array = np.array(tf_feature_values).reshape(1, -1)
                    if tf_scaler is not None:
                        tf_scaled_features = tf_scaler.transform(tf_feature_array)
                    else:
                        tf_scaled_features = tf_feature_array

                    # ทำนายด้วย trend_following model
                    tf_prediction_proba = tf_model.predict_proba(tf_scaled_features)[0]

                    # คำนวณ confidence สำหรับ BUY และ SELL
                    if len(tf_prediction_proba) >= 5:
                        tf_buy_conf = tf_prediction_proba[3] + tf_prediction_proba[4]  # weak_buy + strong_buy
                        tf_sell_conf = tf_prediction_proba[0] + tf_prediction_proba[1]  # strong_sell + weak_sell
                    else:
                        tf_buy_conf = tf_prediction_proba[1] if len(tf_prediction_proba) > 1 else 0.0
                        tf_sell_conf = tf_prediction_proba[0] if len(tf_prediction_proba) > 0 else 0.0

                    analysis_results['trend_following']['buy'] = {
                        'should_trade': tf_buy_conf > 0.5,
                        'confidence': tf_buy_conf,
                        'details': f'TF_BUY_Prob:{tf_buy_conf:.4f}'
                    }
                    analysis_results['trend_following']['sell'] = {
                        'should_trade': tf_sell_conf > 0.5,
                        'confidence': tf_sell_conf,
                        'details': f'TF_SELL_Prob:{tf_sell_conf:.4f}'
                    }

                    print(f"📊 Trend Following - BUY: {tf_buy_conf:.4f}, SELL: {tf_sell_conf:.4f}")

                # คำนวณ confidence สำหรับ counter_trend
                if 'counter_trend' in scenario_models:
                    ct_model = scenario_models['counter_trend']['model']
                    ct_features = scenario_models['counter_trend']['features']
                    ct_scaler = scenario_models['counter_trend']['scaler']

                    # เตรียมข้อมูลสำหรับ counter_trend
                    ct_feature_values = []
                    for feature in ct_features:
                        if feature in prediction_row.index:
                            ct_feature_values.append(prediction_row[feature])
                        else:
                            ct_feature_values.append(0.0)

                    ct_feature_array = np.array(ct_feature_values).reshape(1, -1)
                    if ct_scaler is not None:
                        ct_scaled_features = ct_scaler.transform(ct_feature_array)
                    else:
                        ct_scaled_features = ct_feature_array

                    # ทำนายด้วย counter_trend model
                    ct_prediction_proba = ct_model.predict_proba(ct_scaled_features)[0]

                    # คำนวณ confidence สำหรับ BUY และ SELL
                    if len(ct_prediction_proba) >= 5:
                        ct_buy_conf = ct_prediction_proba[3] + ct_prediction_proba[4]  # weak_buy + strong_buy
                        ct_sell_conf = ct_prediction_proba[0] + ct_prediction_proba[1]  # strong_sell + weak_sell
                    else:
                        ct_buy_conf = ct_prediction_proba[1] if len(ct_prediction_proba) > 1 else 0.0
                        ct_sell_conf = ct_prediction_proba[0] if len(ct_prediction_proba) > 0 else 0.0

                    analysis_results['counter_trend']['buy'] = {
                        'should_trade': ct_buy_conf > 0.5,
                        'confidence': ct_buy_conf,
                        'details': f'CT_BUY_Prob:{ct_buy_conf:.4f}'
                    }
                    analysis_results['counter_trend']['sell'] = {
                        'should_trade': ct_sell_conf > 0.5,
                        'confidence': ct_sell_conf,
                        'details': f'CT_SELL_Prob:{ct_sell_conf:.4f}'
                    }

                    print(f"📊 Counter Trend - BUY: {ct_buy_conf:.4f}, SELL: {ct_sell_conf:.4f}")

                # *** ใช้ Enhanced Model Decision Functions ***
                print(f"\n🔄 Using Enhanced Model Decision with Target_Buy/Target_Sell confirmation...")

                # ==============================================

                # โหลดค่าทั้ง 6 scenarios
                scenarios = [
                    "trend_following", "trend_following_Buy", "trend_following_Sell",
                    "counter_trend", "counter_trend_Buy", "counter_trend_Sell"
                ]

                print(f"\nsymbol {symbol} timeframe {timeframe}\n")

                params = {}
                for sc in scenarios:
                    params[sc] = get_scenario_params(symbol, timeframe, sc)

                # ตัวอย่างใช้งาน
                response_trend_following_threshold = params["trend_following"]["threshold"]
                response_trend_following_nbars = params["trend_following"]["nbars"]
                response_trend_following_buy_nbars = params["trend_following_Buy"]["nbars"]
                response_trend_following_sell_nbars = params["trend_following_Sell"]["nbars"]

                print(f"\ntrend_following : threshold {response_trend_following_threshold}")
                print(f"trend_following : nbars {response_trend_following_nbars} buy {response_trend_following_buy_nbars} sell {response_trend_following_sell_nbars}")

                response_counter_trend_threshold = params["counter_trend"]["threshold"]
                response_counter_trend_nbars = params["counter_trend"]["nbars"]
                response_counter_trend_buy_nbars = params["counter_trend_Buy"]["nbars"]
                response_counter_trend_sell_nbars = params["counter_trend_Sell"]["nbars"]

                print(f"\ncounter_trend : threshold {response_counter_trend_threshold}")
                print(f"counter_trend : nbars {response_counter_trend_nbars} buy {response_counter_trend_buy_nbars} sell {response_counter_trend_sell_nbars}")

                # ==============================================

                # ทำนาย BUY ด้วยฟังก์ชันใหม่
                should_trade_buy, confidence_buy, details_buy, buy_scenario_used = enhanced_model_decision_buy(
                    scenario_models, prediction_row, symbol, timeframe,
                    response_trend_following_threshold, response_counter_trend_threshold
                )

                # ทำนาย SELL ด้วยฟังก์ชันใหม่
                should_trade_sell, confidence_sell, details_sell, sell_scenario_used = enhanced_model_decision_sell(
                    scenario_models, prediction_row, symbol, timeframe,
                    response_trend_following_threshold, response_counter_trend_threshold
                )

                print(f"\n📊 Enhanced Decision Results:")
                print(f"   BUY: {should_trade_buy} (Confidence: {confidence_buy:.4f}) - {details_buy}")
                print(f"   SELL: {should_trade_sell} (Confidence: {confidence_sell:.4f}) - {details_sell}")

                # เก็บผลลัพธ์สำหรับการตัดสินใจขั้นสุดท้าย
                analysis_results['enhanced'] = {
                    'buy': {
                        'should_trade': should_trade_buy,
                        'confidence': confidence_buy,
                        'details': details_buy
                    },
                    'sell': {
                        'should_trade': should_trade_sell,
                        'confidence': confidence_sell,
                        'details': details_sell
                    }
                }

                # *** เลือก signal ที่ดีที่สุดจาก Enhanced Decision ***
                best_signal = "HOLD"
                best_confidence = 0.0
                best_model_used = "Enhanced_Target_Model"
                best_scenario = "enhanced"
                best_action = "none"

                # ตรวจสอบผลลัพธ์จาก Enhanced Decision
                enhanced_buy = analysis_results['enhanced']['buy']
                enhanced_sell = analysis_results['enhanced']['sell']

                print(f"\n🎯 Final Enhanced Decision Analysis:")
                print(f"   BUY Decision: {enhanced_buy['should_trade']} (Confidence: {enhanced_buy['confidence']:.4f})")
                print(f"   SELL Decision: {enhanced_sell['should_trade']} (Confidence: {enhanced_sell['confidence']:.4f})")

                # เลือก signal ที่ดีที่สุดและกำหนด scenario ที่ใช้
                selected_scenario = "none"
                if enhanced_buy['should_trade'] and enhanced_sell['should_trade']:
                    # ถ้าทั้งคู่เป็น True ให้เลือกตาม confidence ที่สูงกว่า
                    if enhanced_buy['confidence'] >= enhanced_sell['confidence']:
                        best_signal = "BUY"
                        best_confidence = enhanced_buy['confidence']
                        best_action = "buy"
                        selected_scenario = buy_scenario_used
                        print(f"🟢 Selected BUY (Higher confidence: {best_confidence:.4f}) - Scenario: {selected_scenario}")
                    else:
                        best_signal = "SELL"
                        best_confidence = enhanced_sell['confidence']
                        best_action = "sell"
                        selected_scenario = sell_scenario_used
                        print(f"🔴 Selected SELL (Higher confidence: {best_confidence:.4f}) - Scenario: {selected_scenario}")
                elif enhanced_buy['should_trade']:
                    best_signal = "BUY"
                    best_confidence = enhanced_buy['confidence']
                    best_action = "buy"
                    selected_scenario = buy_scenario_used
                    print(f"🟢 Selected BUY (Confidence: {best_confidence:.4f}) - Scenario: {selected_scenario}")
                elif enhanced_sell['should_trade']:
                    best_signal = "SELL"
                    best_confidence = enhanced_sell['confidence']
                    best_action = "sell"
                    selected_scenario = sell_scenario_used
                    print(f"🔴 Selected SELL (Confidence: {best_confidence:.4f}) - Scenario: {selected_scenario}")
                else:
                    best_signal = "HOLD"
                    best_confidence = max(enhanced_buy['confidence'], enhanced_sell['confidence'])
                    best_action = "hold"
                    # สำหรับ HOLD ใช้ scenario ที่มี confidence สูงกว่า
                    selected_scenario = buy_scenario_used if enhanced_buy['confidence'] >= enhanced_sell['confidence'] else sell_scenario_used
                    print(f"⚪ Selected HOLD (No conditions met, Max confidence: {best_confidence:.4f})")
                # Enhanced Decision ได้ทำการตัดสินใจแล้ว

                # กำหนดค่าสำหรับการส่งต่อ
                predicted_signal = best_signal
                probability_tp_hit = best_confidence
                model_used = best_model_used
                scenario_name = selected_scenario  # ใช้ scenario ที่เลือกจริงจาก enhanced decision
                print(f"🔍 DEBUG - Final scenario assignment:")
                print(f"   selected_scenario: {selected_scenario}")
                print(f"   scenario_name: {scenario_name}")
                action_type = best_action

                if predicted_signal == "BUY":
                    prediction_class = 3
                elif predicted_signal == "SELL":
                    prediction_class = 1
                else:
                    prediction_class = 2

                print(f"🤖 Final Multi-Model Decision: {predicted_signal} (Confidence: {probability_tp_hit:.4f}, Scenario: {scenario_name})")

                # *** 🔧 แก้ไขปัญหา: ตรวจสอบว่า probability_tp_hit ไม่เป็น 0.0 ***
                if probability_tp_hit == 0.0 and predicted_signal != "HOLD":
                    print(f"⚠️ WARNING: probability_tp_hit is 0.0 but signal is {predicted_signal}")
                    print(f"   This will cause MT5 to reject the trade!")
                    print(f"   best_confidence was: {best_confidence}")
                    print(f"   Checking analysis_results for actual confidence values...")

                    # ตรวจสอบค่า confidence จริงจาก analysis_results
                    if 'analysis_results' in locals():
                        for scenario in ['trend_following', 'counter_trend']:
                            if scenario in analysis_results:
                                for action in ['buy', 'sell']:
                                    if action in analysis_results[scenario] and analysis_results[scenario][action]:
                                        result = analysis_results[scenario][action]
                                        conf = result.get('confidence', 0.0)
                                        should_trade = result.get('should_trade', False)
                                        print(f"   {scenario}_{action}: confidence={conf:.4f}, should_trade={should_trade}")

                # *** ใหม่: โหลดพารามิเตอร์ตาม scenario ที่เลือกจาก Enhanced Decision ***
                print(f"\n🎯 Loading parameters for selected scenario: {selected_scenario}")

                if selected_scenario != "none" and selected_scenario in ["trend_following", "counter_trend"]:
                    try:
                        # โหลดพารามิเตอร์จาก scenario ที่เลือกจริง
                        data_threshold = load_scenario_threshold(symbol, timeframe, selected_scenario)
                        scenario_threshold = data_threshold['best_threshold']
                        scenario_nbars = load_scenario_nbars(symbol, timeframe, selected_scenario)

                        model_confidence_threshold = scenario_threshold
                        num_nBars_SL = scenario_nbars

                        print(f"✅ Using {selected_scenario} parameters:")
                        print(f"   📊 Threshold: {model_confidence_threshold:.4f}")
                        print(f"   📊 nBars_SL: {num_nBars_SL}")

                    except Exception as e:
                        print(f"⚠️ Error loading {selected_scenario} parameters: {e}")
                        print(f"⚠️ Falling back to enhanced (average) parameters")

                        # Fallback: ใช้ค่าเฉลี่ยจาก trend_following และ counter_trend
                        try:
                            tf_data = load_scenario_threshold(symbol, timeframe, "trend_following")
                            ct_data = load_scenario_threshold(symbol, timeframe, "counter_trend")
                            tf_threshold = tf_data['best_threshold']
                            ct_threshold = ct_data['best_threshold']
                            scenario_threshold = (tf_threshold + ct_threshold) / 2

                            tf_nbars = load_scenario_nbars(symbol, timeframe, "trend_following")
                            ct_nbars = load_scenario_nbars(symbol, timeframe, "counter_trend")
                            scenario_nbars = int((tf_nbars + ct_nbars) / 2)

                            model_confidence_threshold = scenario_threshold
                            num_nBars_SL = scenario_nbars

                            print(f"🎯 Using enhanced (fallback) parameters:")
                            print(f"   📊 Threshold: {model_confidence_threshold:.4f} (avg of TF:{tf_threshold:.3f} + CT:{ct_threshold:.3f})")
                            print(f"   📊 nBars_SL: {num_nBars_SL} (avg of TF:{tf_nbars} + CT:{ct_nbars})")

                        except Exception as e2:
                            print(f"⚠️ ไม่สามารถคำนวณค่าเฉลี่ยสำหรับ enhanced: {e2}")
                            model_confidence_threshold = 0.25  # ค่า default
                            num_nBars_SL = 4  # ค่า default
                            print(f"🎯 Using default parameters:")
                            print(f"   📊 Threshold: {model_confidence_threshold}")
                            print(f"   📊 nBars_SL: {num_nBars_SL}")

                else:
                    # ไม่มี scenario ที่เลือก ใช้ค่า default
                    print(f"⚠️ No valid scenario selected ({selected_scenario}), using default parameters")
                    model_confidence_threshold = 0.25
                    num_nBars_SL = 4
                    print(f"🎯 Using default parameters:")
                    print(f"   📊 Threshold: {model_confidence_threshold}")
                    print(f"   📊 nBars_SL: {num_nBars_SL}")

                # ดึง Timestamp ของแท่งล่าสุด
                latest_bar_dt = latest_bar_for_decision['DateTime'].iloc[0]

            except Exception as e:
                print(f"❌ Error in Multi-Model prediction: {e}")
                traceback.print_exc()
                return

        else:
            # === Single Model Architecture Prediction (Legacy) ===
            try:
                if df_ft.empty:
                    print(f"Warning: df_ft is empty when attempting to select features.")
                    return

                if not isinstance(features_list, (list, tuple)) or not features_list:
                        print(f"Error: Loaded features_list is invalid or empty: {features_list}")
                        return

                missing_cols = [col for col in features_list if col not in df_ft.columns]
                if missing_cols:
                    print(f"Error: Missing required feature columns: {missing_cols}")
                    return

                # เตรียม DataFrame สำหรับทำนาย (ใช้แท่งก่อนสุดท้าย)
                prediction_features_df = df_ft.iloc[-2:-1][features_list]
                latest_bar_for_decision_df = df_ft.tail(1)

                # Scale features
                features_array = prediction_features_df.values
                scaled_features_array = scaler.transform(features_array)
                scaled_features_df = pd.DataFrame(scaled_features_array, columns=features_list, index=prediction_features_df.index)

                # ทำนายด้วย Single Model
                prediction_proba_all = model.predict_proba(scaled_features_df)
                prediction_class = model.predict(scaled_features_df)[0]
                probability_tp_hit = prediction_proba_all[0].max()

                # แปลง class เป็น signal
                class_to_signal = {0: "STRONG_SELL", 1: "SELL", 2: "HOLD", 3: "BUY", 4: "STRONG_BUY"}
                predicted_signal = class_to_signal.get(prediction_class, "HOLD")

                # ดึง Timestamp ของแท่งล่าสุด
                latest_bar_dt = latest_bar_for_decision_df.index[0]

                print(f"📊 Single Model Prediction: Class={prediction_class}, Signal={predicted_signal}, Confidence={probability_tp_hit:.4f}")

            except Exception as e:
                print(f"❌ Error in Single Model prediction: {e}")
                traceback.print_exc()
                return

        # แปลง class เป็น signal
        # Class mapping: 0=Strong_Sell, 1=Sell, 2=Hold, 3=Buy, 4=Strong_Buy
        class_to_signal = {0: "STRONG_SELL", 1: "SELL", 2: "HOLD", 3: "BUY", 4: "STRONG_BUY"}
        predicted_signal = class_to_signal.get(prediction_class, "HOLD")

        # print(f"\n✅ Multi-class prediction: Class={prediction_class}, Signal={predicted_signal}, Confidence={probability_tp_hit:.4f}")

        # --- 5. ตัดสินใจ Signal ---
        signal = "HOLD" # Default
        waiting_for = "" # เพิ่มตัวแปรสำหรับเก็บข้อมูลการรอคอย

        # ดึงข้อมูลแถวสุดท้าย (ล่าสุด) ที่มีค่า Feature ครบถ้วน
        latest_bar_features_df_all_i2 = df_ft.tail(2) # ดึงทั้งหมดที่มีใน df_ft
        latest_features_dict_all_i2 = latest_bar_features_df_all_i2.iloc[0].to_dict()
        # print(f"\n✅ latest features dict (dictionary): {latest_features_dict_all_i2}") # แสดง Feature ที่เลือกใช้ (จะเห็นเป็น dictionary แล้ว)

        # ดึงข้อมูลแถวสุดท้าย (ล่าสุด) ที่มีค่า Feature ครบถ้วน
        latest_bar_features_df_all_i1 = df_ft.tail(1) # ดึงทั้งหมดที่มีใน df_ft
        latest_features_dict_all_i1 = latest_bar_features_df_all_i1.iloc[0].to_dict()
        # print(f"\n✅ latest features dict (dictionary): {latest_features_dict_all_i1}") # แสดง Feature ที่เลือกใช้ (จะเห็นเป็น dictionary แล้ว)

        # ตรวจอบช่วงเวลา ที่มีประสิทธิภาพสูงสุด
        # print(f"ทดสอบโหลดฟิวเตอร์ time_filters")
        # time_filters = load_time_filters(symbol, timeframe)

        hour = latest_features_dict_all_i1['Hour']
        day_of_week = latest_features_dict_all_i1['DayOfWeek']
        
        # ป้องกันกรณี time_filters ไม่มี key หรือเป็น list ว่าง
        days_filter = time_filters.get('days', list(range(7)))
        hours_filter = time_filters.get('hours', list(range(24)))
        if not days_filter:
            days_filter = list(range(7))
        if not hours_filter:
            hours_filter = list(range(24))

        # ตรวจสอบเงื่อนไข time_filters
        if ENABLE_TIME_FILTERS:
            time_condition = (
                6 <= latest_features_dict_all_i1['Hour'] <= 20 and
                day_of_week in days_filter and
                hour in hours_filter
            )
            print(f"[{datetime.datetime.now()}] ⏰ Time filters enabled - Day: {day_of_week} in {days_filter}, Hour: {hour} in {hours_filter}, Condition: {time_condition}")
        else:
            time_condition = True  # เทรดได้ตลอดเวลา
            print(f"[{datetime.datetime.now()}] ⏰ Time filters disabled - Trading allowed anytime")

        # print(f"\n✅ แสดงข้อมูลที่ model tp_hit {probability_tp_hit} time {time_condition}")
        # print(f"\n✅ แสดงข้อมูลที่ close {latest_features_dict_all_i2.get('Close', -float('inf'))} open {latest_features_dict_all_i2.get('Open', float('inf'))}")

        # ตรวจสอบเงื่อนไขทางเทคนิคสำหรับ Multi-Model Architecture
        # ⚠️ ใช้เงื่อนไขตาม 4 scenarios จาก python_LightGBM_19_Gemini.py

        # ตรวจสอบสถานการณ์ตลาด
        close = latest_features_dict_all_i2.get('Close', 0.0)
        ema200 = latest_features_dict_all_i2.get('EMA200', close)
        rsi14 = latest_features_dict_all_i2.get('RSI14', 50.0)

        # กำหนดเงื่อนไขตาม scenario ที่ใช้ (ให้ตรงกับ training model)
        print(f"🔍 DEBUG Technical Conditions Setup:")
        print(f"   USE_MULTI_MODEL_ARCHITECTURE: {USE_MULTI_MODEL_ARCHITECTURE}")
        print(f"   scenario_name in locals(): {'scenario_name' in locals()}")
        if 'scenario_name' in locals():
            print(f"   scenario_name: {scenario_name}")
        print(f"   predicted_signal: {predicted_signal}")

        if USE_MULTI_MODEL_ARCHITECTURE and 'scenario_name' in locals():
            if scenario_name == "trend_following":
                # === Trend Following Conditions (ตรงกับ training) ===
                if predicted_signal in ["BUY", "STRONG_BUY"]:
                    # Trend Following BUY: close > EMA200
                    tech_signal_buy_conditions = {
                        'Close > Open (Prev Bar)': close > latest_features_dict_all_i2.get('Open', float('inf')),
                        'Close > EMA200 (Trend Following)': close > ema200,
                        'RSI > input_rsi_level_in AND RSI < input_rsi_level_over': rsi14 > (input_rsi_level_in * 1.0) and rsi14 < (input_rsi_level_over * 1.0),
                        'prev_ratio_buy > input_take_profit': latest_features_dict_all_i2.get('Ratio_Buy', 0.0) > input_take_profit
                    }
                else:
                    # Trend Following SELL: close < EMA200
                    tech_signal_sell_conditions = {
                        'Close < Open (Prev Bar)': close < latest_features_dict_all_i2.get('Open', -float('inf')),
                        'Close < EMA200 (Following Trend)': close < ema200,
                        'RSI < (100-input_rsi_level_in) AND RSI > (100-input_rsi_level_over)': rsi14 < (100 - (input_rsi_level_in * 1.0)) and rsi14 > (100 - (input_rsi_level_over * 1.0)),
                        'prev_ratio_sell > input_take_profit': latest_features_dict_all_i2.get('Ratio_Sell', 0.0) > input_take_profit
                    }
            elif scenario_name == "counter_trend":
                print(f"🔍 DEBUG: Entering counter_trend scenario")
                # === Counter Trend Conditions (ตรงกับ training) ===
                if predicted_signal in ["BUY", "STRONG_BUY"]:
                    print(f"🔍 DEBUG: Setting up counter_trend BUY conditions")
                    # Counter Trend BUY: close < EMA200 แต่ซื้อ (สวนเทรน)
                    tech_signal_buy_conditions = {
                        'Close > Open (Prev Bar)': close > latest_features_dict_all_i2.get('Open', float('inf')),
                        'Close < EMA200 (Below Trend)': close < ema200,
                        'RSI > input_rsi_level_in AND RSI < input_rsi_level_over': rsi14 > (input_rsi_level_in * 1.0) and rsi14 < (input_rsi_level_over * 1.0),
                        'prev_ratio_buy > input_take_profit': latest_features_dict_all_i2.get('Ratio_Buy', 0.0) > input_take_profit
                    }
                else:
                    print(f"🔍 DEBUG: Setting up counter_trend SELL conditions")
                    # Counter Trend SELL: close < EMA200 (ตามเทรน)
                    tech_signal_sell_conditions = {
                        'Close < Open (Prev Bar)': close < latest_features_dict_all_i2.get('Open', -float('inf')),
                        'Close > EMA200 (Still Above Trend)': close > ema200,
                        'RSI < (100-input_rsi_level_in) AND RSI > (100-input_rsi_level_over)': rsi14 < (100 - (input_rsi_level_in * 1.0)) and rsi14 > (100 - (input_rsi_level_over * 1.0)),
                        'prev_ratio_sell > input_take_profit': latest_features_dict_all_i2.get('Ratio_Sell', 0.0) > input_take_profit
                    }
        else:
            # === Fallback: เงื่อนไขเดิมสำหรับ Single Model หรือกรณีไม่มี scenario ===
            tech_signal_buy_conditions = {
                'Close > Open (Prev Bar)': close > latest_features_dict_all_i2.get('Open', float('inf')),
                'RSI > input_rsi_level_in AND < input_rsi_level_over': rsi14 > (input_rsi_level_in * 1.0) and rsi14 < (input_rsi_level_over * 1.0),
                'MACD_signal == 1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == 1.0,
                'Volume > Volume_MA20 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.5,
                'prev_pullback_buy > input_pull_back': latest_features_dict_all_i2.get('PullBack_Up', 0.0) > (input_pull_back * 0.5),
                'prev_ratio_buy > input_take_profit': latest_features_dict_all_i2.get('Ratio_Buy', 0.0) > input_take_profit
            }

            tech_signal_sell_conditions = {
                'Close < Open (Prev Bar)': close < latest_features_dict_all_i2.get('Open', -float('inf')),
                'RSI < (100-input_rsi_level_in) AND > (100-input_rsi_level_over)': rsi14 < (100 - input_rsi_level_in * 1.0) and rsi14 > (100 - input_rsi_level_over * 1.0),
                'MACD_signal == -1.0 (Prev Bar)': latest_features_dict_all_i2.get('MACD_signal', 0.0) == -1.0,
                'Volume > Volume_MA20 (Prev Bar)': latest_features_dict_all_i2.get('Volume', 0.0) > latest_features_dict_all_i2.get('Volume_MA20', 0.0) * 0.5,
                'prev_pullback_sell > input_pull_back': latest_features_dict_all_i2.get('PullBack_Down', 0.0) > (input_pull_back * 0.5),
                'prev_ratio_sell > input_take_profit': latest_features_dict_all_i2.get('Ratio_Sell', 0.0) > input_take_profit
            }

        # กำหนดค่าเริ่มต้นสำหรับ conditions ที่ไม่ได้ใช้
        if 'tech_signal_buy_conditions' not in locals():
            tech_signal_buy_conditions = {}
        if 'tech_signal_sell_conditions' not in locals():
            tech_signal_sell_conditions = {}

        tech_signal_buy = all(tech_signal_buy_conditions.values()) if tech_signal_buy_conditions else False
        tech_signal_sell = all(tech_signal_sell_conditions.values()) if tech_signal_sell_conditions else False

        # 🔍 แสดงผลการตรวจสอบเงื่อนไขทางเทคนิค
        print(f"\n🔍 Technical Analysis Check for {symbol} M{timeframe}:")
        print(f"📊 Model Prediction: {predicted_signal} (Confidence: {probability_tp_hit:.4f})")
        print(f"⚙️ Technical Conditions: {'ENABLED' if ENABLE_TECHNICAL_CONDITIONS else 'DISABLED'}")

        # # แสดงค่าจริงของ features ที่สำคัญ
        # print(f"📈 Key Features Values:")
        # print(f"   Open: {latest_features_dict_all_i2.get('Open', 'N/A')}")
        # print(f"   Close: {latest_features_dict_all_i2.get('Close', 'N/A')}")
        # print(f"   Volume: {latest_features_dict_all_i2.get('Volume', 'N/A')}")
        # print(f"   Volume_MA20: {latest_features_dict_all_i2.get('Volume_MA20', 'N/A')}")
        # print(f"   EMA50: {latest_features_dict_all_i2.get('EMA50', 'N/A')}")
        # print(f"   EMA200: {latest_features_dict_all_i2.get('EMA200', 'N/A')}")
        # print(f"   RSI14: {latest_features_dict_all_i2.get('RSI14', 'N/A')}")
        # print(f"   MACD: {latest_features_dict_all_i2.get('MACD_12_26_9', 'N/A')}")
        # print(f"   MACD signal: {latest_features_dict_all_i2.get('MACDs_12_26_9', 'N/A')}")
        # print(f"   MACD histogram: {latest_features_dict_all_i2.get('MACDh_12_26_9', 'N/A')}")
        # print(f"   PullBack_Up: {latest_features_dict_all_i2.get('PullBack_Up', 'N/A')}")
        # print(f"   PullBack_Down: {latest_features_dict_all_i2.get('PullBack_Down', 'N/A')}")
        # print(f"   Ratio_Buy: {latest_features_dict_all_i2.get('Ratio_Buy', 'N/A')}")
        # print(f"   Ratio_Sell: {latest_features_dict_all_i2.get('Ratio_Sell', 'N/A')}")

        # 📁 ใช้ get_log_path เพื่อได้ชื่อไฟล์ log เดียวกัน
        log_path = get_log_path(symbol, timeframe)

        # 🕒 เวลาปัจจุบันในรูปแบบ "[YYYY-MM-DD HH:MM:SS]"
        now_str = datetime.datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
        # 🧾 สร้างบรรทัดแสดง Symbol และ Timeframe
        log_header_line = f"{now_str} Symbol: {symbol}, Timeframe: {timeframe}"

        # 📝 เตรียมข้อความ log จาก features
        feature_log_lines = [
            log_header_line,
            "📈 Key Features Values:",
            f"   Date: {latest_features_dict_all_i2.get('Date', 'N/A')} {latest_features_dict_all_i2.get('Time', 'N/A')}",
            f"   Open: {latest_features_dict_all_i2.get('Open', 'N/A')}",
            f"   Close: {latest_features_dict_all_i2.get('Close', 'N/A')}",
            f"   Volume: {latest_features_dict_all_i2.get('Volume', 'N/A')}",
            f"   Volume_MA20: {latest_features_dict_all_i2.get('Volume_MA20', 'N/A')}",
            f"   EMA50: {latest_features_dict_all_i2.get('EMA50', 'N/A')}",
            f"   EMA200: {latest_features_dict_all_i2.get('EMA200', 'N/A')}",
            f"   RSI14: {latest_features_dict_all_i2.get('RSI14', 'N/A')}",
            f"   MACD: {latest_features_dict_all_i2.get('MACD_12_26_9', 'N/A')}",
            f"   MACD signal: {latest_features_dict_all_i2.get('MACDs_12_26_9', 'N/A')}",
            f"   MACD histogram: {latest_features_dict_all_i2.get('MACDh_12_26_9', 'N/A')}",
            f"   PullBack_Up: {latest_features_dict_all_i2.get('PullBack_50_Up', 'N/A')}",
            f"   PullBack_Down: {latest_features_dict_all_i2.get('PullBack_50_Down', 'N/A')}",
            f"   Ratio_Buy: {latest_features_dict_all_i2.get('Ratio_Buy', 'N/A')}",
            f"   Ratio_Sell: {latest_features_dict_all_i2.get('Ratio_Sell', 'N/A')}",
            "-" * 80
        ]

        # 🔍 แสดงที่หน้าจอเหมือนเดิม
        for line in feature_log_lines:
            print(line)

        # 📁 เขียนลง log file เดียวกัน
        def append_log_lines(log_path, lines):
            with open(log_path, "a", encoding="utf-8") as f:
                for line in lines:
                    f.write(line + "\n")

        append_log_lines(log_path, feature_log_lines)

        # แสดงเงื่อนไขทางเทคนิคเฉพาะเมื่อเปิดใช้งาน
        if ENABLE_TECHNICAL_CONDITIONS:
            if predicted_signal in ["BUY", "STRONG_BUY"] and tech_signal_buy_conditions:
                print(f"🟢 BUY Technical Conditions ({scenario_name if 'scenario_name' in locals() else 'default'}):")
                for condition, result in tech_signal_buy_conditions.items():
                    status = "✅" if result else "❌"
                    print(f"   {status} {condition}: {result}")
                print(f"🟢 Overall BUY Tech Signal: {tech_signal_buy}")

            if predicted_signal in ["SELL", "STRONG_SELL"] and tech_signal_sell_conditions:
                print(f"🔴 SELL Technical Conditions ({scenario_name if 'scenario_name' in locals() else 'default'}):")
                for condition, result in tech_signal_sell_conditions.items():
                    status = "✅" if result else "❌"
                    print(f"   {status} {condition}: {result}")
                print(f"🔴 Overall SELL Tech Signal: {tech_signal_sell}")

            if not tech_signal_buy_conditions and not tech_signal_sell_conditions:
                print(f"⚠️ No technical conditions defined for {predicted_signal} signal")
        else:
            print(f"⚠️ Technical conditions are DISABLED - Using Model Prediction only")

        # *** กำหนดค่า symbol parameters ก่อนการใช้งาน ***
        symbol_spread = SYMBOL_INFO.get(symbol, {}).get('Spread', 15)
        symbol_digits = SYMBOL_INFO.get(symbol, {}).get('Digits', 5)
        symbol_points = SYMBOL_INFO.get(symbol, {}).get('Points', 0.00001)

        if probability_tp_hit >= model_confidence_threshold:

            if time_condition:

                # print(f"probability_tp_hit {probability_tp_hit} time_condition {time_condition}")
                # print(f"Multi-class prediction: Class={prediction_class}, Signal={predicted_signal}")

                # ใช้ predicted_signal จาก multi-class model เป็นหลัก
                # แต่ยังคงตรวจสอบเงื่อนไขทางเทคนิคเพิ่มเติม

                # ดึงข้อมูลเพื่อตรวจสอบ : ก่อนการซื้อ-ขาย
                # print(f"\n✅ ทดสอบข้อมูลแท่ก่อนหน้า 1 แท่ง : latest_features_dict_all_i2 (tail(2))")
                # if 'DateTime' in latest_features_dict_all_i2:    print(f"time {latest_features_dict_all_i2['DateTime']}")
                # if 'Hour' in latest_features_dict_all_i2:  print(f"entry hour {latest_features_dict_all_i2['Hour']}")
                # if 'Open' in latest_features_dict_all_i2:        print(f"op {latest_features_dict_all_i2['Open']}")
                # if 'High' in latest_features_dict_all_i2:        print(f"hh {latest_features_dict_all_i2['High']}")
                # if 'Low' in latest_features_dict_all_i2:         print(f"ll {latest_features_dict_all_i2['Low']}")
                # if 'Close' in latest_features_dict_all_i2:       print(f"cl {latest_features_dict_all_i2['Close']}")
                # if 'Volume' in latest_features_dict_all_i2:      print(f"vol {latest_features_dict_all_i2['Volume']}")

                # ดึงข้อมูลเพื่อตรวจสอบ : ก่อนการซื้อ-ขาย
                # print(f"\n✅ ทดสอบข้อมูลแท่ปัจจุบัน : latest_features_dict_all_i1 (tail(1))")
                # if 'DateTime' in latest_features_dict_all_i1:    print(f"time {latest_features_dict_all_i1['DateTime']}")
                # if 'Hour' in latest_features_dict_all_i1:  print(f"entry hour {latest_features_dict_all_i1['Hour']}")
                # if 'Open' in latest_features_dict_all_i1:        print(f"op {latest_features_dict_all_i1['Open']}")
                # if 'High' in latest_features_dict_all_i1:        print(f"hh {latest_features_dict_all_i1['High']}")
                # if 'Low' in latest_features_dict_all_i1:         print(f"ll {latest_features_dict_all_i1['Low']}")
                # if 'Close' in latest_features_dict_all_i1:       print(f"cl {latest_features_dict_all_i1['Close']}")
                # if 'Volume' in latest_features_dict_all_i1:      print(f"vol {latest_features_dict_all_i1['Volume']}")

                # โหลด entry condition ที่ดีที่สุดจากการทดสอบ (สำหรับแสดงผลเท่านั้น)
                best_entry_name = load_best_entry_condition(symbol, timeframe)
                print(f"🎯 Legacy entry condition: {best_entry_name} (ไม่ใช้แล้ว)")
                print(f"🎯 ใช้ Multi-Model Architecture conditions แทน")

                # ⚠️ ปิดการใช้ entry_func เพราะจะเขียนทับ tech_signal_buy, tech_signal_sell ที่คำนวณแล้ว
                # และใช้เงื่อนไขจาก Multi-Model Architecture แทน
                # entry_conditions_funcs = get_entry_conditions_functions()
                # entry_func = entry_conditions_funcs.get(best_entry_name, entry_conditions_funcs["entry_v1"])
                # tech_signal_buy, tech_signal_sell = entry_func(...)

                print(f"🔍 Using Multi-Model tech signals: BUY={tech_signal_buy}, SELL={tech_signal_sell}")

                entry_price = 0.0 # Default SL
                sl_price = 0.0 # Default SL
                tp_price = 0.0 # Default TP

                # ใช้ค่า symbol parameters ที่กำหนดไว้แล้วข้างบน

                # ใช้ข้อมูล Indicator จากแท่งก่อนหน้า (index -2) สำหรับ ATR, Support, Resistance
                # ต้องแน่ใจว่าคอลัมน์เหล่านี้ถูกคำนวณและมีค่าใน indicator_features หรือ df_ft
                # และต้องจัดการค่า NaN ที่อาจเกิดขึ้น
                # ในโค้ดเดิมของคุณ ATR, Support, Resistance ถูกคำนวณแยกก่อนสร้าง indicator_features
                # ตรวจสอบว่า indicator_features มีคอลัมน์ 'ATR', 'Support', 'Resistance' ที่มีค่าสำหรับแท่ง -2
                # หรือใช้ series atr, support, resistance ที่คำนวณไว้ก่อนหน้าโดยตรง
                # สมมติว่าใช้คอลัมน์ใน indicator_features
                prev_atr = df_ft['ATR'].iloc[-2] if 'ATR' in df_ft.columns and len(df_ft) >= 2 and not pd.isna(df_ft['ATR'].iloc[-2]) else np.nan
                prev_support = df_ft['Support'].iloc[-2] if 'Support' in df_ft.columns and len(df_ft) >= 2 and not pd.isna(df_ft['Support'].iloc[-2]) else np.nan
                prev_resistance = df_ft['Resistance'].iloc[-2] if 'Resistance' in df_ft.columns and len(df_ft) >= 2 and not pd.isna(df_ft['Resistance'].iloc[-2]) else np.nan

                # print(f"probability_tp_hit {probability_tp_hit} time_condition {time_condition} buy {tech_signal_buy} sell {tech_signal_sell}")

                # *** ใช้ Multi-class prediction เป็นหลักในการตัดสินใจ Signal ***
                # รวมกับเงื่อนไขทางเทคนิคเป็นตัวกรอง (ถ้าเปิดใช้งาน)

                print(f"\n🎯 SIGNAL DECISION PROCESS:")
                print(f"   Model Prediction: {predicted_signal} (Confidence: {probability_tp_hit:.4f})")
                print(f"   Technical Conditions: {'ENABLED' if ENABLE_TECHNICAL_CONDITIONS else 'DISABLED'}")

                if ENABLE_TECHNICAL_CONDITIONS:
                    # ใช้เงื่อนไขทางเทคนิคร่วมกับ Model Prediction
                    print(f"   Decision Logic: Model + Technical Conditions")

                    # Debug: แสดงค่าที่ใช้ในการตัดสินใจ
                    print(f"🔍 DEBUG Decision Values:")
                    print(f"   predicted_signal: '{predicted_signal}'")
                    print(f"   predicted_signal in ['BUY', 'STRONG_BUY']: {predicted_signal in ['BUY', 'STRONG_BUY']}")
                    print(f"   predicted_signal in ['SELL', 'STRONG_SELL']: {predicted_signal in ['SELL', 'STRONG_SELL']}")
                    print(f"   tech_signal_buy: {tech_signal_buy}")
                    print(f"   tech_signal_sell: {tech_signal_sell}")

                    if predicted_signal in ["BUY", "STRONG_BUY"] and tech_signal_buy:
                        signal = "BUY"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: ✅)")
                    elif predicted_signal in ["SELL", "STRONG_SELL"] and tech_signal_sell:
                        signal = "SELL"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: ✅)")
                    elif predicted_signal == "HOLD":
                        signal = "HOLD"
                        print(f"🎯 FINAL DECISION: {signal} (Model predicted HOLD)")
                    else:
                        # ถ้า model ทำนาย BUY/SELL แต่เงื่อนไขทางเทคนิคไม่ผ่าน ให้ HOLD
                        signal = "HOLD"
                        waiting_for = f"Waiting for tech conditions (Model: {predicted_signal})"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: ❌) - {waiting_for}")
                        print(f"🔍 DEBUG: Why HOLD? BUY_check: {predicted_signal in ['BUY', 'STRONG_BUY']} & {tech_signal_buy}, SELL_check: {predicted_signal in ['SELL', 'STRONG_SELL']} & {tech_signal_sell}")
                else:
                    # ใช้เฉพาะ Model Prediction (ไม่ตรวจสอบเงื่อนไขทางเทคนิค)
                    print(f"   Decision Logic: Model Prediction ONLY (Technical conditions ignored)")
                    if predicted_signal in ["BUY", "STRONG_BUY"]:
                        signal = "BUY"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: DISABLED)")
                    elif predicted_signal in ["SELL", "STRONG_SELL"]:
                        signal = "SELL"
                        print(f"🎯 FINAL DECISION: {signal} (Model: {predicted_signal}, Tech: DISABLED)")
                    else:
                        signal = "HOLD"
                        print(f"🎯 FINAL DECISION: {signal} (Model predicted HOLD, Tech: DISABLED)")

                # ==============================================
                if signal == "BUY":

                    # ใช้ข้อมูลจากแท่งล่าสุด (index -1) สำหรับ Entry Price
                    # ถ้า MT5 ส่งบาร์ล่าสุดที่ **ปิดแล้ว** มา และ EA เทรดเมื่อบาร์ปิด ให้ใช้ df_ft['Close'].iloc[-1]
                    # ถ้า MT5 ส่งบาร์ที่ **กำลังก่อตัว** มา และ EA เทรดที่ราคา Open ของบาร์นั้น ให้ใช้ df_ft['Open'].iloc[-1]
                    # สมมติว่าใช้ Close ของบาร์ล่าสุดที่ปิดแล้วเป็นราคา Entry
                    entry_price = df_ft['Open'].iloc[-1] + (symbol_spread + 2) * symbol_points
                    
                    # คำนวณ SL สำหรับ Buy
                    # sl_atr: Entry Price - Multiplier * ATR(Previous Bar)
                    sl_atr = entry_price - input_stop_loss_atr * prev_atr if not pd.isna(prev_atr) else np.inf

                    # sl_prev_bars: Minimum Low of the last num_nBars_SL bars (excluding current) or Entry Price - 2*Points
                    # ใช้ค่า num_nBars_SL ที่โหลดมาจาก scenario parameters
                    required_bars = num_nBars_SL + 1  # +1 เพื่อให้มีข้อมูลเพียงพอสำหรับการคำนวณ
                    print(f"[BUY SL] DEBUG: nBars_SL={num_nBars_SL}, len(df_ft)={len(df_ft)}, required_bars={required_bars}")
                    if len(df_ft) >= required_bars:
                        # คำนวณ Low ของ num_nBars_SL bars ก่อนหน้า (excluding current bar)
                        start_idx = -(num_nBars_SL + 1)  # เริ่มจาก num_nBars_SL+1 bars ก่อน
                        end_idx = -1  # จบที่ bar ก่อนหน้า (ไม่รวม current bar)
                        sl_prev_bars = min(df_ft['Low'].iloc[start_idx:end_idx].min(), entry_price - (100) * symbol_points)
                    else:
                        # sl_prev_bars = entry_price - 2*symbol_points # Fallback if not enough bars

                        # ใช้ Low ที่ต่ำสุดจากข้อมูลที่มี (ไม่รวมแท่งล่าสุด)
                        if len(df_ft) > 1:
                            low_range = df_ft['Low'].iloc[:-1]  # All but last bar
                            sl_prev_bars = min(low_range.min(), entry_price - (100) * symbol_points)
                        else:
                            sl_prev_bars = entry_price - (100) * symbol_points  # Fallback จริง ๆ ถ้าแท่งมีแค่ 1
                    print(f"[BUY SL] entry_price: {entry_price}, sl_prev_bars: {sl_prev_bars}")

                    # sl_support: Previous bar's Support level
                    sl_support = prev_support if not pd.isna(prev_support) else np.inf

                    # SL Final for BUY: The highest of the candidates (closest to entry)
                    sl_candidates = [sl_atr, sl_prev_bars, sl_support]
                    print(f"[BUY SL] sl_candidates: {sl_candidates}")

                    # กรองค่า np.inf ออกก่อนหา max
                    valid_sl_candidates = [x for x in sl_candidates if not np.isinf(x)]
                    print(f"[BUY SL] valid_sl_candidates: {valid_sl_candidates}")

                    if valid_sl_candidates:
                        sl_price = max(valid_sl_candidates)
                        sl_price -= (2) * symbol_points
                        print(f"[BUY SL] sl_price (after spread adjust): {sl_price}")
                    else: # ถ้าทุกคอลัมน์ที่ใช้คำนวณ SL เป็น NaN
                        sl_price = entry_price - 100 * symbol_points # ตั้ง SL Default เช่น 10 points จาก Entry
                        sl_price -= (2) * symbol_points
                        print(f"Warning: Indicators/Past Bars were NaN for BUY SL calculation for {symbol} ({timeframe}). Using default SL.")

                    # คำนวณ TP สำหรับ Buy: Entry Price + (Entry Price - Calculated SL) * Ratio
                    tp_price = entry_price + (entry_price - sl_price) * input_take_profit

                    # ใช้ Resistance ในการปรับ TP (เลือกค่าต่ำสุดระหว่าง TP คำนวณกับ Resistance)
                    # if not pd.isna(prev_resistance) and prev_resistance > entry_price: # ตรวจสอบว่า Resistance สูงกว่า Entry Price ก่อนใช้
                    #     tp_price = min(tp_price, prev_resistance)

                    # ตรวจสอบ TP ขั้นต่ำ (ต้องสูงกว่า Entry Price)
                    if tp_price <= entry_price:
                        tp_price = entry_price + 2*symbol_points # ตั้ง TP ขั้นต่ำเพื่อหลีกเลี่ยง TP <= Entry และให้มี Gap

                    open_price_1 = df_ft['Open'].iloc[-1] # คือแท่งปัจจุบัน (ยังไม่จบ)
                    close_price_1 = df_ft['Close'].iloc[-1]
                    open_price_2 = df_ft['Open'].iloc[-2] # แท่งก่อนหน้า (จบไปแล้ว)
                    close_price_2 = df_ft['Close'].iloc[-2]

                    fmt = f".{symbol_digits}f"
                    if USE_MULTI_MODEL_ARCHITECTURE:
                        print(f"\n🤖 Multi-Model: Model={model_used if 'model_used' in locals() else 'unknown'} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    else:
                        print(f"\n📊 Single Model: Class={prediction_class} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")

                    # --- LOG SUMMARY: BUY ---
                    print("\n================ [BUY SL/TP SUMMARY] ================")
                    print(f"Final Decision: {signal} (time_condition={time_condition})")
                    print(f"symbol: {symbol} timeframe: {timeframe} digit {symbol_digits} spread {symbol_spread} point {symbol_points} ")
                    print(f"entry_price: {entry_price:{fmt}}  sl_price: {sl_price:{fmt}}  tp_price: {tp_price:{fmt}}  RR: {(tp_price-entry_price)/(entry_price-sl_price):0.3f}")
                    print(f"nBars_SL: {num_nBars_SL}  len(df_ft): {len(df_ft)}  required_bars: {required_bars}")
                    print(f"sl_atr: {sl_atr:{fmt}}  sl_prev_bars: {sl_prev_bars:{fmt}}  sl_support: {sl_support:{fmt}}")
                    print(f"prev_atr: {prev_atr:{fmt}}  prev_support: {prev_support:{fmt}}  prev_resistance: {prev_resistance:{fmt}}")
                    print(f"sl_candidates: {sl_candidates}")
                    print(f"valid_sl_candidates: {valid_sl_candidates}")
                    print(f"open(i-1): {open_price_1:{fmt}}  close(i-1): {close_price_1:{fmt}}  open(i-2): {open_price_2:{fmt}}  close(i-2): {close_price_2:{fmt}}")
                    print("====================================================\n")

                    if Telegram_Open:
                        fmt = f".{symbol_digits}f"
                        if USE_MULTI_MODEL_ARCHITECTURE:
                            # ใช้ scenario_name ที่ได้จาก enhanced decision แล้ว
                            scenario_display = "Unknown"
                            if 'scenario_name' in locals() and scenario_name:
                                if scenario_name == "trend_following":
                                    scenario_display = "Trend-Following"
                                elif scenario_name == "counter_trend":
                                    scenario_display = "Counter-Trend"
                                else:
                                    scenario_display = scenario_name.title()

                            MESSAGE = (
                                f"🔔 {symbol} M{timeframe} Signal\n"
                                f"📊 {signal} @ {probability_tp_hit:.4f}\n"
                                f"🎯 Model: {scenario_display}\n"
                                f"📈 Entry: {entry_price:{fmt}} | 🛑 SL: {sl_price:{fmt}} | 🎯 TP: {tp_price:{fmt}}\n"
                                f"📊 RR: {(tp_price-entry_price)/(entry_price-sl_price):0.2f}"
                            )
                        else:
                            MESSAGE = (
                                f"Python Signal : {symbol} {timeframe} : {signal} {probability_tp_hit:.4f}\n"
                                f"Single Model: {predicted_signal} (Class {prediction_class})\n"
                                f"OP {entry_price:{fmt}} SL {sl_price:{fmt}} TP {tp_price:{fmt}} RR {(tp_price-entry_price)/(entry_price-sl_price):0.3f}"
                            )
                        payload = {'chat_id': CHAT_ID, 'text': MESSAGE}
                        response = requests.post(url, data=payload)

                        # print(response.json())

                # ==============================================
                if signal == "SELL":

                    entry_price = df_ft['Open'].iloc[-1]

                    # คำนวณ SL สำหรับ Sell
                    # sl_atr: Entry Price + Multiplier * ATR(Previous Bar)
                    sl_atr = entry_price + input_stop_loss_atr * prev_atr if not pd.isna(prev_atr) else -np.inf

                    # sl_prev_bars: Maximum High of the last num_nBars_SL bars (excluding current) or Entry Price + 2*Points
                    # ใช้ค่า num_nBars_SL ที่โหลดมาจาก scenario parameters
                    required_bars = num_nBars_SL + 1  # +1 เพื่อให้มีข้อมูลเพียงพอสำหรับการคำนวณ
                    print(f"[SELL SL] DEBUG: nBars_SL={num_nBars_SL}, len(df_ft)={len(df_ft)}, required_bars={required_bars}")
                    if len(df_ft) >= required_bars:
                        # คำนวณ High ของ num_nBars_SL bars ก่อนหน้า (excluding current bar)
                        start_idx = -(num_nBars_SL + 1)  # เริ่มจาก num_nBars_SL+1 bars ก่อน
                        end_idx = -1  # จบที่ bar ก่อนหน้า (ไม่รวม current bar)
                        sl_prev_bars = max(df_ft['High'].iloc[start_idx:end_idx].max(), entry_price + (100) * symbol_points)
                    else:
                        # sl_prev_bars = entry_price + 2*symbol_points # Fallback if not enough bars

                        if len(df_ft) > 1:
                            high_range = df_ft['High'].iloc[:-1]  # ไม่รวมแท่งล่าสุด
                            sl_prev_bars = max(high_range.max(), entry_price + (100) * symbol_points)
                        else:
                            sl_prev_bars = entry_price + (100) * symbol_points
                    print(f"[SELL SL] entry_price: {entry_price}, sl_prev_bars: {sl_prev_bars}")

                    # sl_resistance: Previous bar's Resistance level
                    sl_resistance = prev_resistance if not pd.isna(prev_resistance) else -np.inf

                    # SL Final for SELL: The lowest of the candidates (closest to entry)
                    sl_candidates = [sl_atr, sl_prev_bars, sl_resistance]
                    print(f"[SELL SL] sl_candidates: {sl_candidates}")

                    # กรองค่า -np.inf ออกก่อนหา min
                    valid_sl_candidates = [x for x in sl_candidates if not np.isinf(x)]
                    print(f"[SELL SL] valid_sl_candidates: {valid_sl_candidates}")

                    if valid_sl_candidates:
                        sl_price = min(valid_sl_candidates)
                        sl_price += (symbol_spread + 2) * symbol_points
                        print(f"[SELL SL] sl_price (after spread adjust): {sl_price}")
                    else: # ถ้าทุกคอลัมน์ที่ใช้คำนวณ SL เป็น NaN
                        sl_price = entry_price + 100 * symbol_points # ตั้ง SL Default
                        sl_price += (symbol_spread + 2) * symbol_points
                        print(f"Warning: Indicators/Past Bars were NaN for SELL SL calculation for {symbol} ({timeframe}). Using default SL.")

                    # คำนวณ TP สำหรับ Sell: Entry Price - (Calculated SL - Entry Price) * Ratio
                    tp_price = entry_price - (sl_price - entry_price) * input_take_profit - (2) * symbol_points

                    # ใช้ Support ในการปรับ TP (เลือกค่าสูงสุดระหว่าง TP คำนวณกับ Support)
                    # if not pd.isna(prev_support) and prev_support < entry_price: # ตรวจสอบว่า Support ต่ำกว่า Entry Price ก่อนใช้
                    #     tp_price = max(tp_price, prev_support)

                    # ตรวจสอบ TP ขั้นต่ำ (ต้องต่ำกว่า Entry Price)
                    if tp_price >= entry_price:
                        tp_price = entry_price - 2*symbol_points

                    open_price_1 = df_ft['Open'].iloc[-1] # คือแท่งปัจจุบัน (ยังไม่จบ)
                    close_price_1 = df_ft['Close'].iloc[-1]
                    open_price_2 = df_ft['Open'].iloc[-2] # แท่งก่อนหน้า (จบไปแล้ว)
                    close_price_2 = df_ft['Close'].iloc[-2]

                    fmt = f".{symbol_digits}f"
                    if USE_MULTI_MODEL_ARCHITECTURE:
                        print(f"\n🤖 Multi-Model: Model={model_used if 'model_used' in locals() else 'unknown'} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    else:
                        print(f"\n📊 Single Model: Class={prediction_class} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")

                    # --- LOG SUMMARY: SELL ---
                    print("\n================ [SELL SL/TP SUMMARY] ================")
                    print(f"Final Decision: {signal} (time_condition={time_condition})")
                    print(f"symbol: {symbol} timeframe: {timeframe}  digit {symbol_digits} spread {symbol_spread} point {symbol_points} : ")
                    print(f"entry_price: {entry_price:{fmt}}  sl_price: {sl_price:{fmt}}  tp_price: {tp_price:{fmt}}  RR: {(entry_price-tp_price)/(sl_price-entry_price):0.3f}")
                    print(f"nBars_SL: {num_nBars_SL}  len(df_ft): {len(df_ft)}  required_bars: {required_bars}")
                    print(f"sl_atr: {sl_atr:{fmt}}  sl_prev_bars: {sl_prev_bars:{fmt}}  sl_resistance: {sl_resistance:{fmt}}")
                    print(f"prev_atr: {prev_atr:{fmt}}  prev_support: {prev_support:{fmt}}  prev_resistance: {prev_resistance:{fmt}}")
                    print(f"sl_candidates: {sl_candidates}")
                    print(f"valid_sl_candidates: {valid_sl_candidates}")
                    print(f"open(i-1): {open_price_1:{fmt}}  close(i-1): {close_price_1:{fmt}}  open(i-2): {open_price_2:{fmt}}  close(i-2): {close_price_2:{fmt}}")
                    print("====================================================\n")

                    if Telegram_Open:
                        fmt = f".{symbol_digits}f"
                        if USE_MULTI_MODEL_ARCHITECTURE:
                            # ใช้ scenario_name ที่ได้จาก enhanced decision แล้ว
                            scenario_display = "Unknown"
                            if 'scenario_name' in locals() and scenario_name:
                                if scenario_name == "trend_following":
                                    scenario_display = "Trend-Following"
                                elif scenario_name == "counter_trend":
                                    scenario_display = "Counter-Trend"
                                else:
                                    scenario_display = scenario_name.title()

                            MESSAGE = (
                                f"🔔 {symbol} M{timeframe} Signal\n"
                                f"📊 {signal} @ {probability_tp_hit:.4f}\n"
                                f"🎯 Model: {scenario_display}\n"
                                f"📈 Entry: {entry_price:{fmt}} | 🛑 SL: {sl_price:{fmt}} | 🎯 TP: {tp_price:{fmt}}\n"
                                f"📊 RR: {(entry_price-tp_price)/(sl_price-entry_price):0.2f}"
                            )
                        else:
                            MESSAGE = (
                                f"Python Signal : {symbol} {timeframe} : {signal} {probability_tp_hit:.4f}\n"
                                f"Single Model: {predicted_signal} (Class {prediction_class})\n"
                                f"OP {entry_price:{fmt}} SL {sl_price:{fmt}} TP {tp_price:{fmt}} RR {(entry_price-tp_price)/(sl_price-entry_price):0.3f}"
                            )
                        payload = {'chat_id': CHAT_ID, 'text': MESSAGE}
                        response = requests.post(url, data=payload)
                        print(response.json())

                # ==============================================
                # *** อัปเดตการแสดงผลสำหรับ HOLD ***
                if signal == "HOLD":
                    if USE_MULTI_MODEL_ARCHITECTURE:
                        print(f"\n🤖 Multi-Model: Model={model_used if 'model_used' in locals() else 'unknown'} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    else:
                        print(f"\n📊 Single Model: Class={prediction_class} Signal={predicted_signal} Confidence={probability_tp_hit:.4f}")
                    print(f"Final Decision: {signal} ({waiting_for if waiting_for else 'No strong signal'})")

                    waiting_for_list = []

                    # print(f"\nprobability_tp_hit {probability_tp_hit} time_condition {time_condition} signal {signal}")

                    # ตรวจสอบว่าขาดเงื่อนไขอะไรไปบ้างสำหรับ BUY
                    if not tech_signal_buy and tech_signal_buy_conditions:
                        missing_buy_conditions = [cond for cond, is_true in tech_signal_buy_conditions.items() if not is_true]
                        if missing_buy_conditions:
                            waiting_for_list.append(f"BUY : {', '.join(missing_buy_conditions)}")

                    # ตรวจสอบว่าขาดเงื่อนไขอะไรไปบ้างสำหรับ SELL
                    if not tech_signal_sell and tech_signal_sell_conditions:
                        missing_sell_conditions = [cond for cond, is_true in tech_signal_sell_conditions.items() if not is_true]
                        if missing_sell_conditions:
                            waiting_for_list.append(f"SELL : {', '.join(missing_sell_conditions)}")

                    # print(f"[{datetime.datetime.now()}] {timeframe} {symbol} Model Confidence High ({probability_tp_hit:.4f}) but Technicals not met. Waiting for: {waiting_for}")

                    # if not waiting_for_list:
                    #     # กรณีที่โมเดลผ่าน แต่ไม่ BUY/SELL อาจจะมีเงื่อนไขอื่นๆ ที่ซับซ้อน หรือเงื่อนไขเทคนิคขัดแย้งกันเอง
                    #     # แต่ตามโครงสร้างโค้ดน่าจะเข้าเงื่อนไขใดเงื่อนไขหนึ่งถ้า tech_signal_buy หรือ tech_signal_sell เป็น True
                    #     # ถ้ามาถึงตรงนี้โดยที่ waiting_for_list ว่าง อาจจะต้องตรวจสอบ logic หรือเงื่อนไขเพิ่มเติม
                    #     waiting_for = "Technical conditions not met, unclear what specific condition is missing based on current rules."
                    #     print(f"[{datetime.datetime. द्रव()}] Model Confidence High ({probability_tp_hit:.4f}) but Technicals not met. Reason unclear based on defined conditions.")
                    # else:
                    #     waiting_for = " / ".join(waiting_for_list)
                    #     print(f"[{datetime.datetime.now()}] Model Confidence High ({probability_tp_hit:.4f}) but Technicals not met. Waiting for: {waiting_for}")

                # print(f"[{datetime.datetime.now()}] Model confidence ({probability_tp_hit:.4f}) >= {model_confidence_threshold:.2f}. Generated Signal: {signal}")

            else:
                # โมเดลเห็นศักยภาพ แต่ไม่อยู่ในเงื่อนไขเวลาที่กำหนด
                signal = "HOLD"
                waiting_for = "Time condition (6 <= Hour <= 20) not met."
                print(f"[{datetime.datetime.now()}] Model Confidence High ({probability_tp_hit:.4f}), Technicals checked, but Time condition not met. Waiting for: {waiting_for}")

        else: 
            # โมเดลไม่เห็นศักยภาพ (Probability TP Hit ต่ำกว่า Threshold)
            signal = "HOLD"
            waiting_for = f"Model confidence ({probability_tp_hit:.4f}) below threshold ({model_confidence_threshold:.2f})."
            print(f"[{datetime.datetime.now()}] Model confidence ({probability_tp_hit:.4f}) below threshold ({model_confidence_threshold:.2f}). No signal potential detected.")

        # --- เพิ่ม: Print Signal และ Confidence Probability ที่คำนวณได้ ---
        # print(f"\n[{datetime.datetime.now()}] Prediction Result for {symbol} ({timeframe}):")
        # print(f"  Signal: {signal}")
        # print(f"  Confidence (Probability TP Hit): {probability_tp_hit:.4f}")
        # if waiting_for: # แสดงเหตุผลที่รอ ถ้ามีการระบุไว้
        #     print(f"  Waiting Reason: {waiting_for}")
        # print("------------------------------------------------------------")

        if waiting_for: # แสดงเหตุผลที่รอ ถ้ามีการระบุไว้
            print(f"[{datetime.datetime.now()}] {timeframe} {symbol} Signal {signal} Confidence {probability_tp_hit:.4f} Waiting {waiting_for}")
        else:
            print(f"[{datetime.datetime.now()}] {timeframe} {symbol} Signal {signal} Confidence {probability_tp_hit:.4f}")

        # --- จบส่วน Print ---

        # --- ส่วนส่ง Signal หรือข้อมูลกลับไปที่ MT5 (ต้องเพิ่ม Logic จริงๆ ที่นี่) ---
        # ... (ไม่ได้เพิ่ม Logic ส่งกลับในโค้ดนี้) ...

        # --- ส่วนที่เพิ่ม: Store the calculated signal and confidence ---
        # Store the signal and confidence in the global dictionary

        # *** 🔧 แก้ไขปัญหา: ตรวจสอบค่า confidence ก่อนบันทึก ***
        print(f"🔍 DEBUG - Before storing signal data:")
        print(f"   signal: {signal}")
        print(f"   predicted_signal: {predicted_signal}")
        print(f"   probability_tp_hit: {probability_tp_hit}")
        print(f"   type(probability_tp_hit): {type(probability_tp_hit)}")

        # ตรวจสอบว่า probability_tp_hit เป็น 0.0 หรือไม่
        if probability_tp_hit == 0.0 and signal in ["BUY", "SELL"]:
            print(f"🚨 CRITICAL: probability_tp_hit is 0.0 for {signal} signal!")
            print(f"   This will cause MT5 to reject the trade!")
            print(f"   Signal will be stored but MT5 won't execute it.")

        with signals_lock: # <--- บรรทัดนี้เริ่มต้นส่วนที่บันทึก Signal โดยใช้ Lock
            signals_key = (symbol, timeframe) # ใช้ symbol และ timeframe เป็น key
            latest_signals_data[signals_key] = { # <--- บรรทัดนี้บันทึกข้อมูลลง Global Variable
                "symbol": symbol,
                "timeframe": timeframe, # Store enum
                "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'), # Store string for easy reading
                "signal": signal,
                "class": predicted_signal,  # เพิ่ม class level (STRONG_BUY, BUY, HOLD, SELL, STRONG_SELL)
                "confidence": float(probability_tp_hit), # Ensure it's a standard float
                "entry_price": float(entry_price),
                "sl_price": float(sl_price),
                "tp_price": float(tp_price),
                "best_entry": float(entry_price),  # ใช้ entry_price เป็น best_entry
                "nBars_SL": int(num_nBars_SL),  # จำนวนแท่งสำหรับ SL
                "threshold": float(model_confidence_threshold),  # เกณฑ์การตัดสินใจ
                "time_filters": f"Days:{days_filter},Hours:{hours_filter}",  # การกรองเวลา
                "spread": int(symbol_spread),  # ค่า spread
                # *** เพิ่มข้อมูล Multi-Model Analysis ***
                "market_condition": market_condition if 'market_condition' in locals() else "unknown",
                "action_type": action_type if 'action_type' in locals() else "none",
                "scenario_used": scenario_name if 'scenario_name' in locals() else "none",
                # *** เพิ่มข้อมูลการวิเคราะห์ทั้ง 2 ระบบ (แปลงเป็น JSON serializable) ***
                "analysis_summary": prepare_analysis_summary(analysis_results) if 'analysis_results' in locals() else {},
                "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE) # Use bar time if available, otherwise current time
            }
        # *** DEBUG: ตรวจสอบ analysis_results ก่อนบันทึก ***
        if 'analysis_results' in locals():
            print(f"🔍 DEBUG - analysis_results keys: {list(analysis_results.keys())}")
            for scenario, data in analysis_results.items():
                if data and isinstance(data, dict):
                    for action, result in data.items():
                        if result and isinstance(result, dict):
                            conf = result.get('confidence', 0.0)
                            print(f"🔍 DEBUG - {scenario}_{action}: confidence={conf:.4f}")
        else:
            print(f"🔍 DEBUG - analysis_results not found in locals()")

        print(f"[{datetime.datetime.now()}] Stored latest signal for {symbol} (enum: {timeframe}): {signal} ({probability_tp_hit:.4f})") # <--- บรรทัดนี้ Print ยืนยันการบันทึก

    # ... (โค้ดส่วนจัดการ Error ใน process_data_and_trade - ควรมี Logic การบันทึก Error Signal ด้วย) ...
    except Exception as e:
        # Enhanced error logging
        signal = "ERROR"
        probability_tp_hit = 0.0
        entry_price = 0.0
        sl_price = 0.0
        tp_price = 0.0

        # Store ERROR signal in case of failure
        with signals_lock:
            signals_key = (symbol, timeframe)
            latest_signals_data[signals_key] = {
            "symbol": symbol,
            "timeframe": timeframe,
            "timeframe_str": timeframe_map.get(timeframe, 'UNKNOWN'),
            "signal": signal,
            "confidence": float(probability_tp_hit),
            "entry_price": float(entry_price),
            "sl_price": float(sl_price),
            "tp_price": float(tp_price),
            "timestamp": latest_bar_dt if 'latest_bar_dt' in locals() else datetime.datetime.now(MT5_TIMEZONE)
            }
        print(f"[{datetime.datetime.now()}] ❌ ERROR in process_data_and_trade for {symbol} M{timeframe}")
        print(f"[{datetime.datetime.now()}] ⚠️ Exception: {str(e)}")
        print(f"[{datetime.datetime.now()}] 📊 Error details:")
        import traceback
        traceback.print_exc()

        # บันทึก error ลงไฟล์
        try:
            with open('server_error.log', 'a', encoding='utf-8') as f:
                f.write(f"\n[{datetime.datetime.now()}] ERROR in {symbol} M{timeframe}:\n")
                f.write(f"Exception: {str(e)}\n")
                f.write(f"Traceback:\n")
                traceback.print_exc(file=f)
                f.write("\n" + "="*50 + "\n")
        except:
            pass

    print(f"[{datetime.datetime.now()}] Finished processing for {symbol} ({timeframe}).")

# ==============================================

input_initial_threshold = 0.50
input_initial_nbar_sl = 4

def get_scenario_params(symbol, timeframe_int, scenario):
    """
    โหลด threshold และ nbars ของ scenario ที่กำหนด
    - ถ้าไม่มีไฟล์ → คืนค่า default
    - ถ้า scenario เป็น *_Buy หรือ *_Sell แต่ไม่มีไฟล์ → fallback ไปหา base scenario (trend_following หรือ counter_trend)
    """
    try:
        threshold, nbars = None, None

        # --- threshold ---
        try:
            threshold_data = load_scenario_threshold(symbol, f"M{timeframe_int}", scenario)
            if threshold_data:
                threshold = threshold_data.get("best_threshold")
                print(f"✅ โหลด threshold สำหรับ {scenario}: {threshold}")
        except FileNotFoundError:
            print(f"⚠️ ไม่พบไฟล์ threshold สำหรับ {scenario}, จะลอง fallback ไป base")

        # --- nbars ---
        try:
            nbars = load_scenario_nbars(symbol, f"M{timeframe_int}", scenario)
            if nbars is not None:
                print(f"✅ โหลด nBars_SL สำหรับ {scenario}: {nbars}")
        except FileNotFoundError:
            print(f"⚠️ ไม่พบไฟล์ nBars สำหรับ {scenario}, จะลอง fallback ไป base")

        # --- Fallback ---
        if threshold is None or nbars is None:
            if "trend_following" in scenario and scenario != "trend_following":
                base_scenario = "trend_following"
            elif "counter_trend" in scenario and scenario != "counter_trend":
                base_scenario = "counter_trend"
            else:
                base_scenario = None

            if base_scenario:
                print(f"↩️ กำลัง fallback {scenario} → {base_scenario}")
                if threshold is None:
                    try:
                        base_threshold_data = load_scenario_threshold(symbol, f"M{timeframe_int}", base_scenario)
                        if base_threshold_data:
                            threshold = base_threshold_data.get("best_threshold")
                            print(f"✅ ใช้ threshold จาก {base_scenario}: {threshold}")
                    except:
                        pass

                if nbars is None:
                    try:
                        nbars = load_scenario_nbars(symbol, f"M{timeframe_int}", base_scenario)
                        if nbars is not None:
                            print(f"✅ ใช้ nBars จาก {base_scenario}: {nbars}")
                    except:
                        pass

        # --- Defaults ---
        if threshold is None:
            threshold = input_initial_threshold
            print(f"⚠️ ใช้ค่า default threshold: {threshold}")
        if nbars is None:
            nbars = input_initial_nbar_sl
            print(f"⚠️ ใช้ค่า default nBars: {nbars}")

        return {"threshold": threshold, "nbars": nbars}

    except Exception as e:
        print(f"⚠️ Error loading scenario {scenario}: {e}")
        return {"threshold": input_initial_threshold, "nbars": input_initial_nbar_sl}

# ==============================================

# --- HTTP Flask Route to receive data ---
@app.route('/data', methods=['POST'])
def receive_data():
    """Receives JSON data (batch of bars) from MT5 EA via HTTP POST.
        Processes the data and returns the latest processed signal if available.
    """

    try:
        print(f"\n[{datetime.datetime.now()}] 📨 Received HTTP POST request")
        # --- เปลี่ยนการรับข้อมูล: คาดหวัง JSON Object ที่มี key เป็น symbol, timeframe_str, และ bars (เป็น Array) ---
        data = request.get_json(force=True, silent=False)
        print(f"[{datetime.datetime.now()}] 📊 Data received: symbol={data.get('symbol')}, timeframe_str={data.get('timeframe_str')}, bars_count={len(data.get('bars', []))}")

        # ดึงข้อมูลพื้นฐาน
        symbol = data.get('symbol')
        timeframe_str = data.get('timeframe_str')
        bar_time_ts = data.get('time')
        bar_open = data.get('open')
        bar_high = data.get('high')
        bar_low = data.get('low')
        bar_close = data.get('close')
        bar_volume = data.get('tick_volume')
        latest_indicators = data.get('latest_indicators') # <-- ดึงค่า indicators ที่คำนวณจาก MT5
        bars_list = data.get('bars') # <--- ดึง Array ของบาร์ออกมา

        # ตรวจสอบข้อมูลที่ได้รับ
        if not all([symbol, timeframe_str, bars_list]) or not isinstance(bars_list, list) or len(bars_list) == 0:
            print("Received incomplete or incorrectly formatted data.")
            print(f"Received data: {data}")
            return jsonify({"status": "ERROR", "message": "Incomplete or incorrectly formatted data received."}), 400

        # --- แปลง timeframe_str เป็น enum ---
        timeframe_int = timeframe_code_map.get(timeframe_str) # timeframe str PERIOD_H1 enum 60
        if timeframe_int is None:
            print(f"Invalid or unknown timeframe string received: {timeframe_str}")
            return jsonify({"status": "ERROR", "message": f"Invalid or unknown timeframe {timeframe_str}"}), 400

        # --- สร้าง DataFrame จาก List ของ Bar Objects ---
        try:
            print(f"[{datetime.datetime.now()}] 🔄 Creating DataFrame from {len(bars_list)} bars")
            # แปลง list ของ dict เป็น DataFrame
            df_batch = pd.DataFrame(bars_list)
            print(f"[{datetime.datetime.now()}] ✅ DataFrame created: {df_batch.shape}")
            print(f"[{datetime.datetime.now()}] 📊 Columns: {list(df_batch.columns)}")

            # ตรวจสอบว่าคอลัมน์ที่จำเป็นมีครบ
            required_cols = ['time', 'open', 'high', 'low', 'close', 'tick_volume']
            missing_cols = [col for col in required_cols if col not in df_batch.columns]
            if missing_cols:
                print(f"❌ Missing required columns in received bar data: {missing_cols}")
                return jsonify({"status": "ERROR", "message": "Missing required bar data columns."}), 400
            print(f"[{datetime.datetime.now()}] ✅ All required columns present")

            # แปลง Timestamp (integer) เป็น Datetime Objects และตั้งเป็น Index
            print(f"[{datetime.datetime.now()}] 🕒 Converting timestamps to datetime")
            # ใช้ unit='s' เพื่อระบุว่าเป็น Unix timestamp (วินาที)
            df_batch['time'] = pd.to_datetime(df_batch['time'], unit='s', utc=True).dt.tz_convert(MT5_TIMEZONE) # แปลงเป็น Timezone ของ MT5 Server
            df_batch.set_index('time', inplace=True)
            print(f"[{datetime.datetime.now()}] ✅ Timestamps converted, time range: {df_batch.index[0]} to {df_batch.index[-1]}")

            # *** ส่วนที่เพิ่ม: เรียงลำดับ Index (เวลา) จากน้อยไปมาก (เก่าไปใหม่) ***
            df_batch.sort_index(ascending=True, inplace=True) # <--- เพิ่มบรรทัดนี้
            print(f"[{datetime.datetime.now()}] ✅ Data sorted by time")

            # เปลี่ยนชื่อคอลัมน์ tick_volume เป็น volume และเปลี่ยนเป็นตัวพิมพ์เล็ก
            df_batch.rename(columns={'tick_volume': 'volume'}, inplace=True)
            df_batch.columns = df_batch.columns.str.lower() # เปลี่ยนชื่อคอลัมน์ราคาเป็นตัวพิมพ์เล็กทั้งหมด

            # ตรวจสอบว่า DataFrame ถูกสร้างขึ้นมาถูกต้อง
            if df_batch.empty:
                print("Received bar list is empty after processing.")
                return jsonify({"status": "ERROR", "message": "Received bar data is empty."}), 400

            # Print ข้อมูลที่ได้รับ Batch แรกและสุดท้ายเพื่อตรวจสอบ (ตอนนี้ head จะเป็นแท่งเก่าสุด, tail จะเป็นแท่งใหม่สุด)
            # print(f"[{datetime.datetime.now()}] Received batch of {len(df_batch)} bars for {symbol} {timeframe_str}. Time range: {df_batch.index.min()} to {df_batch.index.max()}")
            # print(f"\nข้อมูลดิบที่โหลดและ แปลงเป็นตัวเลข (เรียงตามเวลา):") # เปลี่ยนข้อความ Print
            # print("\n✅ ข้อมูลดิบที่โหลด : df_batch")
            # print(df_batch.info())
            # print(df_batch)

        except Exception as e:
            print(f"Error processing received bar list into DataFrame: {e}")
            traceback.print_exc()
            return jsonify({"status": "ERROR", "message": f"Error processing bar data: {e}"}), 400

        # --- อัปเดต market_data_store ด้วย Batch Data ---
        cleaned_symbol = symbol.replace('#', '')
        key = (cleaned_symbol, timeframe_int)

        with data_lock:
            if key not in market_data_store:
                print(f"\n[{datetime.datetime.now()}] Initializing data store for {cleaned_symbol} ({timeframe_str})")
                # สร้าง DF เปล่าด้วยคอลัมน์ตัวพิมพ์เล็กตามที่ MQL5 ส่งมา
                market_data_store[key] = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume'])
                market_data_store[key].index.name = 'time'

            # *** วิธีที่ 1 (แนะนำสำหรับตอนนี้): แทนที่ข้อมูลเดิมด้วย Batch ใหม่ทั้งหมด ***
            # วิธีนี้ง่ายที่สุดในการเริ่มต้นและทำให้แน่ใจว่ามีข้อมูลล่าสุด
            # ข้อเสีย: ถ้า Batch ไม่ครบถ้วนตามที่ Python ต้องการ จะทำให้คำนวณ Indicator ไม่ได้
            # คุณต้องแน่ใจว่า MQL5 ส่งข้อมูลย้อนหลังครบจำนวน required_bars เสมอ
            required_bars = 210 # *** ต้องตรงกับ BARS_FOR_PYTHON ใน MQL5 ***
            if len(df_batch) < required_bars:
                print(f"Warning: Received batch size ({len(df_batch)}) is less than required ({required_bars}). Processing with available data.")
                # อาจจะตัดสินใจไม่ประมวลผล หรือ ประมวลผลเท่าที่มีก็ได้ ขึ้นอยู่กับ Logic
                # ถ้าไม่ประมวลผล ให้ return ไปเลย
                # return jsonify({"status": "WARNING", "message": f"Received incomplete batch ({len(df_batch)} bars)."}), 200
                # ถ้าประมวลผลต่อ ก็ไปขั้นตอนต่อไป

            # แทนที่ข้อมูลทั้งหมดใน store ด้วย batch ใหม่
            market_data_store[key] = df_batch.copy()
            print(f"[{datetime.datetime.now()}] Replaced data store for {cleaned_symbol} ({timeframe_str}) with {len(market_data_store[key])} bars from batch.")

            # *** วิธีที่ 2 (ซับซ้อนกว่า): รวมข้อมูล Batch เข้ากับข้อมูลเก่าใน store (ถ้ามี) ***
            # วิธีนี้ต้องจัดการกับข้อมูลที่ซ้ำกัน หรือเรียงลำดับข้อมูลให้ถูกต้อง
            # if not market_data_store[key].empty:
            #     # Concatenate และลบข้อมูลที่ซ้ำกัน (เก็บข้อมูลล่าสุด)
            #     combined_df = pd.concat([market_data_store[key], df_batch])
            #     market_data_store[key] = combined_df[~combined_df.index.duplicated(keep='last')].sort_index()
            # else:
            #     market_data_store[key] = df_batch.copy()

            # # จำกัดขนาดข้อมูลใน store
            # market_data_store[key] = market_data_store[key].tail(max(required_bars + 50, 500)) # เก็บอย่างน้อย 500 หรือตาม required_bars + buffer
            # print(f"[{datetime.datetime.now()}] Updated data store for {cleaned_symbol} ({timeframe_str}) with {len(market_data_store[key])} bars.")

        # --- เรียกใช้ฟังก์ชันประมวลผลใน Thread แยก ---
        print(f"[{datetime.datetime.now()}] 🚀 Starting data processing for {cleaned_symbol} M{timeframe_int}")
        # ตอนนี้ process_data_and_trade จะใช้ข้อมูลจาก market_data_store ที่เพิ่งอัปเดต
        # หรืออาจจะปรับ process_data_and_trade ให้รับ df_batch เข้ามาตรงๆ ก็ได้
        # แต่เพื่อให้เข้ากับโครงสร้างเดิม เราจะให้มันอ่านจาก store

        print(f"[{datetime.datetime.now()}] 🔄 Creating processing thread for {cleaned_symbol} M{timeframe_int}")
        processing_thread = threading.Thread(target=process_data_and_trade, args=(cleaned_symbol, timeframe_int, latest_indicators))
        processing_thread.start()
        processing_thread.join()
        print(f"[{datetime.datetime.now()}] ✅ Processing thread completed for {cleaned_symbol} M{timeframe_int}")

        # --- ส่วนที่ดึง Signal และ Confidence จาก latest_signals_data และส่งกลับใน Response ---
        # ตอนนี้ เมื่อมาถึงส่วนนี้ การประมวลผลใน processing_thread เสร็จแล้ว
        # latest_signals_data ควรจะมีค่า Signal ล่าสุดของบาร์ที่เพิ่งได้รับ

        response_signal = "HOLD" # Default response
        response_class = "HOLD" # Default class level
        response_confidence = 0.0
        response_entry_price = 0.0
        response_sl_price = 0.0
        response_tp_price = 0.0
        response_best_entry = 0.0
        response_nBars_SL = 0
        response_threshold = 0.0
        response_time_filters = ""
        response_spread = 0
        response_message = "Data received and processing started. Signal processing in progress."
        response_signal_bar_timestamp = None # Timestamp ของแท่งที่เกี่ยวข้องกับ Signal ที่ส่งกลับ

        signals_key = (cleaned_symbol, timeframe_int)

        # ดึงข้อมูล Signal ล่าสุดออกมา โดยใช้ signals_lock
        with signals_lock:
            latest_sig = latest_signals_data.get(signals_key, None)

            if latest_sig:
                # response_signal = latest_sig["signal"]
                # response_confidence = latest_sig["confidence"]

                response_signal = latest_sig.get("signal", "HOLD") # ใช้ .get() เพื่อป้องกัน KeyError
                response_class = latest_sig.get("class", "HOLD") # ดึง class level
                response_confidence = latest_sig.get("confidence", 0.0) # [147]
                response_entry_price = latest_sig.get("entry_price", 0.0) # ดึง entry price ที่เก็บไว้
                response_sl_price = latest_sig.get("sl_price", 0.0) # ดึง SL ที่เก็บไว้
                response_tp_price = latest_sig.get("tp_price", 0.0) # ดึง TP ที่เก็บไว้
                response_best_entry = latest_sig.get("best_entry", 0.0) # ดึง best_entry ที่เก็บไว้
                response_nBars_SL = latest_sig.get("nBars_SL", 0) # ดึง nBars_SL ที่เก็บไว้
                response_threshold = latest_sig.get("threshold", 0.0) # ดึง threshold ที่เก็บไว้
                response_time_filters = latest_sig.get("time_filters", "") # ดึง time_filters ที่เก็บไว้
                response_spread = latest_sig.get("spread", 0) # ดึง spread ที่เก็บไว้

                if latest_sig["timestamp"]:
                    response_signal_bar_timestamp = latest_sig["timestamp"].timestamp()
                response_message = f"Latest signal: {response_signal} ({response_confidence:.4f}) for bar at {latest_sig['timestamp'].strftime('%Y.%m.%d %H:%M')}"
            # else: ถ้ายังไม่มี Signal ใน latest_signals_data ก็จะใช้ค่า Default

        # Return the JSON response including the latest known signal and confidence
        # MT5 EA จะต้อง Parse JSON response body นี้
        # ใน Response นี้ เราจะส่ง timestamp ของบาร์ล่าสุดที่ได้รับใน Batch กลับไปด้วย
        latest_received_bar_timestamp = df_batch.index.max().timestamp() if not df_batch.empty else None

        # return jsonify({
        #     "status": "OK",
        #     "message": response_message,
        #     "signal": response_signal, # ส่งค่า Signal กลับไปใน Response
        #     "confidence": response_confidence, # ส่งค่า Confidence กลับไปใน Response
        #     "bar_timestamp": latest_received_bar_timestamp, # Timestamp ของบาร์ล่าสุดใน Batch ที่ได้รับ
        #     "signal_bar_timestamp": response_signal_bar_timestamp # Timestamp ของแท่งที่ Signal ที่ส่งกลับใช้คำนวณ
        # }), 200

        # *** ส่วนที่เพิ่ม: สร้าง Dictionary ที่จะถูกแปลงเป็น JSON และ Print ออกมา ***

        symbol_info = SYMBOL_INFO[cleaned_symbol]
        digits = symbol_info["Digits"]

        response_confidence = round(response_confidence, 4)
        response_entry_price = round(response_entry_price, digits)
        response_sl_price = round(response_sl_price, digits)
        response_tp_price = round(response_tp_price, digits)

        # *** ดึงข้อมูล Multi-Model Analysis จาก latest signal ***
        response_market_condition = "unknown"
        response_action_type = "none"
        response_scenario_used = "none"
        response_analysis_summary = {}

        # *** เพิ่มข้อมูลสำหรับแสดงผลทั้ง 2 ระบบ ***
        response_trend_following_threshold = 0.5
        response_trend_following_nbars = 6
        response_counter_trend_threshold = 0.5
        response_counter_trend_nbars = 6

        # *** เพิ่มข้อมูล confidence ของทั้ง 2 ระบบ ***
        response_trend_following_buy_confidence = 0.0
        response_trend_following_sell_confidence = 0.0
        response_counter_trend_buy_confidence = 0.0
        response_counter_trend_sell_confidence = 0.0

        if latest_sig:
            response_market_condition = latest_sig.get("market_condition", "unknown")
            response_action_type = latest_sig.get("action_type", "none")
            response_scenario_used = latest_sig.get("scenario_used", "none")
            response_analysis_summary = latest_sig.get("analysis_summary", {})

            # โหลดพารามิเตอร์ทั้ง 2 ระบบ
            try:
                # tf_threshold = load_scenario_threshold(cleaned_symbol, timeframe_int, 'trend_following')
                # ct_threshold = load_scenario_threshold(cleaned_symbol, timeframe_int, 'counter_trend')

                tf_threshold_data = load_scenario_threshold(cleaned_symbol, f"M{timeframe_int}", 'trend_following')
                tf_threshold = tf_threshold_data['best_threshold']
                ct_threshold_data = load_scenario_threshold(cleaned_symbol, f"M{timeframe_int}", 'counter_trend')
                ct_threshold = ct_threshold_data['best_threshold']

                tf_nbars = load_scenario_nbars(cleaned_symbol, f"M{timeframe_int}", 'trend_following')
                ct_nbars = load_scenario_nbars(cleaned_symbol, f"M{timeframe_int}", 'counter_trend')

                if tf_threshold is not None:
                    response_trend_following_threshold = tf_threshold
                if tf_nbars is not None:
                    response_trend_following_nbars = tf_nbars
                if ct_threshold is not None:
                    response_counter_trend_threshold = ct_threshold
                if ct_nbars is not None:
                    response_counter_trend_nbars = ct_nbars

            except Exception as e:
                print(f"⚠️ Error loading dual system parameters: {e}")

            # *** ดึงข้อมูล confidence จาก analysis_summary ***
            print(f"🔍 DEBUG - response_analysis_summary keys: {list(response_analysis_summary.keys()) if response_analysis_summary else 'Empty'}")

            if response_analysis_summary:
                try:
                    # Trend Following confidence
                    if 'trend_following' in response_analysis_summary:
                        tf_data = response_analysis_summary['trend_following']
                        print(f"🔍 DEBUG - trend_following data: {tf_data}")
                        if 'buy' in tf_data:
                            response_trend_following_buy_confidence = float(tf_data['buy'].get('confidence', 0.0))
                            print(f"🔍 DEBUG - TF BUY confidence: {response_trend_following_buy_confidence}")
                        if 'sell' in tf_data:
                            response_trend_following_sell_confidence = float(tf_data['sell'].get('confidence', 0.0))
                            print(f"🔍 DEBUG - TF SELL confidence: {response_trend_following_sell_confidence}")

                    # Counter Trend confidence
                    if 'counter_trend' in response_analysis_summary:
                        ct_data = response_analysis_summary['counter_trend']
                        print(f"🔍 DEBUG - counter_trend data: {ct_data}")
                        if 'buy' in ct_data:
                            response_counter_trend_buy_confidence = float(ct_data['buy'].get('confidence', 0.0))
                            print(f"🔍 DEBUG - CT BUY confidence: {response_counter_trend_buy_confidence}")
                        if 'sell' in ct_data:
                            response_counter_trend_sell_confidence = float(ct_data['sell'].get('confidence', 0.0))
                            print(f"🔍 DEBUG - CT SELL confidence: {response_counter_trend_sell_confidence}")

                except Exception as e:
                    print(f"⚠️ Error extracting confidence values: {e}")
            else:
                print(f"⚠️ response_analysis_summary is empty - confidence values will remain 0.0")

        # *** 🔧 แก้ไขปัญหา: ตรวจสอบค่า confidence ก่อนส่งไป MT5 ***
        print(f"🔍 DEBUG - Before sending to MT5:")
        print(f"   response_signal: {response_signal}")
        print(f"   response_confidence: {response_confidence}")
        print(f"   type(response_confidence): {type(response_confidence)}")

        # ตรวจสอบว่า confidence เป็น 0.0 หรือไม่
        if response_confidence == 0.0 and response_signal in ["BUY", "SELL"]:
            print(f"🚨 CRITICAL: Sending confidence=0.0 to MT5 for {response_signal} signal!")
            print(f"   MT5 will reject this trade because confidence <= threshold")
            print(f"   Check why probability_tp_hit was 0.0 in the prediction process")

        response_payload = {
            "status": "OK",
            "message": response_message,
            "signal": response_signal,          # ค่า Signal ที่จะส่ง (BUY/SELL/HOLD)
            "class": response_class,            # ค่า Class Level ที่จะส่ง (STRONG_BUY/BUY/HOLD/SELL/STRONG_SELL)
            "confidence": round(response_confidence, 4),   # ค่า Confidence ที่จะส่ง
            "bar_timestamp": latest_received_bar_timestamp, # Timestamp ของบาร์ล่าสุดใน Batch ที่ได้รับ
            "signal_bar_timestamp": response_signal_bar_timestamp, # Timestamp ของแท่งที่ Signal ที่ส่งกลับใช้คำนวณ
            "symbol": cleaned_symbol,          # *** เพิ่ม: Symbol ที่ใช้คำนวณ Signal ***
            "timeframe_str": timeframe_str,     # *** เพิ่ม: Timeframe String ที่ใช้คำนวณ Signal ***
            "entry_price": response_entry_price,
            "sl_price": response_sl_price, # เพิ่ม SL เข้าไป
            "tp_price": response_tp_price,  # เพิ่ม TP เข้าไป
            "best_entry": response_best_entry,  # เพิ่ม best_entry เข้าไป
            "nBars_SL": response_nBars_SL,      # เพิ่ม nBars_SL เข้าไป
            "threshold": round(response_threshold,4),    # เพิ่ม threshold เข้าไป
            "time_filters": response_time_filters, # เพิ่ม time_filters เข้าไป
            "spread": response_spread,           # เพิ่ม spread เข้าไป
            # *** เพิ่มข้อมูล Multi-Model Analysis ***
            "market_condition": response_market_condition,  # สถานการณ์ตลาด (uptrend/downtrend/sideways)
            "action_type": response_action_type,            # ประเภทการกระทำ (buy/sell/none)
            "scenario_used": response_scenario_used,        # scenario ที่ใช้ (trend_following/counter_trend/none)
            # *** เพิ่มข้อมูลทั้ง 2 ระบบสำหรับ MT5 Display ***
            "trend_following_threshold": round(response_trend_following_threshold,4),
            "trend_following_nbars": response_trend_following_nbars,
            "counter_trend_threshold": round(response_counter_trend_threshold,4),
            "counter_trend_nbars": response_counter_trend_nbars,
            # *** เพิ่มข้อมูล confidence ของทั้ง 2 ระบบ ***
            "trend_following_buy_confidence": round(response_trend_following_buy_confidence,4),
            "trend_following_sell_confidence": round(response_trend_following_sell_confidence,4),
            "counter_trend_buy_confidence": round(response_counter_trend_buy_confidence,4),
            "counter_trend_sell_confidence": round(response_counter_trend_sell_confidence,4),
            # "analysis_summary": response_analysis_summary   # ผลการวิเคราะห์ทั้ง 2 ระบบ
        }

        # *** 🔧 แก้ไขปัญหา: ตรวจสอบความยาว JSON ที่ส่งไป MT5 ***
        response_json = json.dumps(response_payload, ensure_ascii=False)
        json_length = len(response_json)

        # 🔽 เรียกใช้ log
        log_response_payload(response_payload)

        print(f"\n📤 PYTHON SERVER - Sending JSON to MT5:")
        print(f"   📏 JSON Length: {json_length} characters")
        print(f"   📊 Confidence in JSON: {response_confidence}")
        print(f"   🎯 Signal: {response_signal}")

        # แสดงตัวอย่าง JSON ที่ส่ง (100 ตัวอักษรแรกและสุดท้าย)
        if json_length > 200:
            preview_start = response_json[:100]
            preview_end = response_json[-100:]
            print(f"   📝 JSON Preview: {preview_start}...{preview_end}")
        else:
            print(f"   📝 Full JSON: {response_json}")

        print(f"\n[{datetime.datetime.now()}] Sending JSON Response to MT5: {response_payload}")

        return jsonify(response_payload), 200 # ใช้ response_payload แทน dictionary ที่สร้างตรงๆ

    except BadRequest as e:
        print(f"[{datetime.datetime.now()}] Received data but failed due to Bad Request: {e}")
        print(f"Raw request data (bytes): {request.data}")
        return jsonify({"status": "ERROR", "message": f"Bad Request: {e}"}), 400

    except Exception as e:
        print(f"[{datetime.datetime.now()}] Error processing request: {e}")
        traceback.print_exc()
        return jsonify({"status": "ERROR", "message": f"Server Error: {e}"}), 500

# --- Optional: Endpoint to Get Signal ---
# ... (ไม่ได้เพิ่มในโค้ดนี้) ...

# --- Main Execution ---
if __name__ == "__main__":
    # initialize_mt5() # Uncomment ถ้า Python ต้องใช้ mt5.py ในการส่งคำสั่งเทรดเอง

    # Setup logging to file
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('server_debug.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    print(f"🚀 Starting HTTP Server on http://{HTTP_HOST}:{HTTP_PORT}")
    print(f"🔄 Using Multi-Model Architecture: {USE_MULTI_MODEL_ARCHITECTURE}")
    print(f"📁 Model base path: {MODEL_BASE_PATH}")
    print(f"📁 Threshold base path: {THRESHOLD_BASE_PATH}")
    print(f"📝 Debug log will be saved to: server_debug.log")

    try:
        app.run(host=HTTP_HOST, port=HTTP_PORT, debug=False)
    except Exception as e:
        print(f"Failed to start Flask server: {e}")
        traceback.print_exc()

    print("Server stopped.")
    # mt5.shutdown() # Uncomment ถ้ามีการ initialize_mt5