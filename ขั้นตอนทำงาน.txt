    # ==============================================
    if Save_Data_file:
        new_file_name = f'{timeframe}_{symbol}_Data_02a_features.csv'
        new_file_path = os.path.join(test_csv, new_file_name)
        df.to_csv(new_file_path, index=False)
        print(f'\n✅ Saved cleaned file to: {new_file_path}')
    # ==============================================
    print(f'🔍 ตรวจสอบ columns ข้อมูล df ก่อนเข้า combined : จำนวน {len(df.columns)}')
    print(df.columns.tolist())
    # ==============================================

print(f'\n🏗️ เปิดใช้งาน create features') if Steps_to_do else None

🏗️ เปิดใช้งาน __name__

🏗️ เปิดใช้งาน run main analysis

🏗️ เปิดใช้งาน main
🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน load scenario threshold
🏗️ เปิดใช้งาน get default threshold by scenario
🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)    
🏗️ เปิดใช้งาน load scenario nbars
🏗️ เปิดใช้งาน load and clean data < 01
🏗️ เปิดใช้งาน create features < 02, 05
🏗️ เปิดใช้งาน create resampled df < 03
🏗️ เปิดใช้งาน create features mtf < 04
🏗️ เปิดใช้งาน check stationarity
🏗️ เปิดใช้งาน check data quality

🏗️ เปิดใช้งาน load and process data < 06, 08
🏗️ เปิดใช้งาน load scenario models
🏗️ เปิดใช้งาน try trade with threshold adjustment
🏗️ เปิดใช้งาน create trade cycles with model
🏗️ เปิดใช้งาน analyze trade performance
🏗️ เปิดใช้งาน process trade targets < 07
🏗️ เปิดใช้งาน create multiclass target
🏗️ เปิดใช้งาน select features
🏗️ เปิดใช้งาน analyze time filters

🏗️ เปิดใช้งาน analyze sl tp performance
🏗️ เปิดใช้งาน analyze time performance

🏗️ เปิดใช้งาน train all scenario models < 09
🏗️ เปิดใช้งาน add market scenario column
🏗️ เปิดใช้งาน prepare scenario data
🏗️ เปิดใช้งาน filter data by scenario

🏗️ เปิดใช้งาน train scenario model
🏗️ เปิดใช้งาน get scenario specific param dist
🏗️ เปิดใช้งาน get scenario param distributions
🏗️ เปิดใช้งาน get lgbm params
🏗️ เปิดใช้งาน evaluate and decide model save
🏗️ เปิดใช้งาน validate model performance
🏗️ เปิดใช้งาน load previous model metrics
🏗️ เปิดใช้งาน compare model with previous
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน save current model metrics
🏗️ เปิดใช้งาน send model alert
🏗️ เปิดใช้งาน plot feature importance
🏗️ เปิดใช้งาน test random forest
🏗️ เปิดใช้งาน compare feature importance
🏗️ เปิดใช้งาน create scenario evaluation report
🏗️ เปิดใช้งาน create performance curves plot
🏗️ เปิดใช้งาน prepare scenario data
🏗️ เปิดใช้งาน filter data by scenario
🏗️ เปิดใช้งาน train scenario model < 10
🏗️ เปิดใช้งาน get scenario specific param dist
🏗️ เปิดใช้งาน get scenario param distributions
🏗️ เปิดใช้งาน get lgbm params
🏗️ เปิดใช้งาน evaluate and decide model save
🏗️ เปิดใช้งาน validate model performance
🏗️ เปิดใช้งาน load previous model metrics
🏗️ เปิดใช้งาน compare model with previous
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert
🏗️ เปิดใช้งาน plot feature importance
🏗️ เปิดใช้งาน test random forest
🏗️ เปิดใช้งาน compare feature importance
🏗️ เปิดใช้งาน create scenario evaluation report
🏗️ เปิดใช้งาน create performance curves plot
🏗️ เปิดใช้งาน save combined random forest importance
🏗️ เปิดใช้งาน create combined feature importance
🏗️ เปิดใช้งาน time series cv
🏗️ เปิดใช้งาน create multi scenario performance analysis
🏗️ เปิดใช้งาน create performance comparison plots
🏗️ เปิดใช้งาน create final and training results
🏗️ เปิดใช้งาน create group feature importance comparison
🏗️ เปิดใช้งาน create feature importance comparison plot
🏗️ เปิดใช้งาน create group feature importance comparison
🏗️ เปิดใช้งาน create feature importance comparison plot
🏗️ เปิดใช้งาน save training results to summary
🏗️ เปิดใช้งาน create training summary system
🏗️ เปิดใช้งาน create training progress report
🏗️ เปิดใช้งาน save training results to summary
🏗️ เปิดใช้งาน create training summary system
🏗️ เปิดใช้งาน create training progress report
🏗️ เปิดใช้งาน load scenario threshold
🏗️ เปิดใช้งาน load time filters
🏗️ เปิดใช้งาน record model performance
🏗️ เปิดใช้งาน format time filters display
🏗️ เปิดใช้งาน save entry config performance
🏗️ เปิดใช้งาน get entry config results folder
🏗️ เปิดใช้งาน get entry config folder name
🏗️ เปิดใช้งาน analyze cross asset feature importance