"""
🔧 Parameter Integration Helper
==============================

เครื่องมือสำหรับนำผลการทดสอบพารามิเตอร์ไปใช้กับ LightGBM_10_3.py และ WebRequest_Server_05.py
"""

import json
import os
import glob
from datetime import datetime

class ParameterIntegrator:
    def __init__(self):
        self.available_results = self._find_result_files()
    
    def _find_result_files(self):
        """หาไฟล์ผลลัพธ์ที่มีอยู่"""
        files = {}
        
        # หา baseline results
        baseline_files = glob.glob("baseline_result*.json")
        if baseline_files:
            files['baseline'] = baseline_files[0]
        
        # หา phase results
        phase_files = glob.glob("phase*_result*.json")
        for file in phase_files:
            if 'phase1' in file:
                files['phase1'] = file
            elif 'phase2' in file:
                files['phase2'] = file
        
        # หา multi-asset results
        multi_files = glob.glob("multi_asset_results_*.json")
        if multi_files:
            # เลือกไฟล์ล่าสุด
            latest_file = max(multi_files, key=os.path.getmtime)
            files['multi_asset'] = latest_file
        
        return files
    
    def get_best_parameters(self, source='phase2'):
        """ดึงพารามิเตอร์ที่ดีที่สุดจากแหล่งที่ระบุ"""
        if source not in self.available_results:
            print(f"❌ ไม่พบไฟล์ผลลัพธ์สำหรับ {source}")
            return None
        
        try:
            with open(self.available_results[source], 'r') as f:
                data = json.load(f)
            
            if source == 'multi_asset':
                # หาสินทรัพย์ที่ดีที่สุด
                results = data['results']
                best_result = max(results, key=lambda x: x['best_result']['performance_score'])
                return best_result['best_result']['parameters']
            
            elif source in ['phase1', 'phase2']:
                return data['best_parameters']
            
            elif source == 'baseline':
                return data['parameters']
            
        except Exception as e:
            print(f"❌ ไม่สามารถอ่านไฟล์ {source}: {e}")
            return None
    
    def get_asset_specific_parameters(self, symbol, timeframe):
        """ดึงพารามิเตอร์เฉพาะสำหรับสินทรัพย์และ timeframe"""
        if 'multi_asset' not in self.available_results:
            print("❌ ไม่พบผลลัพธ์ multi-asset")
            return None
        
        try:
            with open(self.available_results['multi_asset'], 'r') as f:
                data = json.load(f)
            
            # หาผลลัพธ์ที่ตรงกับ symbol และ timeframe
            for result in data['results']:
                if result['symbol'] == symbol and result['timeframe'] == timeframe:
                    return result['best_result']['parameters']
            
            print(f"❌ ไม่พบพารามิเตอร์สำหรับ {symbol}_{timeframe}")
            return None
            
        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาด: {e}")
            return None
    
    def generate_webserver_config(self, symbol=None, timeframe=None, output_file="optimized_parameters.py"):
        """สร้างไฟล์ config สำหรับ WebRequest Server"""
        
        # ลองหาพารามิเตอร์เฉพาะสินทรัพย์ก่อน
        if symbol and timeframe:
            params = self.get_asset_specific_parameters(symbol, timeframe)
            source_info = f"{symbol}_{timeframe} specific"
        else:
            # ใช้พารามิเตอร์ที่ดีที่สุดจาก phase2
            params = self.get_best_parameters('phase2')
            source_info = "Phase 2 best parameters"
        
        if not params:
            # fallback ไปใช้ baseline
            params = self.get_best_parameters('baseline')
            source_info = "Baseline parameters"
        
        if not params:
            print("❌ ไม่พบพารามิเตอร์ใดๆ")
            return None
        
        # สร้างไฟล์ config
        config_content = f'''"""
🔧 Optimized Parameters Configuration
====================================

Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Source: {source_info}
"""

# การตั้งค่าพารามิเตอร์ที่ปรับปรุงแล้ว
input_volume_spike = {params.get('input_volume_spike', 1.5)}
input_rsi_level_in = {params.get('input_rsi_level_in', 40)}
input_stop_loss_atr = {params.get('input_stop_loss_atr', 1.25)}
input_take_profit = {params.get('input_take_profit', 3.0)}

# พารามิเตอร์เพิ่มเติม (ถ้ามี)
input_initial_nbar_sl = {params.get('input_initial_nbar_sl', 4)}

# การตั้งค่าอื่นๆ ที่ไม่เปลี่ยนแปลง
input_rsi_level_over = 70
input_rsi_level_out = 35
input_pull_back = 0.45

# Time Filters (ถ้ามี)
ENABLE_TIME_FILTERS = False
DEFAULT_TIME_FILTERS = {{
    'days': [0, 1, 2, 3, 4],  # จันทร์-ศุกร์
    'hours': list(range(6, 22))  # 06:00-21:59
}}

print("✅ โหลดพารามิเตอร์ที่ปรับปรุงแล้ว")
print(f"   Volume Spike: {{input_volume_spike}}")
print(f"   RSI Level In: {{input_rsi_level_in}}")
print(f"   Stop Loss ATR: {{input_stop_loss_atr}}")
print(f"   Take Profit: {{input_take_profit}}")
'''
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            print(f"✅ สร้างไฟล์ config: {output_file}")
            print(f"   Source: {source_info}")
            print(f"   Parameters:")
            for key, value in params.items():
                print(f"     {key}: {value}")
            
            return output_file
            
        except Exception as e:
            print(f"❌ ไม่สามารถสร้างไฟล์ config: {e}")
            return None
    
    def update_webserver_file(self, webserver_file="WebRequest_Server_05.py", backup=True):
        """อัปเดตพารามิเตอร์ใน WebRequest Server โดยตรง"""
        
        if not os.path.exists(webserver_file):
            print(f"❌ ไม่พบไฟล์ {webserver_file}")
            return False
        
        # สร้าง backup
        if backup:
            backup_file = f"{webserver_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            try:
                with open(webserver_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ สร้าง backup: {backup_file}")
            except Exception as e:
                print(f"⚠️ ไม่สามารถสร้าง backup: {e}")
        
        # ดึงพารามิเตอร์ที่ดีที่สุด
        params = self.get_best_parameters('phase2')
        if not params:
            params = self.get_best_parameters('baseline')
        
        if not params:
            print("❌ ไม่พบพารามิเตอร์ที่จะอัปเดต")
            return False
        
        try:
            # อ่านไฟล์
            with open(webserver_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # อัปเดตพารามิเตอร์
            updated_lines = []
            updated_params = []
            
            for line in lines:
                original_line = line
                
                # ตรวจสอบและอัปเดตแต่ละพารามิเตอร์
                if line.strip().startswith('input_volume_spike =') and 'input_volume_spike' in params:
                    line = f"input_volume_spike = {params['input_volume_spike']}         # Updated by Parameter Optimizer\\n"
                    updated_params.append(f"input_volume_spike: {params['input_volume_spike']}")
                
                elif line.strip().startswith('input_rsi_level_in =') and 'input_rsi_level_in' in params:
                    line = f"input_rsi_level_in = {params['input_rsi_level_in']}          # Updated by Parameter Optimizer\\n"
                    updated_params.append(f"input_rsi_level_in: {params['input_rsi_level_in']}")
                
                elif line.strip().startswith('input_stop_loss_atr =') and 'input_stop_loss_atr' in params:
                    line = f"input_stop_loss_atr = {params['input_stop_loss_atr']}       # Updated by Parameter Optimizer\\n"
                    updated_params.append(f"input_stop_loss_atr: {params['input_stop_loss_atr']}")
                
                elif line.strip().startswith('input_take_profit =') and 'input_take_profit' in params:
                    line = f"input_take_profit = {params['input_take_profit']}          # Updated by Parameter Optimizer\\n"
                    updated_params.append(f"input_take_profit: {params['input_take_profit']}")
                
                updated_lines.append(line)
            
            # เขียนไฟล์ใหม่
            with open(webserver_file, 'w', encoding='utf-8') as f:
                f.writelines(updated_lines)
            
            print(f"✅ อัปเดต {webserver_file} สำเร็จ")
            print("   พารามิเตอร์ที่อัปเดต:")
            for param in updated_params:
                print(f"     {param}")
            
            return True
            
        except Exception as e:
            print(f"❌ ไม่สามารถอัปเดตไฟล์: {e}")
            return False
    
    def show_available_results(self):
        """แสดงไฟล์ผลลัพธ์ที่มีอยู่"""
        print("📁 ไฟล์ผลลัพธ์ที่พบ:")
        print("-" * 30)
        
        if not self.available_results:
            print("   ❌ ไม่พบไฟล์ผลลัพธ์")
            return
        
        for source, file in self.available_results.items():
            modified = datetime.fromtimestamp(os.path.getmtime(file))
            print(f"   {source}: {file} ({modified.strftime('%Y-%m-%d %H:%M')})")

def main():
    print("🔧 Parameter Integration Helper")
    print("=" * 40)
    
    integrator = ParameterIntegrator()
    integrator.show_available_results()
    
    if not integrator.available_results:
        print("\\n❌ ไม่พบไฟล์ผลลัพธ์ กรุณารันการทดสอบก่อน")
        return
    
    while True:
        print(f"\\n🔧 เลือกการดำเนินการ:")
        print("1. 📄 สร้างไฟล์ config พารามิเตอร์")
        print("2. 🔄 อัปเดต WebRequest Server โดยตรง")
        print("3. 🎯 ดูพารามิเตอร์เฉพาะสินทรัพย์")
        print("4. 📊 แสดงพารามิเตอร์ที่ดีที่สุด")
        print("5. 🚪 ออก")
        
        choice = input("\\nเลือก (1-5): ").strip()
        
        if choice == '1':
            symbol = input("Symbol (เว้นว่างสำหรับใช้ค่าทั่วไป): ").strip().upper()
            timeframe = input("Timeframe (เว้นว่างสำหรับใช้ค่าทั่วไป): ").strip()
            
            if not symbol:
                symbol = None
            if not timeframe:
                timeframe = None
            
            integrator.generate_webserver_config(symbol, timeframe)
        
        elif choice == '2':
            confirm = input("⚠️ จะอัปเดต WebRequest_Server_05.py โดยตรง ต้องการสร้าง backup ไหม? (y/n): ").strip().lower()
            backup = confirm == 'y'
            
            integrator.update_webserver_file(backup=backup)
        
        elif choice == '3':
            symbol = input("Symbol: ").strip().upper()
            timeframe = input("Timeframe: ").strip()
            
            params = integrator.get_asset_specific_parameters(symbol, timeframe)
            if params:
                print(f"\\n📊 พารามิเตอร์สำหรับ {symbol}_{timeframe}:")
                for key, value in params.items():
                    print(f"   {key}: {value}")
        
        elif choice == '4':
            print("\\n📊 พารามิเตอร์ที่ดีที่สุด (Phase 2):")
            params = integrator.get_best_parameters('phase2')
            if params:
                for key, value in params.items():
                    print(f"   {key}: {value}")
            else:
                print("   ❌ ไม่พบพารามิเตอร์")
        
        elif choice == '5':
            print("👋 ขอบคุณที่ใช้งาน!")
            break
        
        else:
            print("❌ กรุณาเลือก 1-5")

if __name__ == "__main__":
    main()
