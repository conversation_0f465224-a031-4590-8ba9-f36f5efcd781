
# ==============================================
# Model Versioning และ Performance Tracking
# ==============================================

import os
import json
import pickle
import shutil
from datetime import datetime

class ModelVersionManager:
    def __init__(self, backup_dir="model_backups"):
        self.backup_dir = backup_dir
        self.performance_file = os.path.join(backup_dir, "performance_history.json")
        os.makedirs(backup_dir, exist_ok=True)
        
    def save_model_version(self, model, scaler, features, performance_metrics, symbol, timeframe, version_note=""):
        """บันทึกโมเดลพร้อม version"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        version_dir = os.path.join(self.backup_dir, f"{symbol}_{timeframe}_{timestamp}")
        os.makedirs(version_dir, exist_ok=True)
        
        # บันทึกโมเดล
        model_path = os.path.join(version_dir, "model.pkl")
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        
        # บันทึก scaler
        scaler_path = os.path.join(version_dir, "scaler.pkl")
        with open(scaler_path, 'wb') as f:
            pickle.dump(scaler, f)
        
        # บันทึก features
        features_path = os.path.join(version_dir, "features.pkl")
        with open(features_path, 'wb') as f:
            pickle.dump(features, f)
        
        # บันทึก performance metrics
        version_info = {
            'timestamp': timestamp,
            'symbol': symbol,
            'timeframe': timeframe,
            'performance_metrics': performance_metrics,
            'version_note': version_note,
            'model_path': model_path,
            'scaler_path': scaler_path,
            'features_path': features_path
        }
        
        info_path = os.path.join(version_dir, "version_info.json")
        with open(info_path, 'w') as f:
            json.dump(version_info, f, indent=4)
        
        # อัปเดต performance history
        self._update_performance_history(version_info)
        
        print(f"✅ บันทึกโมเดล version: {timestamp}")
        return version_dir
    
    def _update_performance_history(self, version_info):
        """อัปเดต performance history"""
        
        history = []
        if os.path.exists(self.performance_file):
            with open(self.performance_file, 'r') as f:
                history = json.load(f)
        
        history.append(version_info)
        
        with open(self.performance_file, 'w') as f:
            json.dump(history, f, indent=4)
    
    def get_best_model(self, symbol, timeframe, metric='total_profit'):
        """หาโมเดลที่ดีที่สุด"""
        
        if not os.path.exists(self.performance_file):
            return None
        
        with open(self.performance_file, 'r') as f:
            history = json.load(f)
        
        # กรองตาม symbol และ timeframe
        filtered_history = [
            h for h in history 
            if h['symbol'] == symbol and h['timeframe'] == timeframe
        ]
        
        if not filtered_history:
            return None
        
        # หาโมเดลที่ดีที่สุดตาม metric
        best_model = max(filtered_history, key=lambda x: x['performance_metrics'].get(metric, -999999))
        
        return best_model
    
    def should_save_model(self, current_performance, symbol, timeframe, min_improvement=0.05):
        """ตัดสินใจว่าควรบันทึกโมเดลหรือไม่"""
        
        best_model = self.get_best_model(symbol, timeframe)
        
        if not best_model:
            print("✅ ไม่มีโมเดลก่อนหน้า - บันทึกโมเดลนี้")
            return True, "first_model"
        
        current_profit = current_performance.get('total_profit', 0)
        best_profit = best_model['performance_metrics'].get('total_profit', 0)
        
        improvement = (current_profit - best_profit) / abs(best_profit) if best_profit != 0 else 0
        
        if improvement > min_improvement:
            print(f"✅ โมเดลดีขึ้น {improvement:.2%} - บันทึกโมเดลนี้")
            return True, f"improved_by_{improvement:.2%}"
        else:
            print(f"⚠️ โมเดลไม่ดีขึ้น ({improvement:.2%}) - ไม่บันทึก")
            return False, f"not_improved_{improvement:.2%}"

# การใช้งานใน main function
# version_manager = ModelVersionManager()

# ก่อนบันทึกโมเดล
# should_save, reason = version_manager.should_save_model(
#     current_performance={'total_profit': total_profit, 'win_rate': win_rate},
#     symbol=symbol,
#     timeframe=timeframe
# )

# if should_save:
#     version_manager.save_model_version(
#         model=model,
#         scaler=scaler,
#         features=selected_features,
#         performance_metrics={'total_profit': total_profit, 'win_rate': win_rate},
#         symbol=symbol,
#         timeframe=timeframe,
#         version_note=reason
#     )
