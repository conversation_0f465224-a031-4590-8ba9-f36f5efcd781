"""
🔗 Parameter Testing Integration
===============================

ระบบเชื่อมต่อการทดสอบพารามิเตอร์กับโมเดลที่มีอยู่
รองรับการทดสอบแบบ batch และการบันทึกผลลัพธ์อัตโนมัติ

Author: AI Assistant
Date: 2025-01-25
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
import importlib.util
from typing import Dict, List, Any, Optional, Callable
import traceback
import time

# เพิ่ม path สำหรับ import โมเดลที่มีอยู่
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from Parameter_Optimization_System import ParameterOptimizer

class ParameterTester:
    """
    คลาสสำหรับทดสอบพารามิเตอร์กับโมเดลที่มีอยู่
    """
    
    def __init__(self, base_model_file: str = "LightGBM_10_3.py"):
        """
        เริ่มต้นระบบทดสอบ
        
        Args:
            base_model_file: ไฟล์โมเดลหลักที่จะใช้ทดสอบ
        """
        self.base_model_file = base_model_file
        self.optimizer = ParameterOptimizer()
        self.test_results = []
        
        # โหลดฟังก์ชันจากโมเดลหลัก
        self._load_model_functions()
    
    def _load_model_functions(self):
        """
        โหลดฟังก์ชันที่จำเป็นจากโมเดลหลัก
        """
        try:
            # โหลดโมเดลหลักเป็น module
            spec = importlib.util.spec_from_file_location("main_model", self.base_model_file)
            self.main_model = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.main_model)
            
            print(f"✅ โหลดโมเดลหลักสำเร็จ: {self.base_model_file}")
            
        except Exception as e:
            print(f"❌ ไม่สามารถโหลดโมเดลหลัก: {e}")
            self.main_model = None
    
    def run_single_test(self, 
                       symbol: str,
                       timeframe: str,
                       parameters: Dict,
                       data_file: str = None) -> Dict:
        """
        ทดสอบพารามิเตอร์ชุดเดียว
        
        Args:
            symbol: สัญลักษณ์การเทรด
            timeframe: ช่วงเวลา
            parameters: พารามิเตอร์ที่จะทดสอบ
            data_file: ไฟล์ข้อมูลสำหรับทดสอบ
            
        Returns:
            ผลการทดสอบ
        """
        try:
            print(f"\n🧪 ทดสอบพารามิเตอร์: {parameters}")
            
            # เตรียมข้อมูลสำหรับทดสอบ
            if data_file is None:
                data_file = f"CSV_Files_Fixed/{symbol}_{timeframe}_FIXED.csv"
            
            if not os.path.exists(data_file):
                raise FileNotFoundError(f"ไม่พบไฟล์ข้อมูล: {data_file}")
            
            # โหลดข้อมูล
            df = pd.read_csv(data_file, low_memory=False)
            print(f"📊 โหลดข้อมูล: {len(df)} แถว จาก {data_file}")

            # ลบ header row ที่เป็น text (ถ้ามี)
            if len(df) > 0 and df.iloc[0]['Close'] == '<CLOSE>':
                df = df.drop(0).reset_index(drop=True)
                print(f"🔧 ลบ header row - เหลือ {len(df)} แถว")

            # แปลงคอลัมน์ราคาเป็นตัวเลข
            price_columns = ['Open', 'High', 'Low', 'Close']
            for col in price_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # ลบแถวที่มี NaN หลังจากแปลง
            df = df.dropna(subset=price_columns).reset_index(drop=True)
            print(f"✅ ข้อมูลพร้อมใช้งาน: {len(df)} แถว")
            
            # จำลองการทดสอบ (ในที่นี้เป็นตัวอย่าง - ต้องปรับให้เข้ากับโมเดลจริง)
            test_result = self._simulate_trading_test(df, parameters, symbol, timeframe)
            
            return test_result
            
        except Exception as e:
            print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
            traceback.print_exc()
            return {
                'error': str(e),
                'total_profit': 0,
                'win_rate': 0,
                'total_trades': 0,
                'max_drawdown': 0,
                'profit_factor': 0,
                'expectancy': 0
            }
    
    def _simulate_trading_test(self, 
                              df: pd.DataFrame, 
                              parameters: Dict,
                              symbol: str,
                              timeframe: str) -> Dict:
        """
        จำลองการทดสอบการเทรด (ตัวอย่าง - ต้องปรับให้เข้ากับโมเดลจริง)
        
        Args:
            df: ข้อมูลราคา
            parameters: พารามิเตอร์ที่จะทดสอบ
            symbol: สัญลักษณ์
            timeframe: ช่วงเวลา
            
        Returns:
            ผลการทดสอบ
        """
        # ตัวอย่างการจำลอง - ในการใช้งานจริงต้องเรียกใช้ฟังก์ชันจากโมเดลหลัก
        
        # ดึงพารามิเตอร์
        nbar_sl = parameters.get('input_initial_nbar_sl', 4)
        stop_loss_atr = parameters.get('input_stop_loss_atr', 1.0)
        take_profit = parameters.get('input_take_profit', 2.0)
        rsi_level_in = parameters.get('input_rsi_level_in', 35)
        rsi_level_out = parameters.get('input_rsi_level_out', 30)
        
        # คำนวณ indicators พื้นฐาน
        df = self._calculate_basic_indicators(df)
        print(f"🔧 คำนวณ indicators เสร็จ - RSI range: {df['RSI'].min():.1f}-{df['RSI'].max():.1f}")

        # จำลองการเทรด
        trades = []
        in_trade = False
        entry_price = 0
        sl_price = 0
        tp_price = 0
        entry_time = None
        
        entry_signals = 0
        for i in range(50, len(df)):  # เริ่มจาก bar 50 เพื่อให้มี indicator เพียงพอ
            current_bar = df.iloc[i]

            if not in_trade:
                # ตรวจสอบสัญญาณ entry (เงื่อนไขง่ายๆ)
                if (current_bar['RSI'] < rsi_level_in and  # RSI ต่ำ (oversold)
                    current_bar['Close'] > current_bar['EMA20']):  # ราคาเหนือ EMA

                    entry_signals += 1
                    # เข้า trade
                    entry_price = current_bar['Close']
                    atr = current_bar['ATR']
                    
                    # คำนวณ SL และ TP
                    sl_price = entry_price - (stop_loss_atr * atr)
                    tp_price = entry_price + ((entry_price - sl_price) * take_profit)
                    
                    in_trade = True
                    entry_time = current_bar['Date'] if 'Date' in df.columns else i
                    
            else:
                # ตรวจสอบ exit conditions
                exit_condition = None
                exit_price = None
                
                if current_bar['Low'] <= sl_price:
                    exit_condition = "SL Hit"
                    exit_price = sl_price
                elif current_bar['High'] >= tp_price:
                    exit_condition = "TP Hit"
                    exit_price = tp_price
                elif current_bar['RSI'] < rsi_level_out:
                    exit_condition = "Technical Exit"
                    exit_price = current_bar['Close']
                
                if exit_condition:
                    # บันทึก trade
                    profit = exit_price - entry_price
                    profit_pips = profit * 10000  # สมมติเป็น forex
                    
                    trades.append({
                        'entry_time': entry_time,
                        'exit_time': current_bar['Date'] if 'Date' in df.columns else i,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'profit': profit,
                        'profit_pips': profit_pips,
                        'exit_condition': exit_condition
                    })
                    
                    in_trade = False

        print(f"📊 สรุปการจำลอง: Entry signals: {entry_signals}, Trades: {len(trades)}")

        # คำนวณสถิติ
        if not trades:
            return {
                'total_profit': 0,
                'win_rate': 0,
                'total_trades': 0,
                'max_drawdown': 0,
                'profit_factor': 0,
                'expectancy': 0,
                'trades': []
            }
        
        trades_df = pd.DataFrame(trades)
        
        total_profit = trades_df['profit_pips'].sum()
        winning_trades = trades_df[trades_df['profit_pips'] > 0]
        losing_trades = trades_df[trades_df['profit_pips'] <= 0]
        
        win_rate = (len(winning_trades) / len(trades_df)) * 100
        
        gross_profit = winning_trades['profit_pips'].sum() if len(winning_trades) > 0 else 0
        gross_loss = abs(losing_trades['profit_pips'].sum()) if len(losing_trades) > 0 else 1
        
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0
        expectancy = trades_df['profit_pips'].mean()
        
        # คำนวณ Max Drawdown
        cumulative_profit = trades_df['profit_pips'].cumsum()
        running_max = cumulative_profit.expanding().max()
        drawdown = running_max - cumulative_profit
        max_drawdown = drawdown.max()
        
        return {
            'total_profit': total_profit,
            'win_rate': win_rate,
            'total_trades': len(trades_df),
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'expectancy': expectancy,
            'trades': trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'gross_profit': gross_profit,
            'gross_loss': gross_loss
        }
    
    def _calculate_basic_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        คำนวณ indicators พื้นฐาน
        """
        df = df.copy()
        
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # EMA
        df['EMA20'] = df['Close'].ewm(span=20).mean()
        
        # ATR
        df['TR'] = np.maximum(
            df['High'] - df['Low'],
            np.maximum(
                abs(df['High'] - df['Close'].shift(1)),
                abs(df['Low'] - df['Close'].shift(1))
            )
        )
        df['ATR'] = df['TR'].rolling(window=14).mean()
        
        # Volume MA
        if 'Volume' in df.columns:
            df['Volume_MA'] = df['Volume'].rolling(window=20).mean()
        else:
            df['Volume'] = 1000  # ค่าเริ่มต้น
            df['Volume_MA'] = 1000
        
        return df
    
    def run_batch_test(self, 
                      symbol: str,
                      timeframe: str,
                      test_config: Dict,
                      data_file: str = None) -> List[Dict]:
        """
        ทดสอบพารามิเตอร์แบบ batch
        
        Args:
            symbol: สัญลักษณ์การเทรด
            timeframe: ช่วงเวลา
            test_config: การตั้งค่าการทดสอบ
            data_file: ไฟล์ข้อมูลสำหรับทดสอบ
            
        Returns:
            รายการผลการทดสอบ
        """
        print(f"\n🚀 เริ่มการทดสอบ batch สำหรับ {symbol}_{timeframe}")
        
        # สร้างชุดค่าพารามิเตอร์
        parameters_to_test = test_config.get('parameters_to_test', {})
        max_combinations = test_config.get('max_combinations', 100)
        
        param_combinations = self.optimizer.generate_parameter_combinations(
            parameters_to_test, max_combinations
        )
        
        print(f"📋 จำนวนชุดค่าที่จะทดสอบ: {len(param_combinations)}")
        
        # ทดสอบแต่ละชุดค่า
        results = []
        start_time = time.time()
        
        for i, params in enumerate(param_combinations, 1):
            print(f"\n⏳ ทดสอบชุดที่ {i}/{len(param_combinations)}")
            
            # ทดสอบ
            test_result = self.run_single_test(symbol, timeframe, params, data_file)
            
            # คำนวณคะแนน
            performance_score = self.optimizer.calculate_performance_score(test_result)
            
            # บันทึกผล
            result_record = {
                'test_id': i,
                'parameters': params,
                'results': test_result,
                'performance_score': performance_score,
                'test_timestamp': datetime.now().isoformat()
            }
            
            results.append(result_record)
            
            # บันทึกผลลัพธ์
            self.optimizer.save_test_result(
                symbol, timeframe, params, test_result, performance_score
            )
            
            # แสดงความคืบหน้า
            if i % 10 == 0 or i == len(param_combinations):
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(param_combinations) - i) * avg_time
                
                print(f"📊 ความคืบหน้า: {i}/{len(param_combinations)} "
                      f"({(i/len(param_combinations)*100):.1f}%)")
                print(f"⏱️ เวลาที่ใช้: {elapsed:.1f}s, "
                      f"เหลือประมาณ: {remaining:.1f}s")
        
        # เรียงผลลัพธ์ตามคะแนน
        results.sort(key=lambda x: x['performance_score'], reverse=True)
        
        print(f"\n✅ ทดสอบเสร็จสิ้น!")
        print(f"🏆 คะแนนสูงสุด: {results[0]['performance_score']:.2f}")
        print(f"📈 พารามิเตอร์ที่ดีที่สุด: {results[0]['parameters']}")
        
        return results
    
    def create_test_config_from_current(self, 
                                      current_params: Dict,
                                      focus_params: List[str] = None) -> Dict:
        """
        สร้าง config การทดสอบจากพารามิเตอร์ปัจจุบัน
        
        Args:
            current_params: พารามิเตอร์ปัจจุบัน
            focus_params: พารามิเตอร์ที่ต้องการโฟกัส
            
        Returns:
            Config การทดสอบ
        """
        if focus_params is None:
            focus_params = ['input_initial_nbar_sl', 'input_stop_loss_atr', 'input_take_profit']
        
        test_config = {
            'test_name': f"Parameter_Optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'parameters_to_test': {},
            'max_combinations': 50,  # เริ่มต้นด้วยจำนวนน้อย
            'current_parameters': current_params
        }
        
        # สร้างช่วงการทดสอบสำหรับแต่ละพารามิเตอร์
        for param in focus_params:
            if param in current_params:
                current_value = current_params[param]
                
                if param == 'input_initial_nbar_sl':
                    test_config['parameters_to_test'][param] = {
                        'min': max(2, current_value - 2),
                        'max': min(8, current_value + 2),
                        'step': 1
                    }
                elif param == 'input_stop_loss_atr':
                    test_config['parameters_to_test'][param] = {
                        'min': max(0.5, current_value - 0.5),
                        'max': min(3.0, current_value + 0.5),
                        'step': 0.25
                    }
                elif param == 'input_take_profit':
                    test_config['parameters_to_test'][param] = {
                        'min': max(1.0, current_value - 1.0),
                        'max': min(4.0, current_value + 1.0),
                        'step': 0.5
                    }
        
        return test_config

def run_example_test():
    """
    ตัวอย่างการใช้งาน
    """
    print("🔧 Parameter Testing Integration - ตัวอย่างการใช้งาน")
    print("=" * 60)
    
    # สร้างระบบทดสอบ
    tester = ParameterTester()
    
    # พารามิเตอร์ปัจจุบัน (จากโค้ดที่มีอยู่)
    current_params = {
        'input_initial_nbar_sl': 4,
        'input_stop_loss_atr': 1.00,
        'input_take_profit': 2.0,
        'input_rsi_level_in': 35,
        'input_rsi_level_out': 30
    }
    
    # สร้าง config การทดสอบ
    test_config = tester.create_test_config_from_current(
        current_params, 
        ['input_initial_nbar_sl', 'input_stop_loss_atr', 'input_take_profit']
    )
    
    print("📋 Config การทดสอบ:")
    print(json.dumps(test_config, indent=2, ensure_ascii=False))
    
    # ทดสอบพารามิเตอร์เดียว
    print(f"\n🧪 ทดสอบพารามิเตอร์ปัจจุบัน:")
    test_result = tester.run_single_test(
        symbol="GOLD",
        timeframe="M60", 
        parameters=current_params,
        data_file="CSV_Files_Fixed/GOLD_H1_FIXED.csv"  # ใช้ H1 แทน M60 ถ้าไม่มี
    )
    
    print(f"📊 ผลการทดสอบ:")
    for key, value in test_result.items():
        if key != 'trades':  # ไม่แสดง trades เพราะยาวเกินไป
            print(f"  {key}: {value}")
    
    # คำนวณคะแนน
    score = tester.optimizer.calculate_performance_score(test_result)
    print(f"🏆 คะแนนประสิทธิภาพ: {score:.2f}")
    
    print(f"\n✅ ตัวอย่างเสร็จสิ้น!")
    print(f"💡 เพื่อทดสอบแบบ batch ให้เรียกใช้ tester.run_batch_test()")

if __name__ == "__main__":
    run_example_test()