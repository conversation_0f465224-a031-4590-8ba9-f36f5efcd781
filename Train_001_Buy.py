import os
import pandas as pd # สำหรับอ่านข้อมูล
import numpy as np # สำหรับคำนวณค่าต่างๆ

from sklearn.preprocessing import StandardScaler

test_groups = {
    # "M30": [
    #     "CSV_Files_Fixed/GOLD_M30_FIXED.csv"
    # ],
    "M60": [
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    ]
}

# def procede_data(df):
#     return df

def main():

    for group_name, group_files in test_groups.items():
    
        for file_path in group_files:

            print(f"\nGroup: {group_name} File: {file_path}")

            # ==============================================
            # ต้องการ > ดึง symbol จากชื่อไฟล์ (เช่น GOLD_M30_FIXED.csv -> GOLD) ต้องใช้ os
            file_name = os.path.basename(file_path)
            symbol = file_name.split('_')[0]
            print(f"Symbol: {symbol}")

            # ==============================================
            # ต้องการ > อ่านค่า files เพื่อตรวจสอบจำนวน และวันเริ่มต้น-สิ้นสุด ต้องใช้ pandas
            try:
                df = pd.read_csv(file_path)

                # แสดงจำนวนแถว
                print(f"📊 Rows: {len(df)}")
                # 📊 Rows: 72033
                # ที่ excel ชื่อ column 1 + <Date> 1 + มีข้อมูล 72032 = 72034 หรือจำนวน row ทั้งหมด

                # แสดงชื่อคอลัมน์
                print(f"📋 Columns: {list(df.columns)}") 

                # ตรวจสอบว่า column เวลา ชื่ออะไร (เช่น 'time', 'datetime', 'timestamp')
                datetime_col = None
                for col in df.columns:
                    if 'time' in col.lower():
                        datetime_col = col
                        break

                # if datetime_col:
                #     # แปลงคอลัมน์เวลาเป็น datetime
                #     df[datetime_col] = pd.to_datetime(df[datetime_col], errors='coerce')

                #     start_date = df[datetime_col].min()
                #     end_date = df[datetime_col].max()

                #     print(f"📅 Start Date: {start_date}") # 2013.07.08	00:00:00
                #     print(f"📅 End Date:   {end_date}") # 2025.07.11	23:00:00
                # else:
                #     print("⚠️ No datetime column found")

                # ==============================================
                # ลบส่วนที่ไม่ใช้ปรโยชน์ row index 1
                
                # ตรวจสอบว่า row แรกมีปัญหา
                print(df.head(5))

                # ถ้ารู้ว่าแถวแรกไม่ใช่ข้อมูลที่ต้องใช้:
                df = df.iloc[1:].reset_index(drop=True)

                # ตรวจสอบว่า row หลังลบ
                print(df.head(5))

                # ==============================================
                # ต้องการ > ลบ 'Date', 'Time' แทนี่ด้วย 'DateTime' และจัดเรียงใหม่ 
                # ต้องการ > เนื่องจากขั้นตอนก่อนหน้าอ่านค่า วันเริ่มต้น-สิ้นสุด ไม่ได้

                # df = None # เป็นค่าเริ่มต้น เพื่อป้องกัน

                # ✅ กรณีมี 'Date' และ 'Time' คอลัมน์
                if 'Date' in df.columns and 'Time' in df.columns:
                    # รวมเป็นคอลัมน์ใหม่ 'DateTime'
                    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'], errors='coerce')

                    # ลบคอลัมน์เดิม
                    df.drop(['Date', 'Time'], axis=1, inplace=True)

                    # แสดงค่ามากสุดและน้อยสุด
                    min_dt = df['DateTime'].min()
                    max_dt = df['DateTime'].max()

                    print(f"📅 Start Date: {min_dt}")
                    print(f"📅 End Date:   {max_dt}")

                else:
                    print("⚠️ 'Date' หรือ 'Time' column ไม่พบ")

                # แสดงชื่อคอลัมน์ หลังจากลบ 'Date' และ 'Time' คอลัมน์
                print(f"📋 Columns: {list(df.columns)}") 

                # จัดลำดับคอลัมน์ใหม่ ให้นำ 'DateTime' มาอยู่ข้างหน้า
                cols = ['DateTime'] + [col for col in df.columns if col != 'DateTime']
                df = df[cols]

                # แสดงชื่อคอลัมน์ หลังจากลบ 'Date' และ 'Time' จัดเรียงใหม่
                print(f"📋 Columns: {list(df.columns)}") 
                
                # ==============================================
                # ต้องการ > สร้าง indicators 

                # df['Entry_DayOfWeek'] = df['DateTime'].dt.dayofweek # 0 Mon 1 Tue 2 Wed 3 Thu 4 Fri
                # df['Entry_Hour'] = df['DateTime'].dt.hour

                # สร้างฟีเจอร์สำหรับช่วงเวลาที่สำคัญ
                # df['IsMorning'] = ((df['Entry_Hour'] >= 8) & (df['Entry_Hour'] < 12)).astype(int)
                # df['IsAfternoon'] = ((df['Entry_Hour'] >= 12) & (df['Entry_Hour'] < 16)).astype(int)
                # df['IsEvening'] = ((df['Entry_Hour'] >= 16) & (df['Entry_Hour'] < 20)).astype(int)
                # df['IsNight'] = ((df['Entry_Hour'] >= 20) | (df['Entry_Hour'] < 4)).astype(int)

                # ต้องแน่ใจว่าก่อนจะเทียบหรือใช้ .shift() กับ .rolling() ต้องไม่มี NoneType
                # แปลงคอลัมน์ตัวเลขให้เป็น numeric (ถ้าไม่ได้ทำไว้ก่อนหน้านี้)
                df['Open'] = pd.to_numeric(df['Open'], errors='coerce')
                df['Close'] = pd.to_numeric(df['Close'], errors='coerce')
                df['High'] = pd.to_numeric(df['High'], errors='coerce')
                df['Low'] = pd.to_numeric(df['Low'], errors='coerce')

                # หลังจากแปลงแล้ว ค่อยใช้งานได้อย่างปลอดภัย
                open_shift = df['Open'].shift(1)
                high_shift = df['High'].shift(1)
                low_shift = df['Low'].shift(1)
                close_shift = df['Close'].shift(1)

                high_shiftx2 = df['High'].shift(2)
                low_shiftx2 = df['Low'].shift(2)

                df['Bar_CLp'] = close_shift.where(close_shift > 0.0, 0.0)

                # Price Action (ปลอดภัยแล้วเพราะ dtype เป็น float)
                df["Bar_CL"] = 0.0
                df.loc[df['Close'] > df['Open'], "Bar_CL"] = 1.0
                df.loc[df['Close'] < df['Open'], "Bar_CL"] = -1.0

                df['Price_Range'] = df["High"] - df["Low"]
                df['Price_Move'] = df["Close"] - df["Open"]

                # --- EMA Calculation (Classic, seeded with SMA) ---
                def classic_ema(series, span):
                    sma = series.rolling(window=span, min_periods=span).mean()
                    ema = pd.Series(index=series.index, dtype=float)
                    alpha = 2 / (span + 1)

                    for i in range(len(series)):
                        if i < span-1:
                            ema.iloc[i] = np.nan
                        elif i == span-1:
                            ema.iloc[i] = sma.iloc[i]
                        else:
                            ema.iloc[i] = alpha * series.iloc[i] + (1 - alpha) * ema.iloc[i-1]
                    return ema

                df['EMA50'] = classic_ema(df['Close'], 50)
                df['EMA100'] = classic_ema(df['Close'], 100)
                df['EMA200'] = classic_ema(df['Close'], 200)

                def classic_macd_ema(series: pd.Series, span: int):
                    s = pd.to_numeric(series, errors='coerce').copy()
                    alpha = 2.0 / (span + 1.0)
                    out = pd.Series(index=s.index, dtype='float64')

                    # หาตำแหน่งเริ่มต้นที่มี value จริง
                    valid_idx = s.first_valid_index()
                    if valid_idx is None:
                        return out

                    start = s.index.get_loc(valid_idx)
                    n = len(s)

                    # ถ้ามีข้อมูลมากพอสำหรับ seed SMA
                    if n - start >= span:
                        # seed position at index start+span-1
                        seed_pos = start + span - 1
                        seed = s.iloc[start: start + span].mean()
                        out.iloc[seed_pos] = seed
                        # recursive
                        for i in range(seed_pos + 1, n):
                            out.iloc[i] = alpha * s.iloc[i] + (1 - alpha) * out.iloc[i - 1]
                        # ข้อดี: ค่าก่อน seed_pos จะยังคง NaN
                    else:
                        # fallback: ถ้าไม่พอข้อมูล ให้ใช้ pandas ewm (จะ seed ด้วยค่าที่มี)
                        out = s.ewm(span=span, adjust=False).mean()

                    return out

                def mt5_like_macd_seeded(price: pd.Series, fast=12, slow=26, signal=9):
                    price = pd.to_numeric(price, errors='coerce')
                    ema_fast = classic_macd_ema(price, fast)
                    ema_slow = classic_macd_ema(price, slow)
                    macd_line = ema_fast - ema_slow

                    # สำหรับ signal line เรามักจะใช้ EMA บน macd_line (และสามารถ seed ด้วย SMA ของ macd ส่วนเริ่ม)
                    signal_line = classic_macd_ema(macd_line, signal)
                    hist = macd_line - signal_line
                    return ema_fast, ema_slow, macd_line, signal_line, hist
                
                ema12, ema26, macd_line, macd_signal, macd_hist = mt5_like_macd_seeded(df['Close'])

                df['EMA12'] = ema12
                df['EMA26'] = ema26
                df['MACD'] = macd_line
                df['MACD_SIGNAL'] = macd_signal
                df['MACD_HIST'] = macd_hist

                # ==============================================
                # ต้องการ > บันทึกไฟล์ csv เพื่อตรวจสอบ

                # กำหนด suffix ที่ต้องการเติมท้าย
                suffix_features = "_01_Features"

                # ดึงชื่อไฟล์ + ตำแหน่งโฟลเดอร์
                file_name = os.path.basename(file_path)               # GOLD_H1_FIXED.csv
                file_dir = os.path.dirname(file_path)                 # CSV_Files_Fixed

                # แยกชื่อและนามสกุล
                name, ext = os.path.splitext(file_name)               # ('GOLD_H1_FIXED', '.csv')

                # สร้างชื่อไฟล์ใหม่
                new_file_name = f"{name}{suffix_features}{ext}"                # 'GOLD_H1_FIXED_cleaned.csv'
                # new_file_path = os.path.join(file_dir, new_file_name) # 'CSV_Files_Fixed/GOLD_H1_FIXED_cleaned.csv'

                # # บันทึก DataFrame เป็น CSV
                # df.to_csv(new_file_path, index=False)

                # print(f"✅ Saved cleaned file to: {new_file_path}")

                # ==============================================
                # ต้องการ > ระบุตำแหน่งปลายทางไฟล์ LightGBM_Data เพื่อ save

                # โฟลเดอร์ปลายทาง
                output_dir = "LightGBM_Data"

                # ✅ ตรวจสอบ และสร้างโฟลเดอร์ปลายทาง หากยังไม่มี
                os.makedirs(output_dir, exist_ok=True)

                new_file_path = os.path.join(output_dir, new_file_name)   # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

                # ✅ บันทึกไฟล์ CSV
                df.to_csv(new_file_path, index=False)

                print(f"✅ Saved cleaned file to: {new_file_path}")

                # ==============================================
                # ต้องการ > ลบแถวที่มีค่า NaN ที่เกิดจากการคำนวณ Indicators / Features

                # ลบแถวที่มี NaN
                df_cleaned = df.dropna().reset_index(drop=True)

                # กำหนด suffix ที่ต้องการเติมท้าย
                suffix_cleaned = "_02_Cleaned"

                # สร้างชื่อไฟล์ใหม่
                new_file_name = f"{name}{suffix_cleaned}{ext}"                # 'GOLD_H1_FIXED_cleaned.csv'
                new_file_path = os.path.join(output_dir, new_file_name)   # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

                # ✅ บันทึกไฟล์ CSV
                df_cleaned.to_csv(new_file_path, index=False)

                print(f"✅ Saved cleaned file to: {new_file_path}")

                # ==============================================
                # ต้องการ > ลบคอลัมน์ที่ไม่เกี่ยวข้องกับการเรียนรู้ก่อน feed เข้าโมเดล เพื่อป้องกัน data leakage
                # ต้องการ > เปลี่ยนชื่อคอลัมน์ 'TickVol' เป็น 'Volume'

                # รายชื่อคอลัมน์ที่ไม่ใช้
                drop_cols = ["Vol","Col_8","Bar_CLp", "EMA12", "EMA26"]

                # ลบคอลัมน์
                df_train = df_cleaned.drop(columns=drop_cols, errors="ignore")

                # เปลี่ยนชื่อคอลัมน์ 'TickVol' เป็น 'Volume'
                if 'TickVol' in df_train.columns:
                    df_train = df_train.rename(columns={'TickVol': 'Volume'})

                # แก้ไข: แปลงคอลัมน์ Volume เป็นตัวเลข เพื่อให้ LightGBM ทำงานได้
                if 'Volume' in df_train.columns:
                    df_train['Volume'] = pd.to_numeric(df_train['Volume'], errors='coerce').fillna(0)

                # กำหนด suffix ที่ต้องการเติมท้าย
                suffix_train = "_03_Train"

                # สร้างชื่อไฟล์ใหม่
                new_file_name = f"{name}{suffix_train}{ext}"                # 'GOLD_H1_FIXED_cleaned.csv'
                new_file_path = os.path.join(output_dir, new_file_name)   # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

                # ✅ บันทึกไฟล์ CSV
                df_train.to_csv(new_file_path, index=False)

                print(f"✅ Saved cleaned file to: {new_file_path}")

                # ==============================================
                # ต้องการ > สร้างฟีเจอร์ Market Scenario

                df_Scenario = df_train.copy()

                conditions = [
                    # 1. Uptrend
                    (df_Scenario['Close'] > df_Scenario['EMA200']) & (df_Scenario['Low'] > df_Scenario['EMA200']) & (df_Scenario['MACD'] > df_Scenario['MACD_SIGNAL']),
                    
                    # 2. Uptrend Sideway
                    (df_Scenario['Close'] > df_Scenario['EMA200']) & ((df_Scenario['Low'] > df_Scenario['EMA200']) | (df_Scenario['MACD'] > df_Scenario['MACD_SIGNAL'])),
                    
                    # 3. Natural
                    (
                        ((df_Scenario['Close'] > df_Scenario['EMA200']) & (df_Scenario['Low'] > df_Scenario['EMA200']) & (df_Scenario['MACD'] > df_Scenario['MACD_SIGNAL'])) |
                        ((df_Scenario['Close'] > df_Scenario['EMA200']) & ((df_Scenario['Low'] > df_Scenario['EMA200']) | (df_Scenario['MACD'] > df_Scenario['MACD_SIGNAL']))) |
                        ((df_Scenario['Close'] < df_Scenario['EMA200']) & ((df_Scenario['High'] < df_Scenario['EMA200']) | (df_Scenario['MACD'] < df_Scenario['MACD_SIGNAL']))) |
                        ((df_Scenario['Close'] < df_Scenario['EMA200']) & (df_Scenario['High'] < df_Scenario['EMA200']) & (df_Scenario['MACD'] < df_Scenario['MACD_SIGNAL']))
                    ),
                    
                    # 4. Downtrend Sideway
                    (df_Scenario['Close'] < df_Scenario['EMA200']) & ((df_Scenario['High'] < df_Scenario['EMA200']) | (df_Scenario['MACD'] < df_Scenario['MACD_SIGNAL'])),
                    
                    # 5. Downtrend
                    (df_Scenario['Close'] < df_Scenario['EMA200']) & (df_Scenario['High'] < df_Scenario['EMA200']) & (df_Scenario['MACD'] < df_Scenario['MACD_SIGNAL'])
                ]

                choices = ['trend', 'Sideway', 'Natural', 'Sideway', 'trend'] # ปรับจาก 5 แบบเป็น 3 แบบ ['Uptrend', 'Uptrend_Sideway', 'Natural', 'Downtrend_Sideway', 'Downtrend']

                df_Scenario['Market_Scenario'] = np.select(conditions, choices, default='Natural')

                # df['Scenario_prev'] = df['Market_Scenario'].shift(1)

                # กำหนด suffix ที่ต้องการเติมท้าย
                suffix_scenario = "_05_Scenario"

                # สร้างชื่อไฟล์ใหม่
                new_file_name = f"{name}{suffix_scenario}{ext}"                # 'GOLD_H1_FIXED_cleaned.csv'
                new_file_path = os.path.join(output_dir, new_file_name)   # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

                # ✅ บันทึกไฟล์ CSV
                df_Scenario.to_csv(new_file_path, index=False)

                print(f"✅ Saved cleaned file to: {new_file_path}")

                # ==============================================
                # ต้องการ > Scale ค่าตัวเลขทั้งหมด เพื่อทำให้โมเดลเรียนรู้ได้ดีขึ้น
                # from sklearn.preprocessing import StandardScaler

                # df_scale = None # df_scale เริ่มจาก None ทำให้ df_scale[features] รันไม่ได้
                df_scale = df_train.copy()
                scaler = StandardScaler()
                # features = ['Open', 'High', 'Low', 'Close', 'Volume', ...] # ... ใน Python เป็น object Ellipsis ซึ่ง Pandas ไม่เข้าใจใน context ของ .loc[] หรือ column index
                features = df_train.select_dtypes(include=['float64', 'int64']).columns
                df_scale[features] = scaler.fit_transform(df_train[features])

                # กำหนด suffix ที่ต้องการเติมท้าย
                suffix_scale = "_04_Scale"

                # สร้างชื่อไฟล์ใหม่
                new_file_name = f"{name}{suffix_scale}{ext}"                # 'GOLD_H1_FIXED_cleaned.csv'
                new_file_path = os.path.join(output_dir, new_file_name)   # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

                # ✅ บันทึกไฟล์ CSV
                df_scale.to_csv(new_file_path, index=False)

                print(f"✅ Saved cleaned file to: {new_file_path}")

                # ==============================================
                # ต้องการ > สร้าง Target Column สำหรับการเทรนโมเดล
                # ในตัวอย่างนี้ เราจะสร้าง Target ง่ายๆ:
                # Target_Buy = 1 ถ้า Close ของแท่งถัดไปสูงกว่า Close ปัจจุบัน
                # Target_Buy = 0 ถ้า Close ของแท่งถัดไปไม่สูงกว่า Close ปัจจุบัน

                # ตรวจสอบให้แน่ใจว่าคอลัมน์ 'Close' เป็นตัวเลข
                df_train['Close'] = pd.to_numeric(df_train['Close'], errors='coerce')

                # สร้าง Target โดยใช้ .shift(-1) เพื่อดูราคา Close ของแท่งถัดไป
                # .shift(-1) จะเลื่อนข้อมูลขึ้น 1 แถว ทำให้เราสามารถเปรียบเทียบกับแท่งปัจจุบันได้
                df_train['Previous_Close'] = df_train['Close'].shift(-5)

                # สร้าง Target_Buy: 1 ถ้า Previous_Close > Close, 0 ถ้า Previous_Close <= Close
                # .astype(int) ใช้แปลง True/False เป็น 1/0
                df_train['Target_Buy'] = (
                    (df_train['Close'] > df_train['Open']) &
                    (df_train['Previous_Close'] > df_train['Close'])
                ).astype(int)

                df_train['Target_Sell'] = (
                    (df_train['Close'] < df_train['Open']) &
                    (df_train['Previous_Close'] < df_train['Close'])
                ).astype(int)


                # กำหนด suffix ที่ต้องการเติมท้าย
                suffix_target = "_06_Target_01_before"
                # สร้างชื่อไฟล์ใหม่
                new_file_name = f"{name}{suffix_target}{ext}"             # 'GOLD_H1_FIXED_cleaned.csv'
                new_file_path = os.path.join(output_dir, new_file_name)   # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv
                # ✅ บันทึกไฟล์ CSV
                df_train.to_csv(new_file_path, index=False)
                print(f"✅ Saved cleaned file to: {new_file_path}")


                # ลบแถวสุดท้ายที่มี NaN ใน Previous_Close (เพราะไม่มีแท่งถัดไปให้เปรียบเทียบ)
                df_train.dropna(subset=['Previous_Close', 'Target_Buy'], inplace=True)


                # กำหนด suffix ที่ต้องการเติมท้าย
                suffix_target = "_06_Target_02_after"
                # สร้างชื่อไฟล์ใหม่
                new_file_name = f"{name}{suffix_target}{ext}"             # 'GOLD_H1_FIXED_cleaned.csv'
                new_file_path = os.path.join(output_dir, new_file_name)   # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv
                # ✅ บันทึกไฟล์ CSV
                df_train.to_csv(new_file_path, index=False)
                print(f"✅ Saved cleaned file to: {new_file_path}")


                print(f"✅ สร้าง Target Column 'Target_Buy' เสร็จสิ้น")
                print(f"📊 จำนวนแถวหลังสร้าง Target: {len(df_train)}")
                print(f"📊 การกระจายของ Target_Buy: {df_train['Target_Buy'].value_counts()}")

                # ==============================================
                # ต้องการ > แบ่งข้อมูลสำหรับเทรนและทดสอบ
                # เราจะแยก Features (X) และ Target (y) ออกจากกัน
                # X คือคอลัมน์ที่เราจะใช้ทำนาย (ยกเว้น DateTime, Target, และคอลัมน์ที่ใช้สร้าง Target)
                # y คือคอลัมน์ที่เราต้องการให้โมเดลทำนาย (Target_Buy)

                from sklearn.model_selection import train_test_split
                import lightgbm as lgb
                from sklearn.metrics import accuracy_score, f1_score

                # กำหนดคอลัมน์ Features (X) และ Target (y)
                # ลบคอลัมน์ที่ไม่ใช่ Features ออกไป
                # 'DateTime' ไม่ใช่ Features ที่ใช้เทรนโมเดล
                # 'Previous_Close' เป็นคอลัมน์ที่เราใช้สร้าง Target ไม่ควรเป็น Features
                # 'Target_Buy' คือ Target ของเรา
                feature_columns = [col for col in df_train.columns if col not in ['DateTime', 'Previous_Close', 'Target_Buy']]

                X = df_train[feature_columns]
                y = df_train['Target_Buy']

                # แบ่งข้อมูลเป็น Training (80%) และ Test (20%)
                # random_state=42 เพื่อให้ผลลัพธ์การแบ่งข้อมูลเหมือนเดิมทุกครั้งที่รัน
                # stratify=y เพื่อรักษาสัดส่วนของ Class ใน Target ให้เท่ากันในทั้งสองชุดข้อมูล
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

                print(f"✅ แบ่งข้อมูลเป็น Training ({len(X_train)} แถว) และ Test ({len(X_test)} แถว) เสร็จสิ้น")
                print(f"📊 สัดส่วน Target ใน Training: {y_train.value_counts(normalize=True)}")
                print(f"📊 สัดส่วน Target ใน Test: {y_test.value_counts(normalize=True)}")

                # ==============================================
                # ต้องการ > เทรนโมเดล LightGBM
                # เราจะใช้ LGBMClassifier สำหรับปัญหา Classification (ทำนาย 0 หรือ 1)

                # สร้างโมเดล LightGBM Classifier
                # objective='binary' เพราะเรามี 2 Class (0 หรือ 1)
                # random_state=42 เพื่อให้ผลลัพธ์การเทรนเหมือนเดิมทุกครั้งที่รัน
                model = lgb.LGBMClassifier(objective='binary', random_state=42)

                # เทรนโมเดลด้วยข้อมูล Training Set
                print(f"🚀 เริ่มเทรนโมเดล LightGBM...")
                model.fit(X_train, y_train)
                print(f"✅ เทรนโมเดล LightGBM เสร็จสิ้น")

                # ==============================================
                # ต้องการ > ประเมินประสิทธิภาพโมเดล
                # เราจะใช้ข้อมูล Test Set ที่โมเดลไม่เคยเห็นมาก่อนในการประเมิน

                # ทำนายผลบน Test Set
                y_pred = model.predict(X_test)

                # คำนวณ Accuracy Score (ความแม่นยำ)
                accuracy = accuracy_score(y_test, y_pred)
                print(f"📊 Accuracy บน Test Set: {accuracy:.4f}")

                # คำนวณ F1-Score (เหมาะสำหรับ Class ที่ไม่สมดุล)
                # average='weighted' เพื่อคำนวณ F1-Score โดยถ่วงน้ำหนักตามจำนวนตัวอย่างของแต่ละ Class
                f1 = f1_score(y_test, y_pred, average='weighted')
                print(f"📊 F1-Score บน Test Set: {f1:.4f}")

                print(f"✅ ประเมินประสิทธิภาพโมเดลเสร็จสิ้น")

            except Exception as e:
                print(f"❌ Error reading file: {e}")
                print(f"❌ ข้ามขั้นตอนการเตรียมข้อมูล")

if __name__ == "__main__":
    main()