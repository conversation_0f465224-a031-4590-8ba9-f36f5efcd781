#!/usr/bin/env python3
"""
Integration Guide for Financial Analysis System
คู่มือการผสานรวมระบบวิเคราะห์ทางการเงินกับ create_trade_cycles_with_model()

วิธีการใช้งาน:
1. เพิ่มโค้ดนี้ในไฟล์หลักที่มี create_trade_cycles_with_model()
2. เรียกใช้ฟังก์ชันวิเคราะห์ทางการเงินหลังจากประมวลผลแต่ละสัญลักษณ์
3. รันการวิเคราะห์ทั้งหมดเมื่อเสร็จสิ้น
"""

# ===== การ Import ที่จำเป็น =====
from financial_analysis_system import FinancialAnalysisSystem
from financial_integration import integrate_with_trade_cycles, CURRENT_PRICES

# ===== ตัวอย่างการผสานรวม =====

def enhanced_create_trade_cycles_with_model(symbol, timeframe, financial_system=None):
    """
    ฟังก์ชัน create_trade_cycles_with_model() ที่ปรับปรุงแล้ว
    เพิ่มการวิเคราะห์ทางการเงิน
    
    Args:
        symbol: สัญลักษณ์ (เช่น EURUSD, GOLD)
        timeframe: ไทม์เฟรม (M30, M60)
        financial_system: ระบบวิเคราะห์ทางการเงิน (optional)
    """
    
    print(f"🏗️ เปิดใช้งาน create_trade_cycles_with_model สำหรับ {symbol} {timeframe}")
    
    # ===== โค้ดเดิมของ create_trade_cycles_with_model() =====
    # ... (โค้ดการสร้าง trade cycles ตามปกติ)
    
    # สมมุติว่าได้ผลลัพธ์เป็น DataFrame
    # trade_cycles_df = ... (ผลจากการประมวลผล)
    
    # ===== เพิ่มการวิเคราะห์ทางการเงิน =====
    if financial_system is not None:
        print(f"💰 เริ่มการวิเคราะห์ทางการเงินสำหรับ {symbol} {timeframe}")
        
        # ผสานรวมกับระบบวิเคราะห์ทางการเงิน
        success = integrate_with_trade_cycles(
            symbol=symbol,
            timeframe=timeframe,
            trade_cycles_df=trade_cycles_df,  # ใช้ผลจากการประมวลผลจริง
            financial_system=financial_system
        )
        
        if success:
            print(f"✅ วิเคราะห์ทางการเงิน {symbol} {timeframe} สำเร็จ")
        else:
            print(f"⚠️ วิเคราะห์ทางการเงิน {symbol} {timeframe} ไม่สำเร็จ")
    
    # return trade_cycles_df  # คืนค่าตามปกติ

# ===== ตัวอย่างการใช้งานในลูปหลัก =====

def main_trading_analysis():
    """ฟังก์ชันหลักสำหรับการวิเคราะห์การเทรด"""
    
    print("🚀 เริ่มการวิเคราะห์การเทรดแบบสมบูรณ์")
    
    # สร้างระบบวิเคราะห์ทางการเงิน
    financial_system = FinancialAnalysisSystem(base_currency='USD', leverage=500)
    
    # กำหนดสัญลักษณ์และไทม์เฟรมที่ต้องวิเคราะห์
    TEST_GROUPS = ['AUDUSD', 'EURUSD', 'GBPUSD', 'GOLD', 'NZDUSD', 'USDCAD', 'USDJPY']
    TIMEFRAMES = ['M30', 'M60']
    
    # ประมวลผลแต่ละสัญลักษณ์และไทม์เฟรม
    for symbol in TEST_GROUPS:
        for timeframe in TIMEFRAMES:
            print(f"\n📊 ประมวลผล {symbol} {timeframe}")
            
            # เรียกใช้ฟังก์ชันที่ปรับปรุงแล้ว
            enhanced_create_trade_cycles_with_model(
                symbol=symbol,
                timeframe=timeframe,
                financial_system=financial_system
            )
    
    # รันการวิเคราะห์ทางการเงินทั้งหมด
    print("\n🔍 รันการวิเคราะห์ทางการเงินทั้งหมด...")
    
    results = financial_system.run_complete_analysis(account_balance=1000)
    
    if results:
        print("\n🎉 การวิเคราะห์เสร็จสมบูรณ์!")
        print(f"📁 ผลลัพธ์บันทึกที่: {financial_system.results_folder}")
        
        # แสดงผลสรุป
        combined = results['combined_analysis']
        risk = results['risk_analysis']
        
        print(f"\n📈 สรุปผลการวิเคราะห์:")
        print(f"   💰 ยอดเงินในบัญชี: ${risk['account_balance']:,.2f}")
        print(f"   📊 จำนวนการเทรดทั้งหมด: {combined['total_trades']}")
        print(f"   💵 กำไรรวม (1.0 lot): ${combined['total_profit_usd']:,.2f}")
        print(f"   📉 Drawdown สูงสุด (1.0 lot): ${combined['max_drawdown_usd']:,.2f}")
        print(f"   🎯 ขนาดล็อตที่แนะนำ: {risk['recommended_lot_size']:.4f}")
        print(f"   ⚠️ ความเสี่ยงสูงสุด: {risk['max_risk_percentage']:.2f}%")
        
        return results
    else:
        print("❌ การวิเคราะห์ไม่สำเร็จ")
        return None

# ===== วิธีการปรับปรุงโค้ดเดิม =====

def integration_steps():
    """ขั้นตอนการผสานรวมกับโค้ดเดิม"""
    
    steps = """
    📋 ขั้นตอนการผสานรวมระบบวิเคราะห์ทางการเงิน:
    
    1. 📥 Import modules ที่จำเป็น:
       from financial_analysis_system import FinancialAnalysisSystem
       from financial_integration import integrate_with_trade_cycles
    
    2. 🏗️ สร้างระบบวิเคราะห์ทางการเงิน (ก่อนเข้าลูป):
       financial_system = FinancialAnalysisSystem(base_currency='USD', leverage=500)
    
    3. 🔄 เพิ่มในลูปของ create_trade_cycles_with_model():
       # หลังจากได้ trade_cycles_df แล้ว
       integrate_with_trade_cycles(symbol, timeframe, trade_cycles_df, financial_system)
    
    4. 📊 รันการวิเคราะห์ทั้งหมด (หลังจากลูปเสร็จ):
       results = financial_system.run_complete_analysis(account_balance=1000)
    
    5. 📁 ผลลัพธ์จะบันทึกใน Financial_Analysis_Results/:
       - complete_financial_analysis.json
       - risk_management_table.csv
       - financial_analysis_report.txt
       - trading_performance_analysis.png
       - [Symbol]_[Timeframe]_financial_analysis.json
    """
    
    print(steps)

# ===== ตัวอย่างการปรับปรุงข้อมูลราคา =====

def update_current_prices():
    """อัปเดตราคาปัจจุบันสำหรับการคำนวณ"""
    
    # *** อัปเดตราคาเหล่านี้ให้เป็นราคาปัจจุบัน ***
    updated_prices = {
        'GOLD': 2650.0,      # อัปเดตจากราคาจริง
        'EURUSD': 1.0850,    # อัปเดตจากราคาจริง
        'GBPUSD': 1.2650,    # อัปเดตจากราคาจริง
        'AUDUSD': 0.6750,    # อัปเดตจากราคาจริง
        'NZDUSD': 0.6150,    # อัปเดตจากราคาจริง
        'USDCAD': 1.3550,    # อัปเดตจากราคาจริง
        'USDJPY': 148.50     # อัปเดตจากราคาจริง
    }
    
    # อัปเดตในไฟล์ financial_integration.py
    print("💡 อย่าลืมอัปเดตราคาใน CURRENT_PRICES ในไฟล์ financial_integration.py")
    
    return updated_prices

# ===== การตั้งค่าความเสี่ยง =====

def risk_management_settings():
    """การตั้งค่าการจัดการความเสี่ยง"""
    
    settings = """
    ⚙️ การตั้งค่าการจัดการความเสี่ยง:
    
    📊 ระดับความเสี่ยงที่แนะนำ:
    - 🟢 Safe (ปลอดภัย): ≤ 2% ต่อวัน
    - 🟡 Moderate (ปานกลาง): 2-5% ต่อสัปดาห์
    - 🔴 High Risk (เสี่ยงสูง): > 10% ของทั้งหมด
    
    💰 ตัวอย่างการคำนวณสำหรับบัญชี $1,000:
    - ความเสี่ยง 2%: ขนาดล็อต 0.0053
    - ความเสี่ยง 5%: ขนาดล็อต 0.0133
    - ความเสี่ยง 10%: ขนาดล็อต 0.0267
    
    🎯 สูตรการคำนวณ:
    Lot Size = (Account Balance × Risk %) / Max Drawdown (1.0 lot)
    """
    
    print(settings)

if __name__ == "__main__":
    print("📖 คู่มือการผสานรวมระบบวิเคราะห์ทางการเงิน")
    print("=" * 80)
    
    # แสดงขั้นตอนการผสานรวม
    integration_steps()
    
    print("\n" + "=" * 80)
    
    # แสดงการตั้งค่าความเสี่ยง
    risk_management_settings()
    
    print("\n" + "=" * 80)
    
    # แสดงการอัปเดตราคา
    update_current_prices()
    
    print("\n🎯 พร้อมใช้งาน! ทดสอบด้วย: python test_financial_analysis.py")
