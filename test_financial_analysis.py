#!/usr/bin/env python3
"""
Test Financial Analysis System
ทดสอบระบบวิเคราะห์ทางการเงิน
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

# Import modules
from financial_analysis_system import FinancialAnalysisSystem
from financial_integration import run_complete_financial_analysis, CURRENT_PRICES

def test_pips_calculation():
    """ทดสอบการคำนวณ pips value และ margin"""
    
    print("🧪 ทดสอบการคำนวณ Pips Value และ Margin")
    print("=" * 60)
    
    financial_system = FinancialAnalysisSystem(base_currency='USD', leverage=500)
    
    # ทดสอบแต่ละสัญลักษณ์
    test_symbols = {
        'GOLD': 2650.0,
        'EURUSD': 1.0850,
        'USDJPY': 148.50
    }
    
    for symbol, price in test_symbols.items():
        print(f"\n📊 {symbol} @ {price}")
        print("-" * 30)
        
        # คำนวณสำหรับ lot size ต่างๆ
        for lot_size in [0.01, 0.1, 1.0]:
            result = financial_system.calculate_pips_value_and_margin(symbol, price, lot_size)
            
            print(f"Lot Size: {lot_size}")
            print(f"  Pips Value per Point: ${result['pips_value_per_point']:.4f}")
            print(f"  Pips Value per Pip: ${result['pips_value_per_pip']:.4f}")
            print(f"  Margin Required: ${result['margin_required']:.2f}")
            print()

def test_risk_calculation():
    """ทดสอบการคำนวณความเสี่ยง"""
    
    print("🧪 ทดสอบการคำนวณความเสี่ยง")
    print("=" * 60)
    
    # สร้างข้อมูลจำลอง
    financial_system = FinancialAnalysisSystem()
    
    # สร้างข้อมูลการเทรดจำลอง
    sample_trades = []
    cumulative_profit = 0
    
    for i in range(100):
        profit = np.random.normal(10, 50)  # เฉลี่ย 10 USD, std 50 USD
        cumulative_profit += profit
        
        sample_trades.append({
            'symbol': 'EURUSD',
            'timeframe': 'M30',
            'open_time': datetime.now() - timedelta(days=100-i),
            'close_time': datetime.now() - timedelta(days=100-i) + timedelta(hours=2),
            'direction': 'BUY',
            'open_price': 1.0850,
            'close_price': 1.0850 + (profit * 0.0001),
            'pips_profit': profit,
            'usd_profit': profit,
            'margin_required': 21.7,
            'pips_value_per_pip': 1.0,
            'lot_size': 1.0
        })
    
    # คำนวณ drawdown
    profits = [t['usd_profit'] for t in sample_trades]
    cumulative_profits = np.cumsum(profits)
    running_max = np.maximum.accumulate(cumulative_profits)
    drawdown = cumulative_profits - running_max
    max_drawdown = abs(min(drawdown))
    
    # สร้างข้อมูลการวิเคราะห์จำลอง
    combined_analysis = {
        'total_trades': len(sample_trades),
        'total_profit_usd': sum(profits),
        'max_drawdown_usd': max_drawdown,
        'total_margin_required': 21.7,
        'trades': sample_trades,
        'symbols_analyzed': ['EURUSD_M30'],
        'analysis_date': datetime.now().isoformat()
    }
    
    # ทดสอบการคำนวณความเสี่ยงกับยอดเงินต่างๆ
    account_balances = [500, 1000, 2000, 5000, 10000]
    
    for balance in account_balances:
        print(f"\n💰 Account Balance: ${balance:,}")
        print("-" * 40)
        
        risk_analysis = financial_system.calculate_risk_management(combined_analysis, balance)
        
        if risk_analysis:
            print(f"Max Drawdown (1.0 lot): ${risk_analysis['max_drawdown_at_1lot']:.2f}")
            print(f"Recommended Lot Size: {risk_analysis['recommended_lot_size']:.4f}")
            print(f"Max Risk Percentage: {risk_analysis['max_risk_percentage']:.2f}%")
            
            # สร้างตารางความเสี่ยง
            risk_table = financial_system.create_risk_table(risk_analysis)
            print("\nRisk Management Table:")
            print(risk_table.to_string(index=False))

def test_complete_system():
    """ทดสอบระบบทั้งหมด"""
    
    print("🧪 ทดสอบระบบวิเคราะห์ทางการเงินแบบสมบูรณ์")
    print("=" * 60)
    
    # รันการวิเคราะห์แบบสมบูรณ์
    results = run_complete_financial_analysis(account_balance=1000)
    
    if results:
        print("\n✅ การทดสอบสำเร็จ!")
        
        # แสดงผลสรุป
        combined = results['combined_analysis']
        risk = results['risk_analysis']
        
        print(f"\n📊 สรุปผลการทดสอบ:")
        print(f"   Total Trades: {combined['total_trades']}")
        print(f"   Total Profit: ${combined['total_profit_usd']:.2f}")
        print(f"   Max Drawdown: ${combined['max_drawdown_usd']:.2f}")
        print(f"   Recommended Lot Size: {risk['recommended_lot_size']:.4f}")
        
        # แสดงตารางความเสี่ยง
        print(f"\n📋 Risk Management Table:")
        print(results['risk_table'].to_string(index=False))
        
    else:
        print("❌ การทดสอบไม่สำเร็จ")

def show_usage_example():
    """แสดงตัวอย่างการใช้งาน"""
    
    print("📖 ตัวอย่างการใช้งานระบบวิเคราะห์ทางการเงิน")
    print("=" * 60)
    
    example_code = '''
# 1. Import modules
from financial_analysis_system import FinancialAnalysisSystem
from financial_integration import integrate_with_trade_cycles

# 2. สร้างระบบวิเคราะห์
financial_system = FinancialAnalysisSystem(base_currency='USD', leverage=500)

# 3. ประมวลผลแต่ละสัญลักษณ์ (ใน loop ของ create_trade_cycles_with_model)
for symbol in ['EURUSD', 'GOLD', 'GBPUSD']:  # ตาม TEST_GROUPS
    for timeframe in ['M30', 'M60']:
        
        # เรียกใช้ create_trade_cycles_with_model() ตามปกติ
        trade_cycles_df = create_trade_cycles_with_model(symbol, timeframe)
        
        # เพิ่มการวิเคราะห์ทางการเงิน
        integrate_with_trade_cycles(symbol, timeframe, trade_cycles_df, financial_system)

# 4. รันการวิเคราะห์ทั้งหมดเมื่อเสร็จ
results = financial_system.run_complete_analysis(account_balance=1000)

# 5. ผลลัพธ์จะบันทึกใน Financial_Analysis_Results/
#    - complete_financial_analysis.json
#    - risk_management_table.csv
#    - financial_analysis_report.txt
#    - trading_performance_analysis.png
'''
    
    print(example_code)
    
    print("\n📁 ไฟล์ที่จะถูกสร้าง:")
    print("   📄 complete_financial_analysis.json - ข้อมูลการวิเคราะห์ทั้งหมด")
    print("   📊 risk_management_table.csv - ตารางการจัดการความเสี่ยง")
    print("   📝 financial_analysis_report.txt - รายงานสรุป")
    print("   📈 trading_performance_analysis.png - กราฟผลการเทรด")
    print("   📋 [Symbol]_[Timeframe]_financial_analysis.json - ข้อมูลแต่ละสัญลักษณ์")

if __name__ == "__main__":
    print("🚀 เริ่มการทดสอบระบบวิเคราะห์ทางการเงิน")
    print("=" * 80)
    
    # ทดสอบการคำนวณ pips
    test_pips_calculation()
    
    print("\n" + "=" * 80)
    
    # ทดสอบการคำนวณความเสี่ยง
    test_risk_calculation()
    
    print("\n" + "=" * 80)
    
    # ทดสอบระบบทั้งหมด
    test_complete_system()
    
    print("\n" + "=" * 80)
    
    # แสดงตัวอย่างการใช้งาน
    show_usage_example()
    
    print("\n🎉 การทดสอบเสร็จสมบูรณ์!")
