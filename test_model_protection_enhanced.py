"""
🧪 Test Enhanced Model Protection System
========================================

ทดสอบระบบป้องกันโมเดลที่ปรับปรุงแล้ว
จำลองสถานการณ์การเทรนที่ให้ผลแย่ลง
"""

import os
import json
from model_protection_system import ModelProtectionSystem

def clean_test_files():
    """ลบไฟล์ทดสอบ"""
    test_files = [
        "best_performance_GOLD_MM60.json",
        "model_protection.log"
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ ลบไฟล์: {file}")

def test_scenario_1_first_model():
    """ทดสอบ: โมเดลแรก (ไม่มีโมเดลก่อนหน้า)"""
    
    print("\n🧪 ทดสอบ Scenario 1: โมเดลแรก")
    print("="*50)
    
    protection = ModelProtectionSystem(min_profit_threshold=5000)
    
    # โมเดลแรก - ใช้เทคนิคอย่างเดียว (ผลแย่)
    first_performance = {
        'total_profit': -27159.00,
        'win_rate': 0.1516,  # 15.16%
        'expectancy': -3.3839,
        'max_drawdown': 76069.00,
        'total_trades': 8026
    }
    
    should_save, reason = protection.should_save_model(
        current_performance=first_performance,
        symbol="GOLD",
        timeframe="MM60"
    )
    
    print(f"📊 ผลการตัดสินใจ: {should_save} ({reason})")
    
    # ควรไม่บันทึกเพราะกำไรติดลบ
    expected = False  # เปลี่ยนจาก True เป็น False
    if should_save == expected:
        print("✅ ผ่าน: ไม่บันทึกโมเดลที่ขาดทุน")
        return True
    else:
        print(f"❌ ล้มเหลว: คาดหวัง {expected}, ได้ {should_save}")
        return False

def test_scenario_2_good_model():
    """ทดสอบ: โมเดลที่ดี (ครั้งที่ 2)"""
    
    print("\n🧪 ทดสอบ Scenario 2: โมเดลที่ดี")
    print("="*50)
    
    protection = ModelProtectionSystem(min_profit_threshold=5000)
    
    # โมเดลที่ดี - เทคนิค + ML
    good_performance = {
        'total_profit': 5338.00,
        'win_rate': 0.1636,  # 16.36%
        'expectancy': 24.9439,
        'max_drawdown': 4465.00,
        'total_trades': 214
    }
    
    should_save, reason = protection.should_save_model(
        current_performance=good_performance,
        symbol="GOLD",
        timeframe="MM60"
    )
    
    print(f"📊 ผลการตัดสินใจ: {should_save} ({reason})")
    
    # ควรบันทึกเพราะเป็นโมเดลแรกที่กำไร
    expected = True
    if should_save == expected:
        print("✅ ผ่าน: บันทึกโมเดลที่ดี")
        return True
    else:
        print(f"❌ ล้มเหลว: คาดหวัง {expected}, ได้ {should_save}")
        return False

def test_scenario_3_worse_model():
    """ทดสอบ: โมเดลที่แย่ลง (ครั้งที่ 3)"""
    
    print("\n🧪 ทดสอบ Scenario 3: โมเดลที่แย่ลง")
    print("="*50)
    
    protection = ModelProtectionSystem(min_profit_threshold=5000)
    
    # โมเดลที่แย่ลง
    worse_performance = {
        'total_profit': -4624.00,
        'win_rate': 0.1381,  # 13.81%
        'expectancy': -25.5470,
        'max_drawdown': 7427.00,
        'total_trades': 181
    }
    
    should_save, reason = protection.should_save_model(
        current_performance=worse_performance,
        symbol="GOLD",
        timeframe="MM60"
    )
    
    print(f"📊 ผลการตัดสินใจ: {should_save} ({reason})")
    
    # ไม่ควรบันทึกเพราะแย่ลง
    expected = False
    if should_save == expected:
        print("✅ ผ่าน: ไม่บันทึกโมเดลที่แย่ลง")
        return True
    else:
        print(f"❌ ล้มเหลว: คาดหวัง {expected}, ได้ {should_save}")
        return False

def test_scenario_4_slightly_better():
    """ทดสอบ: โมเดลที่ดีขึ้นเล็กน้อย"""
    
    print("\n🧪 ทดสอบ Scenario 4: โมเดลที่ดีขึ้นเล็กน้อย")
    print("="*50)
    
    protection = ModelProtectionSystem(min_profit_threshold=5000)
    
    # โมเดลที่ดีขึ้นเล็กน้อย (ไม่เพียงพอ)
    slightly_better = {
        'total_profit': 5500.00,  # ดีขึ้นแค่ $162
        'win_rate': 0.17,  # 17%
        'expectancy': 25.0,
        'max_drawdown': 4000.00,
        'total_trades': 200
    }
    
    should_save, reason = protection.should_save_model(
        current_performance=slightly_better,
        symbol="GOLD",
        timeframe="MM60"
    )
    
    print(f"📊 ผลการตัดสินใจ: {should_save} ({reason})")
    
    # ไม่ควรบันทึกเพราะปรับปรุงไม่เพียงพอ (< $500)
    expected = False
    if should_save == expected:
        print("✅ ผ่าน: ไม่บันทึกโมเดลที่ปรับปรุงไม่เพียงพอ")
        return True
    else:
        print(f"❌ ล้มเหลว: คาดหวัง {expected}, ได้ {should_save}")
        return False

def test_scenario_5_significantly_better():
    """ทดสอบ: โมเดลที่ดีขึ้นมาก"""
    
    print("\n🧪 ทดสอบ Scenario 5: โมเดลที่ดีขึ้นมาก")
    print("="*50)
    
    protection = ModelProtectionSystem(min_profit_threshold=5000)
    
    # โมเดลที่ดีขึ้นมาก
    much_better = {
        'total_profit': 6500.00,  # ดีขึ้น $1162 (> $500)
        'win_rate': 0.22,  # 22%
        'expectancy': 30.0,
        'max_drawdown': 3500.00,
        'total_trades': 180
    }
    
    should_save, reason = protection.should_save_model(
        current_performance=much_better,
        symbol="GOLD",
        timeframe="MM60"
    )
    
    print(f"📊 ผลการตัดสินใจ: {should_save} ({reason})")
    
    # ควรบันทึกเพราะดีขึ้นมาก
    expected = True
    if should_save == expected:
        print("✅ ผ่าน: บันทึกโมเดลที่ดีขึ้นมาก")
        return True
    else:
        print(f"❌ ล้มเหลว: คาดหวัง {expected}, ได้ {should_save}")
        return False

def check_performance_file():
    """ตรวจสอบไฟล์ประสิทธิภาพ"""
    
    print("\n🧪 ตรวจสอบไฟล์ประสิทธิภาพ")
    print("="*50)
    
    performance_file = "best_performance_GOLD_MM60.json"
    
    if os.path.exists(performance_file):
        with open(performance_file, 'r') as f:
            data = json.load(f)
        
        print(f"📁 พบไฟล์: {performance_file}")
        print(f"💰 Total Profit: ${data.get('total_profit', 0):,.2f}")
        print(f"🎯 Win Rate: {data.get('win_rate', 0):.1%}")
        print(f"📈 Expectancy: {data.get('expectancy', 0):.2f}")
        
        # ควรเป็นโมเดลที่ดีที่สุด (6500.00)
        if data.get('total_profit', 0) == 6500.00:
            print("✅ ไฟล์ประสิทธิภาพถูกต้อง")
            return True
        else:
            print("❌ ไฟล์ประสิทธิภาพไม่ถูกต้อง")
            return False
    else:
        print("❌ ไม่พบไฟล์ประสิทธิภาพ")
        return False

def run_all_tests():
    """รันการทดสอบทั้งหมด"""
    
    print("🚀 เริ่มทดสอบ Enhanced Model Protection System")
    print("="*60)
    
    # ลบไฟล์ทดสอบเก่า
    clean_test_files()
    
    results = []
    
    # ทดสอบทีละ scenario
    results.append(("first_model", test_scenario_1_first_model()))
    results.append(("good_model", test_scenario_2_good_model()))
    results.append(("worse_model", test_scenario_3_worse_model()))
    results.append(("slightly_better", test_scenario_4_slightly_better()))
    results.append(("significantly_better", test_scenario_5_significantly_better()))
    results.append(("performance_file", check_performance_file()))
    
    # สรุปผล
    print("\n📊 สรุปผลการทดสอบ")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ ผ่าน" if success else "❌ ล้มเหลว"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    total = len(results)
    score = passed / total * 100
    
    print(f"\n🎯 คะแนนรวม: {passed}/{total} ({score:.1f}%)")
    
    if score >= 90:
        print("🟢 ระบบป้องกันทำงานได้ดีมาก!")
        print("💡 พร้อมป้องกันการเทรนที่ให้ผลแย่ลง")
    elif score >= 70:
        print("🟡 ระบบป้องกันทำงานได้ส่วนใหญ่")
        print("💡 ควรตรวจสอบปัญหาที่เหลือ")
    else:
        print("🔴 ระบบป้องกันมีปัญหา")
        print("💡 ต้องแก้ไขก่อนใช้งาน")
    
    return score

if __name__ == "__main__":
    score = run_all_tests()
    
    print("\n" + "="*60)
    print("🎉 การทดสอบเสร็จสิ้น!")
    print(f"📊 คะแนนรวม: {score:.1f}%")
    
    if score >= 90:
        print("\n🛡️ ระบบป้องกันพร้อมใช้งาน!")
        print("💡 จะป้องกันการบันทึกโมเดลที่แย่ลง")
    else:
        print("\n🔧 ต้องแก้ไขปัญหาก่อนใช้งาน")
