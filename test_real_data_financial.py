#!/usr/bin/env python3
"""
Test Financial Analysis with Real Data Structure
ทดสอบระบบวิเคราะห์ทางการเงินด้วยโครงสร้างข้อมูลจริง

วิธีใช้:
1. python test_real_data_financial.py
2. ดูผลลัพธ์ใน Financial_Analysis_Results/
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def test_with_real_data_structure():
    """ทดสอบด้วยโครงสร้างข้อมูลจริงจาก LightGBM_09_MM.py"""
    
    print("🧪 ทดสอบระบบด้วยโครงสร้างข้อมูลจริง")
    print("=" * 60)
    
    try:
        # Import ระบบวิเคราะห์ทางการเงิน
        from financial_analysis_system import FinancialAnalysisSystem
        from financial_integration import integrate_with_trade_cycles
        
        print("✅ โหลดโมดูลสำเร็จ")
        
    except ImportError as e:
        print(f"❌ ไม่สามารถโหลดโมดูลได้: {e}")
        return False
    
    # สร้างระบบวิเคราะห์ทางการเงิน
    financial_system = FinancialAnalysisSystem(base_currency='USD', leverage=500)
    print("✅ สร้างระบบวิเคราะห์ทางการเงินสำเร็จ")
    
    # สร้างข้อมูลจำลองด้วยโครงสร้างจริง
    print("\n🔄 สร้างข้อมูลจำลองด้วยโครงสร้างจริง...")
    
    test_symbols_list = ['EURUSD', 'GOLD', 'USDJPY']
    test_timeframes = ['M30', 'M60']
    
    for symbol in test_symbols_list:
        for timeframe in test_timeframes:
            # สร้างข้อมูลจำลองด้วยโครงสร้างจริง
            trade_data = create_real_structure_trade_data(symbol, timeframe)
            
            print(f"\n🔍 ทดสอบ {symbol} {timeframe}:")
            print(f"   📊 Columns: {list(trade_data.columns)}")
            print(f"   📊 Shape: {trade_data.shape}")
            print(f"   💰 Sample Profit: {trade_data['Profit'].head(3).tolist()}")
            
            # ประมวลผลด้วยระบบวิเคราะห์ทางการเงิน
            success = integrate_with_trade_cycles(
                symbol=symbol,
                timeframe=timeframe,
                trade_cycles_df=trade_data,
                financial_system=financial_system
            )
            
            if success:
                print(f"   ✅ {symbol} {timeframe}: สำเร็จ")
            else:
                print(f"   ❌ {symbol} {timeframe}: ไม่สำเร็จ")
    
    # รันการวิเคราะห์ทั้งหมด
    print("\n🚀 รันการวิเคราะห์ทั้งหมด...")
    
    results = financial_system.run_complete_analysis(account_balance=1000)
    
    if results:
        print("\n🎉 การทดสอบสำเร็จ!")
        
        # แสดงผลสรุป
        combined = results['combined_analysis']
        risk = results['risk_analysis']
        
        print(f"\n📈 ผลการทดสอบ:")
        print(f"   💰 ยอดเงินในบัญชี: ${risk['account_balance']:,.2f}")
        print(f"   📊 จำนวนการเทรด: {combined['total_trades']}")
        print(f"   💵 กำไรรวม (1.0 lot): ${combined['total_profit_usd']:,.2f}")
        print(f"   📉 Drawdown สูงสุด (1.0 lot): ${combined['max_drawdown_usd']:,.2f}")
        print(f"   🎯 ขนาดล็อตที่แนะนำ: {risk['recommended_lot_size']:.4f}")
        print(f"   ⚠️ ความเสี่ยงสูงสุด: {risk['max_risk_percentage']:.2f}%")
        
        # แสดงการคำนวณจริง
        expected_profit = combined['total_profit_usd'] * risk['recommended_lot_size']
        expected_drawdown = combined['max_drawdown_usd'] * risk['recommended_lot_size']
        roi = (expected_profit / risk['account_balance']) * 100
        
        print(f"\n💡 การคำนวณจริง:")
        print(f"   💵 กำไรที่คาดหวัง: ${expected_profit:.2f}")
        print(f"   📉 Drawdown ที่คาดหวัง: ${expected_drawdown:.2f}")
        print(f"   📈 ROI ที่ปลอดภัย: {roi:.2f}%")
        
        return True
        
    else:
        print("❌ การทดสอบไม่สำเร็จ")
        return False

def create_real_structure_trade_data(symbol: str, timeframe: str) -> pd.DataFrame:
    """สร้างข้อมูลการเทรดจำลองด้วยโครงสร้างจริงจาก LightGBM_09_MM.py"""
    
    np.random.seed(42 + hash(symbol + timeframe) % 100)
    num_trades = 25  # จำนวนการเทรดจำลอง
    
    trades = []
    base_time = datetime.now() - timedelta(days=30)
    
    # กำหนดพารามิเตอร์ตามสัญลักษณ์
    if symbol == 'GOLD':
        base_price = 2650.0
        price_volatility = 15.0
        profit_mean = 8.0
        profit_std = 25.0
    elif symbol == 'EURUSD':
        base_price = 1.0850
        price_volatility = 0.015
        profit_mean = 5.0
        profit_std = 15.0
    elif symbol == 'USDJPY':
        base_price = 148.50
        price_volatility = 1.5
        profit_mean = 6.0
        profit_std = 18.0
    else:
        base_price = 1.0
        price_volatility = 0.01
        profit_mean = 5.0
        profit_std = 15.0
    
    for i in range(num_trades):
        # สร้างเวลาการเทรด
        entry_time = base_time + timedelta(hours=i*3)
        exit_time = entry_time + timedelta(hours=2)
        
        # สร้างราคา
        entry_price = base_price + np.random.normal(0, price_volatility)
        
        # สร้างกำไร/ขาดทุน (ใน USD สำหรับ lot size 1.0)
        profit = np.random.normal(profit_mean, profit_std)
        
        # คำนวณ exit price จาก profit
        if symbol == 'GOLD':
            # GOLD: 1 pip ≈ $1
            exit_price = entry_price + (profit * 0.01)
        elif symbol in ['EURUSD']:
            # EURUSD: 1 pip = $10
            exit_price = entry_price + (profit / 10.0 * 0.0001)
        elif symbol in ['USDJPY']:
            # USDJPY: 1 pip ≈ $6.73
            exit_price = entry_price + (profit / 6.73 * 0.01)
        else:
            exit_price = entry_price + (profit * 0.0001)
        
        # สร้างข้อมูลการเทรดตามโครงสร้างจริง
        trade = [
            entry_time.strftime('%Y-%m-%d %H:%M:%S'),  # Entry Time
            entry_price,                               # Entry Price
            exit_time.strftime('%Y-%m-%d %H:%M:%S'),   # Exit Time
            exit_price,                                # Exit Price
            'BUY' if profit > 0 else 'SELL',           # Trade Type
            profit,                                    # Profit (USD)
            entry_time.weekday(),                      # Entry Day
            entry_time.hour,                           # Entry Hour
            entry_price - (abs(profit) * 0.5 / 100),  # SL Price
            entry_price + (abs(profit) * 1.5 / 100),  # TP Price
            'TP' if profit > 0 else 'SL',              # Exit Condition
            abs(profit) * 0.5,                        # Risk
            abs(profit) * 1.5,                        # Reward
            price_volatility,                          # ATR at Entry
            price_volatility * 2,                      # BB Width at Entry
            50 + np.random.normal(0, 20),              # RSI14 at Entry
            2.0,                                       # Pct_Risk
            3.0,                                       # Pct_Reward
            1000,                                      # Volume MA20 at Entry
            1.2                                        # Volume Spike at Entry
        ]
        
        trades.append(trade)
    
    # สร้าง DataFrame ด้วยคอลัมน์จริงจาก LightGBM_09_MM.py
    TRADE_COLUMNS = [
        "Entry Time", "Entry Price", 
        "Exit Time", "Exit Price",
        "Trade Type", "Profit",
        "Entry Day", "Entry Hour",
        "SL Price", "TP Price", 
        "Exit Condition",
        "Risk", "Reward",
        "ATR at Entry", 
        "BB Width at Entry", 
        "RSI14 at Entry",
        "Pct_Risk", 
        "Pct_Reward",
        "Volume MA20 at Entry", "Volume Spike at Entry"
    ]
    
    return pd.DataFrame(trades, columns=TRADE_COLUMNS)

if __name__ == "__main__":
    print("🧪 Test Real Data Structure - Financial Analysis System")
    print("=" * 80)
    
    # รันการทดสอบ
    success = test_with_real_data_structure()
    
    print("\n" + "=" * 80)
    
    if success:
        print("🎉 การทดสอบเสร็จสมบูรณ์!")
        print("📁 ตรวจสอบผลลัพธ์ในโฟลเดอร์ Financial_Analysis_Results/")
        print("\n💡 ตอนนี้สามารถรัน python LightGBM_09_MM.py ได้แล้ว")
    else:
        print("❌ การทดสอบไม่สำเร็จ")
    
    print("🏁 เสร็จสิ้น")
