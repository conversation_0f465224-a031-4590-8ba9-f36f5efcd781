
# ==============================================
# Overfitting Prevention System
# ==============================================

def load_overfitting_config():
    """โหลดการตั้งค่าป้องกัน overfitting"""
    try:
        with open('overfitting_prevention_config.json', 'r') as f:
            config = json.load(f)
        return config
    except:
        # Default config
        return {
            'early_stopping_rounds': 50,
            'validation_size': 0.2,
            'use_cross_validation': True,
            'max_retrain_attempts': 3,
            'min_improvement_threshold': 0.01,
            'patience_rounds': 10
        }

def improved_model_training(X_train, y_train, X_val, y_val, config):
    """การเทรนโมเดลที่ป้องกัน overfitting"""
    
    # LightGBM parameters with overfitting prevention
    lgb_params = {
        'objective': 'binary',
        'metric': 'auc',
        'boosting_type': 'gbdt',
        'num_leaves': 31,
        'learning_rate': 0.05,  # ลดลงเพื่อป้องกัน overfitting
        'feature_fraction': 0.9,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'random_state': 42,
        'min_data_in_leaf': 20,  # เพิ่มเพื่อป้องกัน overfitting
        'lambda_l1': 0.1,        # L1 regularization
        'lambda_l2': 0.1,        # L2 regularization
        'early_stopping_rounds': config['early_stopping_rounds']
    }
    
    model = lgb.LGBMClassifier(**lgb_params)
    
    # เทรนด้วย early stopping
    model.fit(
        X_train, y_train,
        eval_set=[(X_val, y_val)],
        eval_metric='auc',
        verbose=False
    )
    
    return model

# เพิ่มในส่วน main function
overfitting_config = load_overfitting_config()
