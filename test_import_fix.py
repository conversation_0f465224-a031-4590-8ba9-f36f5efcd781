"""
🧪 Test Import Fix
==================

ทดสอบการแก้ไขปัญหา safe_oversampling is not defined
"""

def test_imports():
    """ทดสอบการ import ระบบป้องกันทั้งหมด"""
    
    print("🧪 ทดสอบการ import ระบบป้องกัน")
    print("="*50)
    
    import_results = {}
    
    # ทดสอบ Model Protection System
    try:
        from model_protection_system import ModelProtectionSystem
        protection_system = ModelProtectionSystem(min_profit_threshold=5000)
        import_results['model_protection'] = True
        print("✅ Model Protection System - OK")
    except Exception as e:
        import_results['model_protection'] = False
        print(f"❌ Model Protection System - Error: {e}")
    
    # ทดสอบ Training Prevention System
    try:
        from training_prevention_system import TrainingPreventionSystem
        prevention_system = TrainingPreventionSystem()
        import_results['training_prevention'] = True
        print("✅ Training Prevention System - OK")
    except Exception as e:
        import_results['training_prevention'] = False
        print(f"❌ Training Prevention System - Error: {e}")
    
    # ทดสอบ Data Leakage Prevention
    try:
        from step2_data_leakage_prevention import create_safe_features, validate_no_data_leakage
        import_results['data_leakage_prevention'] = True
        print("✅ Data Leakage Prevention System - OK")
    except Exception as e:
        import_results['data_leakage_prevention'] = False
        print(f"❌ Data Leakage Prevention System - Error: {e}")
    
    # ทดสอบ Safe Oversampling (ปัญหาหลัก)
    try:
        from step5_safe_oversampling import safe_oversampling, use_class_weights_instead
        import_results['safe_oversampling'] = True
        print("✅ Safe Oversampling System - OK")
    except Exception as e:
        import_results['safe_oversampling'] = False
        print(f"❌ Safe Oversampling System - Error: {e}")
    
    # ทดสอบ Overfitting Prevention
    try:
        from step1_overfitting_prevention import load_overfitting_config, improved_model_training
        import_results['overfitting_prevention'] = True
        print("✅ Overfitting Prevention System - OK")
    except Exception as e:
        import_results['overfitting_prevention'] = False
        print(f"❌ Overfitting Prevention System - Error: {e}")
    
    return import_results

def test_safe_oversampling_function():
    """ทดสอบฟังก์ชัน safe_oversampling โดยเฉพาะ"""
    
    print("\n🧪 ทดสอบฟังก์ชัน safe_oversampling")
    print("="*50)
    
    try:
        from step5_safe_oversampling import safe_oversampling
        
        # สร้างข้อมูลทดสอบ
        import pandas as pd
        import numpy as np
        
        # สร้าง imbalanced data
        X = pd.DataFrame({
            'feature1': np.random.randn(100),
            'feature2': np.random.randn(100),
            'feature3': np.random.randn(100)
        })
        
        # สร้าง imbalanced target (90% class 0, 10% class 1)
        y = pd.Series([0] * 90 + [1] * 10)
        
        print(f"📊 Original data: {len(X)} samples")
        print(f"📊 Class distribution: {y.value_counts().to_dict()}")
        
        # ทดสอบ safe_oversampling
        X_balanced, y_balanced = safe_oversampling(X, y)
        
        print(f"📊 Balanced data: {len(X_balanced)} samples")
        print(f"📊 Balanced class distribution: {pd.Series(y_balanced).value_counts().to_dict()}")
        
        print("✅ safe_oversampling ทำงานได้ปกติ")
        return True
        
    except Exception as e:
        print(f"❌ safe_oversampling ล้มเหลว: {e}")
        return False

def test_lightgbm_imports():
    """ทดสอบการ import ใน LightGBM_10_4.py"""
    
    print("\n🧪 ทดสอบการ import ใน LightGBM_10_4.py")
    print("="*50)
    
    try:
        # อ่านไฟล์ LightGBM_10_4.py
        with open('LightGBM_10_4.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # ตรวจสอบการ import ที่จำเป็น
        required_imports = [
            'from step5_safe_oversampling import safe_oversampling, use_class_weights_instead',
            'SAFE_OVERSAMPLING_AVAILABLE = True',
            'if SAFE_OVERSAMPLING_AVAILABLE:'
        ]
        
        missing_imports = []
        for import_line in required_imports:
            if import_line not in content:
                missing_imports.append(import_line)
        
        if missing_imports:
            print("❌ ขาด imports:")
            for missing in missing_imports:
                print(f"   - {missing}")
            return False
        else:
            print("✅ พบ imports ที่จำเป็นครบถ้วน")
            return True
            
    except Exception as e:
        print(f"❌ ไม่สามารถตรวจสอบไฟล์ได้: {e}")
        return False

def run_all_tests():
    """รันการทดสอบทั้งหมด"""
    
    print("🚀 เริ่มทดสอบการแก้ไขปัญหา safe_oversampling")
    print("="*60)
    
    # ทดสอบการ import
    import_results = test_imports()
    
    # ทดสอบฟังก์ชัน safe_oversampling
    oversampling_works = test_safe_oversampling_function()
    
    # ทดสอบการ import ใน LightGBM
    lightgbm_imports_ok = test_lightgbm_imports()
    
    # สรุปผล
    print("\n📊 สรุปผลการทดสอบ")
    print("="*60)
    
    total_systems = len(import_results)
    working_systems = sum(import_results.values())
    
    print(f"🛡️ ระบบป้องกัน: {working_systems}/{total_systems} ทำงานได้")
    
    for system, status in import_results.items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {system}")
    
    print(f"🔄 Safe Oversampling Function: {'✅ ทำงานได้' if oversampling_works else '❌ มีปัญหา'}")
    print(f"📁 LightGBM Imports: {'✅ ครบถ้วน' if lightgbm_imports_ok else '❌ ขาด imports'}")
    
    # คำนวณคะแนนรวม
    total_tests = total_systems + 2  # +2 for oversampling function and lightgbm imports
    passed_tests = working_systems + int(oversampling_works) + int(lightgbm_imports_ok)
    score = passed_tests / total_tests * 100
    
    print(f"\n🎯 คะแนนรวม: {passed_tests}/{total_tests} ({score:.1f}%)")
    
    if score >= 90:
        print("🟢 ระบบพร้อมใช้งาน!")
        print("💡 สามารถรัน LightGBM_10_4.py ได้แล้ว")
    elif score >= 70:
        print("🟡 ระบบใช้งานได้แต่ควรตรวจสอบ")
        print("💡 อาจมีปัญหาเล็กน้อย")
    else:
        print("🔴 ระบบยังมีปัญหา")
        print("💡 ต้องแก้ไขก่อนใช้งาน")
    
    return score

if __name__ == "__main__":
    score = run_all_tests()
    
    print("\n" + "="*60)
    print("🎉 การทดสอบเสร็จสิ้น!")
    print(f"📊 คะแนนรวม: {score:.1f}%")
    
    if score >= 90:
        print("\n🚀 แนะนำ: ลองรัน LightGBM_10_4.py ได้เลย!")
    else:
        print("\n🔧 แนะนำ: ตรวจสอบและแก้ไขปัญหาที่เหลือ")
