import pandas as pd
import pandas_ta as ta
import numpy as np
import sys
import time
import os
import joblib
import traceback
import pickle
import json
import math

import matplotlib.pyplot as plt
from statsmodels.tsa.stattools import adfuller
from sklearn.preprocessing import StandardScaler
import io

# =============================================
# Tee class to redirect print output to file and console
# =============================================
class Tee(object):
    def __init__(self, *files):
        self.files = files
    def write(self, obj):
        for f in self.files:
            try:
                f.write(obj)
                f.flush()
            except (IOError, ValueError):
                pass # Ignore errors, e.g., on closed streams
    def flush(self):
        for f in self.files:
            try:
                f.flush()
            except (IOError, ValueError):
                pass # Ignore errors

# --- Setup Logging ---
# This block replaces the need for Tee-Object in PowerShell
try:
    # 1. Ensure console output itself is UTF-8
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8')
    
    # 2. Open log file with UTF-8 encoding
    log_file = io.open('log.txt', 'w', encoding='utf-8')
    
    # 3. Create a Tee object to write to both console and file
    original_stdout = sys.stdout
    sys.stdout = Tee(original_stdout, log_file)
except Exception as e:
    # If logging setup fails, print error to original console
    # and continue without file logging.
    print(f"Could not set up logging to file: {e}", file=original_stdout)
# --- End of Logging Setup ---

# การตั้งค่าภาษา
# ==============================================
# sys.stdout.reconfigure(encoding='utf-8') # No longer needed, handled by Tee setup

# การตั้งค่าพื้นฐาน
# ==============================================
Steps_to_do = True # แสดงลำดับการทำงาน
Model_Decision = False # ให้ Model ช่วยสร้างข้อมูลเทรด..เพื่อเทรนโมเดล

input_initial_threshold = 0.55  # เพิ่มจาก 0.55 เพื่อเลือกสัญญาณคุณภาพสูงสุด

# กำหนดเงื่อนไขการเทรด (ปรับเพื่อเพิ่ม win rate ตาม Parameter Stability)
input_rsi_level_in = 40          # เพิ่มจาก 40 เข้มงวดมากขึ้น
input_rsi_level_over = 70        # พื้นที่ overbought oversold
input_rsi_level_out = 35         # เพิ่มจาก 35 ออกเร็วขึ้นเพื่อป้องกันขาดทุน
input_stop_loss_atr = 1.25       # ลดจาก 1.25 SL แคบขึ้นเพื่อลดความเสี่ยง
input_take_profit = 3.0          # เพิ่มจาก 3.0 เพื่อ Risk:Reward = 1:3.0
input_pull_back = 0.45           # เพิ่มจาก 0.45 เพื่อเลือกสัญญาณที่แข็งแกร่งที่สุด

# เพิ่มพารามิเตอร์สำหรับ High-Quality Entry Filters
MIN_ATR_THRESHOLD = 0.0008       # ATR ขั้นต่ำเพื่อหลีกเลี่ยง low volatility periods
MIN_VOLUME_MULTIPLIER = 1.2      # Volume ต้องมากกว่า average อย่างน้อย 20%
TREND_CONFIRMATION_PERIODS = 3   # จำนวนแท่งที่ต้องยืนยัน trend

# กำหนดจำนวนรอบการเทรนแบบ 2 ชั้น
# ==============================================
NUM_MAIN_ROUNDS = 1 # รอบหลัก
NUM_TRAINING_ROUNDS = 1 # รอบย่อย

# Debug Messages Configuration
# ==============================================
SHOW_FEATURE_DEBUG = False  # แสดง debug messages เรื่อง features (ลดความ verbose)
SHOW_SCALER_DEBUG = False   # แสดง debug messages เรื่อง scaler

# Profit Thresholds เพื่อเพิ่ม win rate (เน้น quality over quantity)
PROFIT_THRESHOLDS = {
    'strong_buy': 60,               # ลดจาก 80 เป็น 60 points = Strong Buy (class 4) - เป้าหมายที่เข้าถึงได้มากขึ้น
    'weak_buy': 20,                 # ลดจาก 25 เป็น 20 points = Weak Buy (class 3) - ใกล้เคียง spread
    'no_trade': -20,                # ปรับจาก -25 เป็น -20 points = No Trade (class 2) - ลด noise zone
    'weak_sell': -60,               # ปรับจาก -80 เป็น -60 points = Weak Sell (class 1) - สมมาตรกับ strong_buy
    'strong_sell': float('-inf')    # ขาดทุนเกิน 60 points = Strong Sell (class 0) - เหมือนเดิม
}

# Class mapping for multi-class target
CLASS_MAPPING = {
    0: 'strong_sell',    # ขาดทุนมาก
    1: 'weak_sell',      # ขาดทุนปานกลาง
    2: 'no_trade',       # ไม่ควรเทรด
    3: 'weak_buy',       # กำไรปานกลาง
    4: 'strong_buy'      # กำไรมาก
}

# การตั้งค่าปลายทางไฟล์
# ==============================================
model_name      = "LightGBM"
test_data       = f"{model_name}_Data"
test_hyper      = f"{model_name}_Hyper_Multi"
test_folder     = f"{model_name}_Multi"
feature_dir     = f"{test_folder}/feature_importance"
performance_dir = f"{test_folder}/individual_performance"
models_dir      = f"{test_folder}/models"
results_dir     = f"{test_folder}/results"
summaries_dir   = f"{test_folder}/summaries"
thresholds_dir  = f"{test_folder}/thresholds"
training_dir    = f"{test_folder}/training_summaries"

if not os.path.exists(test_data):
    os.makedirs(test_data)
    print(f"สร้างโฟลเดอร์ {test_data} เรียบร้อยแล้ว")

if not os.path.exists(test_hyper):
    os.makedirs(test_hyper)
    print(f"สร้างโฟลเดอร์ {test_hyper} เรียบร้อยแล้ว")

if not os.path.exists(test_folder):
    os.makedirs(test_folder)
    print(f"สร้างโฟลเดอร์ {test_folder} เรียบร้อยแล้ว")

if not os.path.exists(feature_dir):
    os.makedirs(feature_dir)
    print(f"สร้างโฟลเดอร์ {feature_dir} เรียบร้อยแล้ว")

if not os.path.exists(performance_dir):
    os.makedirs(performance_dir)
    print(f"สร้างโฟลเดอร์ {performance_dir} เรียบร้อยแล้ว")

if not os.path.exists(models_dir):
    os.makedirs(models_dir)
    print(f"สร้างโฟลเดอร์ {models_dir} เรียบร้อยแล้ว")

if not os.path.exists(results_dir):
    os.makedirs(results_dir)
    print(f"สร้างโฟลเดอร์ {results_dir} เรียบร้อยแล้ว")

if not os.path.exists(thresholds_dir):
    os.makedirs(thresholds_dir)
    print(f"สร้างโฟลเดอร์ {thresholds_dir} เรียบร้อยแล้ว")

if not os.path.exists(training_dir):
    os.makedirs(training_dir)
    print(f"สร้างโฟลเดอร์ {training_dir} เรียบร้อยแล้ว")

# ==============================================
# ข้อมูลที่ใช้ทดสอบ
# ==============================================
TEST_GROUPS = {
    # "M30": [
    #     "CSV_Files_Fixed/GOLD_M30_FIXED.csv"
    # ],
    "M60": [
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    ]
}

symbol_info = {
    "GOLD":   {"Spread": 25, "Digits": 2, "Points": 0.01, "Swap_Long": 0, "Swap_Short": 0},
    "AUDUSD": {"Spread": 15, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURGBP": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "EURUSD": {"Spread": 13, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "GBPUSD": {"Spread": 25, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "NZDUSD": {"Spread": 22, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDCAD": {"Spread": 28, "Digits": 5, "Points": 0.00001, "Swap_Long": 0, "Swap_Short": 0},
    "USDJPY": {"Spread": 16, "Digits": 3, "Points": 0.001, "Swap_Long": 0, "Swap_Short": 0}
}

timeframe_info = { "M30" : 30, "M60" : 60}

# ==============================================
# Market Scenarios สำหรับ 2 โมเดลแยกกัน
# ==============================================
# เพิ่มข้อมูลเทรนต่อโมเดลจาก 25% เป็น 50% ต่อโมเดล
MARKET_SCENARIOS = {
    'trend_following': {
        'description': 'Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend)',
        'condition': lambda row: (
            # เฉพาะ Strong Trend: ราคาทั้งหมดอยู่เหนือ/ใต้ EMA200
            # Strong Uptrend: Close > EMA200 และ Low > EMA200 (ราคาทั้งหมดเหนือ EMA)
            (row['Close'] > row['EMA200'] and row['Low'] > row['EMA200']) or
            # Strong Downtrend: Close < EMA200 และ High < EMA200 (ราคาทั้งหมดใต้ EMA)
            (row['Close'] < row['EMA200'] and row['High'] < row['EMA200'])
        ),
        'actions': ['buy', 'sell'],  # รองรับทั้ง buy และ sell
        'type': 'trend_following',
        'strategy': 'momentum'
    },
    'counter_trend': {
        'description': 'Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways)',
        'condition': lambda row: (
            # Weak Trend/Sideways: ราคาข้าม EMA200 (ไม่ใช่ strong trend)
            # Weak Uptrend: Close > EMA200 แต่ Low <= EMA200 (ราคาข้าม EMA)
            (row['Close'] > row['EMA200'] and row['Low'] <= row['EMA200']) or
            # Weak Downtrend: Close < EMA200 แต่ High >= EMA200 (ราคาข้าม EMA)
            (row['Close'] < row['EMA200'] and row['High'] >= row['EMA200'])
        ),
        'actions': ['buy', 'sell'],  # รองรับทั้ง buy และ sell
        'type': 'counter_trend',
        'strategy': 'mean_reversion'
    }
}

# ==============================================
# Entry Conditions Comparison System
# ==============================================
# กำหนดการตั้งค่า Entry Conditions ทั้ง 4 แบบสำหรับการเปรียบเทียบ
ENTRY_CONFIGS = {
    "config_1_enhanced_deep": {
        "name": "Enhanced MACD Deep",
        "description": "ใช้ macd_deep พร้อมเงื่อนไข volume และ pullback เพิ่มเติม",
        "conditions": {
            "trend_following": {
                "buy": lambda prev: (
                    prev['close'] > prev['open'] and
                    prev['close'] > prev['ema200'] and
                    prev['rsi14'] > input_rsi_level_in * 1.0 and # rsi > 40
                    prev['rsi14'] < input_rsi_level_over * 1.0 and # rsi < 70
                    prev['macd_deep'] == 1.0 and
                    prev['volume'] > prev['volume_ma20'] * 0.5 and
                    prev['pullback_buy'] > input_pull_back * 0.5 and
                    prev['ratio_buy'] > input_take_profit
                ),
                "sell": lambda prev: (
                    prev['close'] < prev['open'] and
                    prev['close'] < prev['ema200'] and
                    prev['rsi14'] < (100 - input_rsi_level_in) * 1.0 and # rsi < 100-40 = 60
                    prev['rsi14'] > (100 - input_rsi_level_over) * 1.0 and # rsi > 100-70= 30
                    prev['macd_deep'] == -1.0 and
                    prev['volume'] > prev['volume_ma20'] * 0.5 and
                    prev['pullback_sell'] > input_pull_back * 0.5 and
                    prev['ratio_sell'] > input_take_profit
                )
            },
            "counter_trend": {
                "buy": lambda prev: (
                    prev['close'] > prev['open'] and
                    prev['close'] < prev['ema200'] and
                    prev['rsi14'] > input_rsi_level_in * 1.0 and # rsi > 40
                    prev['rsi14'] < input_rsi_level_over * 1.0 and # rsi < 70
                    prev['macd_deep'] == 1.0 and
                    prev['volume'] > prev['volume_ma20'] * 0.8 and
                    prev['pullback_buy'] > input_pull_back * 0.8 and
                    prev['ratio_buy'] > input_take_profit
                ),
                "sell": lambda prev: (
                    prev['close'] < prev['open'] and
                    prev['close'] > prev['ema200'] and
                    prev['rsi14'] < (100 - input_rsi_level_in) * 1.0 and # rsi < 100-40 = 60
                    prev['rsi14'] > (100 - input_rsi_level_over) * 1.0 and # rsi > 100-70= 30
                    prev['macd_deep'] == -1.0 and
                    prev['volume'] > prev['volume_ma20'] * 0.8 and
                    prev['pullback_sell'] > input_pull_back * 0.8 and
                    prev['ratio_sell'] > input_take_profit
                )
            }
        }
    },

    "config_2_enhanced_signal": {
        "name": "Enhanced MACD Signal",
        "description": "ใช้ macd_signal พร้อมเงื่อนไข volume และ pullback เพิ่มเติม",
        "conditions": {
            "trend_following": {
                "buy": lambda prev: (
                    prev['close'] > prev['open'] and
                    prev['close'] > prev['ema200'] and
                    prev['rsi14'] > input_rsi_level_in * 1.0 and # rsi > 40
                    prev['rsi14'] < input_rsi_level_over * 1.0 and # rsi < 70
                    prev['macd_signal'] == 1.0 and
                    prev['volume'] > prev['volume_ma20'] * 0.5 and
                    prev['pullback_buy'] > input_pull_back * 0.5 and
                    prev['ratio_buy'] > input_take_profit
                ),
                "sell": lambda prev: (
                    prev['close'] < prev['open'] and
                    prev['close'] < prev['ema200'] and
                    prev['rsi14'] < (100 - input_rsi_level_in) * 1.0 and # rsi < 100-40 = 60
                    prev['rsi14'] > (100 - input_rsi_level_over) * 1.0 and # rsi > 100-70= 30
                    prev['macd_signal'] == -1.0 and
                    prev['volume'] > prev['volume_ma20'] * 0.5 and
                    prev['pullback_sell'] > input_pull_back * 0.5 and
                    prev['ratio_sell'] > input_take_profit
                )
            },
            "counter_trend": {
                "buy": lambda prev: (
                    prev['close'] > prev['open'] and
                    prev['close'] < prev['ema200'] and
                    prev['rsi14'] > input_rsi_level_in * 1.0 and # rsi > 40
                    prev['rsi14'] < input_rsi_level_over * 1.0 and # rsi < 70
                    prev['macd_signal'] == 1.0 and
                    prev['volume'] > prev['volume_ma20'] * 0.8 and
                    prev['pullback_buy'] > input_pull_back * 0.8 and
                    prev['ratio_buy'] > input_take_profit
                ),
                "sell": lambda prev: (
                    prev['close'] < prev['open'] and
                    prev['close'] > prev['ema200'] and
                    prev['rsi14'] < (100 - input_rsi_level_in) * 1.0 and # rsi < 100-40 = 60
                    prev['rsi14'] > (100 - input_rsi_level_over) * 1.0 and # rsi > 100-70= 30
                    prev['macd_signal'] == -1.0 and
                    prev['volume'] > prev['volume_ma20'] * 0.8 and
                    prev['pullback_sell'] > input_pull_back * 0.8 and
                    prev['ratio_sell'] > input_take_profit
                )
            }
        }
    }
}

# ตัวแปรสำหรับเลือกการตั้งค่าที่จะใช้ (สำหรับการทดสอบแต่ละครั้ง)
CURRENT_ENTRY_CONFIG = "config_2_enhanced_signal"  # เปลี่ยนค่านี้เพื่อทดสอบการตั้งค่าต่างๆ

# Entry conditions ปัจจุบัน (backward compatibility)
entry_conditions = ENTRY_CONFIGS[CURRENT_ENTRY_CONFIG]["conditions"]

# ==============================================

def ceiling_price(value, digits):
    # print(f"\n🏗️ เปิดใช้งาน ceiling price") if Steps_to_do else None
    return math.ceil(value/digits) * digits

def floor_price(value, digits):
    # print(f"\n🏗️ เปิดใช้งาน floor price") if Steps_to_do else None
    return math.floor(value/digits) * digits

def safe_json_serialize(obj):
    # print(f"\n🏗️ เปิดใช้งาน safe json serialize") if Steps_to_do else None
    
    if isinstance(obj, (np.int_, np.intc, np.intp, np.int8,
                    np.int16, np.int32, np.int64, np.uint8,
                    np.uint16, np.uint32, np.uint64)):
        return int(obj)
    elif isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, (np.bool_)):
        return bool(obj)
    elif isinstance(obj, (np.ndarray)):
        return obj.tolist()
    elif isinstance(obj, (pd.Timestamp)):
        return obj.isoformat()
    else:
        return str(obj)

# ==============================================

def save_optimal_nbars(symbol, timeframe, n_bars):
    print(f"\n🏗️ เปิดใช้งาน save optimal nbars") if Steps_to_do else None

    os.makedirs(f"{test_folder}/thresholds", exist_ok=True)
    path = f"{test_folder}/thresholds/{timeframe}_{symbol}_optimal_nBars_SL.pkl"

    with open(path, "wb") as f:
        pickle.dump(n_bars, f)
    print(f"✅ บันทึก nBars SL ที่: {path}")

# ==============================================

def load_model(symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน load model") if Steps_to_do else None

    model_dir = f"{test_folder}/models/{timeframe}_{symbol}"
    model_path = os.path.join(model_dir, f"{timeframe}_{symbol}_trained.pkl")
    features_path = os.path.join(model_dir, f"{timeframe}_{symbol}_features.pkl")

    try:
        # ตรวจสอบว่าโฟลเดอร์และไฟล์มีอยู่จริง
        if not os.path.exists(model_dir):
            print(f"⚠️ ไม่พบ โฟลเดอร์โมเดลสำหรับ symbol {symbol} timeframe {timeframe}")
            return None
        
        if not os.path.exists(model_path):
            print(f"⚠️ ไม่พบไฟล์โมเดลที่ {model_path}")
            return None
        
        print(f"กำลังโหลดโมเดลจาก: {model_path}")
        model = joblib.load(model_path)

        # ตรวจสอบว่าเป็นโมเดลที่ใช้ predict ได้หรือไม่ (LGBMClassifier มี predict)
        if not hasattr(model, 'predict'):
            raise ValueError("ไฟล์ที่โหลดมาไม่ใช่โมเดลที่สามารถใช้ทำนายได้")
        
        loaded_features = None
        if os.path.exists(features_path):
            try:
                loaded_features = joblib.load(features_path)
                print(f"✅ โหลด features สำเร็จ (จำนวน {len(loaded_features)} features)")
            except Exception as e:
                print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดไฟล์ features: {str(e)}")
        else:
            print("⚠️ ไม่พบไฟล์ features")
            if hasattr(model, 'feature_name_') and model.feature_name_:
                loaded_features = model.feature_name_
                print(f"ℹ️ ใช้ feature names จากโมเดลที่โหลดมา (จำนวน {len(loaded_features)} features)")
            elif hasattr(model, 'feature_name') and model.feature_name(): # Fallback สำหรับ Booster หรือบางกรณี
                loaded_features = model.feature_name()
                print(f"ℹ️ ใช้ feature names จากเมธอด feature_name() ของโมเดลที่โหลดมา (จำนวน {len(loaded_features)} features)")
            else:
                print("⚠️ ไม่พบ feature names ในไฟล์หรือในโมเดลที่โหลดมา")

        num_trees_info = "N/A"
        if hasattr(model, 'n_estimators_'): # จำนวนรอบ boosting ที่ใช้จริงหลัง early stopping
            num_trees_info = f"{model.n_estimators_} (used)"
        elif hasattr(model, 'n_estimators'): # จำนวนรอบสูงสุดที่ตั้งไว้
            num_trees_info = f"{model.n_estimators} (total configured)"
        elif hasattr(model, 'num_trees'): # กรณีเป็น Booster (เพื่อความเข้ากันได้ย้อนหลังถ้าจำเป็น)
            num_trees_info = f"{model.num_trees()} (Booster API)"

        print(f"✅ โหลดโมเดลสำเร็จ (มี {num_trees_info} trees)")

        model.loaded_features_ = loaded_features # แนบ features เข้าไปกับ object โมเดลที่โหลดมา
        return model

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดโมเดล: {str(e)}")
        traceback.print_exc()
        return None

def load_scaler(symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน load scaler") if Steps_to_do else None

    """โหลด Scaler ที่บันทึกไว้ตาม timeframe และ model_name"""
    scaler_dir = f"{test_folder}/models/{timeframe}_{symbol}"
    scaler_path = os.path.join(scaler_dir, f"{timeframe}_{symbol}_scaler.pkl") # ใช้ model_name ด้วย

    try:
        if not os.path.exists(scaler_path):
            print(f"⚠️ ไม่พบไฟล์ Scaler ที่ {scaler_path}")
            return None
        print(f"กำลังโหลด Scaler จาก: {scaler_path}")
        scaler = joblib.load(scaler_path)
        # ตรวจสอบว่าเป็น Scaler จริงหรือไม่ (optional แต่ดี)
        if not hasattr(scaler, 'transform'):
            raise ValueError("ไฟล์ที่โหลดมาไม่ใช่ Scaler")
        print(f"✅ โหลด Scaler สำเร็จ")
        return scaler
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลด Scaler: {str(e)}")
        traceback.print_exc()
        return None

def load_optimal_threshold_compatible(symbol, timeframe, scenario_name=None, default=0.5):
    print(f"\n🏗️ เปิดใช้งาน load optimal threshold compatible") if Steps_to_do else None

    """
    โหลด threshold แบบรองรับทั้งระบบเดิมและใหม่

    Args:
        symbol: สัญลักษณ์
        timeframe: timeframe
        scenario_name: ชื่อ scenario (สำหรับระบบใหม่)
        default: ค่า default

    Returns:
        float: threshold value
    """

    # ลำดับการค้นหาไฟล์
    search_patterns = []

    # 1. ถ้ามี scenario_name ให้ลองหาไฟล์รูปแบบใหม่ก่อน (Multi-Model)
    if scenario_name:
        new_multi_format = f"{test_folder}/thresholds/{timeframe}_{symbol}_{scenario_name}_optimal_threshold.pkl"
        search_patterns.append(("New Multi-Model", new_multi_format))

    # 2. ลองหาไฟล์รูปแบบใหม่ (Single-Model)
    new_single_format = f"{test_folder}/thresholds/{timeframe}_{symbol}_optimal_threshold.pkl"
    search_patterns.append(("New Single-Model", new_single_format))

    # 3. ลองหาไฟล์รูปแบบเดิม (เพื่อ backward compatibility)
    old_format = f"{test_folder}/thresholds/{symbol}_{timeframe}_optimal_threshold.pkl"
    search_patterns.append(("Old Single-Model", old_format))

    # 4. ลองหาไฟล์รูปแบบใหม่แบบ default scenario (ถ้าไม่ระบุ scenario)
    if not scenario_name:
        for default_scenario in ["trend_following", "counter_trend"]:
            new_format_default = f"{test_folder}/thresholds/{timeframe}_{symbol}_{default_scenario}_optimal_threshold.pkl"
            search_patterns.append((f"New Multi-Model ({default_scenario})", new_format_default))

    # ค้นหาไฟล์ตามลำดับ
    for pattern_name, file_path in search_patterns:
        try:
            if os.path.exists(file_path):
                with open(file_path, 'rb') as f:
                    threshold = pickle.load(f)
                print(f"✅ โหลด threshold จาก {pattern_name}: {threshold:.4f}")
                print(f"📁 ไฟล์: {os.path.basename(file_path)}")
                return threshold
        except Exception as e:
            print(f"⚠️ ไม่สามารถโหลดจาก {pattern_name}: {e}")
            continue

    print(f"⚠️ ไม่พบไฟล์ threshold สำหรับ {symbol} M{timeframe}, ใช้ค่า default: {default}")
    return default

def load_optimal_threshold(symbol, timeframe, initial_threshold=input_initial_threshold):
    print(f"\n🏗️ เปิดใช้งาน load optimal threshold") if Steps_to_do else None

    """คำนวณค่า threshold ที่เหมาะสมตามประวัติประสิทธิภาพของคู่เงินและ timeframe"""
    # กำหนด path สำหรับไฟล์เก็บค่า threshold
    threshold_file = f"{test_folder}/thresholds/{symbol}_{timeframe}_optimal_threshold.pkl"
    
    # ใช้ฟังก์ชันใหม่ที่รองรับทั้งระบบเดิมและใหม่
    threshold = load_optimal_threshold_compatible(symbol, timeframe, scenario_name=None, default=initial_threshold)

    return threshold

def load_optimal_nbars_compatible(symbol, timeframe, scenario_name=None, default=6):
    print(f"\n🏗️ เปิดใช้งาน load optimal nbars compatible") if Steps_to_do else None

    """
    โหลด nBars_SL แบบรองรับทั้งระบบเดิมและใหม่

    Args:
        symbol: สัญลักษณ์
        timeframe: timeframe
        scenario_name: ชื่อ scenario (สำหรับระบบใหม่)
        default: ค่า default

    Returns:
        int: nBars_SL value
    """

    # ลำดับการค้นหาไฟล์
    search_patterns = []

    # 1. ถ้ามี scenario_name ให้ลองหาไฟล์รูปแบบใหม่ก่อน (Multi-Model)
    if scenario_name:
        new_multi_format = f"{test_folder}/thresholds/{timeframe}_{symbol}_{scenario_name}_optimal_nBars_SL.pkl"
        search_patterns.append(("New Multi-Model", new_multi_format))

    # 2. ลองหาไฟล์รูปแบบใหม่ (Single-Model)
    new_single_format = f"{test_folder}/thresholds/{timeframe}_{symbol}_optimal_nBars_SL.pkl"
    search_patterns.append(("New Single-Model", new_single_format))

    # 3. ลองหาไฟล์รูปแบบเดิม (เพื่อ backward compatibility)
    old_format = f"{test_folder}/thresholds/{symbol}_{timeframe}_optimal_nBars_SL.pkl"
    search_patterns.append(("Old Single-Model", old_format))

    # 4. ลองหาไฟล์รูปแบบใหม่แบบ default scenario (ถ้าไม่ระบุ scenario)
    if not scenario_name:
        for default_scenario in ["trend_following", "counter_trend"]:
            new_format_default = f"{test_folder}/thresholds/{timeframe}_{symbol}_{default_scenario}_optimal_nBars_SL.pkl"
            search_patterns.append((f"New Multi-Model ({default_scenario})", new_format_default))

    # ค้นหาไฟล์ตามลำดับ
    for pattern_name, file_path in search_patterns:
        try:
            if os.path.exists(file_path):
                with open(file_path, 'rb') as f:
                    nbars = pickle.load(f)
                print(f"✅ โหลด nBars_SL จาก {pattern_name}: {nbars}")
                print(f"📁 ไฟล์: {os.path.basename(file_path)}")
                return nbars
        except Exception as e:
            print(f"⚠️ ไม่สามารถโหลดจาก {pattern_name}: {e}")
            continue

    print(f"⚠️ ไม่พบไฟล์ nBars_SL สำหรับ {symbol} M{timeframe}, ใช้ค่า default: {default}")
    return default

def load_optimal_nbars(symbol, timeframe, default=6):
    print(f"\n🏗️ เปิดใช้งาน load optimal nbars") if Steps_to_do else None

    # ใช้ฟังก์ชันใหม่ที่รองรับทั้งระบบเดิมและใหม่
    nbars = load_optimal_nbars_compatible(symbol, timeframe, scenario_name=None, default=default)

    return nbars

def load_scenario_models(symbol, timeframe, base_folder=models_dir):
    """
    โหลดโมเดลทั้ง 2 scenarios (ปรับปรุงใหม่)

    โครงสร้างไฟล์ที่คาดหวัง:
    models/
    ├─ counter_trend/
    │   ├─ 060_SYMBOL_trained.pkl
    │   ├─ 060_SYMBOL_features.pkl
    │   └─ 060_SYMBOL_scaler.pkl
    └─ trend_following/
        ├─ 060_SYMBOL_trained.pkl
        ├─ 060_SYMBOL_features.pkl
        └─ 060_SYMBOL_scaler.pkl

    Args:
        symbol: สัญลักษณ์ (เช่น AUDUSD, GOLD, USDJPY)
        timeframe: timeframe (เช่น 60 สำหรับ H1)
        base_folder: โฟลเดอร์ที่เก็บโมเดล

    Returns:
        dict: โมเดลทั้ง 2 scenarios (trend_following, counter_trend)
    """
    print(f"\n🏗️ เปิดใช้งาน load scenario models") if Steps_to_do else None

    models = {}

    print(f"🔍 กำลังโหลดโมเดลสำหรับ {symbol} M{timeframe}")
    print(f"📁 Base folder: {base_folder}")

    for scenario_name in MARKET_SCENARIOS.keys():
        scenario_folder = os.path.join(base_folder, scenario_name)

        # ใช้รูปแบบชื่อไฟล์ที่ตรงกับการบันทึกจริง
        model_path = os.path.join(scenario_folder, f"{timeframe}_{symbol}_trained.pkl")
        feature_path = os.path.join(scenario_folder, f"{timeframe}_{symbol}_features.pkl")
        scaler_path = os.path.join(scenario_folder, f"{timeframe}_{symbol}_scaler.pkl")

        print(f"\n🔍 ตรวจสอบ {scenario_name}:")
        print(f"  📄 Model: {model_path}")
        print(f"  📄 Features: {feature_path}")
        print(f"  📄 Scaler: {scaler_path}")

        if os.path.exists(model_path) and os.path.exists(feature_path) and os.path.exists(scaler_path):
            try:
                model = joblib.load(model_path)
                features = joblib.load(feature_path)
                scaler = joblib.load(scaler_path)

                models[scenario_name] = {
                    'model': model,
                    'features': features,
                    'scaler': scaler,
                    'model_path': model_path,
                    'feature_path': feature_path,
                    'scaler_path': scaler_path
                }
                print(f"✅ โหลดโมเดล {scenario_name} สำเร็จ")
                print(f"  📊 Features: {len(features)} features")

            except Exception as e:
                print(f"❌ ไม่สามารถโหลดโมเดล {scenario_name}: {e}")
        else:
            missing_files = []
            if not os.path.exists(model_path):
                missing_files.append("trained.pkl")
            if not os.path.exists(feature_path):
                missing_files.append("features.pkl")
            if not os.path.exists(scaler_path):
                missing_files.append("scaler.pkl")
            print(f"⚠️ ไม่พบไฟล์ {scenario_name}: {', '.join(missing_files)}")

    print(f"\n📊 สรุปการโหลดโมเดล: {len(models)}/{len(MARKET_SCENARIOS)} โมเดล")

    if len(models) == 0:
        print("❌ ไม่สามารถโหลดโมเดลใดๆ ได้")
        print("💡 ตรวจสอบว่าได้เทรนโมเดลแล้วหรือยัง และโครงสร้างไฟล์ถูกต้อง")
    elif len(models) < len(MARKET_SCENARIOS):
        print("⚠️ โหลดโมเดลได้ไม่ครบ - อาจส่งผลต่อประสิทธิภาพการทำนาย")
    else:
        print("✅ โหลดโมเดลครบทุก scenarios")

    return models

def load_time_filters(symbol, timeframe):
    # print(f"\n🏗️ เปิดใช้งาน load time filters") if Steps_to_do else None

    # แปลง timeframe จาก PERIOD_M30 -> 30, PERIOD_H1 -> 60 เป็นต้น
    timeframe_map = {
        "PERIOD_M30": 30,
        "PERIOD_H1": 60,
        "PERIOD_M1": 1,
        "PERIOD_M5": 5,
        "PERIOD_M15": 15,
        "PERIOD_H4": 240,
        30: 30,  # รองรับการส่งค่าตัวเลขโดยตรง
        60: 60,
        1: 1,
        5: 5,
        15: 15,
        240: 240
    }

    # ใช้ timeframe_num สำหรับชื่อไฟล์
    timeframe_num = timeframe_map.get(timeframe, timeframe)

    path = f"{test_folder}/thresholds/{str(timeframe_num).zfill(3)}_{symbol}_time_filters.pkl"
    print(f"กำลังโหลด time filters จาก: {path}")
    if os.path.exists(path):
        try:
            with open(path, 'rb') as f:
                filters = pickle.load(f)
            print(f"✅ โหลด time filters สำเร็จ ({symbol}_{timeframe_num})")
            return filters
        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาดขณะโหลด time filters: {e}")
            return {'days': list(range(7)), 'hours': list(range(24))}
    else:
        print(f"⚠️ ไม่พบไฟล์ time filters ที่ {path} จะใช้ค่า default (ทุกวัน/ทุกชั่วโมง)")
        return {'days': list(range(7)), 'hours': list(range(24))}

# ==============================================

def enhanced_look_ahead_check(df, start_index, model_features):
    print(f"\n🏗️ เปิดใช้งาน enhanced look ahead check") if Steps_to_do else None

    print("🔍 ตรวจสอบ Look-Ahead Bias แบบละเอียด")
    
    # ตรวจสอบ model_features ก่อนใช้งาน
    if model_features is None:
        print("⚠️ ไม่พบ model_features (None) ใน enhanced look ahead check, ข้ามการตรวจสอบ Look-Ahead Bias")
        return True  # หรือ False ถ้าต้องการหยุด process

    # 1. ตรวจสอบ Features ที่มีความเสี่ยงสูง (อาจจะต้องเพิ่ม Feature ที่แก้ไขไปแล้วทั้งหมด)
    high_risk_features = [
        'Volume_MA20', 'Rolling_Vol_5', 'Rolling_Vol_15', 
        'MA_Cross', 'EMA_diff', 'MACD_12_26_9', 'RSI14', # Feature ที่เคยมีปัญหา
        'STOCHk_14_3_3', 'STOCHd_14_3_3', # Stochastic
        'Upper_BB', 'Lower_BB', 'BB_width', # Bollinger Bands
        'ADX_14', 'DMP_14', 'DMN_14', # ADX
        'ATR', # ATR
        'Support', 'Resistance' # SR
        ] 
    # เพิ่ม Rolling MAs/Stds ที่เพิ่งแก้ไข
    for window in [3, 5, 10, 20]:
        high_risk_features.extend([f'Close_MA_{window}', f'Volume_MA_{window}', f'Close_Std_{window}'])

    # กรองให้เหลือเฉพาะ Feature ที่มีอยู่ใน DataFrame และอยู่ใน model_features
    features_to_check = [feat for feat in high_risk_features if feat in df.columns and feat in model_features]
    
    problematic_features = set()
    
    print(f"กำลังตรวจสอบ Features จำนวน {len(features_to_check)} รายการ...")

    for feat in features_to_check:
        # ตรวจสอบ 5 จุดสำคัญ
        check_points = [
            start_index + i * (len(df) - start_index) // 4 for i in range(5)
        ]
        check_points = [idx for idx in check_points if idx >= start_index + 1 and idx < len(df) - 1] # ตรวจสอบจุดที่เหมาะสม

        # ถ้า check_points ว่าง ให้ข้ามไป
        if not check_points:
            print(f"ข้าม Feature {feat}: ไม่มีจุดให้ตรวจสอบ")
            continue
            
        # print(f"\nตรวจสอบ Feature: {feat}") # เปิดคอมเมนต์นี้เพื่อดูว่ากำลังเช็ค Feature ไหน
        
        flagged_this_feature = False
        for idx in check_points:
            current_val = df[feat].iloc[idx]
            next_val = df[feat].iloc[idx+1]

            # ปรับ tolerance ให้เหมาะสมกับแต่ละฟีเจอร์
            if feat == 'Volume_MA20' or 'Volume_MA_' in feat:
                tol = 0.01  # 1% tolerance สำหรับ Volume
            elif feat in ['EMA_diff', 'ATR', 'BB_width']:
                tol = 1e-4 # tolerance เล็กน้อย
            elif 'EMA' in feat or 'MACD' in feat or 'RSI' in feat or 'STOCH' in feat or 'ADX' in feat or 'Dist_' in feat or 'BB_' in feat or 'Support' in feat or 'Resistance' in feat or 'Close_MA_' in feat or 'Close_Std_' in feat:
                # tolerance สำหรับ indicators ทั่วไปและค่าเฉลี่ย/Std ของราคา
                # คำนวณ relative tolerance จากค่าเฉลี่ยของสองค่า หรือ absolute tolerance ถ้าค่าใกล้ศูนย์
                avg_val = (abs(current_val) + abs(next_val)) / 2
                if avg_val < 1e-6: # ถ้าค่าใกล้ศูนย์มาก ใช้ absolute tolerance
                        tol = 1e-6 # absolute tolerance
                else:
                        tol = 1e-5 # relative tolerance 0.001%
            else: # features อื่นๆ เช่น Bar patterns, IsMorning etc. (ซึ่งไม่ควรมี look-ahead)
                tol = 1e-9 # strict tolerance สำหรับค่า discrete หรือ binary

            # ถ้าค่าที่ index i และ i+1 *เท่ากันเป๊ะ* หรือ *ใกล้เคียงกันมากเกินไป* #               # อาจบ่งชี้ว่าค่าที่ index i ได้ใช้ข้อมูลจาก index i+1 ในการคำนวณ
            if np.isclose(current_val, next_val, rtol=tol, atol=tol): # ใช้ทั้ง relative และ absolute tolerance
                problematic_features.add(feat)
                # print(f" ⚠️ พบความใกล้เคียงที่น่าสงสัยสำหรับ Feature '{feat}' ที่ index {idx}: {current_val:.5f} -> {next_val:.5f} (tol={tol})")
                flagged_this_feature = True # Mark ว่า Feature นี้มีปัญหาแล้ว
                break # หยุดเช็ค Feature นี้ที่จุดอื่น เพื่อไม่ให้ print ซ้ำเยอะเกินไป

        # ถ้าเช็คเกอร์เดิมแจ้งเตือน ⚠️เมื่อค่า *ไม่* ใกล้เคียง (your original logic)
        # if not np.isclose(current_val, next_val, rtol=tol):
        #      problematic_features.add(feat)
        #      change_pct = ((next_val-current_val)/current_val)*100 if current_val != 0 else 0
        #      print(f" ⚠️ index {idx}: {current_val:.5f} -> {next_val:.5f} (เปลี่ยนแปลง {change_pct:.2f}%)")

    # 2. แสดงตัวอย่างการคำนวณ Features (5 แถวแรก)
    print("\nตัวอย่างการคำนวณ Features (5 แถวแรกหลังจาก dropna):")
    # ใช้เฉพาะ Feature ที่เราสนใจและอยู่ใน model_features
    sample_features = [feat for feat in ['Close', 'Volume', 'Volume_MA20', 'EMA50', 'EMA200', 'EMA_diff', 'RSI14', 'Upper_BB', 'ATR', 'Support', 'Close_MA_20'] if feat in df.columns and feat in model_features] # macd_line_col, stoch_k_col,  เอาออกก่อน
    if not sample_features:
        print("ไม่มี Features ที่เลือกไว้สำหรับแสดงตัวอย่าง")
        # อาจจะเลือก Features อื่นๆ แทน
        sample_features = [col for col in df.columns if col not in ['Date', 'Time', 'DateTime', 'Open', 'High', 'Low', 'Close', 'Volume', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']][:5] # เลือก 5 Features แรกที่เหลือ
        print(f"(แสดงตัวอย่าง 5 Features แรก: {sample_features})")

    # หา index เริ่มต้นหลัง dropna
    start_index_after_dropna = df.index[0]

    for i in range(start_index_after_dropna, min(start_index_after_dropna+3, len(df))): # แสดง 10 แถวแรก
        print(f"\n📌 แถวที่ {i} (เวลา: {df['DateTime'].iloc[i]})")
        for feat in sample_features:
            if feat in df.columns:
                    current_val = df[feat].iloc[i]
                    prev_val = df[feat].iloc[i-1] if i > start_index_after_dropna else None # ใช้ค่าที่ index i-1
                    
                    # แสดงค่าปัจจุบัน
                    current_str = f"{current_val:.5f}" if isinstance(current_val, (int, float)) else str(current_val)
                    
                    # แสดงค่าก่อนหน้า (ที่ index i-1)
                    prev_str = f"{prev_val:.5f}" if prev_val is not None and isinstance(prev_val, (int, float)) else "N/A"
                    
                    print(f"{feat}: {current_str} (ก่อนหน้า: {prev_str})")
            
    if problematic_features:
        print("\n❌ พบ Features ที่น่าสงสัยว่าอาจมี Look-Ahead Bias:")
        for feat in problematic_features:
            print(f"- {feat}")
        print("\n⚠️ โปรดตรวจสอบการคำนวณ Feature เหล่านี้อีกครั้ง โดยเฉพาะการใช้ .shift(1) ที่ผลลัพธ์สุดท้าย")
        return False
    else:
        print("\n✅ ไม่พบ Features ที่น่าสงสัยว่ามี Look-Ahead Bias ในจุดที่ตรวจสอบ")
        return True

def check_look_ahead_bias(df, i, model_features, scaler, nan_count, suspect_feature_count, suspect_features):
    # print(f"\n🏗️ เปิดใช้งาน check look ahead bias") if Steps_to_do else None
    
    """
    ตรวจสอบ look-ahead bias และเตรียม features สำหรับโมเดลในแต่ละแถว
    - df: DataFrame หลัก
    - i: index ปัจจุบัน
    - model_features: รายชื่อ features ที่โมเดลต้องการ
    - scaler: Scaler ที่ fit แล้ว (StandardScaler)
    - nan_count, suspect_feature_count, suspect_features: ตัวแปรนับและเก็บ features ที่มีปัญหา
    คืนค่า: current_features_data, scaled_features_df, nan_count, suspect_feature_count, suspect_features
    """
    # 1. กำหนด features ที่จะใช้
    excluded_columns = ['Target', 'Date', 'Time', 'DateTime', 'Entry Time', 'Exit Time']

    if scaler is not None and hasattr(scaler, 'feature_names_in_'):
        # ใช้ features ที่ scaler รู้จัก แต่กรอง excluded columns ออก
        scaler_features = scaler.feature_names_in_
        if isinstance(scaler_features, (list, tuple)):
            features_for_model = [f for f in scaler_features if f not in excluded_columns]
        elif hasattr(scaler_features, 'tolist'):  # numpy array
            features_for_model = [f for f in scaler_features.tolist() if f not in excluded_columns]
        else:
            features_for_model = [f for f in list(scaler_features) if f not in excluded_columns]
    else:
        # ใช้ model_features ที่ส่งเข้ามา
        if isinstance(model_features, (list, tuple)):
            features_for_model = [f for f in model_features if f not in excluded_columns]
        elif hasattr(model_features, 'tolist'):  # numpy array
            features_for_model = [f for f in model_features.tolist() if f not in excluded_columns]
        else:
            features_for_model = [f for f in list(model_features) if f not in excluded_columns]

    # ตรวจสอบว่า features ที่ต้องการมีอยู่ใน DataFrame จริง
    available_features = [f for f in features_for_model if f in df.columns]
    missing_features = [f for f in features_for_model if f not in df.columns]

    if missing_features:
        print(f"⚠️ ไม่พบ features: {missing_features[:3]}... (แสดง 3 ตัวแรก)")
        features_for_model = available_features

    if not features_for_model:
        print(f"❌ ไม่มี features ที่ใช้ได้ - ใช้ features ทั้งหมดใน DataFrame")
        features_for_model = [col for col in df.columns if col not in excluded_columns]

    if SHOW_FEATURE_DEBUG:
        print(f"🔍 Features ที่จะใช้: {len(features_for_model)} features (กรอง excluded แล้ว)")

    # 2. เตรียมข้อมูล features ของแถว i (ต้อง reindex ให้ครบทุก features)
    current_features_data = df.loc[[i], features_for_model]
    current_features_data = current_features_data.reindex(columns=features_for_model, fill_value=0)

    # จัดการ infinity และ NaN values
    current_features_data = current_features_data.replace([np.inf, -np.inf], np.nan)
    current_features_data = current_features_data.fillna(0)

    # 3. ตรวจสอบ NaN
    nan_features = current_features_data.columns[current_features_data.isna().any()].tolist()
    if nan_features:
        nan_count += 1
        suspect_features.update(nan_features)

    # 4. ทำ scaling
    scaled_features_array = None
    try:
        if scaler:
            # ตรวจสอบว่า scaler มี Target ใน feature_names_in_ หรือไม่
            if hasattr(scaler, 'feature_names_in_') and 'Target' in scaler.feature_names_in_:
                if SHOW_SCALER_DEBUG:
                    print(f"⚠️ Scaler มี Target ใน features - ใช้ข้อมูลดิบแทน")

                # ใช้ข้อมูลดิบแทนการสร้าง scaler ใหม่ (เพื่อหลีกเลี่ยงปัญหา infinity)
                scaled_features_array = current_features_data.values

                if SHOW_SCALER_DEBUG:
                    print(f"✅ ใช้ข้อมูลดิบ (ไม่ scale): {len(features_for_model)} features")

            else:
                # scaler ปกติ - ตรวจสอบว่า features ตรงกันหรือไม่
                scaler_features = list(scaler.feature_names_in_) if hasattr(scaler, 'feature_names_in_') else []
                current_features = list(current_features_data.columns)

                if scaler_features and set(scaler_features) != set(current_features):
                    print(f"⚠️ Features ไม่ตรงกัน - Scaler: {len(scaler_features)}, Current: {len(current_features)}")
                    print(f"   Scaler features: {scaler_features[:3]}...")
                    print(f"   Current features: {current_features[:3]}...")

                    # ใช้ข้อมูลดิบแทน
                    scaled_features_array = current_features_data.values
                    print(f"✅ ใช้ข้อมูลดิบเนื่องจาก features ไม่ตรงกัน")
                else:
                    # Features ตรงกัน - ใช้ scaler ปกติ
                    scaled_features_array = scaler.transform(current_features_data)
        else:
            scaled_features_array = current_features_data.values
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะ scaling features ที่แถว {i}: {str(e)}")
        import traceback
        traceback.print_exc()
        scaled_features_array = current_features_data.values
        suspect_feature_count += 1

    # 5. แปลงกลับเป็น DataFrame (ใช้ชื่อ columns จาก features_for_model)
    if scaled_features_array is not None:
        scaled_features_df = pd.DataFrame(scaled_features_array, index=current_features_data.index, columns=features_for_model)
    else:
        scaled_features_df = pd.DataFrame(columns=features_for_model)

    return current_features_data, scaled_features_df, nan_count, suspect_feature_count, suspect_features

def is_high_quality_entry(df, i, symbol="GOLD"):
    """
    ตรวจสอบว่าเป็น high-quality entry หรือไม่ เพื่อเพิ่ม win rate

    เงื่อนไข:
    1. ATR สูงพอ (volatility เพียงพอ)
    2. Volume สูงกว่าค่าเฉลี่ย
    3. Trend confirmation (EMA alignment)
    4. RSI ไม่อยู่ใน neutral zone
    5. MACD confirmation
    """
    # print(f"\n🏗️ เปิดใช้งาน is high quality entry") if Steps_to_do else None

    try:
        if i < TREND_CONFIRMATION_PERIODS:
            return False

        # 1. ตรวจสอบ ATR - ต้องมี volatility เพียงพอ
        current_atr = df['ATR'].iloc[i-1] if 'ATR' in df.columns else 0
        if current_atr < MIN_ATR_THRESHOLD:
            return False

        # 2. ตรวจสอบ Volume (ถ้ามี)
        if 'Volume' in df.columns:
            current_volume = df['Volume'].iloc[i-1]
            avg_volume = df['Volume'].iloc[max(0, i-20):i].mean()
            if current_volume < avg_volume * MIN_VOLUME_MULTIPLIER:
                return False

        # 3. Trend Confirmation - EMA alignment
        if 'EMA50' in df.columns and 'EMA20' in df.columns:
            ema20_current = df['EMA20'].iloc[i-1]
            ema50_current = df['EMA50'].iloc[i-1]
            close_current = df['Close'].iloc[i-1]

            # สำหรับ Buy: Close > EMA20 > EMA50 (uptrend)
            # สำหรับ Sell: Close < EMA20 < EMA50 (downtrend)
            uptrend = close_current > ema20_current > ema50_current
            downtrend = close_current < ema20_current < ema50_current

            if not (uptrend or downtrend):
                return False

        # 4. RSI ไม่อยู่ใน neutral zone (40-60)
        if 'RSI14' in df.columns:
            rsi = df['RSI14'].iloc[i-1]
            if 40 <= rsi <= 60:  # neutral zone - หลีกเลี่ยง
                return False

        # 5. MACD Confirmation
        if 'MACD' in df.columns and 'MACD_signal' in df.columns:
            macd = df['MACD'].iloc[i-1]
            macd_signal = df['MACD_signal'].iloc[i-1]

            # ต้องมี MACD divergence ที่ชัดเจน
            macd_diff = abs(macd - macd_signal)
            if macd_diff < 0.0001:  # MACD และ Signal ใกล้กันเกินไป
                return False

        return True

    except Exception as e:
        print(f"⚠️ Error in is_high_quality_entry: {e}")
        return False

def create_trade_cycles_with_model(
        df, trained_model=None, scaler=None, model_features=None,
        rsi_level=input_rsi_level_in, rsi_level_out=input_rsi_level_out, 
        stop_loss_atr_multiplier=input_stop_loss_atr, take_profit_stop_loss_ratio=input_take_profit, 
        symbol=None, timeframe=None, identifier=None, 
        nBars_SL=None, model_confidence_threshold=None, 
        entry_condition_func=None, entry_condition_name=None
        ):
    print(f"\n🏗️ เปิดใช้งาน create trade cycles with model") if Steps_to_do else None

    """
    สร้างรายการซื้อขายด้วยเงื่อนไขทางเทคนิค โดยใช้ Model ที่เทรนแล้วช่วยตัดสินใจ
    Args:
        df (pd.DataFrame): DataFrame ที่มีข้อมูล OHLC, Indicators, และ Features
        trained model: โมเดล ML ที่เทรนแล้ว (e.g., LightGBMClassifier)
        scaler: Scaler ที่ใช้ scale features สำหรับโมเดล (e.g., StandardScaler)
        model_features (list): รายชื่อ features ที่โมเดลคาดหวัง
        model confidence threshold (float): เกณฑ์ความน่าจะเป็น (0-1) สำหรับการเข้าเทรด
        ... (parameters อื่นๆ เหมือนเดิม) ...
    """

    is_production = False  # หรือ True ถ้าต้องการใช้งานจริง
    if is_production:
        time_filters = load_time_filters(symbol, timeframe)
    else:
        time_filters = {'days': list(range(7)), 'hours': list(range(24))}

    trades = []
    in_trade_buy = False
    in_trade_sell = False
    entry_price_buy = entry_price_sell = None
    entry_time_buy = entry_time_sell = None
    trade_type_buy = trade_type_sell = None

    nan_count = 0
    suspect_feature_count = 0
    suspect_features = set()

    symbol_spread = symbol_info[symbol]["Spread"]
    symbol_digits = symbol_info[symbol]["Digits"]
    symbol_points = symbol_info[symbol]["Points"]

    stats = {
        'buy': {'total': 0, 'profit_sum': 0},
        'sell': {'total': 0, 'profit_sum': 0},
        'day_stats': {i: {'win': 0, 'loss': 0, 'total': 0} for i in range(7)},  # 0=Monday, 6=Sunday
        'hour_stats': {i: {'win': 0, 'loss': 0, 'total': 0} for i in range(24)}
    }

    # ตรวจสอบว่ามี Model, Scaler, Features พร้อมหรือไม่
    # รองรับทั้ง Single-Model และ Multi-Model Architecture
    is_multi_model = isinstance(trained_model, dict) and len(trained_model) > 0

    if is_multi_model:
        # Multi-Model Architecture: ตรวจสอบว่ามีโมเดลและ features
        use_model_for_decision = (trained_model is not None and model_features is not None and len(model_features) > 0)
        print(f"🔄 ใช้ Multi-Model Architecture: {list(trained_model.keys())}")
        print(f"📊 Features ที่ Model ใช้: {len(model_features)} features")
    else:
        # Single-Model Architecture: ตรวจสอบแบบเดิม
        print(f"📊 ใช้ Single-Model Architecture")

        # Debug แต่ละเงื่อนไข
        print(f"🔍 Debug Single-Model conditions:")
        print(f"   trained_model is not None: {trained_model is not None}")
        print(f"   scaler is not None: {scaler is not None}")
        print(f"   model_features is not None: {model_features is not None}")
        print(f"   len(model_features) > 0: {len(model_features) > 0 if model_features else False}")

        use_model_for_decision = (trained_model is not None and scaler is not None and model_features is not None and len(model_features) > 0)

    print(f"ตรวจสอบการใช้ Model ML : {use_model_for_decision}")
    print(f"trained_model type: {type(trained_model)}")
    if not is_multi_model:
        print(f"scaler type: {type(scaler)}")
    print(f"model_features {len(model_features) if model_features else 0} : {model_features[:5] if model_features else None}...")

    if use_model_for_decision:
        if is_multi_model:
            print("\n🤖 จะใช้ Multi-Model ML ช่วยในการตัดสินใจเข้าเทรด")
            print(f"🎯 Scenarios ที่มี: {list(trained_model.keys())}")
        else:
            print("\n🤖 จะใช้ Single-Model ML ช่วยในการตัดสินใจเข้าเทรด")
        print(f"🎯 เกณฑ์ความน่าจะเป็น Model (TP Hit): > {model_confidence_threshold:.4f}")
    else:
        print("❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม")

    # กำหนดชื่อไฟล์ log โดยใช้ timeframe
    log_file_name = f"{test_folder}/{timeframe}_{symbol}_trade_log_model_enhanced.txt" if timeframe else "trade_log_model_enhanced.txt"

    # เปิดไฟล์สำหรับบันทึกข้อมูล
    with open(log_file_name, "w") as log_file:
        # เริ่ม Loop ตั้งแต่ index ที่พอจะคำนวณ Indicator ต่างๆ ได้ครบ
        # ต้องเผื่อสำหรับ Indicators ที่ต้องใช้ข้อมูลย้อนหลัง และเผื่อสำหรับ SL/TP prev bars (index i-2)
        # ดังนั้น เริ่ม loop ที่ index ที่มากพอ เช่น 200 หรือมากกว่า
        # ปรับ start_index ให้เหมาะสมกับข้อมูลที่มี
        min_required_rows = 50  # ลดจาก 300 เป็น 50 เพื่อให้ทำงานได้กับข้อมูลน้อย
        available_rows = len(df)

        if available_rows < min_required_rows:
            print(f"⚠️ ข้อมูลมีเพียง {available_rows} แถว ต้องการอย่างน้อย {min_required_rows} แถว")
            start_index = max(available_rows - 10, 10)  # ใช้ข้อมูลที่มีให้มากที่สุด
        else:
            start_index = max(min_required_rows, df.first_valid_index() or 0)

        print(f"🔍 ใช้ start_index = {start_index} (ข้อมูลทั้งหมด {available_rows} แถว)")

        if 'DateTime' not in df.columns:
            if 'Entry Time' in df.columns:
                df = df.copy()
                df['DateTime'] = pd.to_datetime(df['Entry Time'], errors='coerce')
            else:
                print("⚠️ ไม่พบคอลัมน์ DateTime หรือ Entry Time ใน DataFrame, ข้ามการแสดงช่วงเวลา")
                df['DateTime'] = pd.NaT

        if start_index >= len(df):
            print(f"⚠️ start_index ({start_index}) >= จำนวนแถวใน df ({len(df)}) ปรับ start_index ใหม่")
            start_index = max(len(df) - 10, 5)  # ใช้ข้อมูล 10 แถวสุดท้าย หรืออย่างน้อย 5 แถว
            print(f"🔧 ปรับ start_index เป็น {start_index}")

        if start_index >= len(df):
            print(f"❌ ข้อมูลน้อยเกินไป ({len(df)} แถว) ไม่สามารถทำ backtest ได้")
            return pd.DataFrame(), {}

        print("\n🔍 ตรวจสอบ Temporal Dependence สำหรับการเทรด")
        start_time = df['DateTime'].iloc[start_index]
        end_time = df['DateTime'].iloc[-1]
        print(f"- ช่วงเวลาที่จะทำ backtest: {start_time} ถึง {end_time}")
        print(f"- จำนวนแท่งข้อมูลทั้งหมดสำหรับ backtest: {len(df) - start_index}")
        
        # ตรวจสอบความถี่ของการเทรด
        trade_freq = df['DateTime'].diff().value_counts().head(5)
        print("\nความถี่ของช่วงเวลาระหว่างแท่งข้อมูล:")
        print(trade_freq)
        
        # ตรวจสอบความสัมพันธ์ระหว่างเวลาและผลลัพธ์การเทรด (ตัวอย่าง)
        if 'Target' in df.columns:
            hour_profit = df.groupby(df['DateTime'].dt.hour)['Target'].mean()
            print("\nอัตราการชน TP (Win Rate) แยกตามชั่วโมง:")
            print(hour_profit)

        print(f"\n▶️ เริ่ม Backtest จาก index: {start_index} (เพื่อให้ Indicators คำนวณได้ครบ)")

        # ส่วนที่เรียกใช้การตรวจสอบในโค้ด Backtest
        # ...
        # start_index = ... # กำหนด index ที่จะเริ่ม backtest หรือ index แรกหลัง dropna
        # model_features = ... # กำหนด list ของ features ที่ใช้ใน model
        # ...
        print("\n🔍 ตรวจสอบ Look-Ahead Bias ก่อนเริ่ม Backtest")
        try:
            # ตรวจสอบตั้งแต่ index แรกหลัง dropna
            start_index_for_check = df.index[0]

            look_ahead_ok = True  # กำหนดค่า default
            if model_features is not None:
                look_ahead_ok = enhanced_look_ahead_check(df, start_index_for_check, model_features)
            else:
                print("⚠️ ไม่พบ model_features ข้ามการตรวจสอบ Look-Ahead Bias")

            if not look_ahead_ok:
                print("\n❌ พบปัญหา Look-Ahead Bias ใน Features ที่ระบุ")
                print("โปรดแก้ไขการคำนวณ Features เหล่านั้นก่อนดำเนินการต่อ")
                # return pd.DataFrame(), {}  # ยกเลิกการ Backtest ถ้าพบปัญหา
                # หรือจะเลือกที่จะดำเนินการต่อแต่มีคำเตือน ⚠️
                # raise ValueError("Look-Ahead Bias detected in features.") # อาจจะ raise Exception เพื่อหยุด Backtest
                # ในตัวอย่างนี้ แค่ print เตือน ⚠️แต่ยังให้โค้ดทำงานต่อได้ (ไม่แนะนำสำหรับการ Backtest จริงจัง)
            else:
                print("\n✅ การตรวจสอบ Look-Ahead Bias เสร็จสมบูรณ์ ไม่มีปัญหาที่ตรวจพบโดยอัตโนมัติ")

        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ขณะตรวจสอบ Look-Ahead Bias: {str(e)}")
            import traceback
            traceback.print_exc()
            # return pd.DataFrame(), {} # ยกเลิกการ Backtest ถ้าเกิดข้อผิดพลาด ⚠️ ในการตรวจสอบ
            # ในตัวอย่างนี้ แค่ print error แต่ยังให้โค้ดทำงานต่อได้ (ไม่แนะนำสำหรับการ Backtest จริงจัง)

        if use_model_for_decision:
            print(f"\n🤖 จะใช้ Model ML ช่วยในการตัดสินใจเข้าเทรด {timeframe} {symbol}")
            print(f"📊 Features ที่ Model ใช้: {model_features}")
            print(f"🎯 เกณฑ์ความน่าจะเป็น Model (TP Hit): > {model_confidence_threshold:.3f} รอบที่ {identifier} / {NUM_TRAINING_ROUNDS}")
        else:
            print("\n❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม")

        # กำหนดชื่อไฟล์ log โดยใช้ timeframe
        log_file_name = f"{test_folder}/{timeframe}_{symbol}_trade_log_model_enhanced.txt" if timeframe else "trade_log_model_enhanced.txt"

        for i in range(start_index, len(df)):
            # current_time refers to the time of the bar 'i' where the potential entry is made at Open[i]
            if 'Date' in df.columns and 'Time' in df.columns:
                current_time = pd.to_datetime(df['Date'].iloc[i] + ' ' + df['Time'].iloc[i])
            elif 'DateTime' in df.columns:
                current_time = df['DateTime'].iloc[i]
            else:
                current_time = pd.NaT  # หรือ raise Exception("No valid datetime columns found")

            hour = current_time.hour
            day_of_week = df['DayOfWeek'].iloc[i] # This can use data from bar i, as DayOfWeek is known

            # --- Check if we have enough previous data for lookback periods ---
            # This check depends on the maximum lookback needed for your indicators/features + SL calculation
            min_lookback = 3 # Minimum bars needed for SL (i-3, i-2, i-1) + features/indicators lookback
            if i < start_index + min_lookback:
                continue # Skip if not enough previous data

            # --- Data from the PREVIOUS completed bar (i-1) ---
            prev_atr = df['ATR'].iloc[i-1] # Assuming ATR is calculated based on previous bars

            # ป้องกันกรณี time_filters ไม่มี key หรือเป็น list ว่าง
            days_filter = time_filters.get('days', list(range(7)))
            hours_filter = time_filters.get('hours', list(range(24)))
            if not days_filter:
                days_filter = list(range(7))
            if not hours_filter:
                hours_filter = list(range(24))

            # เงื่อนไขเวลาที่ปรับปรุงแล้ว (ใช้เวลาของแท่งปัจจุบัน i ซึ่งทราบแล้ว)
            time_condition = (
                4 <= hour < 22 and
                day_of_week in days_filter and
                hour in hours_filter
            )

            # ตรวจสอบชื่อคอลัมน์ก่อนสร้าง prev_dict
            required_cols = ['Close', 'Open', 'EMA50', 'EMA200', 'MACD_signal', 'RSI14', 'Volume', 'Volume_MA20', 'PullBack_Up', 'Ratio_Buy', 'PullBack_Down', 'Ratio_Sell']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"⚠️ ขาดคอลัมน์ที่จำเป็นใน create trade cycles with model : df: {missing_cols} (index {i})")
                continue  # ข้ามรอบนี้

            prev_dict = {
                'close': df['Close'].iloc[i-1],
                'open': df['Open'].iloc[i-1],
                'high': df['High'].iloc[i-1],
                'low': df['Low'].iloc[i-1],
                'ema50': df['EMA50'].iloc[i-1],
                'ema100': df['EMA100'].iloc[i-1],
                'ema200': df['EMA200'].iloc[i-1],
                'macd_deep': df['MACD_deep'].iloc[i-1],
                'macd_signal': df['MACD_signal'].iloc[i-1],
                'rsi14': df['RSI14'].iloc[i-1],
                'volume': df['Volume'].iloc[i-1],
                'volume_ma20': df['Volume_MA_20'].iloc[i-1],
                'pullback_buy': df['PullBack_Up'].iloc[i-1],
                'ratio_buy': df['Ratio_Buy'].iloc[i-1],
                'pullback_sell': df['PullBack_Down'].iloc[i-1],
                'ratio_sell': df['Ratio_Sell'].iloc[i-1],
                # เพิ่ม field อื่นๆ ตามที่ entry_func ต้องใช้
            }

            # --- Technical Signals (using data from the PREVIOUS completed bar i-1) ---
            if not in_trade_buy: # Signal Combination
                
                if entry_condition_func is not None:
                    # ใช้ 4 scenarios สำหรับ Multi-Model Architecture
                    close = prev_dict.get('close', 0)
                    ema200 = prev_dict.get('ema200', close)

                    scenario_used = "trend_following" if close > ema200 else "counter_trend"
                    if close > ema200:
                        # Trend Following (ราคาเหนือ EMA200)
                        if 'trend_following' in entry_conditions:
                            tech_signal_buy = entry_conditions['trend_following']['buy'](prev_dict)
                            # print(f"  🔍 BUY Entry ({scenario_used}): {tech_signal_buy} | Close={close:.5f} > EMA200={ema200:.5f}")
                        else:
                            tech_signal_buy = entry_condition_func['buy'](prev_dict)
                    else:
                        # Counter Trend (ราคาใต้ EMA200)
                        if 'counter_trend' in entry_conditions:
                            tech_signal_buy = entry_conditions['counter_trend']['buy'](prev_dict)
                            # print(f"  🔍 BUY Entry ({scenario_used}): {tech_signal_buy} | Close={close:.5f} < EMA200={ema200:.5f}")
                        else:
                            tech_signal_buy = entry_condition_func['buy'](prev_dict)
                else:
                    # Debug แต่ละเงื่อนไข
                    cond1 = prev_dict['close'] > prev_dict['open']
                    cond2 = prev_dict['rsi14'] > rsi_level
                    cond3 = prev_dict['macd_signal'] == 1.0
                    cond4 = prev_dict['volume'] > prev_dict['volume_ma20']
                    cond5 = prev_dict['pullback_buy'] > input_pull_back
                    cond6 = prev_dict['ratio_buy'] > take_profit_stop_loss_ratio

                    tech_signal_buy = cond1 and cond2 and cond3 and cond4 and cond5 and cond6

                # ตรวจสอบสัญญาณเข้าซื้อ (อิงจากข้อมูลแท่ง i-1 และเวลากับวันของแท่ง i)
                if tech_signal_buy and time_condition:

                    # --- ใช้ Model ML ช่วยตัดสินใจ (ถ้ามี) ---
                    model_decision_buy = True # Default: ถ้าไม่ใช้โมเดล ให้ตัดสินใจเข้าตามกฎเดิม
                    prob_win = -1 # Initialize prob_win

                    if Model_Decision and use_model_for_decision:
                        try:
                            # ==============================================
                            # ส่วนเพิ่มเติม: ตรวจสอบ Look-Ahead Bias ก่อนทำนาย
                            # ใช้ index 'i' ในการเรียก แต่ check look ahead bias
                            # จะดึงข้อมูลจาก df.iloc[i-1] และก่อนหน้า
                            # ==============================================

                            if is_multi_model:
                                # Multi-Model Architecture: เลือกโมเดลตามสถานการณ์ตลาด
                                close = prev_dict.get('close', 0)
                                ema200 = prev_dict.get('ema200', close)

                                # เลือก scenario ตามสถานการณ์ตลาดสำหรับ BUY
                                if close > ema200:
                                    scenario_name = "trend_following"  # Uptrend
                                else:
                                    scenario_name = "counter_trend"    # Downtrend/Sideways

                                if scenario_name not in trained_model:
                                    print(f"⚠️ ไม่พบโมเดล {scenario_name} - ข้าม BUY signal")
                                    model_decision_buy = False
                                    prob_win = 0
                                    continue

                                selected_model = trained_model[scenario_name]['model']
                                selected_scaler = trained_model[scenario_name]['scaler']
                                selected_features = trained_model[scenario_name]['features']

                                # print(f"  🎯 BUY: ใช้โมเดล {scenario_name} (Close={close:.5f}, EMA200={ema200:.5f})")

                                # ใช้ features และ scaler ของโมเดลที่เลือก
                                current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                                    df, i, selected_features, selected_scaler, nan_count, suspect_feature_count, suspect_features)

                                # ทำนายด้วยโมเดลที่เลือก
                                # ตรวจสอบจำนวน features ก่อนทำนาย
                                expected_features = selected_model.n_features_in_ if hasattr(selected_model, 'n_features_in_') else len(selected_features)
                                actual_features = scaled_features.shape[1]

                                if expected_features != actual_features:
                                    if SHOW_FEATURE_DEBUG:
                                        print(f"⚠️ BUY Feature count mismatch: Expected {expected_features}, Got {actual_features}")

                                    if actual_features < expected_features:
                                        # เพิ่ม features ที่ขาดหายไป (fill ด้วย 0)
                                        missing_count = expected_features - actual_features
                                        padding = np.zeros((scaled_features.shape[0], missing_count))
                                        scaled_features = np.hstack([scaled_features, padding])
                                        if SHOW_FEATURE_DEBUG:
                                            print(f"✅ BUY เพิ่ม {missing_count} features (fill ด้วย 0)")
                                    else:
                                        # ตัด features ที่เกิน
                                        scaled_features = scaled_features[:, :expected_features]
                                        print(f"✅ ตัด features เหลือ {expected_features} features")

                                prediction_proba = selected_model.predict_proba(scaled_features)[0]

                            else:
                                # Single-Model Architecture: ใช้โมเดลเดียว
                                current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                                    df, i, model_features, scaler, nan_count, suspect_feature_count, suspect_features)

                                # ทำนายความน่าจะเป็น
                                # ตรวจสอบจำนวน features ก่อนทำนาย
                                expected_features = trained_model.n_features_in_ if hasattr(trained_model, 'n_features_in_') else len(model_features)
                                actual_features = scaled_features.shape[1]

                                if expected_features != actual_features:
                                    print(f"⚠️ Feature count mismatch: Expected {expected_features}, Got {actual_features}")

                                    if actual_features < expected_features:
                                        # เพิ่ม features ที่ขาดหายไป (fill ด้วย 0)
                                        missing_count = expected_features - actual_features
                                        padding = np.zeros((scaled_features.shape[0], missing_count))
                                        scaled_features = np.hstack([scaled_features, padding])
                                        # print(f"✅ เพิ่ม {missing_count} features (fill ด้วย 0)")
                                    else:
                                        # ตัด features ที่เกิน
                                        scaled_features = scaled_features[:, :expected_features]
                                        # print(f"✅ ตัด features เหลือ {expected_features} features")

                                prediction_proba = trained_model.predict_proba(scaled_features)[0]

                            # สำหรับ multiclass: รวม probability ของ buy classes (3, 4)
                            if len(prediction_proba) >= 5:
                                prob_buy = prediction_proba[3] + prediction_proba[4]  # weak_buy + strong_buy
                                prob_win = prob_buy
                                # ใช้ threshold ต่ำกว่าสำหรับ buy signals เพื่อให้ได้ signals มากขึ้น
                                buy_threshold = model_confidence_threshold * 0.8  # ลด 20%
                                print(f"  🎯 BUY Multiclass: weak_buy={prediction_proba[3]:.3f}, strong_buy={prediction_proba[4]:.3f}, total={prob_buy:.3f}, threshold={buy_threshold:.3f}")
                            else:
                                prob_win = prediction_proba[1] # Binary: ความน่าจะเป็นของ Target=1 (TP Hit)
                                buy_threshold = model_confidence_threshold

                            # ตรวจสอบเกณฑ์ความน่าจะเป็น + High-Quality Entry Filter
                            basic_model_decision = (prob_win > buy_threshold)
                            high_quality_check = is_high_quality_entry(df, i, symbol)
                            model_decision_buy = basic_model_decision and high_quality_check

                            # Debug info สำหรับ high-quality filter
                            # if basic_model_decision and not high_quality_check:
                            #     print(f"  🚫 Filtered out low-quality signal at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (prob={prob_win:.3f})")
                            # elif model_decision_buy:
                            #     print(f"  ✅ High-quality BUY signal at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (prob={prob_win:.3f})")

                            # ==============================================
                            # ส่วนเพิ่มเติม: บันทึกข้อมูลการทำนายเพื่อตรวจสอบ
                            # ==============================================
                            # บันทึกข้อมูลแท่งที่ใช้คำนวณ (แท่ง i-1) และแท่งปัจจุบัน (แท่ง i)
                            # print(f"--- Decision at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (Open of Bar i) ---")
                            # print(f"Using data from bar i-1: {df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]} (Closed)")
                            # print(f"  Close[i-1]: {prev_close}, Open[i-1]: {prev_open}, High[i-1]: {prev_high}, Low[i-1]: {prev_low}")
                            # print(f"  Indicators [i-1]: EMA50={prev_ema50:.2f}, RSI14={prev_rsi14:.2f}, MACD_signal={prev_macd_signal}, STO_cross={prev_sto_cross}")
                            # print(f"  Features for Model (from i-1 and earlier): {current_features_data.iloc[0].to_dict()}")
                            # print(f"Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_buy else 'SKIP'}")
                            # print(f"Current Bar (i) Open: {df['Open'].iloc[i]}") # Show the open price where entry would occur
                            # print("-" * 20)

                            if i % 100 == 0:
                                log_entry = {
                                    'index_current_bar': i,
                                    'time_current_bar': str(df['Date'].iloc[i] + ' ' + df['Time'].iloc[i]),
                                    'index_features_bar': i-1,
                                    'time_features_bar': str(df['Date'].iloc[i-1] + ' ' + df['Time'].iloc[i-1]),
                                    'prob_win': float(prob_win),
                                    'decision': bool(model_decision_buy),
                                    'features_from_bar_i_minus_1': {k: safe_json_serialize(v) for k, v in current_features_data.iloc[0].to_dict().items()}
                                }
                                with open(f"{test_folder}/{timeframe}_{symbol}_prediction_log.json", "a") as log:
                                    log.write(json.dumps(log_entry) + "\n")

                            # (Optional) Log การตัดสินใจของ Model
                            log_file.write(f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] BUY Signal Detected based on data up to [{df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]}]. Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_buy else 'SKIP'}\n")

                        except Exception as e:
                            print(f"⚠️ เกิดข้อผิดพลาด ขณะใช้ Model ทำนาย (Buy) ที่แท่ง {df['Date'].iloc[i]} {df['Time'].iloc[i]}: {str(e)}. ข้ามการใช้ Model.")
                            import traceback
                            traceback.print_exc()
                            model_decision_buy = True # กลับไปใช้กฎเดิมถ้า Model มีปัญหา
                            prob_win = -2 # Indicate error
                    else:
                        # ไม่ใช้ ML model - ใช้เงื่อนไขทางเทคนิคเท่านั้น
                        # print(f"  🎯 BUY Technical Only: Decision=ENTER (no ML model)")
                        log_file.write(f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] BUY Signal Detected (Technical Only) based on data up to [{df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]}]. No ML Model. Decision: ENTER\n")

                    # --- ถ้าเงื่อนไขทางเทคนิคและ Model อนุญาต (หรือถ้าไม่ใช้ Model) ---
                    if model_decision_buy:
                        
                        entry_price_buy = df["Open"].iloc[i] + (symbol_spread * symbol_points)
                        entry_time_buy = pd.to_datetime(df["Date"].iloc[i] + ' ' + df["Time"].iloc[i])
                        trade_type_buy = "Buy"

                        # คำนวณ SL/TP
                        sl_atr = entry_price_buy - stop_loss_atr_multiplier * prev_atr # ใช้ ATR จากแท่ง i-1
                        # SL คำนวณจาก Low ของ 3 แท่งก่อนหน้าแท่งปัจจุบัน i (คือ Low[i-3], Low[i-2], Low[i-1])
                        # นี้คือการใช้ข้อมูลที่ทราบแล้ว ณ เวลาเข้าเทรด Open[i]
                        sl_prev_bars = min(df['Low'].iloc[i-nBars_SL:i].min(), entry_price_buy - 2*symbol_points)

                        # แสดงข้อมูลแท่งที่ใช้คำนวณ SL_prev_bars
                        # print(f"Calculating SL_prev_bars for entry at {entry_time_buy} @ {entry_price_buy:.5f}")
                        # print(f"Using Low from bars: {df['Time'].iloc[i-3]}, {df['Time'].iloc[i-2]}, {df['Time'].iloc[i-1]}")
                        # print(f"Low values: {df['Low'].iloc[i-3]:.5f}, {df['Low'].iloc[i-2]:.5f}, {df['Low'].iloc[i-1]:.5f}")
                        # print(f"Min Low of these bars: {df['Low'].iloc[i-3:i].min():.5f}")
                        # print(f"Calculated sl_prev_bars: {sl_prev_bars:.5f}")
                        # print("-" * 20)

                        if 'Support' in df.columns and not pd.isna(df['Support'].iloc[i-1]): # ใช้ Support จากแท่ง i-1
                            sl_support = df['Support'].iloc[i-1]
                            sl_price_buy = max(sl_atr, sl_prev_bars, sl_support)
                        else:
                            sl_price_buy = max(sl_atr, sl_prev_bars)

                        sl_price_buy = floor_price(sl_price_buy, symbol_points)

                        tp_price_buy = entry_price_buy + (entry_price_buy - sl_price_buy) * take_profit_stop_loss_ratio

                        tp_price_buy = ceiling_price(tp_price_buy, symbol_points)

                        # if 'Resistance' in df.columns and not pd.isna(df['Resistance'].iloc[i-1]): # ใช้ Resistance จากแท่ง i-1
                        #     tp_resistance = df['Resistance'].iloc[i-1]
                        #     tp_price_buy = min(tp_price_buy, tp_resistance)

                        if tp_price_buy <= entry_price_buy:
                            tp_price_buy = entry_price_buy + 2*symbol_points # ตั้ง TP ขั้นต่ำเพื่อหลีกเลี่ยง TP <= Entry

                        in_trade_buy = True

                        # บันทึกข้อมูล ณ เวลาที่เข้าซื้อ (Features ที่ใช้ทำนาย)
                        # บันทึก features ที่ได้มาจาก check look ahead bias ซึ่งใช้ข้อมูลจาก i-1
                        atr_entry_buy = df['ATR'].iloc[i-1]
                        bb_width_entry_buy = df['BB_width'].iloc[i-1]
                        rsi14_entry_buy = df['RSI14'].iloc[i-1]
                        volatility_entry_buy = df['Volume_MA20'].iloc[i-1]
                        volume_spike_entry_buy = df['Volume_Spike'].iloc[i-1]

                        # คุณอาจต้องการเก็บค่า prob_win ที่ใช้ตัดสินใจเข้า trade นี้ด้วย
                        prob_win_at_entry_buy = prob_win

                        log_message = f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] Open Order : trade_{trade_type_buy} : Entry_time_{entry_time_buy} Entry_price_{entry_price_buy:0.5f} Entry_sl_{sl_price_buy:0.5f} Entry_tp_{tp_price_buy:0.5f} Prob_Win_{prob_win_at_entry_buy:.4f}\n"
                        log_file.write(log_message)

            # --- เงื่อนไขการออกจากตำแหน่งซื้อ (Buy) ---
            # (โค้ดส่วนนี้เหมือนเดิม ตรวจสอบ SL/TP/Technical Exit)
            if in_trade_buy and trade_type_buy == "Buy":
                exit_condition = None
                exit_price = None

                # ระดับที่ต้องไปถึงก่อนเลื่อน SL
                high_prev = df["High"].iloc[i-1] if i > 0 else None
                trail_trigger = entry_price_buy + 0.5 * (entry_price_buy - sl_price_buy)

                if high_prev is not None and high_prev >= trail_trigger:
                    if sl_price_buy < entry_price_buy:  # เพื่อไม่ให้ปรับซ้ำซ้อน
                        sl_price_buy = entry_price_buy
                        # สามารถ log ได้ว่า "SL moved to Entry"

                if df["Low"].iloc[i] <= sl_price_buy:
                    exit_price = sl_price_buy
                    exit_condition = "SL Hit"
                elif df["High"].iloc[i] > tp_price_buy:
                    exit_price = tp_price_buy
                    exit_condition = "TP Hit"
                # เงื่อนไขอื่นๆ (Technical Exit)
                elif (df["RSI14"].iloc[i] < rsi_level_out if "RSI14" in df.columns else False): # df["Close"].iloc[i] < df["EMA50"].iloc[i] if "EMA50" in df.columns else False or (df["Close"].iloc[i] < entry_price_buy * 0.998)
                    exit_price = df["Close"].iloc[i]
                    exit_condition = "Technical Exit"

                # ถ้ามีเงื่อนไขออกเกิดขึ้น
                if exit_condition:
                    # ... (ส่วนบันทึก Trade และอัปเดต Stats เหมือนเดิม) ...
                    exit_time = pd.to_datetime(df["Date"].iloc[i] + ' ' + df["Time"].iloc[i])
                    profit = (exit_price - entry_price_buy) / symbol_points

                    risk_amount = entry_price_buy - sl_price_buy
                    reward_amount = tp_price_buy - entry_price_buy # ใช้ tp_price_buy ที่คำนวณหรือเลือกไว้

                    entry_datetime = pd.to_datetime(entry_time_buy)
                    entry_hour = entry_datetime.hour
                    entry_day = entry_datetime.dayofweek

                    log_message = f"time_{df['Date'].iloc[i]} {df['Time'].iloc[i]} op_{df['Open'].iloc[i]} cl_{df['Close'].iloc[i]} : trade_{trade_type_buy} : Entry_time_{entry_time_buy} Entry_price_{entry_price_buy:0.5f} Entry_sl_{sl_price_buy:0.5f} Entry_tp_{tp_price_buy:0.5f} : Exit_time_{exit_time} Exit_price_{exit_price:0.5f} : Condition_{exit_condition} Profit_{profit:0.0f}\n"
                    log_file.write(log_message)

                    trades.append([
                        entry_time_buy, entry_price_buy,
                        exit_time, exit_price,
                        trade_type_buy, profit,
                        entry_day, entry_hour,
                        sl_price_buy, tp_price_buy,
                        exit_condition,
                        risk_amount, reward_amount,
                        atr_entry_buy,
                        bb_width_entry_buy,
                        rsi14_entry_buy,
                        (entry_price_buy - sl_price_buy) / entry_price_buy if risk_amount > 0 else 0, # % Risk (ป้องกันหารด้วยศูนย์)
                        (tp_price_buy - entry_price_buy) / entry_price_buy if reward_amount > 0 else 0, # % Reward (ป้องกันหารด้วยศูนย์)
                        volatility_entry_buy, volume_spike_entry_buy
                    ])

                    # อัปเดตสถิติ
                    stats['buy']['total'] += 1
                    stats['buy']['profit_sum'] += profit
                    stats['day_stats'][entry_day]['total'] += 1
                    stats['hour_stats'][entry_hour]['total'] += 1
                    if profit > 0:
                        stats['day_stats'][entry_day]['win'] += 1
                        stats['hour_stats'][entry_hour]['win'] += 1
                    elif profit < 0:
                        stats['day_stats'][entry_day]['loss'] += 1
                        stats['hour_stats'][entry_hour]['loss'] += 1

                    in_trade_buy = False # ออกจากตำแหน่ง

            # --- เงื่อนไขการเข้าขาย (Sell) ---
            if not in_trade_sell:

                if entry_condition_func is not None:
                    # ใช้ 4 scenarios สำหรับ Multi-Model Architecture
                    # ตรวจสอบ market scenario
                    close = prev_dict.get('close', 0)
                    ema200 = prev_dict.get('ema200', close)

                    scenario_used = "trend_following" if close < ema200 else "counter_trend"
                    if close < ema200:
                        # Trend Following (ราคาใต้ EMA200)
                        if 'trend_following' in entry_conditions:
                            tech_signal_sell = entry_conditions['trend_following']['sell'](prev_dict)
                            # print(f"  🔍 SELL Entry ({scenario_used}): {tech_signal_sell} | Close={close:.5f} < EMA200={ema200:.5f}")
                        else:
                            tech_signal_sell = entry_condition_func['sell'](prev_dict)
                    else:
                        # Counter Trend (ราคาเหนือ EMA200)
                        if 'counter_trend' in entry_conditions:
                            tech_signal_sell = entry_conditions['counter_trend']['sell'](prev_dict)
                            # print(f"  🔍 SELL Entry ({scenario_used}): {tech_signal_sell} | Close={close:.5f} > EMA200={ema200:.5f}")
                        else:
                            tech_signal_sell = entry_condition_func['sell'](prev_dict)
                else:
                    # Debug แต่ละเงื่อนไข
                    cond1 = prev_dict['close'] < prev_dict['open']
                    cond2 = prev_dict['rsi14'] < (100 - rsi_level)
                    cond3 = prev_dict['macd_signal'] == -1.0
                    cond4 = prev_dict['volume'] > prev_dict['volume_ma20']
                    cond5 = prev_dict['pullback_sell'] > input_pull_back
                    cond6 = prev_dict['ratio_sell'] > take_profit_stop_loss_ratio

                    tech_signal_sell = cond1 and cond2 and cond3 and cond4 and cond5 and cond6

                # Debug time condition
                # print(f"  🔍 Time Condition: {time_condition}")
                # print(f"  🔍 Final SELL Check: tech_signal={tech_signal_sell} AND time_condition={time_condition} = {tech_signal_sell and time_condition}")

                if tech_signal_sell and time_condition:
                    #print(f"  🎯 SELL Signal Detected! Scenario: {scenario_used}, Time: {df['Date'].iloc[i]} {df['Time'].iloc[i]}")

                    # เริ่มต้นด้วยการอนุญาตให้เทรดตามเงื่อนไขทางเทคนิค
                    model_decision_sell = True
                    prob_win = -1

                    # Debug: แสดงว่าเงื่อนไขทางเทคนิคสำหรับ Sell เป็นจริง
                    #print(f"  🎯 SELL Technical Signal: RSI={prev_dict.get('rsi14', 'N/A'):.1f}, MACD={prev_dict.get('macd_signal', 'N/A')}, PullBack={prev_dict.get('pullback_sell', 'N/A'):.3f}")

                    if Model_Decision and use_model_for_decision:
                        try:
                            if is_multi_model:
                                # Multi-Model Architecture: เลือกโมเดลตามสถานการณ์ตลาด
                                close = prev_dict.get('close', 0)
                                ema200 = prev_dict.get('ema200', close)

                                # เลือก scenario ตามสถานการณ์ตลาดสำหรับ SELL
                                if close < ema200:
                                    scenario_name = "trend_following"  # Downtrend
                                else:
                                    scenario_name = "counter_trend"    # Uptrend/Sideways

                                if scenario_name not in trained_model:
                                    print(f"⚠️ ไม่พบโมเดล {scenario_name} - ข้าม SELL signal")
                                    model_decision_sell = False
                                    prob_win = 0
                                    continue

                                selected_model = trained_model[scenario_name]['model']
                                selected_scaler = trained_model[scenario_name]['scaler']
                                selected_features = trained_model[scenario_name]['features']

                                # print(f"  🎯 SELL: ใช้โมเดล {scenario_name} (Close={close:.5f}, EMA200={ema200:.5f})")

                                # ใช้ features และ scaler ของโมเดลที่เลือก
                                current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                                    df, i, selected_features, selected_scaler, nan_count, suspect_feature_count, suspect_features)

                                # ทำนายด้วยโมเดลที่เลือก
                                # ตรวจสอบจำนวน features ก่อนทำนาย
                                expected_features = selected_model.n_features_in_ if hasattr(selected_model, 'n_features_in_') else len(selected_features)
                                actual_features = scaled_features.shape[1]

                                if expected_features != actual_features:
                                    if SHOW_FEATURE_DEBUG:
                                        print(f"⚠️ SELL Feature count mismatch: Expected {expected_features}, Got {actual_features}")

                                    if actual_features < expected_features:
                                        # เพิ่ม features ที่ขาดหายไป (fill ด้วย 0)
                                        missing_count = expected_features - actual_features
                                        padding = np.zeros((scaled_features.shape[0], missing_count))
                                        scaled_features = np.hstack([scaled_features, padding])
                                        if SHOW_FEATURE_DEBUG:
                                            print(f"✅ SELL เพิ่ม {missing_count} features (fill ด้วย 0)")
                                    else:
                                        # ตัด features ที่เกิน
                                        scaled_features = scaled_features[:, :expected_features]
                                        print(f"✅ SELL ตัด features เหลือ {expected_features} features")

                                prediction_proba = selected_model.predict_proba(scaled_features)[0]

                            else:
                                # Single-Model Architecture: ใช้โมเดลเดียว
                                current_features_data, scaled_features, nan_count, suspect_feature_count, suspect_features = check_look_ahead_bias(
                                    df, i, model_features, scaler, nan_count, suspect_feature_count, suspect_features)

                                # ตรวจสอบจำนวน features ก่อนทำนาย
                                expected_features = trained_model.n_features_in_ if hasattr(trained_model, 'n_features_in_') else len(model_features)
                                actual_features = scaled_features.shape[1]

                                if expected_features != actual_features:
                                    print(f"⚠️ SELL Feature count mismatch: Expected {expected_features}, Got {actual_features}")

                                    if actual_features < expected_features:
                                        # เพิ่ม features ที่ขาดหายไป (fill ด้วย 0)
                                        missing_count = expected_features - actual_features
                                        padding = np.zeros((scaled_features.shape[0], missing_count))
                                        scaled_features = np.hstack([scaled_features, padding])
                                        # print(f"✅ SELL เพิ่ม {missing_count} features (fill ด้วย 0)")
                                    else:
                                        # ตัด features ที่เกิน
                                        scaled_features = scaled_features[:, :expected_features]
                                        # print(f"✅ SELL ตัด features เหลือ {expected_features} features")

                                prediction_proba = trained_model.predict_proba(scaled_features)[0]

                            # สำหรับ multiclass: รวม probability ของ sell classes (0, 1)
                            if len(prediction_proba) >= 5:
                                prob_sell = prediction_proba[0] + prediction_proba[1]  # strong_sell + weak_sell
                                prob_win = prob_sell
                                # ใช้ threshold ต่ำกว่าสำหรับ sell signals เพื่อให้ได้ signals มากขึ้น
                                sell_threshold = model_confidence_threshold * 0.7  # ลด 30% (เพราะ sell ยากกว่า)
                                print(f"  🎯 SELL Multiclass: strong_sell={prediction_proba[0]:.3f}, weak_sell={prediction_proba[1]:.3f}, total={prob_sell:.3f}, threshold={sell_threshold:.3f}")
                            else:
                                prob_win = prediction_proba[1] # Binary: ความน่าจะเป็นของ Target=1 (TP Hit)
                                sell_threshold = model_confidence_threshold

                            # ตรวจสอบเกณฑ์ความน่าจะเป็น + High-Quality Entry Filter
                            basic_model_decision = (prob_win > sell_threshold)
                            high_quality_check = is_high_quality_entry(df, i, symbol)
                            model_decision_sell = basic_model_decision and high_quality_check

                            # # Debug info สำหรับ high-quality filter
                            # if basic_model_decision and not high_quality_check:
                            #     print(f"  🚫 Filtered out low-quality signal at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (prob={prob_win:.3f})")
                            # elif model_decision_sell:
                            #     print(f"  ✅ High-quality SELL signal at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (prob={prob_win:.3f})")
                            
                            # print(f"--- Decision at {df['Date'].iloc[i]} {df['Time'].iloc[i]} (Open of Bar i) ---")
                            # print(f"Using data from bar i-1: {df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]} (Closed)")
                            # print(f"  Close[i-1]: {prev_close}, Open[i-1]: {prev_open}, High[i-1]: {prev_high}, Low[i-1]: {prev_low}")
                            # print(f"  Indicators [i-1]: EMA50={prev_ema50:.2f}, RSI14={prev_rsi14:.2f}, MACD_signal={prev_macd_signal}, STO_cross={prev_sto_cross}")
                            # print(f"  Features for Model (from i-1 and earlier): {current_features_data.iloc[0].to_dict()}")
                            # print(f"Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_sell else 'SKIP'}")
                            # print(f"Current Bar (i) Open: {df['Open'].iloc[i]}")
                            # print("-" * 20)

                            if i % 100 == 0:
                                log_entry = {
                                    'index_current_bar': i,
                                    'time_current_bar': str(df['Date'].iloc[i] + ' ' + df['Time'].iloc[i]),
                                    'index_features_bar': i-1,
                                    'time_features_bar': str(df['Date'].iloc[i-1] + ' ' + df['Time'].iloc[i-1]),
                                    'prob_win': float(prob_win),
                                    'decision': bool(model_decision_sell),
                                    'features_from_bar_i_minus_1': {k: safe_json_serialize(v) for k, v in current_features_data.iloc[0].to_dict().items()}
                                }
                                with open(f"{test_folder}/{timeframe}_{symbol}_prediction_log.json", "a") as log:
                                    log.write(json.dumps(log_entry) + "\n")

                            log_file.write(f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] sell Signal Detected based on data up to [{df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]}]. Model Probability (Win): {prob_win:.4f}. Threshold: {model_confidence_threshold:.3f}. Decision: {'ENTER' if model_decision_sell else 'SKIP'}\n")

                        except Exception as e:
                            print(f"⚠️ เกิดข้อผิดพลาด ขณะใช้ Model ทำนาย (sell) ที่แท่ง {df['Date'].iloc[i]} {df['Time'].iloc[i]}: {str(e)}. ข้ามการใช้ Model.")
                            import traceback
                            traceback.print_exc()
                            model_decision_sell = True
                            prob_win = -2
                    else:
                        # ไม่ใช้ ML model - ใช้เงื่อนไขทางเทคนิคเท่านั้น
                        # print(f"  🎯 SELL Technical Only: Decision=ENTER (no ML model)")
                        log_file.write(f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] SELL Signal Detected (Technical Only) based on data up to [{df['Date'].iloc[i-1]} {df['Time'].iloc[i-1]}]. No ML Model. Decision: ENTER\n")

                    if model_decision_sell:
                        entry_price_sell = df["Open"].iloc[i]
                        entry_time_sell = pd.to_datetime(df["Date"].iloc[i] + ' ' + df["Time"].iloc[i])
                        trade_type_sell = "Sell"

                        sl_atr = entry_price_sell + stop_loss_atr_multiplier * prev_atr
                        sl_prev_bars = max(df['High'].iloc[i-nBars_SL:i].max(), entry_price_sell + 2*symbol_points)

                        # print(f"Calculating SL_prev_bars for entry at {entry_time_sell} @ {entry_price_sell:.5f}")
                        # print(f"Using High from bars: {df['Time'].iloc[i-3]}, {df['Time'].iloc[i-2]}, {df['Time'].iloc[i-1]}")
                        # print(f"High values: {df['High'].iloc[i-3]:.5f}, {df['High'].iloc[i-2]:.5f}, {df['High'].iloc[i-1]:.5f}")
                        # print(f"Max Low of these bars: {df['High'].iloc[i-3:i].max():.5f}")
                        # print(f"Calculated sl_prev_bars: {sl_prev_bars:.5f}")
                        # print("-" * 20)

                        if 'Resistance' in df.columns and not pd.isna(df['Resistance'].iloc[i-1]):
                            sl_resistance = df['Resistance'].iloc[i-1]
                            sl_price_sell = min(sl_atr, sl_prev_bars, sl_resistance) + (symbol_spread * symbol_points)
                        else:
                            sl_price_sell = min(sl_atr, sl_prev_bars) + (symbol_spread * symbol_points)

                        sl_price_sell = ceiling_price(sl_price_sell, symbol_points)

                        tp_price_sell = entry_price_sell - (sl_price_sell - entry_price_sell) * take_profit_stop_loss_ratio

                        tp_price_sell = floor_price(tp_price_sell, symbol_points)

                        # if 'Support' in df.columns and not pd.isna(df['Support'].iloc[i-1]):
                        #     tp_support = df['Support'].iloc[i-1]
                        #     tp_price_sell = max(tp_price_sell, tp_support)

                        if tp_price_sell >= entry_price_sell:
                            tp_price_sell = entry_price_sell - 2*symbol_points

                        in_trade_sell = True

                        atr_entry_sell = df['ATR'].iloc[i-1]
                        bb_width_entry_sell = df['BB_width'].iloc[i-1]
                        rsi14_entry_sell = df['RSI14'].iloc[i-1]
                        volatility_entry_sell = df['Volume_MA20'].iloc[i-1]
                        volume_spike_entry_sell = df['Volume_Spike'].iloc[i-1]

                        prob_win_at_entry_sell = prob_win

                        log_message = f"[{df['Date'].iloc[i]} {df['Time'].iloc[i]}] Open Order : trade_{trade_type_sell} : Entry_time_{entry_time_sell} Entry_price_{entry_price_sell:0.5f} Entry_sl_{sl_price_sell:0.5f} Entry_tp_{tp_price_sell:0.5f} Prob_Win_{prob_win_at_entry_sell:.4f}\n"
                        log_file.write(log_message)

            if in_trade_sell and trade_type_sell == "Sell":
                exit_condition = None
                exit_price = None

                # ระดับที่ต้องไปถึงก่อนเลื่อน SL
                low_prev = df["Low"].iloc[i-1] if i > 0 else None
                trail_trigger = entry_price_sell - 0.5 * (sl_price_sell - entry_price_sell)

                if low_prev is not None and low_prev + (symbol_spread * symbol_points) <= trail_trigger:
                    if sl_price_sell > entry_price_sell:  # เพื่อไม่ให้ปรับซ้ำซ้อน
                        sl_price_sell = entry_price_sell
                        # สามารถ log ได้ว่า "SL moved to Entry"

                if df["High"].iloc[i] + (symbol_spread * symbol_points) >= sl_price_sell:
                    exit_price = sl_price_sell
                    exit_condition = "SL Hit"
                elif df["Low"].iloc[i] + (symbol_spread * symbol_points) < tp_price_sell:
                    exit_price = tp_price_sell
                    exit_condition = "TP Hit"
                elif (df["RSI14"].iloc[i] > (100-rsi_level_out) if "RSI14" in df.columns else False): # df["Close"].iloc[i] > df["EMA50"].iloc[i] if "EMA50" in df.columns else False or (df["Close"].iloc[i] > entry_price_sell * 1.002)
                    exit_price = df["Close"].iloc[i] + (symbol_spread * symbol_points)
                    exit_condition = "Technical Exit"

                if exit_condition:
                    exit_time = pd.to_datetime(df["Date"].iloc[i] + ' ' + df["Time"].iloc[i])
                    profit = (entry_price_sell - exit_price) / symbol_points

                    risk_amount = sl_price_sell - entry_price_sell
                    reward_amount = entry_price_sell - tp_price_sell

                    entry_datetime = pd.to_datetime(entry_time_sell)
                    entry_hour = entry_datetime.hour
                    entry_day = entry_datetime.dayofweek

                    log_message = f"time_{df['Date'].iloc[i]} {df['Time'].iloc[i]} op_{df['Open'].iloc[i]} cl_{df['Close'].iloc[i]} : trade_{trade_type_sell} : Entry_time_{entry_time_sell} Entry_price_{entry_price_sell:0.5f} Entry_sl_{sl_price_sell:0.5f} Entry_tp_{tp_price_sell:0.5f} : Exit_time_{exit_time} Exit_price_{exit_price:0.5f} : Condition_{exit_condition} Profit_{profit:0.0f}\n"
                    log_file.write(log_message)

                    trades.append([
                        entry_time_sell, entry_price_sell,
                        exit_time, exit_price,
                        trade_type_sell, profit, 
                        entry_day, entry_hour,
                        sl_price_sell, tp_price_sell,
                        exit_condition,
                        risk_amount, reward_amount,
                        atr_entry_sell,
                        bb_width_entry_sell,
                        rsi14_entry_sell,
                        (sl_price_sell - entry_price_sell) / entry_price_sell if risk_amount > 0 else 0,
                        (entry_price_sell - tp_price_sell) / entry_price_sell if reward_amount > 0 else 0,
                        volatility_entry_sell, volume_spike_entry_sell
                    ])

                    stats['sell']['total'] += 1
                    stats['sell']['profit_sum'] += profit
                    stats['day_stats'][entry_day]['total'] += 1
                    stats['hour_stats'][entry_hour]['total'] += 1
                    if profit > 0:
                        stats['day_stats'][entry_day]['win'] += 1
                        stats['hour_stats'][entry_hour]['win'] += 1
                    elif profit < 0:
                        stats['day_stats'][entry_day]['loss'] += 1
                        stats['hour_stats'][entry_hour]['loss'] += 1

                    in_trade_sell = False

    # ... (ส่วนท้ายฟังก์ชัน create_trade_cycles เหมือนเดิม) ...
    TRADE_COLUMNS = [
        "Entry Time", "Entry Price", 
        "Exit Time", "Exit Price",
        "Trade Type", "Profit",
        "Entry Day", "Entry Hour",
        "SL Price", "TP Price", 
        "Exit Condition",
        "Risk", "Reward",
        "ATR at Entry", 
        "BB Width at Entry", 
        "RSI14 at Entry",
        "Pct_Risk", 
        "Pct_Reward",
        "Volume MA20 at Entry", "Volume Spike at Entry"
        # เพิ่มคอลัมน์ตาม Features ที่คุณบันทึกไว้
    ]

    trade_df = pd.DataFrame(trades, columns=TRADE_COLUMNS)
    # print(trade_df[trade_df["Profit"] > 0.0][["Trade Type","Profit"]])

    # ==============================================
    # ส่วนเพิ่มเติม: สรุปการตรวจสอบ Look-Ahead Bias
    # ==============================================
    print("\n🔍 สรุปการตรวจสอบ Look-Ahead Bias")
    print(f"- จำนวนการทำนายทั้งหมด: {len(df) - start_index}")
    print(f"- จำนวนครั้งที่พบ NaN ใน Features: {nan_count}")
    print(f"- จำนวนครั้งที่พบ Features อาจมีปัญหา: {suspect_feature_count}")
    
    if suspect_feature_count > 0:
        print("\n⚠️ คำเตือน ⚠️: พบ Features ที่อาจมี Look-Ahead Bias")
        print("โปรดตรวจสอบ Features ต่อไปนี้:")
        for feat in suspect_features:
            print(f"- {feat}")
    else:
        print("✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)")

    TRADE_COLUMNS = [
        "Entry Time", "Entry Price", "Exit Time", "Exit Price",
        "Profit", "Trade Type", 
        # ... คอลัมน์เดิม ...
    ]

    return trade_df, stats

# ==============================================

def detect_market_scenario(row):
    """
    ตรวจจับสถานการณ์ตลาดสำหรับการเลือกโมเดลที่เหมาะสม

    Args:
        row: pandas Series ที่มีข้อมูล Close, High, Low, EMA200

    Returns:
        str: ชื่อ scenario ที่ตรงกับสถานการณ์ปัจจุบัน
    """
    # print(f"\n🏗️ เปิดใช้งาน detect market scenario") if Steps_to_do else None

    close = row['Close']
    high = row['High']
    low = row['Low']
    ema200 = row['EMA200']

    # ตรวจสอบสถานการณ์ตลาด
    if close > ema200 and low > ema200:
        # ราคาอยู่เหนือ EMA200 อย่างชัดเจน = Uptrend
        return 'uptrend'
    elif close < ema200 and high < ema200:
        # ราคาอยู่ใต้ EMA200 อย่างชัดเจน = Downtrend
        return 'downtrend'
    else:
        # ราคาอยู่รอบๆ EMA200 = Sideways/Consolidation
        return 'sideways'

def add_market_scenario_column(df):
    """
    เพิ่มคอลัมน์ market_scenario ใน DataFrame

    Args:
        df: DataFrame ที่มีข้อมูลราคาและ EMA200

    Returns:
        DataFrame: DataFrame ที่เพิ่มคอลัมน์ market_scenario แล้ว
    """
    print(f"\n🏗️ เปิดใช้งาน add market scenario column") if Steps_to_do else None

    df = df.copy()
    df['market_scenario'] = df.apply(detect_market_scenario, axis=1)

    # แสดงสถิติการกระจาย
    scenario_counts = df['market_scenario'].value_counts()
    print(f"\n📈 Market Scenario Distribution:")
    for scenario, count in scenario_counts.items():
        percentage = count / len(df) * 100
        print(f"  {scenario}: {count} ({percentage:.1f}%)")

    return df

def create_trade_cycles_with_multi_model(
        df, scenario_models=None, model_features=None,
        rsi_level=input_rsi_level_in, rsi_level_out=input_rsi_level_out,
        stop_loss_atr_multiplier=input_stop_loss_atr, take_profit_stop_loss_ratio=input_take_profit,
        symbol=None, timeframe=None, identifier=None,
        nBars_SL=None, model_confidence_threshold=None,
        entry_condition_func=None, entry_condition_name=None
        ):
    """
    สร้างรายการซื้อขายด้วย Multi-Model Architecture
    ใช้โมเดลที่เหมาะสมตามสถานการณ์ตลาดในแต่ละจุด

    Args:
        df: DataFrame ที่มีข้อมูลราคาและ indicators
        scenario_models: dict ของโมเดลทั้ง 4 scenarios
        model_features: รายชื่อ features ที่โมเดลใช้
        ... (parameters อื่นๆ เหมือน create trade cycles with model)

    Returns:
        tuple: (trade_df, stats)
    """
    print(f"\n🏗️ เปิดใช้งาน create trade cycles with multi model") if Steps_to_do else None

    if scenario_models is None or len(scenario_models) == 0:
        print("⚠️ ไม่มี scenario models ให้ใช้งาน")
        return create_trade_cycles_with_model(
            df, None, None, None, rsi_level, rsi_level_out,
            stop_loss_atr_multiplier, take_profit_stop_loss_ratio,
            symbol, timeframe, identifier, nBars_SL, model_confidence_threshold,
            entry_condition_func, entry_condition_name
        )

    # เพิ่มคอลัมน์ market_scenario ถ้ายังไม่มี
    if 'market_scenario' not in df.columns:
        df = add_market_scenario_column(df.copy())

    # โหลดโมเดลเป็น format ที่ create_trade_cycles_with_model ต้องการ
    # รวม model, scaler, features ในแต่ละ scenario
    loaded_models = {}
    for scenario_name, model_info in scenario_models.items():
        loaded_models[scenario_name] = {
            'model': model_info['model'],
            'scaler': model_info['scaler'],  # เพิ่ม scaler
            'features': model_info['features']
        }

    print(f"📊 ใช้ Multi-Model: {list(loaded_models.keys())}")

    # ใช้ Multi-Model Architecture จริง
    print("✅ ใช้ Multi-Model Architecture พร้อม 2 scenarios")

    # เรียกใช้ create_trade_cycles_with_model แต่ส่ง scenario_models เข้าไป
    # เพื่อให้ฟังก์ชันรู้ว่าต้องใช้ Multi-Model
    return create_trade_cycles_with_model(
        df,
        trained_model=loaded_models,  # ส่ง dict ของ models พร้อม scaler
        scaler=None,  # ไม่ใช้ scaler เดียว จะใช้ scaler ของแต่ละ model
        model_features=model_features,
        rsi_level=rsi_level,
        rsi_level_out=rsi_level_out,
        stop_loss_atr_multiplier=stop_loss_atr_multiplier,
        take_profit_stop_loss_ratio=take_profit_stop_loss_ratio,
        symbol=symbol,
        timeframe=timeframe,
        nBars_SL=nBars_SL,
        model_confidence_threshold=model_confidence_threshold,
        entry_condition_func=entry_condition_func,
        entry_condition_name=entry_condition_name
    )

# ==============================================

def check_stationarity(series, name='Close'):
    print(f"\n🏗️ เปิดใช้งาน check stationarity") if Steps_to_do else None

    result = adfuller(series.dropna())
    print(f'📊 ผลการทดสอบ Stationarity สำหรับ {name}:')
    print(f'ADF Statistic: {result[0]:.4f}')
    print(f'p-value: {result[1]:.4f}')
    print('Critical Values:')
    for key, value in result[4].items():
        print(f'   {key}: {value:.4f}')
    return result[1] < 0.05  # คืนค่า True ถ้า stationary

def check_data_quality(df, file_name, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน check data quality") if Steps_to_do else None

    print(f"{'='*50}")
    print(f"Data Quality Check for {file_name}")
    print("="*50)
    
    # missing = df.isnull().sum()
    # print("\n[1] Missing Values:")
    # print(missing[missing > 0].to_string())
    
    # print("\n[2] Data Types:")
    # print(df.dtypes.to_string())
    
    # print("\n[3] Descriptive Stats:")
    # print(df.describe(include='all').to_string())
    
    print(f"\n[4] Duplicate Rows: {df.duplicated().sum()}")

    fig, ax = plt.subplots(figsize=(10, 5))
    ax.hist(df['Close'], bins=50)
    ax.set_title('Price Distribution')
    plt.savefig(os.path.join(results_dir, f"{timeframe}_{symbol}_price_dist.png"))
    plt.close(fig)

def calculate_stats(subset):
    # print(f"\n🏗️ เปิดใช้งาน calculate stats") if Steps_to_do else None

    wins = subset[subset['Profit'] > 0]
    losses = subset[subset['Profit'] < 0]
    # total = len(subset)
    num_wins = len(wins)
    num_losses = len(losses)
    total = num_wins + num_losses

    win_rate = (num_wins / total) * 100 if total > 0 else 0
    avg_win = wins['Profit'].mean() if num_wins > 0 else 0
    avg_loss = abs(losses['Profit'].mean()) if num_losses > 0 else 0
    expectancy = (avg_win * (win_rate / 100)) - (avg_loss * (1 - (win_rate / 100)))
    return {"win_rate": round(win_rate, 2), "expectancy": round(expectancy, 3)}

def analyze_trade_performance(trades_df):
    print(f"\n🏗️ เปิดใช้งาน analyze trade performance") if Steps_to_do else None

    """วิเคราะห์ผลการเทรดเพื่อหา Win Rate และ Expectancy แยกตามประเภท"""
    analysis = {}

    # ตรวจสอบว่า DataFrame ว่างหรือไม่มีคอลัมน์ที่จำเป็น
    if trades_df.empty or 'Trade Type' not in trades_df.columns:
        print("⚠️ ไม่มีข้อมูลการเทรดหรือไม่มีคอลัมน์ 'Trade Type'")
        # สร้างสถิติเริ่มต้นสำหรับกรณีที่ไม่มีข้อมูล
        empty_stats = {
            'total_trades': 0,
            'win_rate': 0.0,
            'total_profit': 0.0,
            'avg_profit_per_trade': 0.0,
            'expectancy': 0.0,
            'max_profit': 0.0,
            'max_loss': 0.0,
            'avg_win': 0.0,
            'avg_loss': 0.0,
            'profit_factor': 0.0
        }

        analysis['buy'] = empty_stats.copy()
        analysis['sell'] = empty_stats.copy()
        analysis['buy_sell'] = empty_stats.copy()

        return analysis

    # 1. วิเคราะห์ Buy Trades
    buy_trades = trades_df[trades_df['Trade Type'] == 'Buy']
    analysis['buy'] = calculate_stats(buy_trades)

    # 2. วิเคราะห์ Sell Trades
    sell_trades = trades_df[trades_df['Trade Type'] == 'Sell']
    analysis['sell'] = calculate_stats(sell_trades)

    # 3. วิเคราะห์ Buy + Sell Trades
    analysis['buy_sell'] = calculate_stats(trades_df)

    return analysis

# ======================================================

def create_multiclass_target(profit_series):
    print(f"\n🏗️ เปิดใช้งาน create multiclass target") if Steps_to_do else None

    """
    สร้าง multi-class target จาก profit values

    Args:
        profit_series: pandas Series ของ profit values

    Returns:
        pandas Series ของ class labels (0-4)
    """

    # สร้าง conditions สำหรับแต่ละ class
    conditions = [
        profit_series >= PROFIT_THRESHOLDS['strong_buy'],    # Class 4: Strong Buy
        profit_series >= PROFIT_THRESHOLDS['weak_buy'],      # Class 3: Weak Buy
        profit_series >= PROFIT_THRESHOLDS['no_trade'],      # Class 2: No Trade
        profit_series >= PROFIT_THRESHOLDS['weak_sell'],     # Class 1: Weak Sell
        profit_series >= PROFIT_THRESHOLDS['strong_sell']    # Class 0: Strong Sell
    ]

    # สร้าง choices สำหรับแต่ละ class
    choices = [4, 3, 2, 1, 0]

    # ใช้ np.select เพื่อสร้าง target
    target = np.select(conditions, choices, default=0)

    # แสดงสถิติ
    unique, counts = np.unique(target, return_counts=True)
    print(f"📊 Multi-class Target Statistics:")
    for class_id, count in zip(unique, counts):
        class_name = CLASS_MAPPING.get(class_id, f"Unknown_{class_id}")
        percentage = (count / len(target)) * 100
        print(f"  Class {class_id} ({class_name}): {count} samples ({percentage:.1f}%)")

    # ตรวจสอบว่ามีอย่างน้อย 2 classes และแต่ละ class มีข้อมูลเพียงพอ
    min_samples_per_class = 10
    valid_classes = [class_id for class_id, count in zip(unique, counts) if count >= min_samples_per_class]

    if len(valid_classes) < 2:
        print(f"⚠️ Warning: มีเพียง {len(valid_classes)} classes ที่มีข้อมูลเพียงพอ (>= {min_samples_per_class} samples)")
        print(f"⚠️ จะใช้ Binary Classification แทน Multi-class")
        # แปลงเป็น binary: 0-2 = 0 (sell/no_trade), 3-4 = 1 (buy)
        binary_target = np.where(target >= 3, 1, 0)
        return pd.Series(binary_target, index=profit_series.index)

    print(f"✅ Multi-class Target ถูกต้อง: {len(valid_classes)} classes พร้อมใช้งาน")
    return pd.Series(target, index=profit_series.index)

def process_trade_targets(df, symbol, trameframe):
    print(f"\n🏗️ เปิดใช้งาน process trade targets") if Steps_to_do else None

    """สร้าง target variable โดยจัดการกรณีไม่มีข้อมูล """
    if df.empty or len(df) < 10:
        print("⚠️ ไม่มีข้อมูล เพียงพอสำหรับสร้าง Target")
        return df

    try:
        # ตรวจสอบคอลัมน์ที่จำเป็น
        required_cols = ['Profit', 'Exit Condition', 'Risk', 'Reward', 'Trade Type']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"⚠️ ไม่พบ คอลัมน์ที่จำเป็น: {missing_cols}")
            return df

        # แปลงคอลัมน์ Profit เป็นตัวเลข
        if not pd.api.types.is_numeric_dtype(df["Profit"]):
            df["Profit"] = pd.to_numeric(df["Profit"], errors='coerce')
            df = df.dropna(subset=["Profit"])

        # ======================================================
        # วิธีที่ 1: สร้าง Target จาก Risk/Reward Ratio
        # ======================================================
        df['RR_Ratio'] = df['Reward'] / df['Risk']

        # ======================================================
        # วิธีที่ 2: สร้าง Target หลักแบบเดิม (Binary)
        # 1 = TP Hit (Win), 0 = SL Hit หรือ Technical Exit (Loss)
        # ======================================================
        conditions_main = [
            (df['Exit Condition'] == 'TP Hit')
        ]
        df['Target'] = np.select(conditions_main, [1], default=0)
        df['Main_Target'] = df['Target']  # เก็บค่าเดิมไว้

        # ======================================================
        # วิธีที่ 3: สร้าง Target แยกสำหรับ Buy และ Sell
        # ======================================================
        
        # Target สำหรับ Buy (1=ชนะ, 0=แพ้, -1=ไม่ใช่ Buy Trade)
        conditions_buy = [
            (df['Trade Type'] == 'Buy') & (df['Exit Condition'] == 'TP Hit'),  # ชนะ
            (df['Trade Type'] == 'Buy') & (df['Exit Condition'] != 'TP Hit')   # แพ้
        ]
        choices_buy = [1, 0]
        df['Target_Buy'] = np.select(conditions_buy, choices_buy, default=-1)  # Default สำหรับไม่ใช่ Buy Trade

        # Target สำหรับ Sell (1=ชนะ, 0=แพ้, -1=ไม่ใช่ Sell Trade)
        conditions_sell = [
            (df['Trade Type'] == 'Sell') & (df['Exit Condition'] == 'TP Hit'),  # ชนะ
            (df['Trade Type'] == 'Sell') & (df['Exit Condition'] != 'TP Hit')   # แพ้
        ]
        choices_sell = [1, 0]
        df['Target_Sell'] = np.select(conditions_sell, choices_sell, default=-1)  # Default สำหรับไม่ใช่ Sell Trade

        # ======================================================
        # วิธีที่ 4: สร้าง Multi-class Target จาก Profit Threshold
        # ======================================================
        df['Target_Multiclass'] = create_multiclass_target(df['Profit'])
        print(f"\n📊 Multi-class Target Distribution:")
        print(df['Target_Multiclass'].value_counts().sort_index())

        # แสดงรายละเอียดแต่ละ class
        for class_id, class_name in CLASS_MAPPING.items():
            count = (df['Target_Multiclass'] == class_id).sum()
            profit_range = df[df['Target_Multiclass'] == class_id]['Profit']
            if len(profit_range) > 0:
                print(f"Class {class_id} ({class_name}): {count} trades, "
                      f"Profit range: {profit_range.min():.1f} to {profit_range.max():.1f}")

        # ======================================================
        # ตรวจสอบและทำความสะอาดข้อมูล
        # ======================================================
        valid_trades = df[
            df['Main_Target'].isin([0, 1]) & 
            df['Target_Buy'].isin([-1, 0, 1]) & 
            df['Target_Sell'].isin([-1, 0, 1])
        ].copy()

        print("\n📊 การกระจายของ Target ต่างๆ:")
        print("1. Target หลัก (Binary):")
        print(valid_trades['Main_Target'].value_counts())
        
        print("\n2. Target สำหรับ Buy Trades:")
        print(valid_trades[valid_trades['Target_Buy'] != -1]['Target_Buy'].value_counts())
        
        print("\n3. Target สำหรับ Sell Trades:")
        print(valid_trades[valid_trades['Target_Sell'] != -1]['Target_Sell'].value_counts())

        return valid_trades

    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะสร้าง Target: {str(e)}")
        traceback.print_exc()
        return df

def add_if_exists(features_list, df, column_name):
    # print(f"\n🏗️ เปิดใช้งาน add if exists") if Steps_to_do else None

    if column_name in df.columns:
        features_list.append(column_name)
    else:
        print(f"⚠️ Feature '{column_name}' not found in DataFrame. Skipping.")
    return features_list

def select_features(trade_df, symbol, timeframe):
    print(f"\n🏗️ เปิดใช้งาน select features") if Steps_to_do else None

    # 1. กำหนดรายชื่อ features ที่อาจมีประโยชน์ (จากลักษณะทางเทคนิคและเวลา)
    # รายการนี้ควรรวมคอลัมน์ทั้งหมดใน trade_df ที่คุณคิดว่าเป็น potential feature
    potential_features_list = []

    # เพิ่ม Time Features (ที่คำนวณจาก Entry_DateTime)
    add_if_exists(potential_features_list, trade_df, "Entry_DayOfWeek")
    add_if_exists(potential_features_list, trade_df, "Entry_Hour")
    add_if_exists(potential_features_list, trade_df, "IsWeekend")
    add_if_exists(potential_features_list, trade_df, "IsMorning")
    add_if_exists(potential_features_list, trade_df, "IsAfternoon")
    add_if_exists(potential_features_list, trade_df, "IsEvening")
    add_if_exists(potential_features_list, trade_df, "IsNight")

    # เพิ่ม Price Action Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Bar_CL")
    add_if_exists(potential_features_list, trade_df, "Bar_CL_OC")
    add_if_exists(potential_features_list, trade_df, "Bar_CL_HL")
    add_if_exists(potential_features_list, trade_df, "Bar_SW")
    add_if_exists(potential_features_list, trade_df, "Bar_TL")
    add_if_exists(potential_features_list, trade_df, "Bar_DTB")
    add_if_exists(potential_features_list, trade_df, "Bar_OSB")
    add_if_exists(potential_features_list, trade_df, "Bar_FVG")
    add_if_exists(potential_features_list, trade_df, "Bar_longwick")

    # เพิ่ม Price Info Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Price_Range")
    add_if_exists(potential_features_list, trade_df, "Price_Move")
    add_if_exists(potential_features_list, trade_df, "Price_Strangth")

    # เพิ่ม Volume Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Volume_MA20")
    add_if_exists(potential_features_list, trade_df, "Volume_Spike")

    # เพิ่ม EMA Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "EMA_50")
    add_if_exists(potential_features_list, trade_df, "EMA_100")
    add_if_exists(potential_features_list, trade_df, "EMA_200")
    add_if_exists(potential_features_list, trade_df, "EMA_diff")
    add_if_exists(potential_features_list, trade_df, "MA_Cross")
    add_if_exists(potential_features_list, trade_df, "Price_above_EMA50")
    add_if_exists(potential_features_list, trade_df, "Dist_EMA50")
    add_if_exists(potential_features_list, trade_df, "Dist_EMA100")
    add_if_exists(potential_features_list, trade_df, "Dist_EMA200")

    # เพิ่ม Volatility Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "Rolling_Vol_5")
    add_if_exists(potential_features_list, trade_df, "Rolling_Vol_15")

    # เพิ่ม RSI Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "RSI14")
    add_if_exists(potential_features_list, trade_df, "RSI_signal")
    add_if_exists(potential_features_list, trade_df, "RSI_Overbought")
    add_if_exists(potential_features_list, trade_df, "RSI_Oversold")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i2")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i4")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i6")
    add_if_exists(potential_features_list, trade_df, "RSI_ROC_i8")
    add_if_exists(potential_features_list, trade_df, "RSI_Divergence_i2")
    add_if_exists(potential_features_list, trade_df, "RSI_Divergence_i4")
    add_if_exists(potential_features_list, trade_df, "RSI_Divergence_i6")

    # เพิ่ม MACD Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "MACD_12_26_9")
    add_if_exists(potential_features_list, trade_df, "MACDs_12_26_9")
    add_if_exists(potential_features_list, trade_df, "MACDh_12_26_9")
    add_if_exists(potential_features_list, trade_df, "MACD_line")
    add_if_exists(potential_features_list, trade_df, "MACD_deep")
    add_if_exists(potential_features_list, trade_df, "MACD_signal")

    # เพิ่ม Stochastic Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "STO_cross")
    add_if_exists(potential_features_list, trade_df, "STO_zone")
    add_if_exists(potential_features_list, trade_df, "STO_overbought")
    add_if_exists(potential_features_list, trade_df, "STO_Oversold")

    # เพิ่ม ADX Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "ADX_Deep")
    add_if_exists(potential_features_list, trade_df, "ADX_zone_15")
    add_if_exists(potential_features_list, trade_df, "ADX_zone_25")
    add_if_exists(potential_features_list, trade_df, "ADX_cross")

    # เพิ่ม ATR Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "ATR_Deep")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i2")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i4")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i6")
    add_if_exists(potential_features_list, trade_df, "ATR_ROC_i8")

    # เพิ่ม BB Features (จาก merge)
    add_if_exists(potential_features_list, trade_df, "BB_width")

    # เพิ่ม Support/Resistance (จาก merge ถ้าต้องการใช้)
    add_if_exists(potential_features_list, trade_df, "PullBack_Up")
    add_if_exists(potential_features_list, trade_df, "PullBack_Down")

    add_if_exists(potential_features_list, trade_df, "Ratio_Buy")
    add_if_exists(potential_features_list, trade_df, "Ratio_Sell")

    # Interaction Features
    add_if_exists(potential_features_list, trade_df, "RSI_x_VolumeSpike") 
    add_if_exists(potential_features_list, trade_df, "EMA_diff_x_ATR") 
    add_if_exists(potential_features_list, trade_df, "Momentum5_x_Volatility10") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_BBwidth") 
    add_if_exists(potential_features_list, trade_df, "MACD_signal_x_ADX") 
    add_if_exists(potential_features_list, trade_df, "Price_above_EMA50_x_RSI_signal") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_ATR")     
    add_if_exists(potential_features_list, trade_df, "RSI14_x_PriceMove") 
    add_if_exists(potential_features_list, trade_df, "EMA50_x_RollingVol5") 
    add_if_exists(potential_features_list, trade_df, "EMA_diff_x_BBwidth") 
    add_if_exists(potential_features_list, trade_df, "ADX_14_x_ATR") 
    add_if_exists(potential_features_list, trade_df, "MACD_signal_x_RSI14") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_StochK") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_StochD") 
    add_if_exists(potential_features_list, trade_df, "MACD_line_x_PriceMove") 
    add_if_exists(potential_features_list, trade_df, "ADX_14_x_RollingVol15") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_Volume") 
    add_if_exists(potential_features_list, trade_df, "ATR_x_PriceRange") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_PullBack_Up") 
    add_if_exists(potential_features_list, trade_df, "RSI14_x_PullBack_Down") 
    add_if_exists(potential_features_list, trade_df, "EMA_diff_x_RSI14") 
    add_if_exists(potential_features_list, trade_df, "ADX_14_x_BBwidth") 

    # เพิ่ม Lag Features ในรายการ potential features
    lag_features = [col for col in trade_df.columns if any(x in col for x in ['_Lag_', '_Return_', '_Change_', '_MA_', '_Std_'])]

    # # 2. กำหนดรายชื่อ features ที่เกี่ยวข้องกับ SL/TP (ที่คำนวณบน trade_df)
    # stop_loss_take_profit_features = [
    #     'Risk', 'Reward', 'RR_Ratio', # ต้องแน่ใจว่าคำนวณบน trade_df แล้ว
    #     'Pct_Risk', 'Pct_Reward'    # ต้องแน่ใจว่าคำนวณบน trade_df แล้ว
    #     # ATR, BB_width อยู่ในกลุ่ม Technical Features ที่ merge มาแล้ว
    # ]

    # all_potential_features = potential_features_list + [
    #     f for f in stop_loss_take_profit_features if f in trade_df.columns
    # ]

    # นำ features ทั้งสองกลุ่มมารวมกัน
    all_potential_features = potential_features_list + lag_features # การตรวจสอบเบื้องต้นถ้าใช้ stop_loss_take_profit_features อาจส่งผลกระทบ

    print(f"ℹ️ Potential features for model input (Pre-Trade Only): {potential_features_list}+{lag_features} = {len(all_potential_features)} features considered.")
    
    # 3. เลือกเฉพาะ features ที่มีอยู่ใน DataFrame และเป็นตัวเลข
    numeric_columns = trade_df.select_dtypes(include=['number']).columns.tolist()
    # กรองอีกครั้งเพื่อให้แน่ใจว่ามีอยู่จริงและเป็นตัวเลข
    available_numeric_features = [
        f for f in all_potential_features if f in numeric_columns
    ]

    # 4. ตรวจสอบว่ามีคอลัมน์ Target หรือไม่
    if 'Target' not in trade_df.columns:
        print("⚠️ ไม่พบ คอลัมน์ Target ในข้อมูล ใช้ features ที่มีทั้งหมดที่เป็นตัวเลข")
        return available_numeric_features

    # 5. คำนวณความสัมพันธ์กับ Target
    print("\n🔍 ความสัมพันธ์กับ Target (ทั้งหมด):")
    # เลือกเฉพาะคอลัมน์ที่เป็น available_numeric_features และ Target ก่อนคำนวณ corr เพื่อป้องกัน error
    cols_for_corr = [f for f in available_numeric_features if f in trade_df.columns] + ['Target']
    if 'Target' in trade_df.columns and all(col in trade_df.columns for col in available_numeric_features):
        correlation_with_target = trade_df[cols_for_corr].corr()['Target'].abs().sort_values(ascending=False)
        print(correlation_with_target)
    else:
        print("ไม่สามารถคำนวณความสัมพันธ์กับ Target ได้ เนื่องจากมีคอลัมน์ไม่ครบถ้วน")
        # ในกรณีนี้ อาจจะ return available_numeric_features ไปก่อน หรือหยุดการทำงาน
        return available_numeric_features # หรือ raise Error

    # 6. กำหนดค่า threshold สำหรับความสัมพันธ์ และเลือก features ที่มีความสัมพันธ์สูงกว่า
    correlation_threshold = 0.05
    high_correlation_threshold_for_multicollinearity = 0.8 # ปรับ threshold สำหรับ multicollinearity ให้สูงขึ้นเล็กน้อย

    # เลือก features ที่มีความสัมพันธ์กับ Target สูงกว่า threshold (ไม่รวม Target ตัวเอง)
    highly_correlated_with_target = correlation_with_target[
        (correlation_with_target > correlation_threshold) &
        (correlation_with_target.index != 'Target')
    ].index.tolist()

    # 7. ตรวจสอบ multicollinearity (ความสัมพันธ์สูงระหว่าง features กันเอง)
    # ตรวจสอบให้แน่ใจว่า highly_correlated_with_target ไม่ว่างเปล่าก่อนสร้าง correlation_matrix
    if len(highly_correlated_with_target) > 1:
        correlation_matrix = trade_df[highly_correlated_with_target].corr().abs()
        # ใช้ NumPy ในการสร้าง upper triangle mask
        upper_triangle = correlation_matrix.where(np.triu(np.ones(correlation_matrix.shape), k=1).astype(bool))

        # หา features ที่มีความสัมพันธ์สูงกับ feature อื่นๆ (เก็บชื่อคอลัมน์ที่ต้องการ drop)
        # จะ drop คอลัมน์ที่ 'any' ค่าในคอลัมน์นั้นใน upper_triangle สูงกว่า threshold
        features_to_drop_due_to_multicollinearity = [
            col for col in upper_triangle.columns if any(upper_triangle[col] > high_correlation_threshold_for_multicollinearity)
        ]

        # 8. สร้างรายชื่อ features สุดท้าย โดยตัด features ที่มีความสัมพันธ์กันเองสูงเกินไปออก
        final_selected_features = [
            f for f in highly_correlated_with_target if f not in features_to_drop_due_to_multicollinearity
        ]

        # แสดง features ที่ถูกตัดออกเนื่องจาก Multicollinearity
        if features_to_drop_due_to_multicollinearity:
            print("\n🚫 Features ถูกตัดออกเนื่องจาก Multicollinearity สูง:", features_to_drop_due_to_multicollinearity)

    else:
        # กรณีที่มี highly_correlated_with_target แค่ 0 หรือ 1 ตัว ไม่ต้องทำ multicollinearity check
        print("\nℹ️ มี Features ที่มีความสัมพันธ์กับ Target ต่ำกว่าหรือเท่ากับ 1 ตัว ไม่ต้องตรวจสอบ Multicollinearity.")
        final_selected_features = highly_correlated_with_target

    # 9. กำหนด features ที่จำเป็นต้องมี และเพิ่มเข้าไปในรายชื่อ หากยังไม่มี
    # ควรใช้ชื่อคอลัมน์ที่ถูกต้องหลังจาก merge และคำนวณแล้ว
    # กำหนด features ที่จำเป็นต้องมี (add_features_in_model) ---
    print("\ืเริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance")
    add_features_in_model = ['DayOfWeek', 'Hour', 'RSI_ROC_i2', 'ADX_Deep']

    # ตรวจสอบว่ามีไฟล์ pickle อยู่หรือไม่ ถ้ามี ให้โหลดจากไฟล์ ถ้าไม่มี ให้ใช้ hardcode list
    must_have_pickle_path = os.path.join(f'{test_folder}', 'feature_importance', f'{str(timeframe).zfill(3)}_must_have_features.pkl')
    must_have_features_in_model = []
    if os.path.exists(must_have_pickle_path):
        try:
            with open(must_have_pickle_path, 'rb') as f: # ใช้ 'rb' สำหรับ binary read
                must_have_features_in_model = pickle.load(f)
            print(f"\n👍 โหลดรายชื่อ Features ที่จำเป็นจากไฟล์: {must_have_pickle_path} ({len(must_have_features_in_model)} Features)")
        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ในการโหลดไฟล์ Features ที่จำเป็น {must_have_pickle_path}: {e}")
            # ถ้าโหลดไม่ได้ ให้ใช้ hardcode list แทน
            print("ℹ️ ใช้ Hardcoded must_have_features_in_model เป็นค่าเริ่มต้น")
            must_have_features_in_model = add_features_in_model
    else:
        print(f"\nℹ️ ⚠️ ไม่พบไฟล์ Features ที่จำเป็น '{must_have_pickle_path}'. จะใช้ Hardcoded must_have_features_in_model เป็นค่าเริ่มต้น")
        # Hardcoded list เป็นค่าเริ่มต้นเมื่อ⚠️ ไม่พบไฟล์
        must_have_features_in_model = add_features_in_model
    
    print("\nfeaturesasset_feature_importance : len ",len(must_have_features_in_model))
    print(must_have_features_in_model)

    for feature in must_have_features_in_model:
        # เพิ่มเฉพาะ feature ที่มีอยู่จริงใน numeric pre-trade features และยังไม่มีใน final list
        if feature not in final_selected_features and feature in available_numeric_features:
            final_selected_features.append(feature)
            print(f"👍 เพิ่ม Feature ที่จำเป็น '{feature}' เข้าไปในรายการ")
        elif feature not in available_numeric_features:
            print(f"⚠️ Feature ที่จำเป็น '{feature}' ⚠️ ไม่พบ ในข้อมูลตัวเลขที่มีอยู่")

    # 10. ตรวจสอบและนำ 'Target' ออกจากรายชื่อ features สุดท้าย (ถ้ามี)
    if 'Target' in final_selected_features:
        final_selected_features.remove('Target')
        print("\n✅ นำคอลัมน์ 'Target' ออกจากรายชื่อ Features แล้ว")

    # 11. แสดง Features สุดท้ายที่เลือกได้
    if not final_selected_features:
        print("\n⚠️ ไม่สามารถเลือก Features ใดๆ ได้จากกระบวนการคัดเลือกอัตโนมัติ")
        print("ℹ️ สาเหตุอาจมาจาก:")
        print("- ความสัมพันธ์กับ Target ต่ำกว่า Threshold")
        print("- มี Multicollinearity สูงมาก")
        print("- จำนวนข้อมูลน้อยเกินไปที่จะคำนวณความสัมพันธ์ได้อย่างแม่นยำ")
        # อาจจะ return available_numeric_features ทั้งหมด หรือ [] ขึ้นอยู่กับว่าต้องการให้โมเดลทำงานอย่างไร
        return [] # หรือ available_numeric_features

    # print(f"\n✅ Final selected features for training: {final_selected_features}")
    # print(f"\n✅ Final selected features for training:")
    # for i, feat in enumerate(final_selected_features, 1):
    #     print(f"{i}. {feat}")

    return final_selected_features

def analyze_time_filters(trade_df, min_win_rate=0.25, min_expectancy=0.0, save_path=None, symbol=None):
    """
    วิเคราะห์ win rate/expectancy รายวันและรายชั่วโมงใน trade_df
    คืน dict ของวันที่/ชั่วโมงที่ควรเทรด และบันทึกไฟล์ filter
    เพิ่มการวิเคราะห์แบบละเอียดสำหรับการใช้งานจริง
    """
    print(f"\n🏗️ เปิดใช้งาน analyze time filters") if Steps_to_do else None

    # ตรวจสอบว่ามี target column ที่เหมาะสม
    target_col = "Target_Multiclass" if "Target_Multiclass" in trade_df.columns else "Target"

    result = {'days': [], 'hours': [], 'detailed_stats': {}}

    # วิเคราะห์รายวัน (0=Monday, 1=Tuesday, ..., 6=Sunday)
    day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

    if target_col == "Target_Multiclass":
        # สำหรับ multi-class ใช้ win rate จาก class 3 และ 4 (weak_buy, strong_buy)
        trade_df_copy = trade_df.copy()
        trade_df_copy['Win'] = (trade_df_copy[target_col] >= 3).astype(int)

        day_stats = trade_df_copy.groupby('DayOfWeek').agg({
            'Win': ['mean', 'count'],
            'Profit': 'mean',
            target_col: [
                lambda x: (x == 4).mean(),  # strong_buy_rate
                lambda x: (x == 3).mean(),  # weak_buy_rate
                lambda x: (x == 2).mean()   # no_trade_rate
            ]
        })

        # Flatten column names
        day_stats.columns = ['win_rate', 'total', 'expectancy', 'strong_buy_rate', 'weak_buy_rate', 'no_trade_rate']
    else:
        # สำหรับ binary classification
        day_stats = trade_df.groupby('DayOfWeek').agg({
            target_col: ['mean', 'count'],
            'Profit': 'mean'
        })

        # Flatten column names
        day_stats.columns = ['win_rate', 'total', 'expectancy']

    # เก็บสถิติรายละเอียด
    result['detailed_stats']['days'] = {}
    for day_idx, stats in day_stats.iterrows():
        day_name = day_names[day_idx] if day_idx < len(day_names) else f"Day_{day_idx}"
        result['detailed_stats']['days'][day_name] = {
            'win_rate': stats['win_rate'],
            'expectancy': stats['expectancy'],
            'total_trades': stats['total'],
            'day_index': day_idx
        }

        # เพิ่มข้อมูล multi-class ถ้ามี
        if target_col == "Target_Multiclass":
            result['detailed_stats']['days'][day_name].update({
                'strong_buy_rate': stats['strong_buy_rate'],
                'weak_buy_rate': stats['weak_buy_rate'],
                'no_trade_rate': stats['no_trade_rate']
            })

    # กรองวันที่ควรเทรด (ปรับเกณฑ์ให้เหมาะสม)
    result['days'] = day_stats[
        (day_stats['win_rate'] >= min_win_rate) &
        (day_stats['expectancy'] > min_expectancy) &
        (day_stats['total'] >= 10)  # ต้องมีข้อมูลอย่างน้อย 10 trades
    ].index.tolist()

    # วิเคราะห์รายชั่วโมง
    if target_col == "Target_Multiclass":
        hour_stats = trade_df_copy.groupby('Hour').agg({
            'Win': ['mean', 'count'],
            'Profit': 'mean',
            target_col: lambda x: (x == 4).mean()  # strong_buy_rate
        })

        # Flatten column names
        hour_stats.columns = ['win_rate', 'total', 'expectancy', 'strong_buy_rate']
    else:
        hour_stats = trade_df.groupby('Hour').agg({
            target_col: ['mean', 'count'],
            'Profit': 'mean'
        })

        # Flatten column names
        hour_stats.columns = ['win_rate', 'total', 'expectancy']

    # เก็บสถิติรายชั่วโมง
    result['detailed_stats']['hours'] = {}
    for hour, stats in hour_stats.iterrows():
        result['detailed_stats']['hours'][hour] = {
            'win_rate': stats['win_rate'],
            'expectancy': stats['expectancy'],
            'total_trades': stats['total']
        }

        if target_col == "Target_Multiclass":
            result['detailed_stats']['hours'][hour]['strong_buy_rate'] = stats['strong_buy_rate']

    # กรองชั่วโมงที่ควรเทรด
    result['hours'] = hour_stats[
        (hour_stats['win_rate'] >= min_win_rate) &
        (hour_stats['expectancy'] > min_expectancy) &
        (hour_stats['total'] >= 5)  # ต้องมีข้อมูลอย่างน้อย 5 trades
    ].index.tolist()

    # แสดงผลสถิติ
    print(f"\n📊 Time Filter Analysis for {symbol if symbol else 'Unknown'}:")
    print(f"📅 Recommended Days: {[day_names[d] for d in result['days']]}")
    print(f"⏰ Recommended Hours: {sorted(result['hours'])}")

    # บันทึกไฟล์
    if save_path:
        # ตรวจสอบและสร้าง directory ถ้าจำเป็น
        save_dir = os.path.dirname(save_path)
        if save_dir:  # ถ้ามี directory path
            os.makedirs(save_dir, exist_ok=True)

        with open(save_path, 'wb') as f:
            pickle.dump(result, f)
        print(f"✅ บันทึก time filter ที่: {save_path}")

    return result

def find_optimal_nbars_sl(val_df, symbol, timeframe, entry_func, best_entry_name, nBars_range=range(2, 11)):
    print(f"\n🏗️ เปิดใช้งาน find optimal nbars sl") if Steps_to_do else None

    val_df = val_df.copy() # ป้องกัน SettingWithCopyWarning

    if 'Low' not in val_df.columns or 'High' not in val_df.columns:
        print("⚠️ DataFrame ที่ส่งเข้าไม่มีคอลัมน์ 'Low' หรือ 'High'")
        return load_optimal_nbars(symbol, timeframe)

    # ตรวจสอบขนาดข้อมูล validation set
    min_required_rows = max(nBars_range) + 100  # ต้องการข้อมูลอย่างน้อย 110 แถว
    if len(val_df) < min_required_rows:
        print(f"⚠️ Validation set มีข้อมูลไม่เพียงพอ ({len(val_df)} แถว, ต้องการอย่างน้อย {min_required_rows} แถว)")
        print(f"⚠️ จะใช้ค่า optimal nBars SL เดิมหรือ default")
        return load_optimal_nbars(symbol, timeframe)

    best_nbars = None
    best_metric = -float('inf')
    found_valid = False
    min_trades = 5
    nbars_results = []

    # Debug: ตรวจสอบข้อมูล validation set
    print(f"🔍 Validation set info: {len(val_df)} rows, columns: {list(val_df.columns[:10])}...")
    if len(val_df) > 0:
        print(f"🔍 Sample data range: {val_df.index[0]} to {val_df.index[-1]}")
        print(f"🔍 Entry function: {best_entry_name}")
        print(f"🔍 Entry function type: {type(entry_func)}")

    # ตรวจสอบคอลัมน์ที่จำเป็นสำหรับ backtest
    required_cols = ['Close', 'Open', 'High', 'Low', 'EMA50', 'EMA200', 'MACD_signal', 'RSI14', 'Volume', 'Volume_MA_20', 'PullBack_Up', 'Ratio_Buy', 'PullBack_Down', 'Ratio_Sell', 'ATR']
    missing_cols = [col for col in required_cols if col not in val_df.columns]
    if missing_cols:
        print(f"⚠️ Validation set ขาดคอลัมน์ที่จำเป็น: {missing_cols}")
        print(f"⚠️ จะใช้ค่า optimal nBars SL เดิมหรือ default")
        return load_optimal_nbars(symbol, timeframe)

    for n in nBars_range:
        val_df["Low_Prev_Min"] = val_df["Low"].shift(1).rolling(window=n).min()
        val_df["High_Prev_Max"] = val_df["High"].shift(1).rolling(window=n).max()
        stats = backtest(
            val_df, 
            n, 
            input_rsi_level_in, 
            input_rsi_level_out, 
            input_stop_loss_atr, 
            input_take_profit, 
            symbol,
            entry_condition_func=entry_func,
            entry_condition_name=best_entry_name
        )
        num_trades = stats.get("num_trades", 0) if "num_trades" in stats else None
        # เพิ่มการเก็บผลลัพธ์แต่ละ nBars
        nbars_results.append((n, stats["win_rate"], stats["expectancy"], num_trades))
        # เงื่อนไข: มี trade เกิดขึ้นจริง (win_rate > 0 หรือ expectancy != 0) และจำนวน trade >= min_trades
        if (stats["win_rate"] > 0 or stats["expectancy"] != 0) and (num_trades is None or num_trades >= min_trades):
            found_valid = True
            if stats["expectancy"] > best_metric:
                best_metric = stats["expectancy"]
                best_nbars = n

    # Print ตารางผลลัพธ์แต่ละ nBars
    print("\nnBars grid search (val set):")
    print(f"{'nBars':<6} {'WinRate':<10} {'Expectancy':<12} {'NumTrades':<10}")
    for n, win_rate, expectancy, num_trades in nbars_results:
        print(f"{n:<6} {win_rate:<10.2f} {expectancy:<12.2f} {num_trades if num_trades is not None else '-':<10}")

    if found_valid and best_nbars is not None:
        save_optimal_nbars(symbol, timeframe, best_nbars)
        print(f"✅ Optimal nBars SL for {symbol} {timeframe}: {best_nbars}")
        return best_nbars
    else:
        # ไม่มี trade เกิดขึ้นเลย: ไม่บันทึกค่าใหม่, ใช้ค่าเดิม
        print(f"⚠️ ไม่มีข้อมูลการซื้อขายใน validation set หรือมี trade < {min_trades} จะใช้ค่า optimal nBars SL เดิมหรือ default")
        prev_nbars = load_optimal_nbars(symbol, timeframe)
        print(f"✅ ใช้ค่า optimal nBars SL เดิม: {prev_nbars}")
        return prev_nbars

def load_and_process_data(file, model_name, symbol, timeframe, model, scaler, nBars_SL, confidence_threshold):
    print(f"\n🏗️ เปิดใช้งาน load and process data") if Steps_to_do else None

    # 1. โหลดข้อมูลเบื้องต้น - ลองหลาย separators
    df = None

    try:
        df_temp = pd.read_csv(file, low_memory=False)
        if not df_temp.empty and len(df_temp.columns) > 1:
            df = df_temp
            print(f"✅ อ่านไฟล์สำเร็จด้วย : {file}")
    except Exception as e:
        print(f"⚠️ ไม่สามารถอ่านด้วย {file} separator: {e}")

    if df is None or df.empty:
        print(f"❌ ไม่สามารถอ่านไฟล์ได้: {file}")
        return None, None, None, None, None, None

    if len(df) < 1000:
        print(f"ไฟล์ {file} มีข้อมูลน้อยกว่า 1000 แท่ง ข้ามการคำนวณไฟล์นี้")
        return None, None, None, None, None, None

    # ถ้ารู้ว่าแถวแรกไม่ใช่ข้อมูลที่ต้องใช้:
    df = df.iloc[1:].reset_index(drop=True)

    # รายชื่อคอลัมน์ที่ไม่ใช้ ต้องการลบออก
    drop_cols = ["Vol","Col_8"]
    df = df.drop(columns=drop_cols, errors="ignore")

    # เปลี่ยนชื่อคอลัมน์ 'TickVol' เป็น 'Volume'
    if 'TickVol' in df.columns:
        df = df.rename(columns={'TickVol': 'Volume'})

    # 2. ตรวจสอบและตั้งชื่อคอลัมน์
    print(f"🔍 ตรวจสอบโครงสร้างไฟล์: {file}")
    print(f"📊 จำนวนคอลัมน์: {len(df.columns)}")
    print(f"📊 Shape: {df.shape}")
    print(f"📊 คอลัมน์ปัจจุบัน: {list(df.columns)}")

    # แปลงคอลัมน์ที่เกี่ยวข้องให้เป็นตัวเลข
    numeric_cols = ["Open", "High", "Low", "Close", "Volume"]
    for col in numeric_cols:
        # ใช้ pd.to_numeric เพื่อแปลงเป็นตัวเลข
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # 3. สร้าง technical indicators
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])

    df['DayOfWeek'] = df['DateTime'].dt.dayofweek  # 0=Monday, 6=Sunday
    df['Hour'] = df['DateTime'].dt.hour
    
    # สร้างฟีเจอร์สำหรับช่วงเวลาที่สำคัญ
    df['IsMorning'] = ((df['Hour'] >= 8) & (df['Hour'] < 12)).astype(int)
    df['IsAfternoon'] = ((df['Hour'] >= 12) & (df['Hour'] < 16)).astype(int)
    df['IsEvening'] = ((df['Hour'] >= 16) & (df['Hour'] < 20)).astype(int)
    df['IsNight'] = ((df['Hour'] >= 20) | (df['Hour'] < 4)).astype(int)
    
    df["Bar_CLp"] = df['Close'].shift(1)

    # Price action
    df["Bar_CL"] = 0.0
    df.loc[df['Close'] > df['Open'], "Bar_CL"] = 1.0
    df.loc[df['Close'] < df['Open'], "Bar_CL"] = -1.0

    df["Bar_CL_OC"] = 0.0
    df.loc[df['Close'] > np.maximum(df['Open'].shift(1), df['Close'].shift(1)), "Bar_CL_OC"] = 1.0
    df.loc[df['Close'] < np.minimum(df['Open'].shift(1), df['Close'].shift(1)), "Bar_CL_OC"] = -1.0

    df["Bar_CL_HL"] = 0.0
    df.loc[(df['Close'] > df['High'].shift(1)) & (df['Close'] > df['Open']), "Bar_CL_HL"] = 1.0
    df.loc[(df['Close'] < df['Low'].shift(1)) & (df['Close'] < df['Open']), "Bar_CL_HL"] = -1.0
    
    df["Bar_SW"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] > df['Low'].shift(1)), "Bar_SW"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] < df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_SW"] = -1.0

    df["Bar_TL"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['High'] < df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_TL"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] > df['Low'].shift(1)), "Bar_TL"] = 1.0

    df["Bar_DTB"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['Low'] == df['Low'].shift(1)), "Bar_DTB"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] == df['High'].shift(1)), "Bar_DTB"] = -1.0

    df["Bar_OSB"] = 0.0
    df.loc[(df['Close'] > df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_OSB"] = 1.0
    df.loc[(df['Close'] < df['Open']) & (df['High'] > df['High'].shift(1)) & (df['Low'] < df['Low'].shift(1)), "Bar_OSB"] = -1.0

    df["Bar_FVG"] = 0.0
    df.loc[(df["Low"] > df["High"].shift(2)) & (df["Close"] > df["Open"]), "Bar_FVG"] = 1.0
    df.loc[(df["High"] < df["Low"].shift(2)) & (df["Close"] < df["Open"]), "Bar_FVG"] = -1.0

    df["High_Prev_Max"] = df["High"].shift(0).rolling(window=nBars_SL).max()
    df["Low_Prev_Min"] = df["Low"].shift(0).rolling(window=nBars_SL).min()

    df["Bar_longwick"] = 0.0
    epsilon = 1e-9  # ค่าเล็กๆ เพื่อป้องกันหารด้วยศูนย์
    lower_wick = (np.minimum(df['Open'], df['Close']) - df['Low']).replace(0, epsilon)
    upper_wick = (df['High'] - np.maximum(df['Open'], df['Close'])).replace(0, epsilon)
    pinbar_up = lower_wick / (df['High'] - np.minimum(df['Open'], df['Close']))
    pinbar_down = upper_wick / (np.maximum(df['Open'], df['Close']) - df['Low'])
    df.loc[pinbar_up > pinbar_down, "Bar_longwick"] = pinbar_up
    df.loc[pinbar_down > pinbar_up, "Bar_longwick"] = -1 * pinbar_down

    df['Price_Range'] = df["High"] - df["Low"]
    df['Price_Move'] = df["Close"] - df["Open"]

    df['Price_Strangth'] = 0.0
    body_size_oc = np.abs(df["Close"]-df["Open"])
    body_size_ocp = np.abs(df["Close"].shift(1)-df["Open"].shift(1))
    body_size_hl = np.abs(df["High"]-df["Low"])
    body_size_hlp = np.abs(df["High"].shift(1)-df["Low"].shift(1))

    df.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df["Close"] < df["Open"]), "Price_Strangth"] = 1
    df.loc[(body_size_oc < body_size_ocp) & (body_size_hl < body_size_hlp) & (df["Close"] > df["Open"]), "Price_Strangth"] = -1
    
    # --- Volume_MA20, Volume_Spike ---
    df['Volume_MA20'] = df['Volume'].rolling(20, min_periods=1).mean()
    df['Volume_Spike'] = df['Volume'] / (df['Volume_MA20'] + 1e-10)

    # --- EMA Calculation (Classic, seeded with SMA) ---
    def classic_ema(series, span):
        sma = series.rolling(window=span, min_periods=span).mean()
        ema = pd.Series(index=series.index, dtype=float)
        alpha = 2 / (span + 1)

        for i in range(len(series)):
            if i < span-1:
                ema.iloc[i] = np.nan
            elif i == span-1:
                ema.iloc[i] = sma.iloc[i]
            else:
                ema.iloc[i] = alpha * series.iloc[i] + (1 - alpha) * ema.iloc[i-1]
        return ema

    df['EMA50'] = classic_ema(df['Close'], 50)
    df['EMA100'] = classic_ema(df['Close'], 100)
    df['EMA200'] = classic_ema(df['Close'], 200)

    # --- EMA Related Features ---
    df['EMA_diff'] = (df['EMA50'] - df['EMA200'])
    df['MA_Cross'] = 0.0
    df.loc[(df['EMA50'] > df['EMA200']), "MA_Cross"] = 1.0
    df.loc[(df['EMA50'] < df['EMA200']), "MA_Cross"] = -1.0
    df["Price_above_EMA50"] = 0.0
    df.loc[(df["Close"] > df["EMA50"]), "Price_above_EMA50"] = 1.0
    df.loc[(df["Close"] < df["EMA50"]), "Price_above_EMA50"] = -1.0

    # ความผันผวนระยะสั้น
    df['Rolling_Vol_5'] = df['Close'].pct_change().rolling(5, min_periods=1).std()
    df['Rolling_Vol_15'] = df['Close'].pct_change().rolling(15, min_periods=1).std()

    # ระยะทางจาก EMA
    df['Dist_EMA50'] = (df['Close'] - df['EMA50']) / (df['EMA50'] + 1e-10)
    df['Dist_EMA100'] = (df['Close'] - df['EMA100']) / (df['EMA100'] + 1e-10)
    df['Dist_EMA200'] = (df['Close'] - df['EMA200']) / (df['EMA200'] + 1e-10)

    # --- RSI Calculation (Classic, seeded with SMA, or TA-Lib if available) ---
    window_rsi = 14
    try:
        import talib
        df["RSI14"] = talib.RSI(df["Close"].values, timeperiod=window_rsi)
    except Exception:
        delta = df["Close"].diff(1)
        gain = pd.Series(np.where(delta > 0, delta, 0), index=df.index)
        loss = pd.Series(np.where(delta < 0, -delta, 0), index=df.index)
        avg_gain = gain.rolling(window=window_rsi, min_periods=window_rsi).mean()
        avg_loss = loss.rolling(window=window_rsi, min_periods=window_rsi).mean()
        rsi = pd.Series(index=df.index, dtype=float)
        for i in range(len(df)):
            if i < window_rsi:
                rsi.iloc[i] = np.nan
            elif i == window_rsi:
                rsi.iloc[i] = 100 - (100 / (1 + (avg_gain.iloc[i] / avg_loss.iloc[i] if avg_loss.iloc[i] != 0 else np.nan)))
                prev_avg_gain = avg_gain.iloc[i]
                prev_avg_loss = avg_loss.iloc[i]
            else:
                prev_avg_gain = (prev_avg_gain * (window_rsi - 1) + gain.iloc[i]) / window_rsi
                prev_avg_loss = (prev_avg_loss * (window_rsi - 1) + loss.iloc[i]) / window_rsi

                # rs = prev_avg_gain / prev_avg_loss if prev_avg_loss != 0 else np.nan

                # หาก avg_loss == 0, คุณตั้ง rs = NaN แต่ MT5 อาจใช้ rs = ∞ → RSI = 100 (หรือ 0 ถ้า gain = 0)
                if prev_avg_loss == 0:
                    rs = np.inf
                else:
                    rs = prev_avg_gain / prev_avg_loss

                rsi.iloc[i] = 100 - (100 / (1 + rs))
        df["RSI14"] = rsi
    df.loc[df.index[:window_rsi-1], "RSI14"] = np.nan # เติม NaN ใน window_rsi - 1 แถวแรก

    df["RSI_signal"] = np.select(
        [df["RSI14"] < 30, df["RSI14"] > 70],
        [-1, 1],
        default=0
    )

    df['RSI_Overbought'] = (df['RSI14'] > 70).astype(int)
    df['RSI_Oversold'] = (df['RSI14'] < 30).astype(int)

    # RSI_ROC
    divisor = df['RSI14'] + 1e-10
    df['RSI_ROC_i2'] = (df['RSI14'] - df['RSI14'].shift(2)) / divisor
    df['RSI_ROC_i4'] = (df['RSI14'] - df['RSI14'].shift(4)) / divisor
    df['RSI_ROC_i6'] = (df['RSI14'] - df['RSI14'].shift(6)) / divisor
    df['RSI_ROC_i8'] = (df['RSI14'] - df['RSI14'].shift(8)) / divisor

    close_shift_2 = df['Close'].shift(2)
    rsi14_shift_2 = df['RSI14'].shift(2)
    if not (pd.isna(close_shift_2).any() or pd.isna(rsi14_shift_2).any()): # ตรวจสอบว่าไม่มี NaN ในแถวที่จะเปรียบเทียบ
        df['RSI_Divergence_i2'] = np.where(
            (df['Close'] > close_shift_2) & (df['RSI14'] < rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                1, np.where(
                    (df['Close'] < close_shift_2) & (df['RSI14'] > rsi14_shift_2), # แก้ไข: ใช้ rsi14_shift_2
                    -1, 0
            )
        )
    else:
        df['RSI_Divergence_i2'] = 0 # หรือ np.nan ขึ้นอยู่กับความต้องการ

    close_shift_4 = df['Close'].shift(4)
    rsi14_shift_4 = df['RSI14'].shift(4)
    if not (pd.isna(close_shift_4).any() or pd.isna(rsi14_shift_4).any()):
        df['RSI_Divergence_i4'] = np.where(
            (df['Close'] > close_shift_4) & (df['RSI14'] < rsi14_shift_4),
                1, np.where(
                    (df['Close'] < close_shift_4) & (df['RSI14'] > rsi14_shift_4),
                    -1, 0
            )
        )
    else:
        df['RSI_Divergence_i4'] = 0 # หรือ np.nan ขึ้นอยู่กับความต้องการ

    close_shift_6 = df['Close'].shift(6)
    rsi14_shift_6 = df['RSI14'].shift(6)
    if not (pd.isna(close_shift_6).any() or pd.isna(rsi14_shift_6).any()):
        df['RSI_Divergence_i6'] = np.where(
            (df['Close'] > close_shift_6) & (df['RSI14'] < rsi14_shift_6),
                1, np.where(
                    (df['Close'] < close_shift_6) & (df['RSI14'] > rsi14_shift_6),
                    -1, 0
            )
        )
    else:
        df['RSI_Divergence_i6'] = 0

    # --- Indicator Calculation (MACD, Stochastic, BB, ADX, ATR, SR) ---
    # print(f"📝 เริ่มคำนวณ MACD, Stochastic, BB, ADX, ATR, SR") if Steps_to_calculating else None

    def classic_macd_ema(series: pd.Series, span: int):
        s = pd.to_numeric(series, errors='coerce').copy()
        alpha = 2.0 / (span + 1.0)
        out = pd.Series(index=s.index, dtype='float64')

        # หาตำแหน่งเริ่มต้นที่มี value จริง
        valid_idx = s.first_valid_index()
        if valid_idx is None:
            return out

        start = s.index.get_loc(valid_idx)
        n = len(s)

        # ถ้ามีข้อมูลมากพอสำหรับ seed SMA
        if n - start >= span:
            # seed position at index start+span-1
            seed_pos = start + span - 1
            seed = s.iloc[start: start + span].mean()
            out.iloc[seed_pos] = seed
            # recursive
            for i in range(seed_pos + 1, n):
                out.iloc[i] = alpha * s.iloc[i] + (1 - alpha) * out.iloc[i - 1]
            # ข้อดี: ค่าก่อน seed_pos จะยังคง NaN
        else:
            # fallback: ถ้าไม่พอข้อมูล ให้ใช้ pandas ewm (จะ seed ด้วยค่าที่มี)
            out = s.ewm(span=span, adjust=False).mean()

        return out

    def mt5_like_macd_seeded(price: pd.Series, fast=12, slow=26, signal=9):
        price = pd.to_numeric(price, errors='coerce')
        ema_fast = classic_macd_ema(price, fast)
        ema_slow = classic_macd_ema(price, slow)
        macd_line = ema_fast - ema_slow

        # สำหรับ signal line เรามักจะใช้ EMA บน macd_line (และสามารถ seed ด้วย SMA ของ macd ส่วนเริ่ม)
        signal_line = classic_macd_ema(macd_line, signal)
        hist = macd_line - signal_line
        return ema_fast, ema_slow, macd_line, signal_line, hist
    
    ema12, ema26, macd_line, macd_signal, macd_hist = mt5_like_macd_seeded(df['Close'])

    df['EMA12'] = ema12
    df['EMA26'] = ema26
    df["MACD_12_26_9"] = macd_line
    df["MACDs_12_26_9"] = macd_signal
    df["MACDh_12_26_9"] = macd_hist

    if "MACD_12_26_9" in df.columns:
        df["MACD_line"] = (df["MACD_12_26_9"] > 0.0).astype(int) - (df["MACD_12_26_9"] < 0.0).astype(int) # Convert to 1, 0, -1

    if "MACD_12_26_9" in df.columns:
        df["MACD_deep"] = (
            (df["MACD_12_26_9"] > df["MACD_12_26_9"].shift(1)).astype(int) -
            (df["MACD_12_26_9"] < df["MACD_12_26_9"].shift(1)).astype(int)
        ) # Convert to 1, 0, -1

    if "MACD_12_26_9" in df.columns and "MACDs_12_26_9" in df.columns:
        df["MACD_signal"] = (
            (df["MACD_12_26_9"] > df["MACDs_12_26_9"]).astype(int) -
            (df["MACD_12_26_9"] < df["MACDs_12_26_9"]).astype(int)
        ) # Convert to 1, 0, -1

    macd = pd.DataFrame({
        "MACD_12_26_9": macd_line,
        "MACDs_12_26_9": macd_signal,
        "MACDh_12_26_9": macd_hist
    })

    print("📊 Close count:", df["Close"].count())
    print("📉 MACD line non-NaN:", macd_line.count())
    print("📈 MACD signal non-NaN:", macd_signal.count())
    print("📊 MACD hist non-NaN:", macd_hist.count())
    print("📏 DataFrame rows:", len(df))

    # Bollinger Bands Features
    window_bb = 20
    rolling_mean_bb = df["Close"].rolling(window=window_bb, min_periods=1).mean()
    rolling_std_bb = df["Close"].rolling(window=window_bb, min_periods=1).std()
    bb_upper = (rolling_mean_bb + (rolling_std_bb * 2))
    bb_lower = (rolling_mean_bb - (rolling_std_bb * 2))
    bb_width = bb_upper - bb_lower

    window_atr = 14
    if all(col in df.columns for col in ['High', 'Low', 'Close']):
        tr1 = df['High'] - df['Low']
        if len(df) > 1:
            tr2 = (df['High'] - df['Close'].shift()).abs()
            tr3 = (df['Low'] - df['Close'].shift()).abs()
            true_range_df = pd.concat([tr1, tr2, tr3], axis=1) # สามารถ concat ตรงนี้ได้
            true_range = true_range_df.max(axis=1)
        else:
            true_range = pd.Series(np.nan, index=df.index)

        atr = true_range.rolling(window_atr, min_periods=1).mean()
    else:
        atr = pd.Series(np.nan, index=df.index) # สร้าง Series ว่างถ้าคอลัมน์ไม่ครบ

    # Stochastic Features

    stoch = ta.stoch(high=df["High"], low=df["Low"], close=df["Close"])

    stoch_k_col = 'STOCHk_14_3_3'
    stoch_d_col = 'STOCHd_14_3_3'

    if stoch_k_col in stoch.columns and stoch_d_col in stoch.columns:
        df["STO_cross"] = (stoch[stoch_k_col] > stoch[stoch_d_col]).astype(int) - (stoch[stoch_k_col] < stoch[stoch_d_col]).astype(int)
        df["STO_zone"] = (stoch[stoch_k_col] > 50).astype(int) - (stoch[stoch_k_col] < 50).astype(int)
        df["STO_overbought"] = (stoch[stoch_k_col] > 80).astype(int)
        df["STO_Oversold"] = (stoch[stoch_k_col] < 20).astype(int)

    # ADX Features
    adx = ta.adx(high=df["High"], low=df["Low"], close=df["Close"])

    adx_col = 'ADX_14'
    dmp_col = 'DMP_14'
    dmn_col = 'DMN_14'

    df["ADX_14"] = adx[adx_col]
    df["DMP_14"] = adx[dmp_col]
    df["DMN_14"] = adx[dmn_col]

    df["ADX_Deep"] = (df["ADX_14"] > df["ADX_14"].shift(1)).astype(int) - (df["ADX_14"] < df["ADX_14"].shift(1)).astype(int)

    df["ADX_zone_25"] = (df["ADX_14"] > 25).astype(int)
    df["ADX_zone_15"] = (df["ADX_14"] > 15).astype(int)

    if dmp_col in adx.columns and dmn_col in adx.columns:
        df["ADX_cross"] = (adx[dmp_col] > adx[dmn_col]).astype(int) - (adx[dmp_col] < adx[dmn_col]).astype(int)

    # ATR Feature
    df['ATR'] = atr # ATR Series ที่คำนวณไว้ก่อนหน้า

    df['ATR_Deep'] = (df["ATR"] > df["ATR"].shift(1)).astype(int) - (df["ATR"] < df["ATR"].shift(1)).astype(int)

    df['ATR_ROC_i2'] = (df['ATR'] - df['ATR'].shift(2)) / (df['ATR'] + 1e-10)
    df['ATR_ROC_i4'] = (df['ATR'] - df['ATR'].shift(4)) / (df['ATR'] + 1e-10)
    df['ATR_ROC_i6'] = (df['ATR'] - df['ATR'].shift(6)) / (df['ATR'] + 1e-10)
    df['ATR_ROC_i8'] = (df['ATR'] - df['ATR'].shift(8)) / (df['ATR'] + 1e-10)

    # BB Width Feature
    df['BB_width'] = bb_width # BB_width Series ที่คำนวณไว้ก่อนหน้า

    # SR Features
    lookback_sr = 50
    if all(col in df.columns for col in ['Low', 'High']):
        support = df['Low'].rolling(lookback_sr, min_periods=1).min()
        resistance = df['High'].rolling(lookback_sr, min_periods=1).max()
    else:
        support = pd.Series(np.nan, index=df.index)
        resistance = pd.Series(np.nan, index=df.index)

    df['Support'] = support # 'Low'
    df['Resistance'] = resistance # 'High'

    df['PullBack_Up'] = (df['Resistance'] - df['Close']) / (df['Resistance'] - df['Support'] + 1e-10)
    df['PullBack_Down'] = (df['Close'] - df['Support']) / (df['Resistance'] - df['Support'] + 1e-10)
    # print(df[["DateTime", "Open", "High", "Low", "Close", "Support", "Resistance", "PullBack_Up", "PullBack_Down"]].tail(25))

    # หลีกเลี่ยงการคำนวณที่อาจทำให้เกิด division by zero
    epsilon = 1e-9
    Points = symbol_info[symbol]["Points"]

    df["SL_Buy"] =  np.minimum(
                    df["Open"] - 100 * Points,
                    np.maximum(df["Low_Prev_Min"], df["Support"])
                ) + epsilon
    
    df["SL_Sell"] = np.maximum(
                    df["Open"] + 100 * Points,
                    np.minimum(df["High_Prev_Max"], df["Resistance"])
                ) + epsilon
    
    # สร้างคอลัมน์สัดส่วนของ Buy Sell
    df["Ratio_Buy"] = ((df["Resistance"] - df["Open"]) / (df["Open"] - df["SL_Buy"]))
    df["Ratio_Sell"] = ((df["Open"] - df["Support"]) / (df["SL_Sell"] - df["Open"]))
    # print(df[["DateTime", "Open", "High", "Low", "Close", "Support", "Resistance", "Low_Prev_Min", "High_Prev_Max", "SL_Buy", "SL_Sell", "Ratio_Buy", "Ratio_Sell"]].tail(25))
    # print(df[df['Ratio_Buy'] > 1.00][['Date', 'Time', 'Ratio_Buy']].tail(10))
    # print(df[df['Ratio_Sell'] > 1.00][['Date', 'Time', 'Ratio_Sell']].tail(10))

    # Interaction Features
    interaction_features = {
        'RSI_x_VolumeSpike' : df['RSI14'] * df['Volume_Spike'],
        'EMA_diff_x_ATR' : df['EMA_diff'] * df['ATR'],
        'Momentum5_x_Volatility10' : (df['Close'] - df['Close'].shift(5)) * df['Close'].rolling(10).std(),
        'RSI14_x_BBwidth' : df['RSI14'] * df['BB_width'],
        'MACD_signal_x_ADX' : df['MACD_signal'] * df['ADX_14'],
        'Price_above_EMA50_x_RSI_signal' : df['Price_above_EMA50'] * df['RSI_signal'],
        'RSI14_x_ATR' : df['RSI14'] * df['ATR'],
        'RSI14_x_PriceMove' : df['RSI14'] * df['Price_Move'],
        'EMA50_x_RollingVol5' : df['EMA50'] * df['Rolling_Vol_5'],
        'EMA_diff_x_BBwidth' : df['EMA_diff'] * df['BB_width'],
        'ADX_14_x_ATR' : df['ADX_14'] * df['ATR'],
        'MACD_signal_x_RSI14' : df['MACD_signal'] * df['RSI14'],
        'RSI14_x_StochK' : df['RSI14'] * stoch['STOCHk_14_3_3'] if 'STOCHk_14_3_3' in stoch.columns else np.nan,
        'RSI14_x_StochD' : df['RSI14'] * stoch['STOCHd_14_3_3'] if 'STOCHd_14_3_3' in stoch.columns else np.nan,
        'MACD_line_x_PriceMove' : df['MACD_line'] * df['Price_Move'],
        'ADX_14_x_RollingVol15' : df['ADX_14'] * df['Rolling_Vol_15'],
        'RSI14_x_Volume' : df['RSI14'] * df['Volume'],
        'ATR_x_PriceRange' : df['ATR'] * df['Price_Range'],
        'RSI14_x_PullBack_Up' : df['RSI14'] * df['PullBack_Up'],
        'RSI14_x_PullBack_Down' : df['RSI14'] * df['PullBack_Down'],
        'EMA_diff_x_RSI14' : df['EMA_diff'] * df['RSI14'],
        'ADX_14_x_BBwidth' : df['ADX_14'] * df['BB_width']
        }

    # รวมเข้า DataFrame เดียว
    interaction_df = pd.DataFrame(interaction_features)

    # แก้ไข: รวม interaction_df เข้า df ก่อนเลือกคอลัมน์
    df = pd.concat([df, interaction_df], axis=1)

    timeframe_int = timeframe_info.get(timeframe)  # แปลงเป็น int
    # กำหนด Lag periods ที่ต้องการ (ปรับตามความเหมาะสมของ timeframe)
    if timeframe_int >= 240:  # สำหรับ timeframe ขนาดใหญ่ (H4, D1)
        lags = [1, 2, 3, 5, 10]  # ตัวอย่างสำหรับ daily data
    else:  # สำหรับ timeframe ที่เล็กกว่า (M1, M5, M15, H1)
        lags = [1, 2, 3, 5, 10, 15, 20, 30, 50]  # ตัวอย่างสำหรับ intraday data

    # --- สร้าง Lag Features (ปรับปรุงเพื่อ Performance) ---
    # สร้าง DataFrame เปล่าสำหรับเก็บ Lag Features
    lag_features = pd.DataFrame(index=df.index)

    # Lag Features สำหรับคอลัมน์ราคา/Volume
    price_cols_upper = ['Close', 'Open', 'High', 'Low', 'Volume']
    for col in price_cols_upper:
        if col in df.columns:
            for lag in lags:
                lag_features[f'{col}_Lag_{lag}'] = df[col].shift(lag)
        else:
            print(f"Warning: Price column '{col}' not found in df for Lag Features.")

    # กรองเฉพาะคอลัมน์ที่มีอยู่จริงใน df หรือ DataFrame indicator_features
    # หรือใน Series ที่คำนวณ Indicators หลัก
    existing_indicator_cols_for_lag = [
        'RSI14' if 'RSI14' in df.columns else None,
        'EMA50' if 'EMA50' in df.columns else None,
        'EMA100' if 'EMA100' in df.columns else None,
        'EMA200' if 'EMA200' in df.columns else None,
        'ATR' if 'ATR' in df.columns else None, # หรือ atr.name ถ้า atr เป็น Series
        'BB_width' if 'BB_width' in df.columns else None, # หรือ bb_width.name
        
        # macd_line_col if macd_line_col in macd.columns else None,
        # macd_signal_col if macd_signal_col in macd.columns else None,
        'MACD_12_26_9' if 'MACD_12_26_9' in df.columns else None,
        'MACDs_12_26_9' if 'MACDs_12_26_9' in df.columns else None,

        stoch_k_col if stoch_k_col in stoch.columns else None,
        stoch_d_col if stoch_d_col in stoch.columns else None,
        adx_col if adx_col in adx.columns else None,
        dmp_col if dmp_col in adx.columns else None,
        dmn_col if dmn_col in adx.columns else None
    ]
    existing_indicator_cols_for_lag = [col for col in existing_indicator_cols_for_lag if col is not None] # กรอง None ออก

    # รวม df และ indicator_features (และ Series indicators หลัก) ชั่วคราวเพื่อทำ Lag
    # หรือเข้าถึง Series indicators หลักโดยตรง
    temp_df_for_lag = pd.concat([df[['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100', 'EMA200']],
                                macd, stoch, adx, atr.rename('ATR'), bb_width.rename('BB_width'), support.rename('Support'), resistance.rename('Resistance')], axis=1)

    for indicator in existing_indicator_cols_for_lag:
        # ตรวจสอบว่าคอลัมน์ Indicator มีอยู่จริงใน temp_df_for_lag
        if indicator in temp_df_for_lag.columns:
            for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ indicators
                lag_features[f'{indicator}_Lag_{lag}'] = temp_df_for_lag[indicator].shift(lag)
        else:
            print(f"Warning: Indicator column '{indicator}' not found in combined data for Lag Features.")

    # --- สร้าง Features แบบเปลี่ยนแปลงจาก Lag (Returns, Changes) (ปรับปรุงเพื่อ Performance) ---
    # สร้าง DataFrame เปล่าสำหรับเก็บ Features Returns/Changes
    returns_changes_features = pd.DataFrame(index=df.index)

    for lag in [1, 2, 3, 5]: # ใช้ lag periods ที่ใช้กับ returns/changes
        if 'Close' in df.columns:
            returns_changes_features[f'Close_Return_{lag}'] = df['Close'].pct_change(lag)
        else:
            print(f"Warning: 'Close' column not found for Close_Return_{lag}.")
            returns_changes_features[f'Close_Return_{lag}'] = np.nan

        if 'Volume' in df.columns:
            returns_changes_features[f'Volume_Change_{lag}'] = df['Volume'].diff(lag) / (df['Volume'].shift(lag) + 1e-10)
        else:
            print(f"Warning: 'Volume' column not found for Volume_Change_{lag}.")
            returns_changes_features[f'Volume_Change_{lag}'] = np.nan

    # --- สร้าง Rolling Features (ปรับปรุงเพื่อ Performance) ---
    # สร้าง DataFrame เปล่าสำหรับเก็บ Rolling Features
    rolling_features = pd.DataFrame(index=df.index)

    for window in [3, 5, 10, 20]: # ใช้ window sizes ที่ใช้กับ rolling features
        if 'Close' in df.columns:
            rolling_features[f'Close_MA_{window}'] = df['Close'].rolling(window, min_periods=1).mean().shift(1)
            rolling_features[f'Close_Std_{window}'] = df['Close'].rolling(window, min_periods=1).std().shift(1)
        else:
            print(f"Warning: 'Close' column not found for Close Rolling Features (window {window}).")
            rolling_features[f'Close_MA_{window}'] = np.nan
            rolling_features[f'Close_Std_{window}'] = np.nan

        if 'Volume' in df.columns:
            rolling_features[f'Volume_MA_{window}'] = df['Volume'].rolling(window, min_periods=1).mean().shift(1)
        else:
            print(f"Warning: 'Volume' column not found for Volume_MA_{window}.")
            rolling_features[f'Volume_MA_{window}'] = np.nan

    # ==============================================
    # ต้องการ > บันทึกไฟล์ csv ขั้นตอน feature
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_train = "data_01_features"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_train}.csv" # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(test_data, new_file_name)     # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv
    
    # ✅ บันทึกไฟล์ CSV
    df.to_csv(new_file_path, index=False)
    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    df_combined = pd.concat([df[['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', # ราคาเดิม
                                                'DateTime', 'DayOfWeek', 'Hour', # Features เวลา
                                                'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', # ช่วงเวลา
                                                'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 
                                                'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', # Price Action
                                                'Price_Range', 'Price_Move', 'Price_Strangth', # Price Movement
                                                'Volume_MA20', 'Volume_Spike', # Volume Features
                                                'EMA50', 'EMA100', 'EMA200', 
                                                'EMA_diff', 'MA_Cross', 'Price_above_EMA50', # EMA Features
                                                'Rolling_Vol_5', 'Rolling_Vol_15', # Volatility
                                                'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', # Distance to EMA
                                                'RSI14', # RSI Calculation
                                                'RSI_signal', 'RSI_Overbought', 'RSI_Oversold',
                                                'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8',
                                                'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6',
                                                'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9',
                                                'MACD_line', 'MACD_deep', 'MACD_signal', 
                                                'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold',
                                                'ADX_14', 'DMP_14', 'DMN_14',
                                                'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross',
                                                'ATR',
                                                'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8',
                                                'BB_width',
                                                'Support', 'Resistance',
                                                'PullBack_Up', 'PullBack_Down',
                                                'Ratio_Buy', 'Ratio_Sell',
                                                'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 
                                                'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 
                                                'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 
                                                'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 
                                                'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 
                                                ]], # เลือกคอลัมน์เดิมที่ยังต้องการเก็บไว้ก่อน concat
                                            lag_features, # Lag Features
                                            returns_changes_features, # Returns/Changes Features
                                            rolling_features # Rolling Features
                                        ], axis=1)

    # ==============================================
    # ต้องการ > บันทึกไฟล์ csv ขั้นตอน feature
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_combined = "data_02_combined"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_combined}.csv"             # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(test_data, new_file_name)   # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv
    
    # ✅ บันทึกไฟล์ CSV
    df_combined.to_csv(new_file_path, index=False)
    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    # --- ลบแถวที่มีค่า NaN ที่เกิดจากการสร้าง Indicators และ Features ---
    initial_rows = len(df_combined)
    if not df_combined.empty:
        df = df_combined.dropna() # ตอนนี้ df จะเป็น DF ที่รวม features แล้ว
        print(f"✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป {initial_rows - len(df)} จาก {initial_rows} แถว)")
    else:
        print(f"Warning: df_combined is empty before dropna.")
        return None, None, None, None, None, None

    # ==============================================
    # ต้องการ > บันทึกไฟล์ csv ขั้นตอน feature
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_dropna = "data_03_dropna"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_dropna}.csv"             # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(test_data, new_file_name)   # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv
    
    # ✅ บันทึกไฟล์ CSV
    df.to_csv(new_file_path, index=False)
    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    if df.empty:
        print(f"Warning: No data left in df after dropna.")
        return None, None, None, None, None, None

    # ตัวอย่างการสร้าง model_features (คุณอาจจะต้องปรับปรุง list นี้ให้ตรงกับ features ที่จะใช้จริง)
    model_features = [col for col in df.columns if col not in ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']]
    # ลบคอลัมน์ชั่วคราวหรือคอลัมน์ที่ไม่ใช้เป็น input model ออก
    # model_features = [f for f in model_features if f not in ['RSI_Shift']] # ตัวอย่างลบคอลัมน์ชั่วคราว

    # ตรวจสอบข้อมูลหลังสร้าง Features
    # print(f"\n✅ ข้อมูล df หลังสร้างรวม df_ft_combined")
    # print(df.info())
    # print(df.columns.tolist())
    # print(df)
    # print(df.head())
    # print(df.tail())

    print("\n🔍 ตรวจสอบ Temporal Dependence และคุณสมบัติอนุกรมเวลา")
    is_sorted = df['DateTime'].is_monotonic_increasing
    print(f"- ข้อมูลเรียงตามเวลา: {'ใช่' if is_sorted else 'ไม่'} (ควรเป็น 'ใช่')")
    
    if not is_sorted:
        print("⚠️ เตือน : ข้อมูลไม่เรียงตามเวลา กำลังเรียงข้อมูลใหม่...")
        df = df.sort_values('DateTime')
    
    # ตรวจสอบช่วงเวลาของข้อมูล
    time_diff = df['DateTime'].diff().dropna()
    print(f"- ช่วงเวลาข้อมูล: {df['DateTime'].min()} ถึง {df['DateTime'].max()}")
    print(f"- ระยะเวลารวม: {df['DateTime'].max() - df['DateTime'].min()}")
    print(f"- ช่วงห่างระหว่างบันทึก (เฉลี่ย): {time_diff.mean()}")
    print(f"- ช่วงห่างระหว่างบันทึก (สูงสุด): {time_diff.max()}")
    print(f"- ช่วงห่างระหว่างบันทึก (ต่ำสุด): {time_diff.min()}")
    
    # ตรวจสอบความต่อเนื่องของข้อมูล
    timeframe_int = timeframe_info.get(timeframe)  # แปลงเป็น int
    expected_interval = pd.Timedelta(minutes=timeframe_int)  # ใช้ timeframe จากข้อมูล
    missing_periods = (time_diff > expected_interval * 1.5).sum()  # 1.5 เท่าของช่วงเวลาปกติ
    print(f"- จำนวนช่วงเวลาที่หายไป: {missing_periods} (จากทั้งหมด {len(df)-1} ช่วง)")
    
    if missing_periods > 0:
        print("⚠️ เตือน : พบช่วงเวลาที่หายไปซึ่งอาจส่งผลต่อการวิเคราะห์")
        # สามารถเพิ่มโค้ดสำหรับจัดการข้อมูลที่หายไปที่นี่
    
    # ตรวจสอบ duplicate timestamps
    duplicate_times = df['DateTime'].duplicated().sum()
    print(f"- จำนวน timestamp ที่ซ้ำกัน: {duplicate_times}")
    
    if duplicate_times > 0:
        print("⚠️ เตือน : พบ timestamp ที่ซ้ำกัน กำลังจัดการด้วยการเฉลี่ย...")
        df = df.groupby('DateTime').mean().reset_index()  # หรือใช้วิธีอื่นที่เหมาะสม

    # ตรวจสอบ Stationarity ของราคา ตรวจสอบว่าข้อมูลนิ่งหรือไม่ (stationary) ด้วย ADF test และแสดงผลลัพธ์
    print("\n🔍 ตรวจสอบ Stationarity ของข้อมูล:")
    is_close_stationary = check_stationarity(df['Close'], 'Close')
    is_returns_stationary = check_stationarity(df['Close'].pct_change(), 'Returns')

    # สร้างรายงานสรุป
    temporal_report = {
        'symbol': symbol,
        'timeframe': timeframe,
        'data_period': f"{df['DateTime'].min()} to {df['DateTime'].max()}",
        'total_bars': len(df),
        'missing_periods': missing_periods,
        'duplicate_timestamps': duplicate_times,
        'is_stationary_close': is_close_stationary,
        'is_stationary_returns': is_returns_stationary,
        'average_time_gap': str(time_diff.mean())
        # 'hourly_win_rate_correlation': hour_win_rate.corr(pd.Series(np.arange(24)))  # ตรวจสอบความสัมพันธ์กับชั่วโมง
    }

    report_path = os.path.join(results_dir, f"{timeframe}_{symbol}_temporal_report.json")
    with open(report_path, 'w') as f:
        json.dump(temporal_report, f, indent=2, default=str)
    print(f"\n💾 บันทึกรายงาน Temporal Analysis ที่: {report_path}")

    # --- จัดการ Missing Values ---
    initial_rows = len(df)
    print(f"\n📌 จำนวน Missing Values หลังการประมวลผลเบื้องต้น:")
    # แสดงเฉพาะคอลัมน์ที่มีค่าว่างและจำนวน > 0
    print(df.isnull().sum()[df.isnull().sum() > 0])

    # # จัดการ Missing Values ใน Support และ Resistance
    df = df.copy()
    df.loc[:, 'Support'] = df['Low'].rolling(lookback_sr).min().bfill()
    df.loc[:, 'Resistance'] = df['High'].rolling(lookback_sr).max().bfill()

    # ตรวจสอบ Missing Values หลังการประมวลผล
    missing_values_after_processing = df.isnull().sum()
    print("\n📌 จำนวน Missing Values หลังการประมวลผล:")
    print(missing_values_after_processing[missing_values_after_processing > 0])
    
    print(df)
    check_data_quality(df, file, symbol, timeframe)

    print("\n🔍 ตรวจสอบข้อมูลก่อนสร้าง trade cycles:")
    print(f"ช่วงเวลาข้อมูล: {df['Date'].min()} ถึง {df['Date'].max()}")
    print(f"ค่าเฉลี่ย Close: {df['Close'].mean():.2f}")
    print(f"ค่า EMA50 ล่าสุด: {df['EMA50'].iloc[-1]:.2f}")
    print(f"ค่า RSI14 ล่าสุด: {df['RSI14'].iloc[-1]:.2f}")
    print(f"ค่า MACD ล่าสุด: {df['MACD_12_26_9'].iloc[-1]:.2f}")
    
    # ตรวจสอบค่า DayOfWeek แสดงตัวอย่างวันที่มีการบันทึก
    print(f"Unique values in df['DayOfWeek']: {df['DayOfWeek'].unique()}")
    
    # 4. สร้าง trade cycles
    print("\n🔍 กำลังสร้าง trade cycles...")
    model_name_to_use = model_name # ชื่อโมเดลที่ใช้ในการบันทึก/โหลด
    symbol_to_use = symbol
    timeframe_to_use = timeframe

    # โหลดรายชื่อ features ที่โมเดลใช้ - รองรับทั้ง Single และ Multi-Model Architecture
    model_features = None
    model_features_path = None

    # สำหรับ Multi-Model Architecture - ใช้ features จาก scenario แรก (trend_following)
    scenario_dir = f"{test_folder}/models/trend_following"
    model_features_path = os.path.join(scenario_dir, f"{str(timeframe_to_use).zfill(3)}_{symbol_to_use}_features.pkl")
    print(f"🔄 Multi-Model: กำลังโหลด features จาก {model_features_path}")

    if model_features_path and os.path.exists(model_features_path):
        try:
            model_features = joblib.load(model_features_path)
            print(f"\n✅ โหลดรายชื่อ Features ที่ Model ใช้สำเร็จ: {len(model_features)} features")

            # แสดงเฉพาะ 10 features แรก
            features_to_show = model_features[:10] if len(model_features) > 10 else model_features
            for i, feat in enumerate(features_to_show, 1):
                print(f"{i}. {feat}")
            if len(model_features) > 10:
                print(f"... และอีก {len(model_features) - 10} features")
            print("\nFirst 5 rows of df:")
            print(df.head()) # ดูหน้าตาข้อมูลและชื่อคอลัมน์

        except Exception as e:
            print(f"⚠️ เกิดข้อผิดพลาด ขณะโหลดรายชื่อ Features: {str(e)}")
            model_features = None # ใช้ None ถ้าโหลดไม่ได้
    else:
        print(f"\n⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: {model_features_path}")
        model_features = None # ใช้ None ถ้า⚠️ ไม่พบไฟล์

    # ตรวจสอบ ก่อนเข้า create trade cycles with model
    print(f"🔍 ตรวจสอบ columns")
    print(df.columns.tolist())

    # --- โหลด entry condition ที่ดีที่สุดจากรอบก่อน (ถ้ามี) ---
    best_entry_path = os.path.join(results_dir, f"{timeframe}_{symbol}_best_entry.pkl")
    best_entry_name = None
    if os.path.exists(best_entry_path):
        try:
            with open(best_entry_path, "rb") as f:
                best_entry_info = pickle.load(f)
            best_entry_name = best_entry_info.get("entry_name", None)
            print(f"\n⭐ ใช้ entry condition ที่ดีที่สุดจากรอบก่อน: {best_entry_name}")
        except Exception as e:
            print(f"\n⚠️ เกิดข้อผิดพลาดขณะโหลด best_entry: {e}")
            best_entry_name = None

    # สำหรับ Multi-Model Architecture ใช้ entry conditions ใหม่
    if best_entry_name is not None and best_entry_name in entry_conditions:
        entry_func = entry_conditions[best_entry_name]
        print(f"🔎 ใช้ entry_func (Multi-Model): {best_entry_name}")
    else:
        # ใช้ scenario แรกเป็น default สำหรับ Multi-Model
        first_scenario = list(entry_conditions.keys())[0]
        entry_func = entry_conditions[first_scenario]
        print(f"🔎 ใช้ entry_func (Multi-Model default): {first_scenario}")

    # เรียกใช้ฟังก์ชันสร้างรายการซื้อขายพร้อม Model ช่วยตัดสินใจ
    # df_processed ในที่นี้ควรเป็น Dataframe ที่เตรียม Features ครบถ้วนแล้ว

    if entry_func is not None:
        # โหลดโมเดลทั้ง 2 scenarios สำหรับ Multi-Model Architecture
        print(f"🔄 โหลดโมเดล Multi-Model Architecture สำหรับ {symbol_to_use} M{timeframe_to_use}")
        scenario_models = load_scenario_models(symbol_to_use, timeframe_to_use)

        if scenario_models and len(scenario_models) > 0:
            print(f"✅ โหลดโมเดล Multi-Model สำเร็จ: {list(scenario_models.keys())}")

            # ใช้ฟังก์ชัน Multi-Model
            trade_df, cycle_stats = create_trade_cycles_with_multi_model(
                df,
                scenario_models=scenario_models,
                model_features=model_features,
                rsi_level=input_rsi_level_in,
                rsi_level_out=input_rsi_level_out,
                stop_loss_atr_multiplier=input_stop_loss_atr,
                take_profit_stop_loss_ratio=input_take_profit,
                symbol=symbol_to_use,
                timeframe=timeframe_to_use,
                nBars_SL=nBars_SL,
                model_confidence_threshold=confidence_threshold,
                entry_condition_func=entry_func,
                entry_condition_name=best_entry_name
            )
        else:
            print("❌ ไม่สามารถโหลดโมเดล Multi-Model ได้ - ใช้ Technical Analysis แทน")
            trade_df, cycle_stats = create_trade_cycles_with_model(
                df,
                trained_model=None,
                scaler=None,
                model_features=None,
                rsi_level=input_rsi_level_in,
                rsi_level_out=input_rsi_level_out,
                stop_loss_atr_multiplier=input_stop_loss_atr,
                take_profit_stop_loss_ratio=input_take_profit,
                symbol=symbol_to_use,
                timeframe=timeframe_to_use,
                nBars_SL=nBars_SL,
                model_confidence_threshold=confidence_threshold,
                entry_condition_func=entry_func,
                entry_condition_name=best_entry_name
            )
    else:
        print("❌ ไม่มี entry_func ที่จะใช้สำหรับ backtest/production")
        # สร้าง empty trade_df เพื่อป้องกัน error
        trade_df = pd.DataFrame()
        cycle_stats = {
            'total_trades': 0,
            'win_rate': 0.0,
            'total_profit': 0.0,
            'avg_profit_per_trade': 0.0,
            'expectancy': 0.0
        }

    # ==============================================
    # ต้องการ > บันทึกไฟล์ csv ขั้นตอน feature
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_tradedf = "data_04_tradedf"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_tradedf}.csv" # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(test_data, new_file_name)       # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv
    
    # ✅ บันทึกไฟล์ CSV
    trade_df.to_csv(new_file_path, index=False)
    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    # print(f"\n✅ ข้อมูล df หลังจาก create trade cycles with model")
    # print("df columns:", df.columns.tolist())
    # print("model_features:", model_features)
    # print("จำนวน columns ใน df:", len(df.columns))
    # print("จำนวน features ที่โมเดลใช้:", len(model_features))

    # print(f"\n✅ ตรวจสอบ features หลังเรียก create trade cycles with model")
    # for i, feat in enumerate(model_features, 1):
    #     print(f"{i}. {feat}")

    # แสดงข้อมูลก่อนตรวจสอบ
    print("\n📌 ข้อมูลก่อนตรวจสอบ:")
    print(f"จำนวนแถวข้อมูลทั้งหมด: {len(df)} ตัวอย่างข้อมูล df")
    print(df.head() if not df.empty else "❌ ไม่มีข้อมูล การซื้อขาย")
    print(f"จำนวนการซื้อขายที่พบ: {len(trade_df)} ตัวอย่างข้อมูล trade_df")
    print(trade_df.head() if not trade_df.empty else "❌ ไม่มีข้อมูล การซื้อขาย")

    print("\n📊 สถิติการซื้อขาย:")
    print(f"{'='*40}")
    print(f"{'ประเภท':<20}{'ค่าสถิติ':<20}")
    print(f"{'-'*40}")

    # วิเคราะห์ประสิทธิภาพการซื้อขาย (จะได้ stats ที่มี buy, sell, buy_sell)
    stats = analyze_trade_performance(trade_df)

    # แสดงสถิติหลัก
    if 'buy' in stats and 'sell' in stats and 'buy_sell' in stats:
        print("📈 สถิติสำหรับ Buy Trades:")
        print(f"{'Win%':<20}{stats['buy'].get('win_rate', 0):<20.2f}")
        print(f"{'Expectancy':<20}{stats['buy'].get('expectancy', 0):<20.2f}")

        print("📈 สถิติสำหรับ Sell Trades:")
        print(f"{'Win%':<20}{stats['sell'].get('win_rate', 0):<20.2f}")
        print(f"{'Expectancy':<20}{stats['sell'].get('expectancy', 0):<20.2f}")

        print("📈 สถิติสำหรับ Buy_sell Trades:")
        print(f"{'Win%':<20}{stats['buy_sell'].get('win_rate', 0):<20.2f}")
        print(f"{'Expectancy':<20}{stats['buy_sell'].get('expectancy', 0):<20.2f}")
    else:
        print("⚠️ ไม่พบ สถิติหลัก")

    print(f"{'='*40}")

    # แสดงสถิติรายวัน (ใช้ cycle_stats ที่ได้จาก create_trade_cycles)
    if 'day_stats' in cycle_stats:
        print("\n📊 สถิติรายวัน:")
        print(f"{'วัน':<10}{'Win Rate (%)':<15}{'จำนวนการซื้อขาย':<20}")
        print(f"{'-'*45}")
        day_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        for day, data in cycle_stats['day_stats'].items():
            if data.get('total', 0) > 0:  # เพิ่มเงื่อนไขตรวจสอบจำนวนการซื้อขาย
                win_rate = (data.get('win', 0) / data.get('total', 1)) * 100 if data.get('total', 0) > 0 else 0
                day_name = day_names[day] if isinstance(day, int) and 0 <= day < 7 else str(day)
                print(f"{day_name:<10}{win_rate:<15.2f}{data.get('total', 0):<20}")
        if not any(data.get('total', 0) > 0 for data in cycle_stats['day_stats'].values()):
            print("ไม่มีข้อมูล สถิติรายวัน") # แสดงข้อความหากไม่มีข้อมูล 
    else:
        print("⚠️ ไม่พบ สถิติรายวัน")

    print(f"{'='*40}")
    
    # แสดงสถิติรายชั่วโมง (ใช้ cycle_stats ที่ได้จาก create_trade_cycles)
    if 'hour_stats' in cycle_stats:
        print("\n📊 สถิติรายชั่วโมง:")
        print(f"{'ชั่วโมง':<10}{'Win Rate (%)':<15}{'จำนวนการซื้อขาย':<20}")
        print(f"{'-'*45}")
        has_data = False # เพิ่มตัวแปรตรวจสอบว่ามีข้อมูลหรือไม่
        for hour, data in sorted(cycle_stats['hour_stats'].items()):
            if data.get('total', 0) > 0:  # เพิ่มเงื่อนไขตรวจสอบจำนวนการซื้อขาย
                win_rate = (data.get('win', 0) / data.get('total', 1)) * 100 if data.get('total', 0) > 0 else 0
                print(f"{hour:<10}{win_rate:<15.2f}{data.get('total', 0):<20}")
                has_data = True
        if not has_data:
            print("ไม่มีข้อมูล สถิติรายชั่วโมง") # แสดงข้อความหากไม่มีข้อมูล 
    else:
        print("⚠️ ไม่พบ สถิติรายชั่วโมง")

    print(f"{'='*40}")
    
    if trade_df.empty:
        print("\n⚠️ ไม่มีข้อมูล การซื้อขายที่ตรงตามเงื่อนไข")
        print("ℹ️ สาเหตุอาจมาจาก:")
        print("- เงื่อนไขการซื้อขายเข้มงวดเกินไป")
        print("- ข้อมูลไม่เหมาะสมกับกลยุทธ์")
        print("- ช่วงเวลาที่วิเคราะห์ไม่มีสัญญาณซื้อขาย")
        return None, None, None, None, None, None
    
    # เพิ่มการตรวจสอบว่า trade_df เป็น DataFrame และไม่ว่างเปล่า
    if not isinstance(trade_df, pd.DataFrame) or trade_df.empty:
        print("⚠️ ไม่มีข้อมูล การซื้อขายที่ตรงตามเงื่อนไข")
        return None, None, None, None, None, None

    # 5. รวม features กับ trade data
    print("\n🔍 กำลังรวม features กับ trade data...")

    # print("🔍 ทดลองพิมพ์ trade_df")
    # print(trade_df)

    # print("\n🔍 ทดลองพิมพ์ df")
    # print(df)
    
    # แปลงรูปแบบวันที่ให้ตรงกันก่อน merge
    # trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time'], format='%Y.%m.%d %H:%M')
    # df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])

    # --- START: FIX LOOKAHEAD BIAS & TIME FEATURES ---
    # คุณสังเกตได้ถูกต้องครับ การดึงข้อมูล feature ณ เวลาที่เข้าเทรด (Entry Time)
    # ถือเป็น "Lookahead Bias" เพราะการตัดสินใจเข้าเทรดเกิดขึ้นโดยใช้ข้อมูลจาก "แท่งก่อนหน้า"
    # วิธีแก้คือ เราจะปรับเวลาที่ใช้ค้นหาข้อมูล feature ไปก่อนหน้า Entry Time เล็กน้อย
    # เพื่อให้ pd.merge_asof ดึงข้อมูลจากแท่งที่ถูกต้อง (แท่งก่อนเข้าเทรด)

    # 1. สร้างคอลัมน์ DateTime ในทั้งสอง DataFrame
    trade_df['Entry_DateTime'] = pd.to_datetime(trade_df['Entry Time'], format='%Y.%m.%d %H:%M:%S')
    df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'], format='%Y.%m.%d %H:%M:%S')

    # 2. สร้าง "คีย์" สำหรับการ merge โดยการเลื่อนเวลาของ trade_df ไปข้างหลังเล็กน้อย
    trade_df['Merge_Key_Time'] = trade_df['Entry_DateTime'] - pd.Timedelta('5min')

    # ตรวจสอบเวลาที่สร้างขึ้น
    print("\nตัวอย่าง Entry_DateTime ใน trade_df:", trade_df['Entry_DateTime'].head())
    print("ตัวอย่าง Merge_Key_Time ที่จะใช้ join:", trade_df['Merge_Key_Time'].head())
    print("ตัวอย่าง DateTime ใน df:", df['DateTime'].head())

    # 3. กำหนดคอลัมน์ feature ที่จะนำมารวม
    columns_to_merge = [
        # Indicator
        'DateTime', 'DayOfWeek', 'Hour', 
        'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 
        'Bar_CLp', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 
        'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 
        'Price_Range', 'Price_Move', 'Price_Strangth', 
        'Volume_MA20', 'Volume_Spike', 
        'EMA50', 'EMA100', 'EMA200', 
        'EMA_diff', 'MA_Cross', 'Price_above_EMA50', 
        'Rolling_Vol_5', 'Rolling_Vol_15', 
        'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 
        'RSI14', 
        'RSI_signal', 'RSI_Overbought', 'RSI_Oversold', 
        'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 
        'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 
        'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 
        'MACD_line', 'MACD_deep', 'MACD_signal', 
        'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 
        'ADX_14', 'DMP_14', 'DMN_14', 
        'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 
        'ATR', 
        'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 
        'BB_width', 
        'Support', 'Resistance', 
        'PullBack_Up', 'PullBack_Down', 
        'Ratio_Buy', 'Ratio_Sell',

        # Interaction Features
        'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 
        'Price_above_EMA50_x_RSI_signal', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 
        'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 
        'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_Up', 'RSI14_x_PullBack_Down', 
        'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 

        # Lag Features สำหรับ Price Lags
        'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 
        'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 
        'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 
        'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 

        # Lag Features สำหรับ Volume Lags
        'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 

        # Lag Features สำหรับ Indicator Lags
        'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 
        'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 
        'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 
        'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 
        'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 
        'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 
        'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 
        'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 
        'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 
        'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 
        'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 
        'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 
        'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 

        # Return and Change Features
        'Close_Return_1', 'Volume_Change_1', 
        'Close_Return_2', 'Volume_Change_2', 
        'Close_Return_3', 'Volume_Change_3', 
        'Close_Return_5', 'Volume_Change_5', 

        # Moving Averages and Std
        'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 
        'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 
        'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 
        'Close_MA_20', 'Close_Std_20', 'Volume_MA_20'
    ]
    
    # กรองคอลัมน์ที่ต้องการ Merge เฉพาะคอลัมน์ที่มีอยู่ใน df จริงๆ
    available_columns_to_merge = [col for col in columns_to_merge if col in df.columns]
    print(f"🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ {len(available_columns_to_merge)} features : {available_columns_to_merge}")

    # 4. ทำการ merge โดยใช้ "Merge_Key_Time" เป็นตัวเชื่อม
    trade_df = pd.merge_asof(
        trade_df.sort_values('Merge_Key_Time'), # <--- ใช้คีย์เวลาที่ปรับแล้ว
        df.sort_values('DateTime')[available_columns_to_merge], # ใช้รายการคอลัมน์ที่กรองแล้ว
        left_on='Merge_Key_Time', # <--- ใช้คีย์เวลาที่ปรับแล้ว
        right_on='DateTime',
        direction='backward' # ดึงข้อมูลล่าสุดที่ "ไม่เกิน" Merge_Key_Time
    )

    # --- START: FIX TIME-BASED FEATURE INCONSISTENCY ---
    # หลังจาก merge แล้ว เราต้องใช้ 'DateTime' (จากแท่ง i-1) ที่ได้มาในการคำนวณ feature ด้านเวลา
    # ไม่ใช่ 'Entry_DateTime' (จากแท่ง i) เพื่อให้ feature ทั้งหมดมาจากจุดเวลาเดียวกัน
    print("🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)")
    trade_df['DayOfWeek'] = trade_df['DateTime'].dt.dayofweek
    trade_df['Hour'] = trade_df['DateTime'].dt.hour
    trade_df['IsWeekend'] = (trade_df['DayOfWeek'] >= 5).astype(int)
    trade_df['IsMorning'] = ((trade_df['Hour'] >= 8) & (trade_df['Hour'] < 12)).astype(int)
    trade_df['IsAfternoon'] = ((trade_df['Hour'] >= 12) & (trade_df['Hour'] < 16)).astype(int)
    trade_df['IsEvening'] = ((trade_df['Hour'] >= 16) & (trade_df['Hour'] < 20)).astype(int)
    trade_df['IsNight'] = ((trade_df['Hour'] >= 20) | (trade_df['Hour'] < 4)).astype(int)

    # 5. ลบคอลัมน์ที่ไม่จำเป็นออก
    # ตอนนี้สามารถลบ 'DateTime' (ที่ใช้คำนวณ), 'Merge_Key_Time', และ 'Entry_DateTime' ได้
    trade_df.drop(['DateTime', 'Merge_Key_Time', 'Entry_DateTime'], axis=1, inplace=True, errors='ignore')
    # --- END: FIX TIME-BASED FEATURE INCONSISTENCY ---

    # ตรวจสอบหลัง merge
    missing_values = trade_df.isnull().sum()
    has_missing = (missing_values > 0).any()

    print("\n📌 ตรวจสอบ Missing Values หลัง Merge:")
    if has_missing:
        print("พบ Missing Values ในคอลัมน์ต่อไปนี้:")
        print(missing_values[missing_values > 0])
    else:
        print("✅ ไม่พบ Missing Values ใน DataFrame")
    
    # แสดงข้อมูลหลังรวม features
    print("\n📌 ข้อมูลหลังรวม features:")
    print(f"จำนวนแถว: {len(trade_df)}")
    print("ตัวอย่างคอลัมน์ใหม่ 5 แถว:")
    print(trade_df[['Entry Time', 'Hour', 'DayOfWeek', 'EMA50', 'RSI14']].head())

    # เพิ่มใน features ที่จะใช้สำหรับโมเดล >> เนื่องจากใช้การคัดเลือกอัตโนมัติ ดูความสัมพันธ์กับ Target
    features = []
    
    # 6. สร้าง target variable
    print("\n🔍 กำลังสร้าง target variable...")
    
    # ตรวจสอบว่าคอลัมน์ Profit มีอยู่และเป็นตัวเลข
    if 'Profit' not in trade_df.columns or not pd.api.types.is_numeric_dtype(trade_df['Profit']):
        print("⚠️ ไม่พบ คอลัมน์ Profit หรือไม่ใช่ตัวเลข ไม่สามารถสร้าง Target ได้")
        return None, None, None, None, None, None
    
    # ==============================================
    # ต้องการ > บันทึกไฟล์ csv ขั้นตอน feature
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_merge = "data_05_merge"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_merge}.csv" # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(test_data, new_file_name)     # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv
    
    # ✅ บันทึกไฟล์ CSV
    trade_df.to_csv(new_file_path, index=False)
    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    # เพิ่มใน features ที่จะใช้สำหรับโมเดล >> เนื่องจากใช้การคัดเลือกอัตโนมัติ ดูความสัมพันธ์กับ Target
    features = []
    
    # 6. สร้าง target variable
    print("\n🔍 กำลังสร้าง target variable...")
    
    # ตรวจสอบว่าคอลัมน์ Profit มีอยู่และเป็นตัวเลข
    if 'Profit' not in trade_df.columns or not pd.api.types.is_numeric_dtype(trade_df['Profit']):
        print("⚠️ ไม่พบ คอลัมน์ Profit หรือไม่ใช่ตัวเลข ไม่สามารถสร้าง Target ได้")
        return None, None, None, None, None, None
    
    # ==============================================
    # ต้องการ > บันทึกไฟล์ csv ขั้นตอน feature
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_merge = "data_05_merge"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_merge}.csv" # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(test_data, new_file_name)     # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv
    
    # ✅ บันทึกไฟล์ CSV
    trade_df.to_csv(new_file_path, index=False)
    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    trade_df = process_trade_targets(trade_df, symbol, timeframe)

    # ==============================================
    # ต้องการ > บันทึกไฟล์ csv ขั้นตอน feature
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_target = "data_06_target"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_target}.csv" # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(test_data, new_file_name)     # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv
    
    # ✅ บันทึกไฟล์ CSV
    trade_df.to_csv(new_file_path, index=False)
    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    print("\n📌 ข้อมูลหลังสร้าง target variable:")
    print(f"จำนวนแถว: {len(trade_df)}")
    print("ตัวอย่างคอลัมน์ใหม่ 5 แถว:")
    print(trade_df[['Entry Time', 'EMA50', 'EMA100', 'RSI14']].head())
    
    # แสดงข้อมูล target
    print("\n📌 ข้อมูลหลังสร้าง target:")
    if 'Target' in trade_df.columns:
        print("การกระจายของ Target:")
        print(trade_df['Target'].value_counts())
    else:
        print("⚠️ ไม่พบ คอลัมน์ Target ในข้อมูล")
    
    # เพิ่มการตรวจสอบว่า trade_df ไม่ว่างเปล่าและมีคอลัมน์ Target
    if trade_df.empty or 'Target' not in trade_df.columns:
        print("\n⚠️ ไม่สามารถสร้าง Target ได้ >> ออกจาก load and process data")
        return None, None, None, None, None, None
    
    # ==============================================
    # ส่วนปรับปรุง: ใช้ฟังก์ชัน select features แทนกระบวนการเดิม
    # ==============================================

    # # ทำความสะอาดข้อมูล
    # trade_df = trade_df.dropna()  # ลบแถวที่มี missing values
    
    # # แก้ไขชื่อ features ให้สอดคล้องกัน
    # trade_df = trade_df.rename(columns={'Entry Price': 'Entry_Price', 'Exit Price': 'Exit_Price'})

    # # เรียกใช้ฟังก์ชัน select features
    # print("\n🔍 เริ่มกระบวนการเลือก Features...")
    # features = select features(trade_df)

    # ส่วนเรียกใช้ select features ใน load and process data
    # ... (โค้ดส่วนอื่นๆ ใน load and process data ก่อนเรียก select features)
    # ทำความสะอาดข้อมูล (ควรทำก่อน select features เพื่อให้การคำนวณ correlation แม่นยำ)
    trade_df = trade_df.dropna() # ลบแถวที่มี missing values ที่เกิดจากการ merge หรือคำนวณ

    # แก้ไขชื่อ features ให้สอดคล้องกัน (ถ้าจำเป็น)
    # trade_df = trade_df.rename(columns={'OldName': 'NewName'}) # ตรวจสอบว่าชื่อคอลัมน์หลัง merge เป็นอย่างไร

    # ตรวจสอบจำนวนข้อมูลหลัง dropna
    if trade_df.empty:
        print("⚠️ ข้อมูล trade_df ว่างเปล่าหลังจากจัดการ Missing Values ไม่สามารถเลือก Features ได้")
        return None, None, None, None, None, None

    # เรียกใช้ฟังก์ชัน select features
    print("\n🔍 เริ่มกระบวนการเลือก Features...")
    features = select_features(trade_df, symbol, timeframe) # เรียกใช้ฟังก์ชันที่แก้ไขแล้ว

    # แสดงสรุป features ที่จะใช้ (เฉพาะ 15 ตัวแรก)
    print("\n📌 สรุป Features ที่จะใช้สำหรับโมเดล:")
    print(f"จำนวน Features: {len(features)}")
    features_to_show = features[:15] if len(features) > 15 else features
    for i, feat in enumerate(features_to_show, 1):
        print(f"{i}. {feat}")
    if len(features) > 15:
        print(f"... และอีก {len(features) - 15} features")

    # ==============================================
    # ส่วนตรวจสอบ Class Imbalance
    # ==============================================
    
    print("\n🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...")

    # 1. ตรวจสอบการกระจายของ Target
    if 'Target' in trade_df.columns:
        target_dist = trade_df['Target'].value_counts(normalize=True)
        print("\n📊 การกระจายของ Target:")
        print(target_dist)
        
        # ตรวจสอบว่ามีทั้งสองคลาสหรือไม่
        if len(target_dist) < 2:
            print("⚠️ มีเพียงคลาสเดียวใน Target ไม่สามารถฝึกโมเดลได้")
            return None, None, None, None, None, None
            
        # ตรวจสอบ Class Imbalance
        imbalance_ratio = target_dist.min() / target_dist.max()
        print(f"อัตราส่วน Class Imbalance: {imbalance_ratio:.2f}")
        
        if imbalance_ratio < 0.2:  # ถ้าคลาส minority มีน้อยกว่า 20% ของคลาส majority
            print("⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)")

    # 2. ตรวจสอบจำนวนข้อมูลขั้นต่ำ
    min_samples = 50  # กำหนดค่าต่ำสุดตามความเหมาะสม
    if len(trade_df) < min_samples:
        print(f"⚠️ ข้อมูลมีน้อยเกินไป ({len(trade_df)} แถว) ขั้นต่ำที่ต้องการ: {min_samples} แถว")
        return None, None, None, None, None, None

    # 3. ตรวจสอบ missing values
    missing_values = trade_df[features].isnull().sum().sum()
    if missing_values > 0:
        print(f"⚠️ พบ missing values {missing_values} ค่า ใน features")
        # แสดงคอลัมน์ที่มี missing values
        print("คอลัมน์ที่มี missing values:")
        print(trade_df[features].isnull().sum()[trade_df[features].isnull().sum() > 0])
    else:
        print("✅ ไม่พบ missing values ใน features")

    # 4. ตรวจสอบค่าผิดปกติใน features
    print("\n📊 สถิติพื้นฐานของ features:")
    print(trade_df[features].describe().transpose())

    # 5. ตรวจสอบ correlation สูงระหว่าง features
    print("\n🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...")
    try:
        corr_matrix = trade_df[features].corr().abs()
        
        # สร้าง upper triangle matrix แบบ boolean
        upper = np.triu(np.ones(corr_matrix.shape, dtype=bool), k=1)
        
        # นับจำนวนคู่ที่มี correlation สูง
        high_corr = (corr_matrix.where(upper) > 0.8).sum().sum()
        
        if high_corr > 0:
            print(f"⚠️ พบ {high_corr} คู่ features ที่มีความสัมพันธ์สูง (>0.8)")
            
            # แสดงคู่ features ที่มีความสัมพันธ์สูง
            high_corr_pairs = corr_matrix.stack()[
                (corr_matrix.stack() > 0.8) & 
                (corr_matrix.stack() < 1.0)  # ไม่รวมความสัมพันธ์กับตัวเอง
            ].reset_index()
            
            high_corr_pairs.columns = ['Feature 1', 'Feature 2', 'Correlation']
            print("\nคู่ features ที่มีความสัมพันธ์สูง:")
            print(high_corr_pairs.sort_values('Correlation', ascending=False).to_string(index=False))
        else:
            print("✅ ไม่พบ features ที่มีความสัมพันธ์สูงเกินไป")
            
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะตรวจสอบความสัมพันธ์ระหว่าง features: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # ==============================================
    # แบ่งข้อมูลเป็น train/val/test หลังจากเตรียม features, สร้าง target, และตรวจสอบข้อมูลเสร็จแล้ว
    # ==============================================
    
    print("\n🔍 กำลังแบ่งข้อมูลเป็น train/val/test...")
    if len(trade_df) < 10:
        print(f"\n⚠️ ข้อมูลใน trade_df มีน้อยเกินไป ({len(trade_df)} แถว)")
        print("ตัวอย่างข้อมูลสุดท้าย 5 แถว:")
        print(trade_df.tail())
        return None, None, None, None, None, None

    # เรียงข้อมูลตามเวลา (หากยังไม่ได้เรียง) เพื่อให้การแบ่งข้อมูลไม่เกิด data leakage (ข้อมูลอนาคตไปอยู่ใน train)
    trade_df = trade_df.sort_values('Entry Time')
    
    # แบ่งตามสัดส่วนเวลา
    train_size = int(0.6 * len(trade_df))  # 60% สำหรับฝึก
    val_size = int(0.2 * len(trade_df))    # 20% สำหรับ validation
    
    # แบ่งข้อมูล
    train = trade_df.iloc[:train_size]
    val = trade_df.iloc[train_size:train_size + val_size]
    test = trade_df.iloc[train_size + val_size:]
    
    # ตรวจสอบการกระจายของ Target ในแต่ละชุด
    print("\nการกระจายของ Target ในชุดข้อมูล:")
    # เลือก target column ตาม configuration
    target_column = "Target_Multiclass" if "Target_Multiclass" in train.columns else "Target"

    print(f"\n📊 ใช้ Target Column: {target_column}")
    print("Train:", train[target_column].value_counts(normalize=True))
    print("Val:", val[target_column].value_counts(normalize=True))
    print("Test:", test[target_column].value_counts(normalize=True))

    # กรอง Target columns ออกจาก features ก่อนแยกข้อมูล (เพื่อป้องกัน data leakage)
    excluded_columns = ['Target', 'Target_Multiclass', 'Date', 'Time', 'DateTime', 'Entry Time', 'Exit Time']
    clean_features = [f for f in features if f not in excluded_columns]

    if len(clean_features) != len(features):
        removed_features = [f for f in features if f in excluded_columns]
        print(f"🔧 กรอง features ที่ไม่ควรใช้: {removed_features}")
        print(f"📊 Features ที่ใช้จริง: {len(clean_features)} features (จากเดิม {len(features)})")
        features = clean_features  # อัปเดต features list

    # แยก features และ target
    X_train, y_train = train[features], train[target_column]
    X_val, y_val = val[features], val[target_column]
    X_test, y_test = test[features], test[target_column]

    # ==============================================

    # รวม X และ y กลับมาเป็น DataFrame เดียวก่อนบันทึก
    train_export = X_train.copy()
    train_export[target_column] = y_train

    val_export = X_val.copy()
    val_export[target_column] = y_val

    test_export = X_test.copy()
    test_export[target_column] = y_test

    # บันทึกเป็นไฟล์ CSV
    # train_export.to_csv("train_data.csv", index=False)
    # val_export.to_csv("val_data.csv", index=False)
    # test_export.to_csv("test_data.csv", index=False)

    # ==============================================
    # ต้องการ > บันทึกไฟล์ csv ขั้นตอน feature
    suffix_train = "data_07a_train"
    new_file_name = f"{timeframe}_{symbol}_{suffix_train}.csv"
    new_file_path = os.path.join(test_data, new_file_name)
    train_export.to_csv(new_file_path, index=False)

    suffix_train = "data_07b_val"
    new_file_name = f"{timeframe}_{symbol}_{suffix_train}.csv"
    new_file_path = os.path.join(test_data, new_file_name)
    val_export.to_csv(new_file_path, index=False)

    suffix_train = "data_07c_test"
    new_file_name = f"{timeframe}_{symbol}_{suffix_train}.csv"
    new_file_path = os.path.join(test_data, new_file_name)
    test_export.to_csv(new_file_path, index=False)

    print("✅ บันทึกไฟล์ train_data.csv, val_data.csv, test_data.csv เรียบร้อย")
    # ==============================================

    # === หา optimal thresholds จาก validation set ===
    time_filter_path = f"{test_folder}/thresholds/{timeframe}_{symbol}_time_filters.pkl"
    analyze_time_filters(
        test,  # หรือ val แล้วแต่ต้องการ
        min_win_rate=0.30,
        min_expectancy=0.0,
        save_path=time_filter_path,
        symbol=symbol
    )

    # === หา optimal nBars SL ===
    # ควรใช้ validation set (val_idx_start:val_idx_end) เพื่อหา optimal nBars SL
    # แล้วนำค่า optimal ที่ได้ไปใช้กับ test set เพื่อประเมินผล "จริง"
    # ไม่ควรใช้ test set ในการหา parameter ใดๆ เพราะจะทำให้ผลประเมินโมเดลไม่สะท้อนความสามารถจริง

    # ถ้าต้องการใช้ validation set ของ df (raw OHLC) ช่วงนี้คือ "กลาง" เพื่อหา optimal nBars SL ของข้อมูลทั้งหมด (หลัง train, ก่อน test)
    val_idx_start = train_size
    val_idx_end = train_size + val_size
    find_optimal_nbars_sl(df.iloc[val_idx_start:val_idx_end].copy(), symbol, timeframe, entry_func, best_entry_name)

    # ถ้าต้องการใช้ test set ของ df (raw OHLC) ช่วงนี้คือ "ท้ายสุด" เพื่อหา optimal nBars SL เป็นของข้อมูล (อนาคตสุด)
    # test_idx_start = train_size + val_size
    # find_optimal_nbars_sl(df.iloc[test_idx_start:].copy(), symbol, timeframe)

    # 6. ทำ Feature Scaling ปรับฟีเจอร์อยู่ในสเกลใกล้เคียงกัน
    # การปรับขนาดข้อมูลแต่ละฟีเจอร์ให้อยู่ในสเกลที่เหมาะสม เพื่อให้โมเดลเรียนรู้และทำนายได้แม่นยำและเสถียรขึ้น
    print("\n🔍 กำลังทำ Feature Scaling...")
    try:
        scaler = StandardScaler()
        
        # ตรวจสอบว่ามีข้อมูลใน X_train หรือไม่
        if len(X_train) == 0:
            print("⚠️ ไม่มีข้อมูล ใน X_train ไม่สามารถทำ Feature Scaling ได้")
            return None, None, None, None, None, None
            
        X_train_scaled = scaler.fit_transform(X_train) # จะคำนวณค่าเฉลี่ยและส่วนเบี่ยงเบนมาตรฐานจาก train set แล้วนำไปปรับขนาดข้อมูล
        X_train = pd.DataFrame(X_train_scaled, columns=features, index=X_train.index)
        
        X_val_scaled = scaler.transform(X_val) # จะใช้ค่าที่ได้จาก train set ไปปรับขนาด validation/test set (ป้องกัน data leakage)
        X_val = pd.DataFrame(X_val_scaled, columns=features, index=X_val.index)
        
        X_test_scaled = scaler.transform(X_test)
        X_test = pd.DataFrame(X_test_scaled, columns=features, index=X_test.index)
        
        print("✅ ทำ Feature Scaling เรียบร้อยแล้ว")
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาด ขณะทำ Feature Scaling: {str(e)}")
        return None, None, None, None, None, None

    return (X_train, y_train), (X_val, y_val), (X_test, y_test), df, trade_df, stats

# ==============================================

def backtest(
        df, 
        nBars_SL, rsi_level=input_rsi_level_in, rsi_level_out=input_rsi_level_out, 
        stop_loss_atr_multiplier=input_stop_loss_atr, take_profit_stop_loss_ratio=input_take_profit, 
        symbol="GOLD",
        entry_condition_func=None, entry_condition_name=None
        ):
    # print(f"\n🏗️ เปิดใช้งาน backtest") if Steps_to_do else None

    # แสดงชื่อคอลัมน์ของ DataFrame ใน pandas
    # print(df.columns.tolist())
    # แบบทีละบรรทัด
    # for col in df.columns:
    #     print(col)

    symbol_spread = symbol_info[symbol]["Spread"]
    symbol_digits = symbol_info[symbol]["Digits"]
    symbol_points = symbol_info[symbol]["Points"]

    trades = []
    in_trade_buy = False
    in_trade_sell = False
    entry_price_buy = entry_price_sell = None
    sl_price_buy = sl_price_sell = None
    tp_price_buy = tp_price_sell = None
    trade_type_buy = trade_type_sell = None

    # print(f"ตรวจสอบขนาดของ df {len(df)} nBars {nBars_SL}")

    # ตรวจสอบคอลัมน์ที่จำเป็นก่อน loop
    required_cols = ['Close', 'Open', 'High', 'Low', 'EMA50', 'EMA200', 'MACD_signal', 'RSI14', 'Volume', 'Volume_MA_20', 'PullBack_Up', 'Ratio_Buy', 'PullBack_Down', 'Ratio_Sell', 'ATR']
    missing_cols = [col for col in required_cols if col not in df.columns]
    use_simple_conditions = len(missing_cols) > 0

    if use_simple_conditions:
        print(f"⚠️ ขาดคอลัมน์ที่จำเป็นใน backtest: {missing_cols}")
        print(f"⚠️ จะใช้ entry conditions แบบง่าย (เฉพาะ OHLC)")

    for i in range(max(100, nBars_SL), len(df)):

        # สร้าง prev_dict โดยตรวจสอบว่าคอลัมน์มีอยู่หรือไม่
        prev_dict = {
            'close': df['Close'].iloc[i-1],
            'open': df['Open'].iloc[i-1],
            'high': df['High'].iloc[i-1],
            'low': df['Low'].iloc[i-1],
        }

        # เพิ่มคอลัมน์อื่นๆ ถ้ามี
        if not use_simple_conditions:
            prev_dict.update({
                'ema50': df['EMA50'].iloc[i-1],
                'ema200': df['EMA200'].iloc[i-1],
                'macd_signal': df['MACD_signal'].iloc[i-1],
                'rsi14': df['RSI14'].iloc[i-1],
                'volume': df['Volume'].iloc[i-1],
                'volume_ma20': df['Volume_MA_20'].iloc[i-1],
                'pullback_buy': df['PullBack_Up'].iloc[i-1],
                'ratio_buy': df['Ratio_Buy'].iloc[i-1],
                'pullback_sell': df['PullBack_Down'].iloc[i-1],
                'ratio_sell': df['Ratio_Sell'].iloc[i-1],
            })
        else:
            # ใช้ค่า default สำหรับคอลัมน์ที่ขาด
            prev_dict.update({
                'ema50': prev_dict['close'],  # ใช้ close แทน
                'ema200': prev_dict['close'],  # ใช้ close แทน
                'macd_signal': 1.0,  # ค่า default
                'rsi14': 50.0,  # ค่า default
                'volume': 1000.0,  # ค่า default
                'volume_ma20': 1000.0,  # ค่า default
                'pullback_buy': 50.0,  # ค่า default
                'ratio_buy': 3.0,  # ค่า default
                'pullback_sell': 50.0,  # ค่า default
                'ratio_sell': 3.0,  # ค่า default
            })

        # --- BUY SIGNAL ---
        if not in_trade_buy:

            if entry_condition_func is not None:
                try:
                    tech_signal_buy = entry_condition_func['buy'](prev_dict)
                except (KeyError, TypeError) as e:
                    print(f"⚠️ Error in entry_condition_func: {e}")
                    tech_signal_buy = False
            else:
                if use_simple_conditions:
                    # ใช้ entry conditions แบบง่าย (เฉพาะ OHLC)
                    tech_signal_buy = (
                        prev_dict['close'] > prev_dict['open'] and
                        prev_dict['close'] > prev_dict['low'] * 1.001  # ราคาปิดสูงกว่าราคาต่ำสุด 0.1%
                    )
                else:
                    # ใช้ entry conditions แบบปกติ
                    tech_signal_buy = (
                        prev_dict['close'] > prev_dict['open'] and # Previous bar closed higher
                        prev_dict['macd_signal'] == 1.0 and
                        prev_dict['rsi14'] > rsi_level and # prev_sto_cross == 1.0 and
                        prev_dict['volume'] > prev_dict['volume_ma20'] * 0.8 and
                        prev_dict['pullback_buy'] > input_pull_back and
                        prev_dict['ratio_buy'] > take_profit_stop_loss_ratio
                    )

            if tech_signal_buy:
                entry_price_buy = df["Open"].iloc[i] + (symbol_spread * symbol_points)

                sl_atr = entry_price_buy - stop_loss_atr_multiplier * df['ATR'].iloc[i-1]
                sl_prev_bars = min(df['Low'].iloc[i-nBars_SL:i].min(), entry_price_buy - 2*symbol_points)
                sl_price_buy = max(sl_atr, sl_prev_bars)
                sl_price_buy = floor_price(sl_price_buy, symbol_points)

                tp_price_buy = entry_price_buy + (entry_price_buy - sl_price_buy) * take_profit_stop_loss_ratio
                tp_price_buy = ceiling_price(tp_price_buy, symbol_points)
                if tp_price_buy <= entry_price_buy:
                    tp_price_buy = entry_price_buy + 2*symbol_points

                in_trade_buy = True
                entry_time_buy = pd.to_datetime(df["Date"].iloc[i] + ' ' + df["Time"].iloc[i])

        # --- BUY EXIT ---
        if in_trade_buy:
            exit_condition = None
            exit_price = None
            if df["Low"].iloc[i] <= sl_price_buy:
                exit_price = sl_price_buy
                exit_condition = "SL Hit"
            elif df["High"].iloc[i] > tp_price_buy:
                exit_price = tp_price_buy
                exit_condition = "TP Hit"
            elif (df["Close"].iloc[i] < df["EMA50"].iloc[i] or df["RSI14"].iloc[i] < rsi_level_out):
                exit_price = df["Close"].iloc[i]
                exit_condition = "Technical Exit"

            if exit_condition:
                profit = (exit_price - entry_price_buy) / symbol_points
                trades.append({
                    "Entry Time": entry_time_buy,
                    "Entry Price": entry_price_buy,
                    "Exit Time": pd.to_datetime(df["Date"].iloc[i] + ' ' + df["Time"].iloc[i]),
                    "Exit Price": exit_price,
                    "Profit": profit,
                    "Trade Type": "Buy",
                    "Exit Condition": exit_condition
                })
                in_trade_buy = False

        # --- SELL SIGNAL ---
        if not in_trade_sell:

            if entry_condition_func is not None:
                try:
                    tech_signal_sell = entry_condition_func['sell'](prev_dict)
                except (KeyError, TypeError) as e:
                    print(f"⚠️ Error in entry_condition_func (sell): {e}")
                    tech_signal_sell = False
            else:
                if use_simple_conditions:
                    # ใช้ entry conditions แบบง่าย (เฉพาะ OHLC)
                    tech_signal_sell = (
                        prev_dict['close'] < prev_dict['open'] and
                        prev_dict['close'] < prev_dict['high'] * 0.999  # ราคาปิดต่ำกว่าราคาสูงสุด 0.1%
                    )
                else:
                    # ใช้ entry conditions แบบปกติ
                    tech_signal_sell = (
                        prev_dict['close'] < prev_dict['open'] and
                        prev_dict['macd_signal'] == -1.0 and
                        prev_dict['rsi14'] > (100 - rsi_level) and
                        prev_dict['volume'] > prev_dict['volume_ma20'] * 0.8 and
                        prev_dict['pullback_sell'] > input_pull_back and
                        prev_dict['ratio_sell'] > take_profit_stop_loss_ratio
                    )

            if tech_signal_sell:
                entry_price_sell = df["Open"].iloc[i]

                sl_atr = entry_price_sell + stop_loss_atr_multiplier * df['ATR'].iloc[i-1]
                sl_prev_bars = max(df['High'].iloc[i-nBars_SL:i].max(), entry_price_sell + 2*symbol_points)
                sl_price_sell = min(sl_atr, sl_prev_bars) + (symbol_spread * symbol_points)
                sl_price_sell = ceiling_price(sl_price_sell, symbol_points)

                tp_price_sell = entry_price_sell - (sl_price_sell - entry_price_sell) * take_profit_stop_loss_ratio
                tp_price_sell = floor_price(tp_price_sell, symbol_points)
                if tp_price_sell >= entry_price_sell:
                    tp_price_sell = entry_price_sell - 2*symbol_points
                    
                in_trade_sell = True
                entry_time_sell = df["Date"].iloc[i] + ' ' + df["Time"].iloc[i]

        # --- SELL EXIT ---
        if in_trade_sell:
            exit_condition = None
            exit_price = None
            if df["High"].iloc[i] + (symbol_spread * symbol_points) >= sl_price_sell:
                exit_price = sl_price_sell
                exit_condition = "SL Hit"
            elif df["Low"].iloc[i] + (symbol_spread * symbol_points) < tp_price_sell:
                exit_price = tp_price_sell
                exit_condition = "TP Hit"
            elif (df["Close"].iloc[i] > df["EMA50"].iloc[i] or df["RSI14"].iloc[i] > (100 - rsi_level_out)):
                exit_price = df["Close"].iloc[i] + (symbol_spread * symbol_points)
                exit_condition = "Technical Exit"

            if exit_condition:
                profit = (entry_price_sell - exit_price) / symbol_points
                trades.append({
                    "Entry Time": entry_time_sell,
                    "Entry Price": entry_price_sell,
                    "Exit Time": df["Date"].iloc[i] + ' ' + df["Time"].iloc[i],
                    "Exit Price": exit_price,
                    "Profit": profit,
                    "Trade Type": "Sell",
                    "Exit Condition": exit_condition
                })
                in_trade_sell = False

    # สรุปผลลัพธ์
    trades_df = pd.DataFrame(trades)
    if trades_df.empty or 'Profit' not in trades_df.columns:
        # คืนค่า default ถ้าไม่มี trade เกิดขึ้น
        # print(f"backtest results for nBars_SL = {nBars_SL} : trades_df.empty : expectancy = 0.0, win_rate = 0.0")
        return {"expectancy": 0.0, "win_rate": 0.0, "num_trades": 0}

    wins = trades_df[trades_df['Profit'] > 0]
    losses = trades_df[trades_df['Profit'] < 0]
    num_wins = len(wins)
    num_losses = len(losses)
    total = num_wins + num_losses
    win_rate = num_wins / total if total > 0 else 0
    avg_win = wins['Profit'].mean() if num_wins > 0 else 0
    avg_loss = abs(losses['Profit'].mean()) if num_losses > 0 else 0
    expectancy = (avg_win * win_rate) - (avg_loss * (1 - win_rate))

    print(f"backtest results for nBars_SL = {nBars_SL} : expectancy = {expectancy:.2f}, win_rate = {win_rate:.3f}")
    return {"expectancy": expectancy, "win_rate": win_rate, "num_trades": total}

# ==============================================

# ==============================================

def main(current_main_round=None, group_name=None, group_files=None):
    print(f"\n🏗️ เปิดใช้งาน main") if Steps_to_do else None

    for file in group_files:

        file_name = os.path.basename(file)  # GOLD_H1_FIXED.csv
        symbol = file_name.split('_')[0] # GOLD
        timeframe = group_name # M60
        print(f"file {file} main_round {current_main_round} symbol {symbol} timeframe {timeframe}")

        if Model_Decision: # True ขั้นตอนทดลอง/พัฒนา (Experiment/Development) False ขั้นตอน Production (ใช้งานจริง/Deploy)
            # สร้างโมเดลใหม่ ถ้าทดลอง/พัฒนา: สร้างใหม่ตลอด
            model = None
            scaler = None
        else:
            # ใช้โมเดลเดิม ถ้าถ้า production/ต้องการความเร็ว: ใช้โมเดลเดิม
            model = load_model(symbol, timeframe)
            scaler = load_scaler(symbol, timeframe)

        confidence_threshold = load_optimal_threshold(symbol, timeframe)
        nBars_SL = load_optimal_nbars(symbol, timeframe)

        # สร้างตารางข้อมูล
        train_data, val_data, test_data, df, trade_df, stats = load_and_process_data(file, model_name, symbol, timeframe, model, scaler, nBars_SL, confidence_threshold)

# ==============================================

def run_main_analysis(symbol = None, timeframe = None):
    print(f"\n🏗️ เปิดใช้งาน run main analysis") if Steps_to_do else None

    for main_round in range(NUM_MAIN_ROUNDS):
        current_main_round = main_round + 1

        for group_name, group_files in TEST_GROUPS.items():
                try:
                    round_results = main(current_main_round, group_name, group_files)
                    if round_results:
                        # เก็บผลลัพธ์แต่ละรอบ (รวม main_round และ training_round)
                        for key, value in round_results.items():
                            all_results[f"{key}_main_{current_main_round}"] = value
                except Exception as e:
                    print(f"❌ เกิดข้อผิดพลาดในรอบที่ {current_main_round}: {e}")
                    import traceback
                    traceback.print_exc()

if __name__ == "__main__":
    print(f"\n🏗️ เปิดใช้งาน __name__") if Steps_to_do else None

    # รันการวิเคราะห์หลัก
    all_results = run_main_analysis()