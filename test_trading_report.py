#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ไฟล์ทดสอบฟังก์ชันรายงานการเทรดใหม่
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# เพิ่ม path ของไฟล์หลัก
sys.path.append('.')

# Import ฟังก์ชันจากไฟล์หลัก
from LightGBM_10_Test import (
    create_comprehensive_trading_report,
    create_trading_summary_report,
    create_detailed_metrics_report,
    create_risk_analysis_report,
    create_performance_charts_report,
    calculate_advanced_trading_metrics,
    calculate_portfolio_metrics
)

def test_trading_reports():
    """
    ทดสอบฟังก์ชันรายงานการเทรดทั้งหมด
    """
    print("🧪 ทดสอบฟังก์ชันรายงานการเทรดใหม่")
    print("="*60)
    
    # โหลดข้อมูลทดสอบ
    try:
        trade_df = pd.read_csv('test_trades.csv')
        print(f"✅ โหลดข้อมูลทดสอบ: {len(trade_df)} trades")
        print(f"📊 คอลัมน์: {list(trade_df.columns)}")
        
        # แสดงตัวอย่างข้อมูล
        print(f"\n📋 ตัวอย่างข้อมูล:")
        print(trade_df.head())
        
    except Exception as e:
        print(f"❌ ไม่สามารถโหลดข้อมูลทดสอบได้: {e}")
        return
    
    # ทดสอบ Advanced Trading Metrics
    print(f"\n🔍 ทดสอบ Advanced Trading Metrics...")
    try:
        advanced_metrics = calculate_advanced_trading_metrics(trade_df)
        print(f"✅ คำนวณ Advanced Metrics สำเร็จ")
        for key, value in advanced_metrics.items():
            print(f"   {key}: {value}")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดใน Advanced Metrics: {e}")
    
    # ทดสอบ Portfolio Metrics
    print(f"\n💼 ทดสอบ Portfolio Metrics...")
    try:
        portfolio_metrics = calculate_portfolio_metrics(trade_df)
        print(f"✅ คำนวณ Portfolio Metrics สำเร็จ")
        for key, value in portfolio_metrics.items():
            print(f"   {key}: {value}")
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดใน Portfolio Metrics: {e}")
    
    # ทดสอบรายงานแบบครอบคลุม
    print(f"\n📊 ทดสอบรายงานแบบครอบคลุม...")
    try:
        report_data = create_comprehensive_trading_report(
            trades_df=trade_df,
            symbol="GOLD",
            timeframe="M60",
            scenario="test_scenario",
            output_folder="test_reports"
        )
        
        if 'error' not in report_data:
            print(f"✅ สร้างรายงานครอบคลุมสำเร็จ")
            print(f"📁 บันทึกที่: {report_data['report_info']['report_folder']}")
            print(f"📄 ไฟล์ที่สร้าง: {len(report_data['report_info']['files_created'])} ไฟล์")
        else:
            print(f"❌ เกิดข้อผิดพลาด: {report_data['error']}")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการสร้างรายงาน: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_trading_reports()