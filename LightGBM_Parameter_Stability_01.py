#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
สคริปต์ตรวจสอบ Parameter Stability ระหว่าง symbols
"""

import os
import json
import pandas as pd
import numpy as np
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

def check_lightgbm_compatibility():
    """ตรวจสอบความเข้ากันได้กับ LightGBM_10_1.py"""
    print("🔍 ตรวจสอบความเข้ากันได้กับ LightGBM_10_1.py...")

    # ตรวจสอบไฟล์หลัก (อัปเดตให้ตรงกับไฟล์ใหม่)
    main_files = ["LightGBM_10_1.py", "LightGBM_03_Compare.py"]  # รองรับทั้งไฟล์เก่าและใหม่

    main_file_found = False
    for main_file in main_files:
        if os.path.exists(main_file):
            print(f"✅ พบไฟล์หลัก: {main_file}")
            main_file_found = True
            break

    if not main_file_found:
        print(f"❌ ไม่พบไฟล์หลัก: {main_files}")
        return False

    # ตรวจสอบโฟลเดอร์ที่จำเป็น (อัปเดตให้ตรงกับ LightGBM_10_1.py)
    required_dirs = [
        "LightGBM/Multi",           # โฟลเดอร์หลักจาก LightGBM_10_1.py
        "LightGBM/Hyper_Multi",     # โฟลเดอร์ hyperparameters จาก LightGBM_10_1.py
        "LightGBM_Multi",           # โฟลเดอร์เก่า (backward compatibility)
        "LightGBM_Hyper_Multi"      # โฟลเดอร์เก่า (backward compatibility)
    ]

    found_dirs = []
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✅ พบโฟลเดอร์: {dir_name}")
            found_dirs.append(dir_name)
        else:
            print(f"⚠️ ไม่พบโฟลเดอร์: {dir_name}")

    if not found_dirs:
        print("⚠️ ไม่พบโฟลเดอร์ใดเลย - กรุณารันการเทรนก่อน")

    return True

def detect_model_architecture():
    """ตรวจสอบว่าใช้ Single หรือ Multi-Model Architecture"""
    # ตรวจสอบโฟลเดอร์ hyperparameter tuning (รองรับทั้ง LightGBM_10_1.py และ LightGBM_03_Compare.py)
    possible_dirs = [
        # จาก LightGBM_10_1.py
        ("LightGBM/Hyper_Single", "LightGBM/Multi/models", "Single"),
        ("LightGBM/Hyper_Multi", "LightGBM/Multi/models", "Multi"),
        # จาก LightGBM_03_Compare.py (เก่า)
        ("LightGBM_Hyper_Single", "LightGBM_Model_Single/models", "Single"),
        ("LightGBM_Hyper_Multi", "LightGBM_Multi/models", "Multi"),
        # Legacy
        (None, "Test_LightGBM/models", "Legacy")
    ]

    architectures = []

    for hyper_dir, model_dir, arch_name in possible_dirs:
        found = False

        # ตรวจสอบ hyperparameter directory
        if hyper_dir and os.path.exists(hyper_dir) and os.listdir(hyper_dir):
            found = True
            print(f"✅ พบ {arch_name} Architecture: {hyper_dir}")

        # ตรวจสอบ model directory
        if model_dir and os.path.exists(model_dir) and os.listdir(model_dir):
            found = True
            print(f"✅ พบ {arch_name} Architecture: {model_dir}")

        if found and arch_name not in architectures:
            architectures.append(arch_name)

    if not architectures:
        print("⚠️ ไม่พบ Architecture ใดเลย")

    return architectures

def load_single_model_params(hyper_dir=None):
    """โหลดพารามิเตอร์จาก Single Model Architecture (รองรับทั้ง LightGBM_10_1.py และ LightGBM_03_Compare.py)"""
    all_params = {}

    # ลองหาโฟลเดอร์ที่เป็นไปได้
    possible_dirs = [
        "LightGBM/Hyper_Single",      # จาก LightGBM_10_1.py (ถ้ามี single model)
        "LightGBM_Hyper_Single",      # จาก LightGBM_03_Compare.py
        hyper_dir                     # ที่ระบุมา
    ]

    hyper_dir_to_use = None
    for dir_path in possible_dirs:
        if dir_path and os.path.exists(dir_path):
            hyper_dir_to_use = dir_path
            break

    if not hyper_dir_to_use:
        print("⚠️ ไม่พบโฟลเดอร์ Single Model hyperparameters")
        return {}

    print(f"📂 กำลังโหลดจาก: {hyper_dir_to_use}")

    for folder in os.listdir(hyper_dir_to_use):
        folder_path = os.path.join(hyper_dir_to_use, folder)
        if os.path.isdir(folder_path):
            # หาไฟล์ best_params.json ในโฟลเดอร์
            param_files = [f for f in os.listdir(folder_path) if f.endswith('_best_params.json')]

            for param_file_name in param_files:
                param_file = os.path.join(folder_path, param_file_name)

                if os.path.exists(param_file):
                    try:
                        with open(param_file, 'r') as f:
                            data = json.load(f)

                        # รองรับทั้งรูปแบบเก่าและใหม่
                        if "best_params" in data:
                            params = data["best_params"]
                            score = data.get("best_score", None)
                            tuning_date = data.get("tuning_date", None)
                        else:
                            params = data
                            score = None
                            tuning_date = None

                        # แยก timeframe และ symbol (รองรับ M30, M60 format)
                        if "_" in folder:
                            timeframe_str, symbol = folder.split("_", 1)
                            # แปลง M30 -> 30, M60 -> 60, หรือใช้ตัวเลขตรงๆ
                            if timeframe_str.startswith('M') and timeframe_str[1:].isdigit():
                                timeframe = int(timeframe_str[1:])  # M30 -> 30, M60 -> 60
                            elif timeframe_str.isdigit():
                                timeframe = int(timeframe_str)      # 30 -> 30, 60 -> 60
                            else:
                                timeframe = timeframe_str           # เก็บเป็น string ถ้าแปลงไม่ได้
                        else:
                            timeframe = "Unknown"
                            symbol = folder

                        key = f"{timeframe}_{symbol}"
                        all_params[key] = {
                            'params': params,
                            'score': score,
                            'tuning_date': tuning_date,
                            'timeframe': timeframe,
                            'symbol': symbol,
                            'architecture': 'Single'
                        }

                    except Exception as e:
                        print(f"⚠️ ไม่สามารถอ่านไฟล์ {param_file}: {e}")

    return all_params

def load_multi_model_params(hyper_dir=None):
    """โหลดพารามิเตอร์จาก Multi-Model Architecture (รองรับโครงสร้างไฟล์ที่ซับซ้อน)"""
    all_params = {}

    # ลองหาโฟลเดอร์ที่เป็นไปได้
    possible_dirs = [
        "LightGBM/Hyper_Multi",       # จาก LightGBM_10_1.py
        "LightGBM_Hyper_Multi",       # จาก LightGBM_03_Compare.py
        hyper_dir                     # ที่ระบุมา
    ]

    hyper_dir_to_use = None
    for dir_path in possible_dirs:
        if dir_path and os.path.exists(dir_path):
            hyper_dir_to_use = dir_path
            break

    if not hyper_dir_to_use:
        print("⚠️ ไม่พบโฟลเดอร์ Multi-Model hyperparameters")
        return {}

    print(f"📂 กำลังโหลดจาก: {hyper_dir_to_use}")

    # ตรวจสอบโฟลเดอร์ symbol_timeframe
    for folder in os.listdir(hyper_dir_to_use):
        folder_path = os.path.join(hyper_dir_to_use, folder)
        if os.path.isdir(folder_path):
            print(f"🔍 ตรวจสอบโฟลเดอร์: {folder}")

            # หาไฟล์ที่มี scenario name และ action (Buy/Sell)
            # รูปแบบไฟล์: {timeframe}_{symbol}_{scenario}_{action}_best_params.json
            # หรือ: {timeframe}_{symbol}_{scenario}_best_params.json
            param_files = [f for f in os.listdir(folder_path)
                          if f.endswith('_best_params.json') and
                          ('trend_following' in f or 'counter_trend' in f)]

            print(f"   พบไฟล์: {len(param_files)} ไฟล์")

            for param_file_name in param_files:
                param_file = os.path.join(folder_path, param_file_name)
                print(f"   📄 กำลังโหลด: {param_file_name}")

                if os.path.exists(param_file):
                    try:
                        with open(param_file, 'r') as f:
                            data = json.load(f)

                        # รองรับทั้งรูปแบบเก่าและใหม่
                        if "best_params" in data:
                            params = data["best_params"]
                            score = data.get("best_score", None)
                            tuning_date = data.get("tuning_date", None)
                            scenario = data.get("scenario", None)
                            action = data.get("action", None)
                        else:
                            params = data
                            score = None
                            tuning_date = None
                            scenario = None
                            action = None

                        # แยก scenario และ action จากชื่อไฟล์
                        if not scenario:
                            if 'trend_following' in param_file_name:
                                scenario = 'trend_following'
                            elif 'counter_trend' in param_file_name:
                                scenario = 'counter_trend'
                            else:
                                scenario = 'unknown'

                        if not action:
                            if '_Buy_' in param_file_name:
                                action = 'Buy'
                            elif '_Sell_' in param_file_name:
                                action = 'Sell'
                            else:
                                action = 'Combined'  # สำหรับไฟล์ที่ไม่มี Buy/Sell

                        # แยก timeframe และ symbol จากชื่อโฟลเดอร์
                        if "_" in folder:
                            timeframe_str, symbol = folder.split("_", 1)
                            # แปลง M30 -> 30, M60 -> 60
                            if timeframe_str.startswith('M') and timeframe_str[1:].isdigit():
                                timeframe = int(timeframe_str[1:])
                            elif timeframe_str.isdigit():
                                timeframe = int(timeframe_str)
                            else:
                                timeframe = timeframe_str
                        else:
                            timeframe = "Unknown"
                            symbol = folder

                        # สร้าง key ที่ unique
                        if action == 'Combined':
                            key = f"{timeframe}_{symbol}_{scenario}"
                        else:
                            key = f"{timeframe}_{symbol}_{scenario}_{action}"

                        all_params[key] = {
                            'params': params,
                            'score': score,
                            'tuning_date': tuning_date,
                            'timeframe': timeframe,
                            'symbol': symbol,
                            'scenario': scenario,
                            'action': action,
                            'architecture': 'Multi',
                            'file_name': param_file_name
                        }

                        print(f"      ✅ โหลดสำเร็จ: {key}")

                    except Exception as e:
                        print(f"      ⚠️ ไม่สามารถอ่านไฟล์ {param_file}: {e}")

    print(f"📊 รวมโหลดได้: {len(all_params)} โมเดล")
    return all_params

def load_all_best_params():
    """โหลดพารามิเตอร์ที่ดีที่สุดจากทุก architectures"""
    all_params = {}

    # ตรวจสอบ architectures ที่มีอยู่
    architectures = detect_model_architecture()
    print(f"พบ Model Architectures: {architectures}")

    # โหลดจาก Single Model
    if "Single" in architectures:
        print(f"\nโหลดพารามิเตอร์จาก Single Model Architecture...")
        single_params = load_single_model_params()
        all_params.update(single_params)
        print(f"โหลดได้ {len(single_params)} โมเดลจาก Single Architecture")

    # โหลดจาก Multi-Model
    if "Multi" in architectures:
        print(f"\nโหลดพารามิเตอร์จาก Multi-Model Architecture...")
        multi_params = load_multi_model_params()
        all_params.update(multi_params)
        print(f"โหลดได้ {len(multi_params)} โมเดลจาก Multi Architecture")

    # โหลดจาก Legacy (เก่า)
    if "Legacy" in architectures:
        print(f"\nโหลดพารามิเตอร์จาก Legacy Architecture...")
        legacy_params = load_legacy_params()
        all_params.update(legacy_params)
        print(f"โหลดได้ {len(legacy_params)} โมเดลจาก Legacy Architecture")

    return all_params

def load_legacy_params(models_dir="Test_LightGBM/models"):
    """โหลดพารามิเตอร์จาก Legacy Architecture (เก่า)"""
    all_params = {}

    if not os.path.exists(models_dir):
        return {}

    for folder in os.listdir(models_dir):
        folder_path = os.path.join(models_dir, folder)
        if os.path.isdir(folder_path):
            param_file = os.path.join(folder_path, f"{folder}_best_params.json")

            if os.path.exists(param_file):
                try:
                    with open(param_file, 'r') as f:
                        data = json.load(f)

                    # รองรับทั้งรูปแบบเก่าและใหม่
                    if "best_params" in data:
                        params = data["best_params"]
                        score = data.get("best_score", None)
                        tuning_date = data.get("tuning_date", None)
                    else:
                        params = data
                        score = None
                        tuning_date = None

                    # แยก timeframe และ symbol (รองรับ M30, M60 format)
                    if "_" in folder:
                        timeframe_str, symbol = folder.split("_", 1)
                        # แปลง M30 -> 30, M60 -> 60, หรือใช้ตัวเลขตรงๆ
                        if timeframe_str.startswith('M') and timeframe_str[1:].isdigit():
                            timeframe = int(timeframe_str[1:])  # M30 -> 30, M60 -> 60
                        elif timeframe_str.isdigit():
                            timeframe = int(timeframe_str)      # 30 -> 30, 60 -> 60
                        else:
                            timeframe = timeframe_str           # เก็บเป็น string ถ้าแปลงไม่ได้
                    else:
                        timeframe = "Unknown"
                        symbol = folder

                    key = f"{timeframe}_{symbol}_legacy"
                    all_params[key] = {
                        'params': params,
                        'score': score,
                        'tuning_date': tuning_date,
                        'timeframe': timeframe,
                        'symbol': symbol,
                        'architecture': 'Legacy'
                    }

                except Exception as e:
                    print(f"⚠️ ไม่สามารถอ่านไฟล์ {param_file}: {e}")

    return all_params

def analyze_parameter_stability(all_params):
    """วิเคราะห์ความเสถียรของพารามิเตอร์"""
    if not all_params:
        print("❌ ไม่มีข้อมูลพารามิเตอร์ให้วิเคราะห์")
        return

    print(f"วิเคราะห์ Parameter Stability")
    print("="*60)
    print(f"จำนวน models ที่พบ: {len(all_params)}")

    # สร้าง DataFrame สำหรับการวิเคราะห์
    rows = []
    for model_id, data in all_params.items():
        row = {
            'model_id': model_id,
            'symbol': data['symbol'],
            'timeframe': data['timeframe'],
            'score': data['score'],
            'architecture': data.get('architecture', 'Unknown'),
            'scenario': data.get('scenario', 'N/A'),
            'action': data.get('action', 'N/A'),  # เพิ่ม action (Buy/Sell/Combined)
            'file_name': data.get('file_name', 'N/A')  # เพิ่มชื่อไฟล์เพื่อ debug
        }
        row.update(data['params'])
        rows.append(row)

    df = pd.DataFrame(rows)

    # แสดงข้อมูลพื้นฐาน
    print(f"\nข้อมูลพื้นฐาน:")
    print(f"Symbols: {sorted(df['symbol'].unique())}")
    print(f"Timeframes: {sorted(df['timeframe'].unique())}")
    print(f"Architectures: {sorted(df['architecture'].unique())}")

    # แสดงข้อมูล Actions (Buy/Sell/Combined)
    if 'action' in df.columns:
        actions = df['action'].unique()
        print(f"Actions: {sorted([a for a in actions if a != 'N/A'])}")

    # แสดงข้อมูลแยกตาม Architecture
    for arch in df['architecture'].unique():
        arch_df = df[df['architecture'] == arch]
        print(f"\n{arch} Architecture:")
        print(f"   จำนวนโมเดล: {len(arch_df)}")

        if arch == 'Multi':
            scenarios = arch_df['scenario'].unique()
            print(f"   Scenarios: {sorted([s for s in scenarios if s != 'N/A'])}")

            for scenario in scenarios:
                if scenario == 'N/A':
                    continue
                scenario_df = arch_df[arch_df['scenario'] == scenario]
                scenario_count = len(scenario_df)
                print(f"     - {scenario}: {scenario_count} โมเดล")

                # แสดงการแยก Buy/Sell ถ้ามี
                if 'action' in scenario_df.columns:
                    actions = scenario_df['action'].unique()
                    for action in sorted([a for a in actions if a != 'N/A']):
                        action_count = len(scenario_df[scenario_df['action'] == action])
                        print(f"       └─ {action}: {action_count} โมเดล")

    # วิเคราะห์แต่ละพารามิเตอร์
    numeric_params = ['learning_rate', 'num_leaves', 'max_depth', 'min_data_in_leaf',
                     'feature_fraction', 'bagging_fraction', 'lambda_l1', 'lambda_l2']

    print(f"\nการกระจายของพารามิเตอร์ (รวมทุก Architecture):")
    print("-"*60)

    stability_report = {}

    for param in numeric_params:
        if param in df.columns:
            values = df[param].dropna()
            if len(values) > 0:
                mean_val = values.mean()
                std_val = values.std()
                cv = (std_val / mean_val) * 100 if mean_val != 0 else 0  # Coefficient of Variation

                print(f"{param:20}: Mean={mean_val:.4f}, Std={std_val:.4f}, CV={cv:.1f}%")

                stability_report[param] = {
                    'mean': mean_val,
                    'std': std_val,
                    'cv': cv,
                    'stability': 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'
                }

    # วิเคราะห์แยกตาม Architecture
    for arch in df['architecture'].unique():
        arch_df = df[df['architecture'] == arch]
        if len(arch_df) > 1:  # ต้องมีมากกว่า 1 โมเดลถึงจะวิเคราะห์ได้
            print(f"\nการกระจายของพารามิเตอร์ใน {arch} Architecture:")
            print("-"*60)

            for param in numeric_params:
                if param in arch_df.columns:
                    values = arch_df[param].dropna()
                    if len(values) > 1:
                        mean_val = values.mean()
                        std_val = values.std()
                        cv = (std_val / mean_val) * 100 if mean_val != 0 else 0
                        stability = 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'

                        print(f"{param:20}: Mean={mean_val:.4f}, Std={std_val:.4f}, CV={cv:.1f}% ({stability})")

            # สำหรับ Multi-Model ให้วิเคราะห์แยกตาม scenario ด้วย
            if arch == 'Multi':
                scenarios = arch_df['scenario'].unique()
                for scenario in scenarios:
                    if scenario == 'N/A':
                        continue
                    scenario_df = arch_df[arch_df['scenario'] == scenario]
                    if len(scenario_df) > 1:
                        print(f"\nการกระจายของพารามิเตอร์ใน {scenario} scenario:")
                        print("-"*50)

                        for param in numeric_params:
                            if param in scenario_df.columns:
                                values = scenario_df[param].dropna()
                                if len(values) > 1:
                                    mean_val = values.mean()
                                    std_val = values.std()
                                    cv = (std_val / mean_val) * 100 if mean_val != 0 else 0
                                    stability = 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'

                                    print(f"{param:20}: Mean={mean_val:.4f}, Std={std_val:.4f}, CV={cv:.1f}% ({stability})")

                        # วิเคราะห์แยกตาม Action (Buy/Sell) ถ้ามี
                        if 'action' in scenario_df.columns:
                            actions = scenario_df['action'].unique()
                            for action in sorted([a for a in actions if a != 'N/A' and a != 'Combined']):
                                action_df = scenario_df[scenario_df['action'] == action]
                                if len(action_df) > 1:
                                    print(f"\n  การกระจายของพารามิเตอร์ใน {scenario} - {action}:")
                                    print("  " + "-"*45)

                                    for param in numeric_params:
                                        if param in action_df.columns:
                                            values = action_df[param].dropna()
                                            if len(values) > 1:
                                                mean_val = values.mean()
                                                std_val = values.std()
                                                cv = (std_val / mean_val) * 100 if mean_val != 0 else 0
                                                stability = 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'

                                                print(f"  {param:18}: Mean={mean_val:.4f}, Std={std_val:.4f}, CV={cv:.1f}% ({stability})")

    # วิเคราะห์ตาม timeframe
    print(f"\nการวิเคราะห์ตาม Timeframe:")
    print("-"*60)

    for tf in sorted(df['timeframe'].unique()):
        tf_data = df[df['timeframe'] == tf]
        print(f"\nTimeframe {tf} ({len(tf_data)} models):")

        for param in ['learning_rate', 'num_leaves']:
            if param in tf_data.columns:
                values = tf_data[param].dropna()
                if len(values) > 0:
                    print(f"  {param}: {values.mean():.4f} +/- {values.std():.4f}")

    # วิเคราะห์ตาม symbol type
    print(f"\nการวิเคราะห์ตาม Symbol Type:")
    print("-"*60)

    # จัดกลุ่ม symbols
    forex_pairs = ['AUDUSD', 'EURGBP', 'EURUSD', 'GBPUSD', 'NZDUSD', 'USDCAD', 'USDJPY']
    commodities = ['GOLD']

    for group_name, symbols in [('Forex', forex_pairs), ('Commodities', commodities)]:
        group_data = df[df['symbol'].isin(symbols)]
        if len(group_data) > 0:
            print(f"\n{group_name} ({len(group_data)} models):")
            for param in ['learning_rate', 'num_leaves']:
                if param in group_data.columns:
                    values = group_data[param].dropna()
                    if len(values) > 0:
                        print(f"  {param}: {values.mean():.4f} +/- {values.std():.4f}")

    # สรุปความเสถียร
    print(f"\nสรุปความเสถียรของพารามิเตอร์:")
    print("-"*60)

    high_stability = [p for p, data in stability_report.items() if data['stability'] == 'High']
    medium_stability = [p for p, data in stability_report.items() if data['stability'] == 'Medium']
    low_stability = [p for p, data in stability_report.items() if data['stability'] == 'Low']

    if high_stability:
        print(f"High Stability (CV < 20%): {', '.join(high_stability)}")
    if medium_stability:
        print(f"Medium Stability (CV 20-50%): {', '.join(medium_stability)}")
    if low_stability:
        print(f"Low Stability (CV > 50%): {', '.join(low_stability)}")

    # แนะนำการปรับปรุง
    print(f"\nแนะนำการปรับปรุง:")
    print("-"*60)

    if low_stability:
        print(f"1. พารามิเตอร์ที่ไม่เสถียร ({', '.join(low_stability)}):")
        print(f"   - ลองใช้ค่าเฉลี่ยเป็น default")
        print(f"   - ลดช่วงการค้นหาใน param_dist")
        print(f"   - เพิ่มข้อมูลสำหรับ training")

    if len(all_params) < 5:
        print(f"2. ข้อมูลไม่เพียงพอ (มีเพียง {len(all_params)} models):")
        print(f"   - ควรทำ tuning กับ symbols เพิ่มเติม")
        print(f"   - ทดสอบกับ timeframes ต่างๆ")

    return stability_report

def suggest_param_dist_updates(stability_report):
    """แนะนำการอัปเดต param_dist ตามผลการวิเคราะห์"""
    print(f"\nแนะนำการอัปเดต param_dist:")
    print("="*60)

    suggestions = {}

    for param, data in stability_report.items():
        mean_val = data['mean']
        cv = data['cv']
        stability = data['stability']

        if stability == 'High':
            # พารามิเตอร์เสถียร - ลดช่วงการค้นหา
            if param == 'learning_rate':
                center = round(mean_val, 3)
                suggestions[param] = [max(0.001, center-0.02), center, min(0.2, center+0.02)]
            elif param == 'num_leaves':
                center = int(mean_val)
                suggestions[param] = [max(15, center-15), center, min(127, center+15)]
            elif param in ['feature_fraction', 'bagging_fraction']:
                center = round(mean_val, 1)
                suggestions[param] = [max(0.6, center-0.1), center, min(1.0, center+0.1)]

        elif stability == 'Low':
            # พารามิเตอร์ไม่เสถียร - ใช้ช่วงกว้าง
            if param == 'learning_rate':
                suggestions[param] = [0.01, 0.02, 0.05, 0.1]
            elif param == 'num_leaves':
                suggestions[param] = [15, 31, 63, 127]

    if suggestions:
        print("แนะนำการปรับปรุง param_dist:")
        print("```python")
        print("param_dist = {")
        for param, values in suggestions.items():
            print(f"    '{param}': {values},")
        print("}")
        print("```")
    else:
        print("ไม่มีการแนะนำเฉพาะ - param_dist ปัจจุบันเหมาะสมแล้ว")

def show_step_by_step_guide(stability_report, all_params):
    """แสดงคำแนะนำแบบ Step-by-Step สำหรับผู้ใช้ใหม่"""
    print(f"\n" + "="*80)
    print("📋 คำแนะนำการปรับแก้แบบ Step-by-Step (สำหรับผู้ใช้ใหม่)")
    print("="*80)

    print(f"\n🎯 **สรุปสถานการณ์:**")
    high_stability = [param for param, data in stability_report.items() if data['stability'] == 'High']
    medium_stability = [param for param, data in stability_report.items() if data['stability'] == 'Medium']
    low_stability = [param for param, data in stability_report.items() if data['stability'] == 'Low']

    if high_stability:
        print(f"   ✅ พารามิเตอร์ที่เสถียรดี: {', '.join(high_stability)}")
    if medium_stability:
        print(f"   ⚠️ พารามิเตอร์ที่เสถียรปานกลาง: {', '.join(medium_stability)}")
    if low_stability:
        print(f"   ❌ พารามิเตอร์ที่ไม่เสถียร: {', '.join(low_stability)}")

    print(f"\n📝 **ขั้นตอนการแก้ไข:**")
    print(f"")
    print(f"**ขั้นตอนที่ 1: เปิดไฟล์ LightGBM_03_Compare.py**")
    print(f"   - ใช้ text editor หรือ IDE เปิดไฟล์ LightGBM_03_Compare.py")
    print(f"   - ค้นหาคำว่า 'param_dist' (ใช้ Ctrl+F)")
    print(f"")

    print(f"**ขั้นตอนที่ 2: หาส่วน Hyperparameter Configuration**")
    print(f"   - มองหาส่วนที่มีข้อความ 'HYPERPARAMETER TUNING CONFIGURATION'")
    print(f"   - หรือค้นหา 'param_dist = {{' ในไฟล์")
    print(f"   - จะอยู่ประมาณบรรทัดที่ 100-200")
    print(f"")

    print(f"**ขั้นตอนที่ 3: แทนที่ param_dist ทั้ง 4 ส่วน**")
    print(f"   ⚠️ ระบบใช้ Multi-Model Architecture ต้องปรับ 4 ส่วน:")
    print(f"   1. PARAM_DIST (บรรทัด ~265) - Global Default")
    print(f"   2. Trend Following param_dist (บรรทัด ~5733)")
    print(f"   3. Counter Trend param_dist (บรรทัด ~5733)")
    print(f"   4. get_optimized_param_dist_from_analysis() (บรรทัด ~6097)")
    print(f"")

    # วิเคราะห์ค่าจาก scenarios
    counter_trend_lr = 0.0832  # จากผลการวิเคราะห์
    counter_trend_leaves = 40.7
    trend_following_lr = 0.0255
    trend_following_leaves = 20.3

    # สร้าง param_dist สำหรับแต่ละส่วน
    print(f"**📋 ส่วนที่ 1: PARAM_DIST (Global Default) - บรรทัด ~265:**")
    print(f"```python")
    print(f"PARAM_DIST = {{")
    print(f"    # ใช้ช่วงกว้างที่ครอบคลุมทั้ง 2 scenarios")
    print(f"    'learning_rate': [0.02, 0.03, 0.05, 0.08, 0.10],")
    print(f"    'num_leaves': [15, 20, 30, 40, 50],")
    print(f"    'max_depth': [4, 5, 6, 7, 8],")
    print(f"    'min_data_in_leaf': [6, 8, 10, 15, 20],")
    print(f"    'feature_fraction': [0.7, 0.8, 0.85, 0.9],")
    print(f"    'bagging_fraction': [0.7, 0.8, 0.85, 0.9],")
    print(f"    'bagging_freq': [1, 2, 3, 5],")
    print(f"    'reg_alpha': [0.0, 0.005, 0.01, 0.02],")
    print(f"    'reg_lambda': [0.0, 0.005, 0.01, 0.02],")
    print(f"}}")
    print(f"```")
    print(f"")

    print(f"**📋 ส่วนที่ 2: Trend Following - ในฟังก์ชัน get_scenario_specific_param_dist():**")
    print(f"```python")
    print(f"if scenario_name == 'trend_following':")
    print(f"    # Trend Following: โฟกัสรอบค่าที่เหมาะสม (lr=0.0255, leaves=20.3)")
    print(f"    param_dist = {{")
    print(f"        'learning_rate': [0.02, 0.025, 0.03, 0.035],")
    print(f"        'num_leaves': [18, 20, 22, 25],")
    print(f"        'max_depth': [6, 7, 8],")
    print(f"        'min_data_in_leaf': [8, 10, 12],")
    print(f"        'feature_fraction': [0.82, 0.84, 0.86],")
    print(f"        'bagging_fraction': [0.87, 0.89, 0.91],")
    print(f"        'reg_alpha': [0.0, 0.005, 0.01],")
    print(f"        'reg_lambda': [0.0, 0.005, 0.01],")
    print(f"        'bagging_freq': [1, 3, 5],")
    print(f"    }}")
    print(f"```")
    print(f"")

    print(f"**📋 ส่วนที่ 3: Counter Trend - ในฟังก์ชัน get_scenario_specific_param_dist():**")
    print(f"```python")
    print(f"elif scenario_name == 'counter_trend':")
    print(f"    # Counter Trend: โฟกัสรอบค่าที่เหมาะสม (lr=0.0832, leaves=40.7)")
    print(f"    param_dist = {{")
    print(f"        'learning_rate': [0.07, 0.08, 0.09, 0.10],")
    print(f"        'num_leaves': [38, 40, 42, 45],")
    print(f"        'max_depth': [6, 7, 8],")
    print(f"        'min_data_in_leaf': [6, 7, 8, 9],")
    print(f"        'feature_fraction': [0.86, 0.88, 0.90],")
    print(f"        'bagging_fraction': [0.78, 0.79, 0.80],")
    print(f"        'reg_alpha': [0.0, 0.005, 0.01],")
    print(f"        'reg_lambda': [0.0, 0.005, 0.01],")
    print(f"        'bagging_freq': [1, 2, 3, 5],")
    print(f"    }}")
    print(f"```")
    print(f"")

    print(f"**📋 ส่วนที่ 4: get_optimized_param_dist_from_analysis() - บรรทัด ~6097:**")
    print(f"```python")
    print(f"optimized_param_dist = {{")
    print(f"    # ค่าเฉลี่ยรวมจากการวิเคราะห์ (ใช้เป็น fallback)")
    print(f"    'learning_rate': [0.02, 0.03, 0.05, 0.08],")
    print(f"    'num_leaves': [20, 25, 30, 35, 40],")
    print(f"    'max_depth': [5, 6, 7, 8],")
    print(f"    'min_data_in_leaf': [6, 8, 10, 12],")
    print(f"    'feature_fraction': [0.82, 0.86, 0.90],")
    print(f"    'bagging_fraction': [0.78, 0.84, 0.90],")
    print(f"    'reg_alpha': [0.0, 0.005, 0.01, 0.02],")
    print(f"    'reg_lambda': [0.0, 0.005, 0.01, 0.02],")
    print(f"    'bagging_freq': [1, 3, 5],")
    print(f"}}")
    print(f"```")
    print(f"")

    print(f"**ขั้นตอนที่ 4: บันทึกไฟล์และทดสอบ**")
    print(f"   - บันทึกไฟล์ (Ctrl+S)")
    print(f"   - รันคำสั่ง: python LightGBM_03_Compare.py")
    print(f"   - รอให้การเทรนเสร็จ")
    print(f"")

    print(f"**ขั้นตอนที่ 5: ตรวจสอบผลลัพธ์**")
    print(f"   - รันคำสั่ง: python check_parameter_stability.py")
    print(f"   - ดูว่า CV% ลดลงหรือไม่")
    print(f"   - ถ้ายังไม่ดีพอ ทำซ้ำขั้นตอนที่ 3-5")
    print(f"")

    print(f"🎯 **เป้าหมาย:**")
    print(f"   - CV% ของ learning_rate < 30% (ปัจจุบัน: {stability_report.get('learning_rate', {}).get('cv', 0):.1f}%)")
    print(f"   - CV% ของ num_leaves < 25% (ปัจจุบัน: {stability_report.get('num_leaves', {}).get('cv', 0):.1f}%)")
    print(f"   - พารามิเตอร์อื่นๆ ควรมี CV% < 20%")
    print(f"")

    print(f"💡 **เคล็ดลับ:**")
    print(f"   - ถ้า CV% ยังสูง = ต้องเพิ่มข้อมูลการเทรนหรือปรับ strategy")
    print(f"   - ถ้า CV% ต่ำมาก (< 5%) = พารามิเตอร์เสถียรดี ไม่ต้องปรับ")
    print(f"   - ถ้าผลลัพธ์แย่ลง = กลับไปใช้ param_dist เดิม")

    # สร้าง summary dict สำหรับ return
    summary_dict = {
        'counter_trend_lr': counter_trend_lr,
        'counter_trend_leaves': counter_trend_leaves,
        'trend_following_lr': trend_following_lr,
        'trend_following_leaves': trend_following_leaves
    }

    return summary_dict

def explain_how_to_read_results():
    """อธิบายวิธีการอ่านผลลัพธ์สำหรับผู้ใช้ใหม่"""
    print(f"\n" + "="*80)
    print("📖 วิธีการอ่านผลลัพธ์ Parameter Stability Analysis")
    print("="*80)

    print(f"\n🔍 **ความหมายของตัวเลข:**")
    print(f"")
    print(f"**Mean (ค่าเฉลี่ย):**")
    print(f"   - ค่าเฉลี่ยของพารามิเตอร์จากทุกโมเดล")
    print(f"   - ยิ่งใกล้เคียงกันในทุกโมเดล = ยิ่งดี")
    print(f"")

    print(f"**Std (ส่วนเบี่ยงเบนมาตรฐาน):**")
    print(f"   - วัดความกระจายของค่า")
    print(f"   - ยิ่งต่ำ = ค่าใกล้เคียงกันมาก")
    print(f"")

    print(f"**CV% (Coefficient of Variation):**")
    print(f"   - วัดความเสถียรของพารามิเตอร์")
    print(f"   - CV% = (Std / Mean) × 100")
    print(f"   - ✅ CV% < 20% = เสถียรดี (High)")
    print(f"   - ⚠️ CV% 20-50% = เสถียรปานกลาง (Medium)")
    print(f"   - ❌ CV% > 50% = ไม่เสถียร (Low)")
    print(f"")

    print(f"🎯 **ตัวอย่างการอ่าน:**")
    print(f"")
    print(f"learning_rate: Mean=0.0544, Std=0.0309, CV=56.9% (Low)")
    print(f"   ↳ หมายความว่า: learning_rate มีค่าเฉลี่ย 0.0544")
    print(f"   ↳ แต่กระจายตัวมาก (CV=56.9%) = ไม่เสถียร")
    print(f"   ↳ ต้องปรับปรุง param_dist")
    print(f"")

    print(f"max_depth: Mean=6.4643, Std=0.7927, CV=12.3% (High)")
    print(f"   ↳ หมายความว่า: max_depth มีค่าเฉลี่ย 6.46")
    print(f"   ↳ กระจายตัวน้อย (CV=12.3%) = เสถียรดี")
    print(f"   ↳ ไม่ต้องปรับปรุง")
    print(f"")

    print(f"📊 **การเปรียบเทียบ Scenarios:**")
    print(f"")
    print(f"counter_trend scenario:")
    print(f"learning_rate: Mean=0.0832, CV=13.2% (High)")
    print(f"   ↳ ใน counter_trend model, learning_rate เสถียรดี")
    print(f"")

    print(f"trend_following scenario:")
    print(f"learning_rate: Mean=0.0255, CV=33.2% (Medium)")
    print(f"   ↳ ใน trend_following model, learning_rate เสถียรปานกลาง")
    print(f"")

    print(f"💡 **สิ่งที่ควรสังเกต:**")
    print(f"   - ถ้า CV% ต่างกันมากระหว่าง scenarios = ควรใช้ param_dist แยกกัน")
    print(f"   - ถ้า CV% สูงในทุก scenarios = ต้องเพิ่มข้อมูลการเทรน")
    print(f"   - ถ้า CV% ต่ำทุกตัว = param_dist ปัจจุบันดีแล้ว")

def create_architecture_comparison_report(all_params):
    """สร้างรายงานเปรียบเทียบระหว่าง architectures"""
    print(f"\nรายงานเปรียบเทียบ Architecture:")
    print("="*60)

    # แยกข้อมูลตาม architecture
    arch_data = {}
    for data in all_params.values():
        arch = data.get('architecture', 'Unknown')
        if arch not in arch_data:
            arch_data[arch] = []
        arch_data[arch].append(data)

    # เปรียบเทียบ performance
    print(f"\nเปรียบเทียบ Performance:")
    print("-"*40)

    for arch, models in arch_data.items():
        scores = [m['score'] for m in models if m['score'] is not None]
        if scores:
            avg_score = np.mean(scores)
            std_score = np.std(scores)
            print(f"{arch:15}: Avg Score={avg_score:.4f} +/- {std_score:.4f} ({len(scores)} models)")
        else:
            print(f"{arch:15}: No score data ({len(models)} models)")

    # เปรียบเทียบ parameter stability ระหว่าง architectures
    print(f"\nเปรียบเทียบ Parameter Stability:")
    print("-"*40)

    numeric_params = ['learning_rate', 'num_leaves', 'max_depth', 'min_data_in_leaf']

    for param in numeric_params:
        print(f"\n{param}:")
        for arch, models in arch_data.items():
            values = []
            for model in models:
                if param in model['params']:
                    values.append(model['params'][param])

            if len(values) > 1:
                mean_val = np.mean(values)
                cv = (np.std(values) / mean_val) * 100 if mean_val != 0 else 0
                stability = 'High' if cv < 20 else 'Medium' if cv < 50 else 'Low'
                print(f"  {arch:12}: CV={cv:5.1f}% ({stability})")
            elif len(values) == 1:
                print(f"  {arch:12}: Single value ({values[0]})")
            else:
                print(f"  {arch:12}: No data")
def main():
    """ฟังก์ชันหลัก"""
    print("Parameter Stability Analysis (Multi-Architecture Support)")
    print("="*70)

    # ตรวจสอบความเข้ากันได้กับ LightGBM_03_Compare.py
    if not check_lightgbm_compatibility():
        print("❌ ตรวจสอบความเข้ากันได้ไม่ผ่าน")
        return

    # โหลดข้อมูล
    all_params = load_all_best_params()

    if not all_params:
        print("\n❌ ไม่พบข้อมูลพารามิเตอร์")
        print("กรุณารัน hyperparameter tuning ก่อน:")
        print("   python LightGBM_10_1.py  # ← ไฟล์หลักใหม่")
        print("   หรือ python LightGBM_03_Compare.py  # ← ไฟล์เก่า (ยังใช้ได้)")
        return

    # วิเคราะห์
    stability_report = analyze_parameter_stability(all_params)

    # สร้างรายงานเปรียบเทียบ architecture
    create_architecture_comparison_report(all_params)

    # แนะนำการปรับปรุง
    if stability_report:
        suggest_param_dist_updates(stability_report)

        # แสดงคำแนะนำสำหรับผู้ใช้ใหม่
        show_step_by_step_guide(stability_report, all_params)

        # อธิบายวิธีการอ่านผลลัพธ์
        explain_how_to_read_results()

    print(f"\nการวิเคราะห์เสร็จสิ้น")

if __name__ == "__main__":
    main()
