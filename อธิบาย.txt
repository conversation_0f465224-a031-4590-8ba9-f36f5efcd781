****************************************************************************

select_features
load_and_process_data
train_all_scenario_models
train_scenario_model

****************************************************************************

แน่นอนครับ นี่คือคำอธิบายการทำงานของฟังก์ชัน select_features ที่คุณเลือกมา:

สรุปการทำงานของฟังก์ชัน select_features
ฟังก์ชันนี้ทำหน้าที่สำคัญในการ คัดเลือก "ฟีเจอร์" (Features) หรือตัวแปรที่ดีที่สุด จากข้อมูลดิบ (trade_df) เพื่อนำไปใช้สอนโมเดล Machine Learning (เช่น LightGBM) ต่อไป

เป้าหมายหลักคือการเลือกชุดฟีเจอร์ที่:

มีความสัมพันธ์กับผลลัพธ์ (Target) ที่เราต้องการทำนาย
ไม่มีความซ้ำซ้อนกันเอง (ลดปัญหา Multicollinearity)
มีฟีเจอร์ที่จำเป็น จากการวิเคราะห์ก่อนหน้าติดไปด้วยเสมอ
กระบวนการนี้เป็นแบบอัตโนมัติและมีขั้นตอนการกรองหลายชั้นเพื่อให้ได้ชุดฟีเจอร์ที่มีคุณภาพที่สุดสำหรับการเทรนโมเดล

ขั้นตอนการทำงานอย่างละเอียด
กำหนดคลังฟีเจอร์ (Feature Groups):

โค้ดเริ่มต้นด้วยการประกาศ Dictionary ขนาดใหญ่ชื่อ feature_groups ซึ่งเปรียบเสมือน "คลังฟีเจอร์" ที่เป็นไปได้ทั้งหมด
ฟีเจอร์เหล่านี้ถูกจัดกลุ่มตามประเภทอย่างชัดเจน เช่น:
time: ฟีเจอร์เกี่ยวกับเวลา (วัน, ชั่วโมง)
price_action, price_info: ฟีเจอร์เกี่ยวกับพฤติกรรมราคา
ema, rsi, sto, adx, atr, bb: ฟีเจอร์จาก Technical Indicators ต่างๆ
Inter: ฟีเจอร์ที่เกิดจากการนำ Indicator มาผสมกัน (Interaction Features)
leg_...: ฟีเจอร์ข้อมูลย้อนหลัง (Lag Features) ของราคา, Volume, และ Indicators
H2, H4, D1, ...: ฟีเจอร์จาก Timeframe ที่ใหญ่กว่า (Multi-Timeframe Features)
รวบรวมและคัดกรองฟีเจอร์เบื้องต้น:

ฟังก์ชันจะวนลูปใน feature_groups เพื่อดึงชื่อฟีเจอร์ทั้งหมดมารวมไว้ใน potential_features_list
จากนั้นจะรวมกับฟีเจอร์ประเภท Lag, Return, Change, MA, Std ที่มีใน DataFrame
สุดท้าย จะคัดกรองอีกครั้งให้เหลือเฉพาะฟีเจอร์ที่ มีอยู่จริงใน DataFrame และเป็นข้อมูลประเภทตัวเลข (number) เท่านั้น
กรองด้วยความสัมพันธ์กับ Target (Correlation Filtering):

ฟังก์ชันจะคำนวณค่า Correlation ระหว่างฟีเจอร์แต่ละตัวกับคอลัมน์ Target (ซึ่งเป็นผลลัพธ์ที่โมเดลต้องทาย)
ฟีเจอร์ที่มีค่า Correlation (แบบค่าสัมบูรณ์) ต่ำกว่าเกณฑ์ที่กำหนด (correlation_threshold = 0.05) จะถูกตัดออก เพราะถือว่าไม่มีความสัมพันธ์กับผลลัพธ์เพียงพอ
กรองด้วย VIF (Multicollinearity Filtering):

จากฟีเจอร์ที่ผ่านขั้นตอนที่ 3 มาแล้ว ฟังก์ชันจะตรวจสอบปัญหา Multicollinearity หรือ "การที่ฟีเจอร์มีความสัมพันธ์กันเองสูงเกินไป"
โดยใช้เทคนิคที่เรียกว่า Variance Inflation Factor (VIF) เพื่อวัดว่าฟีเจอร์ตัวหนึ่งๆ สามารถถูกอธิบายด้วยฟีเจอร์ตัวอื่นๆ ได้ดีแค่ไหน
ฟีเจอร์ที่มีค่า VIF สูงกว่า 10 จะถูกตัดออก เพื่อลดความซ้ำซ้อนและทำให้โมเดลมีความเสถียรมากขึ้น
เพิ่มฟีเจอร์ที่จำเป็น (Forcing Must-Have Features):

ขั้นตอนนี้สำคัญมาก ฟังก์ชันจะพยายามโหลดไฟล์ ..._must_have_features.pkl ซึ่งเก็บรายชื่อฟีเจอร์ที่ จำเป็นต้องมี ในการเทรนโมเดลเสมอ (อาจได้มาจากการวิเคราะห์ Cross-Asset Feature Importance หรือความรู้เฉพาะทาง)
หากหาไฟล์ไม่เจอ จะใช้รายการ add_features_in_model ที่ hardcode ไว้เป็นค่าเริ่มต้น
ฟังก์ชันจะนำฟีเจอร์ในรายการนี้ เพิ่มกลับเข้าไป ในชุดฟีเจอร์ที่เลือกไว้ (หากยังไม่มี) เพื่อให้แน่ใจว่าฟีเจอร์สำคัญๆ จะไม่ถูกตัดออกไปในขั้นตอนก่อนหน้า
ทำความสะอาดและส่งคืนผลลัพธ์:

ตรวจสอบให้แน่ใจว่าคอลัมน์ 'Target' ไม่ได้ถูกรวมอยู่ในชุดฟีเจอร์สุดท้าย
กรณีพิเศษ: หากหลังจากผ่านทุกขั้นตอนแล้วไม่มีฟีเจอร์เหลือเลย ฟังก์ชันจะคืนค่า available_numeric_features ทั้งหมดกลับไป เพื่อป้องกันไม่ให้กระบวนการทั้งหมดล้มเหลว
สุดท้าย ฟังก์ชันจะคืนค่า List ของชื่อฟีเจอร์ (Final Selected Features) ที่ผ่านการคัดเลือกทั้งหมด เพื่อนำไปใช้เป็น Input สำหรับการเทรนโมเดลต่อไป

****************************************************************************

แน่นอนครับ นี่คือคำอธิบายการทำงานของฟังก์ชัน load_and_process_data ที่คุณเลือกมา ซึ่งเป็นหัวใจสำคัญของการเตรียมข้อมูลทั้งหมดก่อนส่งให้โมเดลเรียนรู้

สรุปการทำงานของฟังก์ชัน load_and_process_data
ฟังก์ชันนี้เปรียบเสมือน "โรงงานเตรียมข้อมูล" ที่รับข้อมูลราคาดิบ (df) เข้ามา แล้วผ่านกระบวนการหลายขั้นตอนเพื่อสร้างชุดข้อมูลที่พร้อมสำหรับการฝึก (Train), ประเมิน (Validate), และทดสอบ (Test) โมเดล Machine Learning

เป้าหมายหลักของฟังก์ชันนี้คือ:

จำลองการซื้อขาย (Trade Cycles): สร้างรายการซื้อขายจำลองจากข้อมูลราคาดิบตามเงื่อนไขที่กำหนด โดยอาจใช้โมเดลที่เคยฝึกไว้แล้วมาช่วยกรองสัญญาณ (Multi-Model Architecture)
แก้ไขปัญหา Lookahead Bias: ตรวจสอบและแก้ไขไม่ให้ข้อมูลจากอนาคตรั่วไหลไปในอดีตขณะที่รวมข้อมูล (Merge)
สร้าง Target Variable: กำหนด "คำตอบ" ที่จะให้โมเดลเรียนรู้ (เช่น เทรดนี้จะกำไรหรือขาดทุน)
คัดเลือกฟีเจอร์ (Feature Selection): เรียกใช้ฟังก์ชัน select_features เพื่อคัดเลือกเฉพาะตัวแปรที่สำคัญ
ตรวจสอบคุณภาพข้อมูล: เช็คปัญหาต่างๆ เช่น Class Imbalance, Missing Values, และข้อมูลน้อยเกินไป
แบ่งข้อมูล: แบ่งข้อมูลทั้งหมดออกเป็น 3 ส่วน (Train, Validation, Test) ตามลำดับเวลา
ปรับสเกลฟีเจอร์ (Feature Scaling): ปรับค่าของฟีเจอร์ทั้งหมดให้อยู่ในสเกลเดียวกัน
ขั้นตอนการทำงานอย่างละเอียด
โหลดข้อมูลเสริมและสร้าง Trade Cycles:

โหลด Features: พยายามโหลดรายชื่อฟีเจอร์ที่โมเดลเคยใช้จากไฟล์ .pkl เพื่อให้แน่ใจว่าใช้ฟีเจอร์ชุดเดียวกัน
โหลดเงื่อนไขเข้าเทรด (Entry Condition): โหลดเงื่อนไขการเข้าเทรดที่ดีที่สุดจากรอบก่อนหน้า (ถ้ามี) เพื่อใช้เป็นพื้นฐานในการสร้างสัญญาณ
โหลดโมเดลและ Thresholds: สำหรับ Multi-Model Architecture จะมีการโหลดโมเดลที่เคยฝึกไว้แล้วจากหลายๆ สถานการณ์ (Scenarios) พร้อมกับค่า threshold (ค่าความมั่นใจ) ที่เหมาะสมของแต่ละโมเดล
สร้างรายการซื้อขาย (Trade Cycles):
เรียกใช้ฟังก์ชัน try_trade_with_threshold_adjustment เพื่อจำลองการเทรดตามเงื่อนไขที่กำหนด
หากมีโมเดลอยู่แล้ว จะใช้โมเดลช่วยกรองสัญญาณ (ให้แม่นยำขึ้น) หากไม่มี จะใช้แค่ Technical Analysis พื้นฐาน
ผลลัพธ์ที่ได้คือ trade_df ซึ่งเป็น DataFrame ที่เก็บประวัติการซื้อขายจำลอง (เวลาเข้า, ราคาเข้า, เวลาออก, กำไร/ขาดทุน, ฯลฯ)
วิเคราะห์ผลการเทรดเบื้องต้น:

นำ trade_df มาคำนวณสถิติพื้นฐาน เช่น Win Rate, Expectancy, และแยกตามวัน/ชั่วโมง เพื่อดูภาพรวมประสิทธิภาพของกลยุทธ์
รวมข้อมูลฟีเจอร์ (Merge Features) และแก้ไข Lookahead Bias:

ปัญหา: การตัดสินใจเข้าเทรด ณ เวลา T ต้องใช้ข้อมูลจากแท่งเทียนก่อนหน้า (T-1) เท่านั้น การนำข้อมูลจากเวลา T มารวมโดยตรงถือเป็น Lookahead Bias (การมองไปในอนาคต)
วิธีแก้:
สร้างคอลัมน์เวลา (Merge_Key_Time) ใน trade_df โดย เลื่อนเวลาเข้าเทรดไปข้างหลังเล็กน้อย (เช่น 5 นาที)
ใช้ pd.merge_asof เพื่อนำข้อมูลฟีเจอร์ทั้งหมดจาก df (ข้อมูลดิบ) มารวมกับ trade_df โดยใช้ Merge_Key_Time เป็นตัวเชื่อม และตั้งค่า direction='backward'
ผลลัพธ์คือ แต่ละเทรดใน trade_df จะได้ข้อมูลฟีเจอร์มาจาก แท่งเทียนล่าสุดก่อนที่จะเกิดสัญญาณเข้าเทรด ซึ่งเป็นการแก้ไข Lookahead Bias ได้อย่างถูกต้อง
สร้าง Target Variable:

เรียกใช้ฟังก์ชัน process_trade_targets เพื่อสร้างคอลัมน์ Target
โดยทั่วไป Target จะถูกกำหนดจากคอลัมน์ Profit เช่น 1 ถ้าเทรดนั้นกำไร และ 0 ถ้าขาดทุน
คัดเลือกฟีเจอร์ (Feature Selection):

หลังจากข้อมูลพร้อมแล้ว (มีทั้งฟีเจอร์และ Target) จะเรียกใช้ฟังก์ชัน select_features (ที่อธิบายไปก่อนหน้านี้) เพื่อคัดกรองและเลือกชุดฟีเจอร์ที่ดีที่สุดสำหรับนำไปฝึกโมเดล
ตรวจสอบคุณภาพข้อมูลขั้นสุดท้าย:

Class Imbalance: ตรวจสอบสัดส่วนของ Target (0 vs 1) ว่ามีความสมดุลหรือไม่ หากไม่สมดุลอาจต้องใช้เทคนิคพิเศษในการเทรน
Missing Values: ตรวจสอบว่ามีข้อมูลหายไปในฟีเจอร์ที่เลือกมาหรือไม่
จำนวนข้อมูล: เช็คว่ามีข้อมูลเพียงพอสำหรับการเทรนหรือไม่
Correlation: ตรวจสอบอีกครั้งว่าฟีเจอร์ที่เลือกมามีความสัมพันธ์กันเองสูงเกินไปหรือไม่
แบ่งข้อมูล (Train/Validation/Test Split):

เรียงข้อมูลตามเวลา (Entry Time)
แบ่งข้อมูลออกเป็น 3 ส่วนตามสัดส่วนเวลา (เช่น 60% แรกเป็น Train, 20% ถัดมาเป็น Validation, 20% สุดท้ายเป็น Test) เพื่อป้องกันไม่ให้ข้อมูลอนาคตปนอยู่ในชุดฝึก
ปรับสเกลฟีเจอร์ (Feature Scaling):

ใช้ StandardScaler เพื่อปรับขนาดข้อมูล
fit_transform กับชุด X_train เพื่อเรียนรู้ค่าเฉลี่ยและส่วนเบี่ยงเบนมาตรฐาน
transform กับชุด X_val และ X_test โดยใช้ค่าที่เรียนรู้จาก X_train เพื่อให้ข้อมูลทั้งหมดอยู่ในสเกลเดียวกัน
สุดท้าย ฟังก์ชันจะคืนค่าชุดข้อมูลทั้ง 6 ชุด (X_train, y_train, X_val, y_val, X_test, y_test) ที่พร้อมสำหรับขั้นตอนการฝึกและประเมินโมเดลต่อไป

****************************************************************************

แน่นอนครับ โค้ดส่วนที่คุณเลือกมาเป็นส่วนควบคุมหลัก (Main Controller) สำหรับการฝึกโมเดล หรือการโหลดโมเดลที่เคยฝึกไว้แล้ว และยังรวมถึงการทดสอบพารามิเตอร์ที่สำคัญ (Optimal Parameters) ด้วย

สรุปการทำงานของโค้ดส่วนนี้
โค้ดส่วนนี้ทำหน้าที่เป็น "ทางแยก" โดยจะตรวจสอบค่าของ TRAIN_NEW_MODEL เพื่อตัดสินใจว่าจะ:

เทรนโมเดลใหม่ (TRAIN_NEW_MODEL = True):

เรียกใช้ฟังก์ชัน train_all_scenario_models เพื่อฝึกโมเดลสำหรับทุกสถานการณ์ (เช่น Trend Following, Counter Trend)
รวบรวมผลลัพธ์การเทรน (Metrics) จากทุกสถานการณ์มาหาค่าเฉลี่ย
โหลดโมเดลเดิม (TRAIN_NEW_MODEL = False):

เรียกใช้ฟังก์ชัน load_scenario_models เพื่อโหลดโมเดลที่เคยฝึกและบันทึกไว้แล้ว
มี Fallback Logic: หากโหลดไม่สำเร็จ (เช่น ไม่มีไฟล์โมเดล) ระบบจะเปลี่ยนไปเทรนโมเดลใหม่โดยอัตโนมัติ
หลังจากได้โมเดลมาแล้ว (ไม่ว่าจะจากการเทรนใหม่หรือโหลดมา) โค้ดจะทำขั้นตอนต่อไปนี้:

ทดสอบหาพารามิเตอร์ที่เหมาะสมที่สุด (TEST_OPTIMAL_PARAMETERS = True):
บันทึก Artifacts: บันทึกไฟล์โมเดลและชุดข้อมูล Validation (validation_set.csv) เพื่อให้สามารถนำไปทดสอบแบบ Offline หรือวิเคราะห์ซ้ำได้ในภายหลัง
หา Optimal Threshold: เรียกใช้ find_optimal_threshold_multi_model เพื่อหาค่าความมั่นใจ (Threshold) ที่ดีที่สุดสำหรับแต่ละสถานการณ์ โดยใช้ข้อมูล Validation Set
หา Optimal nBars SL: เรียกใช้ find_optimal_nbars_sl_multi_model เพื่อหาระยะ Stop Loss ที่เหมาะสมที่สุด (ในหน่วยของจำนวนแท่งเทียน) สำหรับแต่ละสถานการณ์
สุดท้าย โค้ดจะรวบรวมผลลัพธ์ทั้งหมด (โมเดล, ฟีเจอร์ที่ใช้, ผลการประเมิน, และ optimal parameters) เก็บไว้ใน result_dict เพื่อนำไปใช้ในขั้นตอนต่อไป เช่น การทำนายผล หรือการ Backtest

ขั้นตอนการทำงานอย่างละเอียด
ตรวจสอบ TRAIN_NEW_MODEL:

ถ้าเป็น True:
เตรียมข้อมูล combined_df โดยการนำ df (ข้อมูลราคาและ Indicators) มารวมกับ trade_df (ที่มีข้อมูล Target)
เรียก train_all_scenario_models ซึ่งเป็นฟังก์ชันหลักที่เข้าไปจัดการการเทรนโมเดลสำหรับแต่ละ Scenario (สถานการณ์)
ถ้าเป็น False:
พยายามโหลดโมเดลด้วย load_scenario_models
ถ้าโหลดสำเร็จ ก็จะเตรียม combined_df สำหรับขั้นตอนต่อไป
ถ้าโหลดล้มเหลว: จะพิมพ์ข้อความแจ้งเตือนและเปลี่ยนไปเรียก train_all_scenario_models เพื่อเทรนใหม่แทน (นี่คือ Fallback Mechanism ที่ดี)
รวบรวมผลลัพธ์การเทรน:

หลังจากได้ scenario_results (ซึ่งเป็น Dictionary ที่เก็บโมเดลและข้อมูลของแต่ละสถานการณ์)
โค้ดจะวนลูปเพื่อดึงค่า Metrics (เช่น Accuracy, Precision) และ CV Results (ผลการทำ Cross-Validation) จากทุกสถานการณ์มารวมกัน
คำนวณค่าเฉลี่ยของ Metrics ทั้งหมด เพื่อให้ได้ภาพรวมประสิทธิภาพของระบบ Multi-Model ทั้งหมด
ทดสอบ Optimal Parameters (ถ้า TEST_OPTIMAL_PARAMETERS เป็น True):

เตรียมข้อมูล Validation: ดึงข้อมูลส่วน Validation (val_data) ที่แบ่งไว้ก่อนหน้านี้มาใช้สำหรับการทดสอบพารามิเตอร์
บันทึก Artifacts:
Models Artifact: บันทึก scenario_results ทั้งหมดลงในไฟล์ .pkl
Validation Set Artifact: บันทึกข้อมูล val_df_for_optimization (ซึ่งก็คือข้อมูล Validation) ลงในไฟล์ .csv เพื่อความสะดวกในการนำไปใช้ที่อื่น
หา Optimal Threshold:
สำหรับแต่ละ Scenario (trend_following, counter_trend) จะมีการโหลดโมเดล, scaler, และ feature list ที่ถูกต้อง
ทำนายความน่าจะเป็น (predict_proba) บนข้อมูล Validation
เรียก find_optimal_threshold_multi_model เพื่อคำนวณหาค่า Threshold ที่ให้ผลลัพธ์ดีที่สุด (เช่น ค่า Youden's J statistic สูงสุด)
บันทึกค่า Threshold ที่ได้ลงไฟล์ .pkl
หา Optimal nBars SL:
เรียก find_optimal_nbars_sl_multi_model เพื่อจำลองการเทรดบนข้อมูล Validation โดยเปลี่ยนค่า nBars_SL ไปเรื่อยๆ เพื่อหาค่าที่ให้ผลตอบแทนดีที่สุดสำหรับแต่ละ Scenario
Fallback to Single-Model:

ในกรณีที่การเทรนแบบ Multi-Model ล้มเหลวทั้งหมด โค้ดมีกลไกสำรองที่จะเปลี่ยนไปเทรนแบบ Single-Model (โมเดลเดียว) โดยใช้ฟังก์ชัน train_and_evaluate แทน เพื่อให้กระบวนการยังสามารถดำเนินต่อไปได้

****************************************************************************

แน่นอนครับ โค้ดส่วนนี้คือฟังก์ชัน train_all_scenario_models ซึ่งเป็น "โรงงานฝึกโมเดล" หลักของระบบ ทำหน้าที่จัดการกระบวนการฝึกโมเดลสำหรับทุกสถานการณ์ตลาดที่กำหนดไว้

สรุปการทำงานของฟังก์ชัน train_all_scenario_models
ฟังก์ชันนี้จะรับ DataFrame ที่เตรียมข้อมูลมาแล้ว (df) และจัดการฝึกโมเดลหลายๆ ตัวตาม "สถานการณ์ตลาด" (Market Scenarios) ที่แตกต่างกัน เช่น trend_following (เทรดตามแนวโน้ม) และ counter_trend (เทรดสวนแนวโน้ม) นอกจากนี้ยังแยกฝึกโมเดลสำหรับเป้าหมาย Buy และ Sell โดยเฉพาะอีกด้วย

กระบวนการหลักประกอบด้วย:

เตรียมสภาพแวดล้อม: สร้างโฟลเดอร์ต่างๆ เพื่อจัดเก็บผลลัพธ์, โมเดล, และกราฟอย่างเป็นระเบียบ
ระบุสถานการณ์ตลาด: เพิ่มคอลัมน์ market_scenario ใน DataFrame เพื่อแบ่งข้อมูลตามสภาวะตลาด
วนลูปฝึกโมเดล:
วนลูปตามแต่ละ scenario_name (เช่น trend_following)
วนลูปตามแต่ละ target_type (เช่น Target_Multiclass, Target_Buy, Target_Sell)
ในแต่ละรอบ จะเตรียมข้อมูล (prepare_scenario_data) และเรียกใช้ train_scenario_model เพื่อฝึกโมเดลสำหรับสถานการณ์และเป้าหมายนั้นๆ
รวบรวมและวิเคราะห์ผล:
เก็บผลลัพธ์การเทรนทั้งหมด (โมเดล, metrics, feature importance) ไว้ใน Dictionary ชื่อ results
สร้างรายงานและกราฟเปรียบเทียบประสิทธิภาพของโมเดลต่างๆ
ทำการวิเคราะห์ Feature Importance เพื่อดูว่าตัวแปรใดมีผลต่อการทำนายมากที่สุด
ทำการ Cross-Validation เพื่อประเมินความเสถียรของโมเดล
บันทึกผลสรุป: บันทึกสถิติและผลการประเมินของแต่ละโมเดลลงในระบบสรุปผล (Summary System) เพื่อการติดตามและเปรียบเทียบในระยะยาว
ขั้นตอนการทำงานอย่างละเอียด
Initialization:

เริ่มต้นด้วยการพิมพ์ Log เพื่อบอกสถานะการทำงาน
สร้างโครงสร้างโฟลเดอร์ที่จำเป็นทั้งหมด (results/plots, results/trend_following, results/counter_trend, etc.) เพื่อให้ไฟล์ผลลัพธ์ถูกจัดเก็บอย่างเป็นระบบ
Add Market Scenario:

เรียกใช้ฟังก์ชัน add_market_scenario_column เพื่อเพิ่มคอลัมน์ใหม่ชื่อ market_scenario เข้าไปใน DataFrame
คอลัมน์นี้จะระบุว่า ณ เวลานั้นๆ ตลาดอยู่ในสภาวะ "Trend Following" หรือ "Counter Trend" โดยอิงจากเงื่อนไขทางเทคนิค (เช่น ตำแหน่งราคาเทียบกับเส้น EMA200)
Main Training Loop:

Loop 1 (Target_Multiclass):
วนลูปตาม scenario_name ที่กำหนดไว้ใน MARKET_SCENARIOS
เรียก prepare_scenario_data เพื่อกรอง DataFrame ให้เหลือเฉพาะข้อมูลที่ตรงกับ scenario_name ปัจจุบัน และแยกข้อมูลเป็น X (Features) และ y (Target)
เรียก train_scenario_model ซึ่งเป็นฟังก์ชันย่อยที่ทำการฝึกโมเดล LightGBM จริงๆ, ประเมินผล, และคืนค่าผลลัพธ์กลับมา
เก็บผลลัพธ์ที่ได้ไว้ใน results
Loop 2 (Target_Buy and Target_Sell):
เพิ่มการวนลูปสำหรับ target_type ที่เป็น 'Buy' และ 'Sell'
กรอง DataFrame ให้เหลือเฉพาะข้อมูลที่มี Target สำหรับ Buy หรือ Sell (โดยที่ค่าไม่ใช่ -1)
ทำกระบวนการเดียวกันกับ Loop 1 คือ prepare_scenario_data และ train_scenario_model แต่จะตั้งชื่อโมเดลให้มี Suffix ต่อท้าย เช่น trend_following_Buy
ขั้นตอนนี้เป็นการสร้างโมเดลที่เชี่ยวชาญในการหาจังหวะ Buy หรือ Sell โดยเฉพาะ
Post-Training Analysis & Reporting:

Feature Importance:
เรียก save_combined_random_forest_importance และ create_combined_feature_importance เพื่อรวบรวม Feature Importance จากทุกโมเดลที่เทรนมา แล้วสร้างเป็นรายงานสรุป ทำให้เห็นภาพรวมว่าฟีเจอร์กลุ่มไหนสำคัญที่สุดในทุกสภาวะตลาด
Cross-Validation (CV):
เรียก time_series_cv เพื่อทำการประเมินโมเดลด้วยเทคนิค Time Series Cross-Validation ซึ่งเหมาะสมกับข้อมูลอนุกรมเวลามากกว่า CV แบบปกติ เป็นการทดสอบความทนทานของโมเดล
Performance Reports:
เรียกฟังก์ชันต่างๆ (create_multi_scenario_performance_analysis, create_performance_comparison_plots, create_final_and_training_results) เพื่อสร้างไฟล์สรุปและกราฟเปรียบเทียบประสิทธิภาพในด้านต่างๆ ของแต่ละโมเดล
Save to Summary System:

ขั้นตอนสุดท้ายคือการนำผลลัพธ์ทั้งหมด (สถิติการเทรด, ค่า metrics ของโมเดล, configuration ที่ใช้) ของแต่ละโมเดลที่เทรนสำเร็จ
เรียก save_training_results_to_summary เพื่อบันทึกข้อมูลเหล่านี้ลงในไฟล์สรุปกลาง (อาจเป็นไฟล์ CSV หรือ JSON) ซึ่งช่วยให้สามารถติดตามผลการทดลองต่างๆ ในระยะยาวได้ง่ายขึ้น

****************************************************************************

แน่นอนครับ นี่คือคำอธิบายของฟังก์ชัน train_scenario_model ซึ่งเป็นหน่วยปฏิบัติการหลักในการฝึกโมเดลสำหรับแต่ละสถานการณ์ (Scenario) ที่กำหนด

สรุปการทำงานของฟังก์ชัน train_scenario_model
ฟังก์ชันนี้เปรียบเสมือน "ห้องเครื่อง" ที่รับข้อมูล Features (X) และ Target (y) ที่ผ่านการกรองตามสถานการณ์มาแล้ว และทำหน้าที่ทั้งหมดที่เกี่ยวข้องกับการฝึกโมเดล LightGBM หนึ่งตัว ตั้งแต่ต้นจนจบ

กระบวนการหลักประกอบด้วย:

การเตรียมข้อมูล (Data Preparation):

ทำความสะอาดข้อมูล เช่น ลบค่า NaN หรือ inf
ตรวจสอบให้แน่ใจว่ามีข้อมูลเพียงพอและมี Class ของ Target มากกว่า 1 Class
แบ่งข้อมูลออกเป็น 3 ส่วน: Train (สำหรับฝึก), Validation (สำหรับจูนโมเดล), และ Test (สำหรับวัดผลสุดท้าย)
ทำการปรับสเกลข้อมูล (Feature Scaling) ด้วย StandardScaler
การจูนไฮเปอร์พารามิเตอร์ (Hyperparameter Tuning):

ตรวจสอบว่าต้องทำการจูนใหม่หรือไม่ (เช็คจาก flag_file หรือ force_retune)
ถ้าต้องจูน: ใช้ RandomizedSearchCV เพื่อค้นหาชุดพารามิเตอร์ที่ดีที่สุดสำหรับโมเดล LightGBM แล้วบันทึกผลไว้
ถ้าไม่ต้องจูน: โหลดชุดพารามิเตอร์ที่ดีที่สุดที่เคยบันทึกไว้มาใช้งาน
การฝึกโมเดล (Model Training):

สร้างโมเดล LightGBM (LGBMClassifier) ด้วยพารามิเตอร์ที่ได้จากขั้นตอนที่แล้ว
ทำการ fit หรือฝึกโมเดลด้วยข้อมูล X_train และ y_train โดยใช้ X_val และ y_val ในการทำ Early Stopping (หยุดเทรนเมื่อโมเดลไม่ดีขึ้นแล้ว)
การประเมินผลและบันทึก (Evaluation & Saving):

นำโมเดลที่ฝึกเสร็จแล้วไปทดสอบกับข้อมูล X_test
คำนวณค่า Metrics ต่างๆ เช่น Accuracy, F1-Score, AUC, Classification Report, Confusion Matrix
ตรวจสอบคุณภาพ: เรียกใช้ evaluate_and_decide_model_save เพื่อประเมินว่าโมเดลใหม่ดีกว่าโมเดลเก่าที่เคยบันทึกไว้หรือไม่
ถ้าดีกว่า (หรือยังไม่มีโมเดลเก่า): บันทึกไฟล์โมเดล (.pkl), ไฟล์รายชื่อ Features, และไฟล์ Scaler ไว้
ถ้าไม่ดีกว่า: จะไม่บันทึกทับโมเดลเก่า
การสร้างรายงาน (Reporting):

สร้างกราฟ Feature Importance เพื่อแสดงว่าฟีเจอร์ใดมีผลต่อการทำนายของโมเดลมากที่สุด
สร้างผลลัพธ์ทั้งหมดรวมกันเป็น Dictionary (result) เพื่อส่งกลับไปให้ฟังก์ชันแม่ (train_all_scenario_models) รวบรวมต่อไป
ขั้นตอนการทำงานอย่างละเอียด
Data Cleaning & Splitting:

Sanity Checks: เริ่มต้นด้วยการตรวจสอบข้อมูลเบื้องต้น เช่น ข้อมูลว่างเปล่า, มี NaN หรือไม่, มีจำนวนข้อมูลน้อยเกินไปหรือไม่, และมี Target ครบทุกคลาสหรือไม่ หากไม่ผ่านเงื่อนไข ฟังก์ชันจะหยุดทำงานและคืนค่า None
Train-Test Split: แบ่งข้อมูล X และ y ออกเป็นชุด Train (60%), Validation (20%), และ Test (20%) โดยพยายามใช้ stratify=y เพื่อให้สัดส่วนของ Target ในแต่ละชุดใกล้เคียงกับข้อมูลตั้งต้น (สำคัญมากสำหรับข้อมูลที่ไม่สมดุล)
Feature Scaling: สร้าง StandardScaler และ fit เฉพาะบน X_train จากนั้นนำ Scaler ที่ได้ไป transform ข้อมูลทั้ง 3 ชุด (Train, Val, Test) เพื่อป้องกัน Data Leakage
Hyperparameter Tuning:

เป็นระบบอัตโนมัติที่ช่วยลดภาระการจูนโมเดลซ้ำๆ
ครั้งแรกที่รันสำหรับ Scenario นั้นๆ จะทำการ Tuning ด้วย RandomizedSearchCV เพื่อหา best_params
จากนั้นจะบันทึก best_params ลงไฟล์ .json และสร้าง flag_file เพื่อบอกว่า "จูนเสร็จแล้ว"
ในการรันครั้งต่อไป ฟังก์ชันจะเห็น flag_file และจะข้ามการจูนไป แต่จะโหลด best_params จากไฟล์ .json มาใช้ทันที ซึ่งช่วยประหยัดเวลาได้มาก
Model Fitting:

สร้างโมเดล LGBMClassifier พร้อมใส่ best_params ที่ได้มา
เรียก model.fit() โดยส่ง eval_set ที่เป็นข้อมูล Validation เข้าไปด้วย
ใช้ callbacks=[lgb.early_stopping(50)] หมายความว่า "ถ้าเทรนไป 50 รอบแล้วคะแนนบน eval_set ไม่ดีขึ้นเลย ให้หยุดเทรนทันที" เพื่อป้องกัน Overfitting และประหยัดเวลา
Evaluation & Saving Logic:

หลังจากโมเดลเทรนเสร็จ จะนำไป predict บนชุด Test เพื่อวัดประสิทธิภาพที่แท้จริง
ส่วนที่สำคัญคือ evaluate_and_decide_model_save ซึ่งเป็นระบบป้องกันไม่ให้โมเดลที่แย่ลงถูกบันทึกทับโมเดลที่ดีอยู่แล้ว โดยจะมีการเปรียบเทียบ Metrics ของโมเดลใหม่กับโมเดลเก่าก่อนตัดสินใจบันทึก
Result Compilation:

สุดท้าย ฟังก์ชันจะรวบรวมทุกอย่างที่เกี่ยวข้องกับโมเดลนี้ ไม่ว่าจะเป็นตัวโมเดลเอง, Scaler, รายชื่อ Features, ผล Metrics, และกราฟ Feature Importance แพ็ครวมเป็น Dictionary ก้อนใหญ่แล้ว return กลับไป

****************************************************************************