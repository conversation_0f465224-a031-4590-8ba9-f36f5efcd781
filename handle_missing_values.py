import pandas as pd
import numpy as np
from sklearn.impute import KNNImputer
import warnings

def handle_missing_values(df, method='knn', n_neighbors=5, weights='uniform', columns_to_exclude=None):
    """
    จัดการ Missing Values ด้วยวิธีต่างๆ โดยเน้น KNN Imputer
    
    Args:
        df (pd.DataFrame): DataFrame ที่ต้องการจัดการ Missing Values
        method (str): วิธีการจัดการ Missing Values ('knn', 'mean', 'median', 'forward', 'backward')
        n_neighbors (int): จำนวนเพื่อนบ้านที่ใช้ในการคำนวณ (สำหรับ KNN)
        weights (str): น้ำหนักที่ใช้ในการคำนวณ ('uniform', 'distance')
        columns_to_exclude (list): รายชื่อคอลัมน์ที่ไม่ต้องการจัดการ Missing Values
        
    Returns:
        pd.DataFrame: DataFrame ที่จัดการ Missing Values แล้ว
    """
    # ตรวจสอบว่ามี Missing Values หรือไม่
    if df.isnull().sum().sum() == 0:
        print("✅ ไม่พบ Missing Values ในข้อมูล")
        return df
    
    # คัดลอก DataFrame เพื่อไม่ให้กระทบข้อมูลต้นฉบับ
    df_clean = df.copy()
    
    # กำหนดคอลัมน์ที่ต้องการจัดการ Missing Values
    if columns_to_exclude is None:
        columns_to_exclude = ['Date', 'Time', 'Symbol', 'Timeframe', 'Entry Time', 'Exit Time', 'Trade Type']
    
    # คัดกรองคอลัมน์ที่เป็นตัวเลขและมี Missing Values
    numeric_columns = df_clean.select_dtypes(include=['number']).columns.tolist()
    columns_to_process = [col for col in numeric_columns if col not in columns_to_exclude]
    
    # ตรวจสอบว่ามีคอลัมน์ที่ต้องการจัดการหรือไม่
    if not columns_to_process:
        print("⚠️ ไม่พบคอลัมน์ตัวเลขที่มี Missing Values")
        return df_clean
    
    # จัดการ Missing Values ตามวิธีที่เลือก
    try:
        if method == 'knn':
            print(f"🔍 กำลังใช้ KNN Imputer (n_neighbors={n_neighbors}, weights={weights}) จัดการ Missing Values...")
            
            # เตรียมข้อมูลสำหรับ KNN Imputer
            data_for_impute = df_clean[columns_to_process].values
            
            # สร้างและใช้งาน KNN Imputer
            imputer = KNNImputer(n_neighbors=n_neighbors, weights=weights)
            imputed_data = imputer.fit_transform(data_for_impute)
            
            # นำข้อมูลที่ impute แล้วกลับไปใส่ใน DataFrame
            df_clean[columns_to_process] = imputed_data
            
        elif method == 'mean':
            print("🔍 กำลังใช้ Mean Imputation จัดการ Missing Values...")
            for col in columns_to_process:
                df_clean[col].fillna(df_clean[col].mean(), inplace=True)
                
        elif method == 'median':
            print("🔍 กำลังใช้ Median Imputation จัดการ Missing Values...")
            for col in columns_to_process:
                df_clean[col].fillna(df_clean[col].median(), inplace=True)
                
        elif method == 'forward':
            print("🔍 กำลังใช้ Forward Fill จัดการ Missing Values...")
            df_clean[columns_to_process] = df_clean[columns_to_process].fillna(method='ffill')
            
        elif method == 'backward':
            print("🔍 กำลังใช้ Backward Fill จัดการ Missing Values...")
            df_clean[columns_to_process] = df_clean[columns_to_process].fillna(method='bfill')
            
        else:
            print(f"⚠️ ไม่รู้จักวิธี '{method}' ใช้ KNN Imputer แทน")
            imputer = KNNImputer(n_neighbors=5)
            df_clean[columns_to_process] = imputer.fit_transform(df_clean[columns_to_process])
    
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการจัดการ Missing Values: {str(e)}")
        print("⚠️ ใช้วิธี Forward Fill และ Backward Fill แทน")
        
        # ใช้ Forward Fill และ Backward Fill เป็น Fallback
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            df_clean[columns_to_process] = df_clean[columns_to_process].fillna(method='ffill').fillna(method='bfill')
    
    # ตรวจสอบว่ายังมี Missing Values เหลืออยู่หรือไม่
    remaining_missing = df_clean[columns_to_process].isnull().sum().sum()
    if remaining_missing > 0:
        print(f"⚠️ ยังมี Missing Values เหลืออยู่ {remaining_missing} ค่า หลังจากใช้วิธี {method}")
        print("⚠️ ใช้ค่าเฉลี่ยแทนสำหรับค่าที่เหลือ")
        
        # ใช้ค่าเฉลี่ยแทนสำหรับค่าที่เหลือ
        for col in columns_to_process:
            if df_clean[col].isnull().sum() > 0:
                df_clean[col].fillna(df_clean[col].mean(), inplace=True)
    
    # แสดงสรุปผลการจัดการ Missing Values
    print(f"✅ จัดการ Missing Values ด้วยวิธี {method} เรียบร้อยแล้ว")
    
    return df_clean