"""
🔄 Model Recovery System
========================

ระบบกู้คืนโมเดลที่ดีจากการเทรนครั้งที่ 2 (+$5,940)
และป้องกันการเทรนทับด้วยโมเดลที่แย่
"""

import os
import json
import pickle
import shutil
from datetime import datetime
import pandas as pd

class ModelRecoverySystem:
    def __init__(self):
        self.backup_dir = "model_recovery_backup"
        self.performance_log = "model_performance_log.json"
        self.recovery_log = "recovery_operations.log"
        
        # สร้างโฟลเดอร์
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def backup_current_models(self):
        """สำรองโมเดลปัจจุบันก่อนการกู้คืน"""
        
        print("💾 สำรองโมเดลปัจจุบัน...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        current_backup_dir = os.path.join(self.backup_dir, f"current_models_{timestamp}")
        os.makedirs(current_backup_dir, exist_ok=True)
        
        # โฟลเดอร์ที่ต้องสำรอง
        model_dirs = [
            "LightGBM/Multi/models",
            "LightGBM/Multi/thresholds", 
            "LightGBM/Multi/results",
            "LightGBM/Data_Trained"
        ]
        
        backed_up_count = 0
        for model_dir in model_dirs:
            if os.path.exists(model_dir):
                backup_path = os.path.join(current_backup_dir, os.path.basename(model_dir))
                shutil.copytree(model_dir, backup_path)
                print(f"   ✅ สำรอง: {model_dir} → {backup_path}")
                backed_up_count += 1
            else:
                print(f"   ⚠️ ไม่พบ: {model_dir}")
        
        # บันทึก log
        backup_info = {
            'timestamp': timestamp,
            'backup_dir': current_backup_dir,
            'backed_up_dirs': backed_up_count,
            'operation': 'backup_current_models'
        }
        
        self._log_operation(backup_info)
        
        print(f"✅ สำรองโมเดลปัจจุบันเสร็จสิ้น: {backed_up_count} โฟลเดอร์")
        return current_backup_dir
    
    def find_best_model_backup(self):
        """ค้นหาโมเดลที่ดีที่สุดจาก backup"""
        
        print("🔍 ค้นหาโมเดลที่ดีที่สุด...")
        
        # ค้นหาไฟล์ performance log
        performance_files = []
        
        # ค้นหาใน Financial_Analysis_Results
        if os.path.exists("Financial_Analysis_Results"):
            for file in os.listdir("Financial_Analysis_Results"):
                if file.endswith('.json') and 'financial_analysis' in file:
                    performance_files.append(os.path.join("Financial_Analysis_Results", file))
        
        # ค้นหาใน LightGBM/Multi/results
        if os.path.exists("LightGBM/Multi/results"):
            for root, dirs, files in os.walk("LightGBM/Multi/results"):
                for file in files:
                    if file.endswith('.json'):
                        performance_files.append(os.path.join(root, file))
        
        print(f"📊 พบไฟล์ performance: {len(performance_files)}")
        
        best_performance = None
        best_file = None
        
        for file_path in performance_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # ค้นหา total profit
                total_profit = None
                if isinstance(data, dict):
                    total_profit = data.get('total_profit')
                    if total_profit is None:
                        total_profit = data.get('Total Profit')
                    if total_profit is None and 'financial_summary' in data:
                        total_profit = data['financial_summary'].get('total_profit')
                
                if total_profit is not None:
                    if isinstance(total_profit, str):
                        # แปลง "$5,940.00" เป็น 5940.0
                        total_profit = float(total_profit.replace('$', '').replace(',', ''))
                    
                    if best_performance is None or total_profit > best_performance:
                        best_performance = total_profit
                        best_file = file_path
                        
                    print(f"   📈 {os.path.basename(file_path)}: ${total_profit:,.2f}")
                
            except Exception as e:
                print(f"   ⚠️ ไม่สามารถอ่าน {file_path}: {e}")
        
        if best_performance is not None:
            print(f"\n🏆 โมเดลที่ดีที่สุด: ${best_performance:,.2f}")
            print(f"📁 ไฟล์: {best_file}")
            return best_performance, best_file
        else:
            print("❌ ไม่พบข้อมูล performance")
            return None, None
    
    def create_model_protection_system(self):
        """สร้างระบบป้องกันการเทรนทับโมเดลที่ดี"""
        
        print("🛡️ สร้างระบบป้องกันโมเดล...")
        
        protection_code = '''
# ==============================================
# Model Protection System
# ==============================================

import os
import json
import shutil
from datetime import datetime

class ModelProtectionSystem:
    def __init__(self, min_profit_threshold=5000):
        self.min_profit_threshold = min_profit_threshold
        self.protection_log = "model_protection.log"
        
    def should_save_model(self, current_performance, symbol, timeframe):
        """ตัดสินใจว่าควรบันทึกโมเดลหรือไม่"""
        
        current_profit = current_performance.get('total_profit', 0)
        current_win_rate = current_performance.get('win_rate', 0)
        
        # โหลดประสิทธิภาพโมเดลปัจจุบัน
        best_performance = self._load_best_performance(symbol, timeframe)
        
        if best_performance is None:
            print("✅ ไม่มีโมเดลก่อนหน้า - บันทึกโมเดลนี้")
            return True, "first_model"
        
        best_profit = best_performance.get('total_profit', 0)
        best_win_rate = best_performance.get('win_rate', 0)
        
        # เกณฑ์การตัดสินใจ
        profit_improvement = current_profit - best_profit
        win_rate_improvement = current_win_rate - best_win_rate
        
        # ต้องดีขึ้นอย่างน้อย 5% หรือ $500
        min_profit_improvement = max(best_profit * 0.05, 500)
        
        decision_log = {
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            'timeframe': timeframe,
            'current_profit': current_profit,
            'best_profit': best_profit,
            'profit_improvement': profit_improvement,
            'min_required_improvement': min_profit_improvement,
            'current_win_rate': current_win_rate,
            'best_win_rate': best_win_rate,
            'win_rate_improvement': win_rate_improvement
        }
        
        # ตัดสินใจ
        if current_profit < 0:
            decision = False
            reason = f"negative_profit_{current_profit:.0f}"
            print(f"❌ กำไรติดลบ (${current_profit:,.0f}) - ไม่บันทึก")
            
        elif current_win_rate < 0.3:
            decision = False
            reason = f"low_win_rate_{current_win_rate:.1%}"
            print(f"❌ Win Rate ต่ำ ({current_win_rate:.1%}) - ไม่บันทึก")
            
        elif profit_improvement < min_profit_improvement:
            decision = False
            reason = f"insufficient_improvement_{profit_improvement:.0f}"
            print(f"❌ ปรับปรุงไม่เพียงพอ (${profit_improvement:,.0f} < ${min_profit_improvement:,.0f}) - ไม่บันทึก")
            
        else:
            decision = True
            reason = f"improved_by_{profit_improvement:.0f}"
            print(f"✅ โมเดลดีขึ้น (${profit_improvement:,.0f}) - บันทึกโมเดล")
        
        decision_log['decision'] = decision
        decision_log['reason'] = reason
        
        # บันทึก log
        self._log_decision(decision_log)
        
        return decision, reason
    
    def _load_best_performance(self, symbol, timeframe):
        """โหลดประสิทธิภาพโมเดลที่ดีที่สุด"""
        
        performance_file = f"best_performance_{symbol}_{timeframe}.json"
        
        if os.path.exists(performance_file):
            with open(performance_file, 'r') as f:
                return json.load(f)
        
        return None
    
    def _log_decision(self, decision_log):
        """บันทึก log การตัดสินใจ"""
        
        log_entry = (
            f"[{decision_log['timestamp']}] "
            f"{decision_log['symbol']}_{decision_log['timeframe']}: "
            f"${decision_log['current_profit']:,.0f} vs ${decision_log['best_profit']:,.0f} "
            f"({decision_log['profit_improvement']:+.0f}) "
            f"→ {'SAVE' if decision_log['decision'] else 'REJECT'} "
            f"({decision_log['reason']})\\n"
        )
        
        with open(self.protection_log, 'a', encoding='utf-8') as f:
            f.write(log_entry)

# การใช้งานใน LightGBM_10_4.py
protection_system = ModelProtectionSystem(min_profit_threshold=5000)

# ก่อนบันทึกโมเดล
should_save, reason = protection_system.should_save_model(
    current_performance={
        'total_profit': total_profit,
        'win_rate': win_rate,
        'expectancy': expectancy
    },
    symbol=symbol,
    timeframe=timeframe
)

if should_save:
    # บันทึกโมเดลตามปกติ
    save_model(model, scaler, features, symbol, timeframe)
    print(f"✅ บันทึกโมเดล: {reason}")
else:
    print(f"⚠️ ไม่บันทึกโมเดล: {reason}")
    print("💡 ใช้โมเดลเดิมที่ดีกว่า")
'''
        
        with open('model_protection_system.py', 'w', encoding='utf-8') as f:
            f.write(protection_code)
        
        print("✅ สร้างไฟล์ model_protection_system.py")
    
    def create_recovery_script(self):
        """สร้าง script สำหรับกู้คืนโมเดลที่ดี"""
        
        print("🔄 สร้าง recovery script...")
        
        recovery_script = '''
# ==============================================
# Model Recovery Script
# ==============================================

import os
import shutil
from datetime import datetime

def recover_good_model():
    """กู้คืนโมเดลที่ดีจากการเทรนครั้งที่ 2"""
    
    print("🔄 เริ่มกู้คืนโมเดลที่ดี...")
    
    # สำรองโมเดลปัจจุบันก่อน
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_before_recovery_{timestamp}"
    
    if os.path.exists("LightGBM/Multi"):
        shutil.copytree("LightGBM/Multi", backup_dir)
        print(f"✅ สำรองโมเดลปัจจุบัน: {backup_dir}")
    
    # ค้นหาโมเดลที่ดีที่สุด
    best_model_dir = None
    
    # ค้นหาใน backup folders
    for item in os.listdir("."):
        if os.path.isdir(item) and "backup" in item.lower():
            # ตรวจสอบว่ามีโมเดลใน backup นี้หรือไม่
            models_path = os.path.join(item, "models")
            if os.path.exists(models_path):
                print(f"📁 พบโมเดลใน: {item}")
                best_model_dir = item
                break
    
    if best_model_dir:
        # กู้คืนโมเดล
        source_models = os.path.join(best_model_dir, "models")
        target_models = "LightGBM/Multi/models"
        
        if os.path.exists(target_models):
            shutil.rmtree(target_models)
        
        shutil.copytree(source_models, target_models)
        print(f"✅ กู้คืนโมเดล: {source_models} → {target_models}")
        
        # กู้คืน thresholds
        source_thresholds = os.path.join(best_model_dir, "thresholds")
        target_thresholds = "LightGBM/Multi/thresholds"
        
        if os.path.exists(source_thresholds):
            if os.path.exists(target_thresholds):
                shutil.rmtree(target_thresholds)
            shutil.copytree(source_thresholds, target_thresholds)
            print(f"✅ กู้คืน thresholds: {source_thresholds} → {target_thresholds}")
        
        print("🎉 กู้คืนโมเดลเสร็จสิ้น!")
        print("💡 ตอนนี้ระบบใช้โมเดลที่ดีจากการเทรนครั้งที่ 2")
        
    else:
        print("❌ ไม่พบโมเดลที่ดีในระบบ backup")
        print("💡 กรุณาตรวจสอบโฟลเดอร์ backup ด้วยตนเอง")

if __name__ == "__main__":
    recover_good_model()
'''
        
        with open('recover_good_model.py', 'w', encoding='utf-8') as f:
            f.write(recovery_script)
        
        print("✅ สร้างไฟล์ recover_good_model.py")
    
    def create_training_prevention_system(self):
        """สร้างระบบป้องกันการเทรนซ้ำ"""
        
        print("🚫 สร้างระบบป้องกันการเทรนซ้ำ...")
        
        prevention_code = '''
# ==============================================
# Training Prevention System
# ==============================================

import os
import json
from datetime import datetime, timedelta

class TrainingPreventionSystem:
    def __init__(self):
        self.training_log = "training_attempts.log"
        self.max_daily_attempts = 3
        self.cooldown_hours = 6
        
    def can_train_now(self, symbol, timeframe):
        """ตรวจสอบว่าสามารถเทรนได้หรือไม่"""
        
        today = datetime.now().date()
        recent_attempts = self._get_recent_attempts(symbol, timeframe, today)
        
        if len(recent_attempts) >= self.max_daily_attempts:
            print(f"⚠️ เทรนเกินจำนวนที่กำหนดแล้ววันนี้ ({len(recent_attempts)}/{self.max_daily_attempts})")
            return False, "daily_limit_exceeded"
        
        # ตรวจสอบ cooldown
        if recent_attempts:
            last_attempt = max(recent_attempts)
            time_since_last = datetime.now() - last_attempt
            
            if time_since_last < timedelta(hours=self.cooldown_hours):
                remaining_time = timedelta(hours=self.cooldown_hours) - time_since_last
                print(f"⚠️ ยังอยู่ในช่วง cooldown อีก {remaining_time}")
                return False, f"cooldown_{remaining_time}"
        
        return True, "allowed"
    
    def log_training_attempt(self, symbol, timeframe, result):
        """บันทึกการพยายามเทรน"""
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'symbol': symbol,
            'timeframe': timeframe,
            'result': result
        }
        
        # อ่าน log เดิม
        attempts = []
        if os.path.exists(self.training_log):
            with open(self.training_log, 'r') as f:
                attempts = [json.loads(line) for line in f if line.strip()]
        
        # เพิ่ม attempt ใหม่
        attempts.append(log_entry)
        
        # เก็บเฉพาะ 30 วันล่าสุด
        cutoff_date = datetime.now() - timedelta(days=30)
        attempts = [a for a in attempts if datetime.fromisoformat(a['timestamp']) > cutoff_date]
        
        # บันทึกกลับ
        with open(self.training_log, 'w') as f:
            for attempt in attempts:
                f.write(json.dumps(attempt) + '\\n')
        
        print(f"📝 บันทึกการเทรน: {symbol}_{timeframe} → {result}")
    
    def _get_recent_attempts(self, symbol, timeframe, date):
        """ดึงการพยายามเทรนล่าสุด"""
        
        if not os.path.exists(self.training_log):
            return []
        
        attempts = []
        with open(self.training_log, 'r') as f:
            for line in f:
                if line.strip():
                    attempt = json.loads(line)
                    attempt_date = datetime.fromisoformat(attempt['timestamp']).date()
                    
                    if (attempt['symbol'] == symbol and 
                        attempt['timeframe'] == timeframe and 
                        attempt_date == date):
                        attempts.append(datetime.fromisoformat(attempt['timestamp']))
        
        return attempts

# การใช้งานใน LightGBM_10_4.py
prevention_system = TrainingPreventionSystem()

# ก่อนเริ่มเทรน
can_train, reason = prevention_system.can_train_now(symbol, timeframe)

if not can_train:
    print(f"🚫 ไม่สามารถเทรนได้: {reason}")
    print("💡 กรุณารอหรือใช้โมเดลเดิม")
    return  # ออกจากการเทรน
else:
    print(f"✅ สามารถเทรนได้: {reason}")
    
    # เทรนโมเดล...
    
    # บันทึกผลการเทรน
    prevention_system.log_training_attempt(symbol, timeframe, "completed")
'''
        
        with open('training_prevention_system.py', 'w', encoding='utf-8') as f:
            f.write(prevention_code)
        
        print("✅ สร้างไฟล์ training_prevention_system.py")
    
    def _log_operation(self, operation_info):
        """บันทึก log การดำเนินการ"""
        
        log_entry = f"[{operation_info['timestamp']}] {operation_info['operation']}: {operation_info}\n"
        
        with open(self.recovery_log, 'a', encoding='utf-8') as f:
            f.write(log_entry)
    
    def run_recovery_system(self):
        """รันระบบกู้คืนโมเดล"""
        
        print("🔄 Model Recovery System")
        print("="*50)
        
        # 1. สำรองโมเดลปัจจุบัน
        backup_dir = self.backup_current_models()
        
        # 2. ค้นหาโมเดลที่ดีที่สุด
        best_performance, best_file = self.find_best_model_backup()
        
        # 3. สร้างระบบป้องกัน
        self.create_model_protection_system()
        self.create_recovery_script()
        self.create_training_prevention_system()
        
        print("\n🎉 ระบบกู้คืนโมเดลเสร็จสมบูรณ์!")
        print("="*50)
        
        print("\n📁 ไฟล์ที่สร้าง:")
        files = [
            "model_protection_system.py",
            "recover_good_model.py", 
            "training_prevention_system.py"
        ]
        
        for file in files:
            if os.path.exists(file):
                print(f"   ✅ {file}")
        
        print(f"\n📂 โฟลเดอร์ backup: {backup_dir}")
        
        if best_performance is not None:
            print(f"\n🏆 โมเดลที่ดีที่สุดที่พบ: ${best_performance:,.2f}")
            print(f"📁 ไฟล์: {best_file}")
        
        print("\n🎯 ขั้นตอนต่อไป:")
        print("1. รัน recover_good_model.py เพื่อกู้คืนโมเดลที่ดี")
        print("2. เพิ่ม model_protection_system.py เข้า LightGBM_10_4.py")
        print("3. เพิ่ม training_prevention_system.py เข้า LightGBM_10_4.py")
        print("4. หยุดการเทรนซ้ำจนกว่าจะแก้ไขปัญหาหลัก")
        print("5. ใช้โมเดลที่กู้คืนได้สำหรับการเทรด")

if __name__ == "__main__":
    recovery_system = ModelRecoverySystem()
    recovery_system.run_recovery_system()
