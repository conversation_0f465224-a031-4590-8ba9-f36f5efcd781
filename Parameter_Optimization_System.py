"""
🔧 Parameter Optimization System for Trading Models
===================================================

ระบบทดสอบและปรับปรุงพารามิเตอร์สำหรับโมเดลการเทรด
รองรับการทดสอบหลายพารามิเตอร์พร้อมกัน และบันทึกผลลัพธ์เพื่อเปรียบเทียบ

Author: AI Assistant
Date: 2025-01-25
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import itertools
from typing import Dict, List, Tuple, Any, Optional
import pickle
import logging

# ตั้งค่า logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ParameterOptimizer:
    """
    คลาสสำหรับทดสอบและปรับปรุงพารามิเตอร์ของโมเดลการเทรด
    """
    
    def __init__(self, base_dir: str = "Parameter_Optimization_Results"):
        """
        เริ่มต้นระบบ Parameter Optimization
        
        Args:
            base_dir: โฟลเดอร์หลักสำหรับเก็บผลลัพธ์
        """
        self.base_dir = base_dir
        self.results_dir = os.path.join(base_dir, "results")
        self.configs_dir = os.path.join(base_dir, "configs")
        self.reports_dir = os.path.join(base_dir, "reports")
        
        # สร้างโฟลเดอร์ถ้ายังไม่มี
        for dir_path in [self.base_dir, self.results_dir, self.configs_dir, self.reports_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # กำหนดช่วงพารามิเตอร์ที่แนะนำ
        self.recommended_ranges = {
            'input_initial_nbar_sl': {
                'min': 2, 'max': 8, 'step': 1,
                'description': 'จำนวน bars สำหรับ stop loss (ยิ่งน้อยยิ่งเสี่ยง แต่ลด SL Hit)',
                'impact': 'สูง - ส่งผลต่อ Risk Management โดยตรง'
            },
            'input_stop_loss_atr': {
                'min': 0.5, 'max': 3.0, 'step': 0.25,
                'description': 'ตัวคูณ ATR สำหรับ Stop Loss (ยิ่งน้อยยิ่งเสี่ยง)',
                'impact': 'สูงมาก - ส่งผลต่อ SL Hit Rate และ Risk/Reward'
            },
            'input_take_profit': {
                'min': 1.0, 'max': 4.0, 'step': 0.5,
                'description': 'อัตราส่วน Take Profit ต่อ Stop Loss',
                'impact': 'สูงมาก - ส่งผลต่อ TP Hit Rate และ Expectancy'
            },
            'input_rsi_level_in': {
                'min': 25, 'max': 45, 'step': 5,
                'description': 'RSI level สำหรับ entry signal',
                'impact': 'ปานกลาง - ส่งผลต่อจำนวน signals'
            },
            'input_rsi_level_out': {
                'min': 20, 'max': 40, 'step': 5,
                'description': 'RSI level สำหรับ exit signal',
                'impact': 'ปานกลาง - ส่งผลต่อ Technical Exit'
            },
            'input_volume_spike': {
                'min': 1.0, 'max': 2.0, 'step': 0.25,
                'description': 'ตัวคูณ Volume สำหรับ entry confirmation',
                'impact': 'ต่ำ - กรองสัญญาณคุณภาพ'
            }
        }
        
        # น้ำหนักสำหรับการให้คะแนน
        self.scoring_weights = {
            'total_profit': 0.30,      # กำไรรวม
            'win_rate': 0.25,          # อัตราชนะ
            'profit_factor': 0.20,     # Profit Factor
            'max_drawdown': -0.15,     # Max Drawdown (ลบเพราะยิ่งน้อยยิ่งดี)
            'expectancy': 0.10         # Expectancy per trade
        }
    
    def generate_parameter_combinations(self, 
                                     parameters: Dict[str, Dict],
                                     max_combinations: int = 1000) -> List[Dict]:
        """
        สร้างชุดค่าพารามิเตอร์สำหรับทดสอบ
        
        Args:
            parameters: พารามิเตอร์และช่วงค่าที่ต้องการทดสอบ
            max_combinations: จำนวนชุดค่าสูงสุด
            
        Returns:
            รายการชุดค่าพารามิเตอร์
        """
        param_lists = []
        param_names = []
        
        for param_name, param_config in parameters.items():
            if 'values' in param_config:
                # ใช้ค่าที่กำหนดเอง
                values = param_config['values']
            else:
                # สร้างช่วงค่าจาก min, max, step
                min_val = param_config.get('min', 0)
                max_val = param_config.get('max', 1)
                step = param_config.get('step', 0.1)
                
                if isinstance(min_val, int) and isinstance(step, int):
                    values = list(range(min_val, max_val + 1, step))
                else:
                    values = np.arange(min_val, max_val + step, step).round(3).tolist()
            
            param_lists.append(values)
            param_names.append(param_name)
        
        # สร้างชุดค่าผสม
        combinations = list(itertools.product(*param_lists))
        
        # จำกัดจำนวนชุดค่า
        if len(combinations) > max_combinations:
            # สุ่มเลือกชุดค่า
            np.random.seed(42)  # เพื่อให้ผลลัพธ์เหมือนเดิม
            selected_indices = np.random.choice(len(combinations), max_combinations, replace=False)
            combinations = [combinations[i] for i in selected_indices]
            logger.warning(f"จำกัดจำนวนชุดค่าจาก {len(list(itertools.product(*param_lists)))} เป็น {max_combinations}")
        
        # แปลงเป็น dictionary
        param_combinations = []
        for combo in combinations:
            param_dict = dict(zip(param_names, combo))
            param_combinations.append(param_dict)
        
        logger.info(f"สร้างชุดค่าพารามิเตอร์ทั้งหมด {len(param_combinations)} ชุด")
        return param_combinations
    
    def calculate_performance_score(self, trade_results: Dict) -> float:
        """
        คำนวณคะแนนประสิทธิภาพจากผลการเทรด
        
        Args:
            trade_results: ผลการเทรดที่ได้จากการทดสอบ
            
        Returns:
            คะแนนประสิทธิภาพ (0-100)
        """
        try:
            # ดึงข้อมูลสถิติ
            total_profit = trade_results.get('total_profit', 0)
            win_rate = trade_results.get('win_rate', 0) / 100  # แปลงเป็น 0-1
            profit_factor = trade_results.get('profit_factor', 0)
            max_drawdown = abs(trade_results.get('max_drawdown', 0))  # ใช้ค่าบวก
            expectancy = trade_results.get('expectancy', 0)
            total_trades = trade_results.get('total_trades', 0)
            
            # ตรวจสอบข้อมูลขั้นต่ำ
            if total_trades < 10:
                return 0  # ข้อมูลไม่เพียงพอ
            
            # ปรับค่าให้อยู่ในช่วง 0-1
            normalized_profit = max(0, min(1, (total_profit + 5000) / 10000))  # สมมติช่วง -5000 ถึง +5000
            normalized_win_rate = win_rate
            normalized_pf = max(0, min(1, profit_factor / 3))  # PF > 3 ถือว่าดีมาก
            normalized_dd = max(0, min(1, 1 - (max_drawdown / 10000)))  # DD น้อยกว่า 10000 ถือว่าดี
            normalized_exp = max(0, min(1, (expectancy + 100) / 200))  # สมมติช่วง -100 ถึง +100
            
            # คำนวณคะแนนรวม
            score = (
                normalized_profit * self.scoring_weights['total_profit'] +
                normalized_win_rate * self.scoring_weights['win_rate'] +
                normalized_pf * self.scoring_weights['profit_factor'] +
                normalized_dd * abs(self.scoring_weights['max_drawdown']) +
                normalized_exp * self.scoring_weights['expectancy']
            ) * 100
            
            return round(score, 2)
            
        except Exception as e:
            logger.error(f"Error calculating performance score: {e}")
            return 0
    
    def save_test_result(self, 
                        symbol: str,
                        timeframe: str,
                        parameters: Dict,
                        trade_results: Dict,
                        performance_score: float,
                        test_timestamp: str = None) -> str:
        """
        บันทึกผลการทดสอบ
        
        Args:
            symbol: สัญลักษณ์การเทรด
            timeframe: ช่วงเวลา
            parameters: พารามิเตอร์ที่ใช้ทดสอบ
            trade_results: ผลการเทรด
            performance_score: คะแนนประสิทธิภาพ
            test_timestamp: เวลาทดสอบ
            
        Returns:
            ชื่อไฟล์ที่บันทึก
        """
        if test_timestamp is None:
            test_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # สร้างชื่อไฟล์
        filename = f"{symbol}_{timeframe}_param_test_{test_timestamp}.json"
        filepath = os.path.join(self.results_dir, filename)
        
        # เตรียมข้อมูลสำหรับบันทึก
        result_data = {
            'metadata': {
                'symbol': symbol,
                'timeframe': timeframe,
                'test_timestamp': test_timestamp,
                'performance_score': performance_score
            },
            'parameters': parameters,
            'trade_results': trade_results,
            'scoring_details': {
                'weights_used': self.scoring_weights,
                'score_calculation': 'Weighted average of normalized metrics'
            }
        }
        
        # บันทึกไฟล์
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"บันทึกผลการทดสอบ: {filename}")
        return filename
    
    def load_test_results(self, symbol: str = None, timeframe: str = None) -> List[Dict]:
        """
        โหลดผลการทดสอบที่บันทึกไว้
        
        Args:
            symbol: กรองตามสัญลักษณ์ (ถ้าไม่ระบุจะโหลดทั้งหมด)
            timeframe: กรองตามช่วงเวลา (ถ้าไม่ระบุจะโหลดทั้งหมด)
            
        Returns:
            รายการผลการทดสอบ
        """
        results = []
        
        if not os.path.exists(self.results_dir):
            return results
        
        for filename in os.listdir(self.results_dir):
            if not filename.endswith('.json'):
                continue
            
            # กรองตามเงื่อนไข
            if symbol and symbol not in filename:
                continue
            if timeframe and timeframe not in filename:
                continue
            
            try:
                filepath = os.path.join(self.results_dir, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)
                    result_data['filename'] = filename
                    results.append(result_data)
            except Exception as e:
                logger.error(f"Error loading {filename}: {e}")
        
        # เรียงตามคะแนน
        results.sort(key=lambda x: x.get('metadata', {}).get('performance_score', 0), reverse=True)
        
        return results
    
    def generate_optimization_report(self, 
                                   symbol: str,
                                   timeframe: str,
                                   top_n: int = 10) -> str:
        """
        สร้างรายงานสรุปผลการปรับปรุงพารามิเตอร์
        
        Args:
            symbol: สัญลักษณ์การเทรด
            timeframe: ช่วงเวลา
            top_n: จำนวนผลลัพธ์ที่ดีที่สุดที่จะแสดง
            
        Returns:
            ชื่อไฟล์รายงาน
        """
        results = self.load_test_results(symbol, timeframe)
        
        if not results:
            logger.warning(f"ไม่พบผลการทดสอบสำหรับ {symbol}_{timeframe}")
            return None
        
        # สร้างรายงาน
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append(f"📊 PARAMETER OPTIMIZATION REPORT")
        report_lines.append("=" * 80)
        report_lines.append(f"Symbol: {symbol}")
        report_lines.append(f"Timeframe: {timeframe}")
        report_lines.append(f"Total Tests: {len(results)}")
        report_lines.append(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("=" * 80)
        
        # แสดงผลลัพธ์ที่ดีที่สุด
        report_lines.append(f"\n🏆 TOP {min(top_n, len(results))} BEST RESULTS:")
        report_lines.append("-" * 80)
        
        for i, result in enumerate(results[:top_n], 1):
            metadata = result.get('metadata', {})
            parameters = result.get('parameters', {})
            trade_results = result.get('trade_results', {})
            
            report_lines.append(f"\n#{i} - Score: {metadata.get('performance_score', 0):.2f}")
            report_lines.append(f"Parameters:")
            for param, value in parameters.items():
                report_lines.append(f"  {param}: {value}")
            
            report_lines.append(f"Results:")
            report_lines.append(f"  Total Profit: {trade_results.get('total_profit', 0):,.2f}")
            report_lines.append(f"  Win Rate: {trade_results.get('win_rate', 0):.2f}%")
            report_lines.append(f"  Total Trades: {trade_results.get('total_trades', 0)}")
            report_lines.append(f"  Max Drawdown: {trade_results.get('max_drawdown', 0):,.2f}")
            report_lines.append("-" * 40)
        
        # วิเคราะห์พารามิเตอร์ที่มีผลกระทบ
        report_lines.append(f"\n📈 PARAMETER IMPACT ANALYSIS:")
        report_lines.append("-" * 80)
        
        if len(results) >= 5:  # ต้องมีข้อมูลเพียงพอ
            param_analysis = self._analyze_parameter_impact(results[:20])  # วิเคราะห์ top 20
            for param, analysis in param_analysis.items():
                report_lines.append(f"\n{param}:")
                report_lines.append(f"  Best Value: {analysis['best_value']}")
                report_lines.append(f"  Average Score: {analysis['avg_score']:.2f}")
                report_lines.append(f"  Impact Level: {analysis['impact_level']}")
        
        # แนะนำการตั้งค่า
        if results:
            best_result = results[0]
            best_params = best_result.get('parameters', {})
            
            report_lines.append(f"\n🎯 RECOMMENDED SETTINGS:")
            report_lines.append("-" * 80)
            for param, value in best_params.items():
                report_lines.append(f"{param} = {value}")
        
        # บันทึกรายงาน
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"{symbol}_{timeframe}_optimization_report_{timestamp}.txt"
        report_filepath = os.path.join(self.reports_dir, report_filename)
        
        with open(report_filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        logger.info(f"สร้างรายงาน: {report_filename}")
        
        # แสดงสรุปใน console
        print('\n'.join(report_lines))
        
        return report_filename
    
    def _analyze_parameter_impact(self, results: List[Dict]) -> Dict:
        """
        วิเคราะห์ผลกระทบของแต่ละพารามิเตอร์
        """
        param_impact = {}
        
        # รวบรวมข้อมูลแต่ละพารามิเตอร์
        for result in results:
            parameters = result.get('parameters', {})
            score = result.get('metadata', {}).get('performance_score', 0)
            
            for param, value in parameters.items():
                if param not in param_impact:
                    param_impact[param] = {'values': [], 'scores': []}
                
                param_impact[param]['values'].append(value)
                param_impact[param]['scores'].append(score)
        
        # วิเคราะห์แต่ละพารามิเตอร์
        analysis = {}
        for param, data in param_impact.items():
            values = np.array(data['values'])
            scores = np.array(data['scores'])
            
            # หาค่าที่ให้คะแนนดีที่สุด
            best_idx = np.argmax(scores)
            best_value = values[best_idx]
            
            # คำนวณคะแนนเฉลี่ย
            avg_score = np.mean(scores)
            
            # ประเมินระดับผลกระทบ (จาก correlation)
            if len(set(values)) > 1:  # มีค่าที่แตกต่างกัน
                correlation = abs(np.corrcoef(values, scores)[0, 1])
                if correlation > 0.7:
                    impact_level = "สูง"
                elif correlation > 0.4:
                    impact_level = "ปานกลาง"
                else:
                    impact_level = "ต่ำ"
            else:
                impact_level = "ไม่สามารถประเมินได้"
            
            analysis[param] = {
                'best_value': best_value,
                'avg_score': avg_score,
                'impact_level': impact_level
            }
        
        return analysis
    
    def get_recommended_test_ranges(self, 
                                  current_params: Dict = None,
                                  focus_params: List[str] = None) -> Dict:
        """
        แนะนำช่วงการทดสอบสำหรับพารามิเตอร์
        
        Args:
            current_params: พารามิเตอร์ปัจจุบัน
            focus_params: พารามิเตอร์ที่ต้องการโฟกัส
            
        Returns:
            ช่วงการทดสอบที่แนะนำ
        """
        if focus_params is None:
            # เลือกพารามิเตอร์ที่มีผลกระทบสูง
            focus_params = ['input_stop_loss_atr', 'input_take_profit', 'input_initial_nbar_sl']
        
        recommended_tests = {}
        
        for param in focus_params:
            if param in self.recommended_ranges:
                range_info = self.recommended_ranges[param].copy()
                
                # ปรับช่วงตามค่าปัจจุบัน (ถ้ามี)
                if current_params and param in current_params:
                    current_value = current_params[param]
                    range_info['current_value'] = current_value
                    range_info['suggested_focus'] = f"ทดสอบรอบ ±50% ของค่าปัจจุบัน ({current_value})"
                
                recommended_tests[param] = range_info
        
        return recommended_tests

def create_sample_test_config():
    """
    สร้างไฟล์ config ตัวอย่างสำหรับการทดสอบ
    """
    sample_config = {
        "test_name": "GOLD_M60_Parameter_Optimization",
        "symbol": "GOLD",
        "timeframe": "M60",
        "parameters_to_test": {
            "input_initial_nbar_sl": {
                "min": 2,
                "max": 6,
                "step": 1,
                "description": "จำนวน bars สำหรับ stop loss"
            },
            "input_stop_loss_atr": {
                "min": 1.0,
                "max": 3.0,
                "step": 0.5,
                "description": "ตัวคูณ ATR สำหรับ Stop Loss"
            },
            "input_take_profit": {
                "min": 1.5,
                "max": 3.5,
                "step": 0.5,
                "description": "อัตราส่วน Take Profit ต่อ Stop Loss"
            }
        },
        "max_combinations": 100,
        "scoring_weights": {
            "total_profit": 0.30,
            "win_rate": 0.25,
            "profit_factor": 0.20,
            "max_drawdown": -0.15,
            "expectancy": 0.10
        }
    }
    
    config_path = "Parameter_Optimization_Results/configs/sample_config.json"
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ สร้างไฟล์ config ตัวอย่าง: {config_path}")
    return config_path

if __name__ == "__main__":
    # ตัวอย่างการใช้งาน
    print("🔧 Parameter Optimization System")
    print("=" * 50)
    
    # สร้างระบบ
    optimizer = ParameterOptimizer()
    
    # สร้างไฟล์ config ตัวอย่าง
    config_path = create_sample_test_config()
    
    # แสดงช่วงการทดสอบที่แนะนำ
    current_params = {
        'input_initial_nbar_sl': 4,
        'input_stop_loss_atr': 1.00,
        'input_take_profit': 2.0
    }
    
    recommended = optimizer.get_recommended_test_ranges(current_params)
    
    print("\n📋 ช่วงการทดสอบที่แนะนำ:")
    print("-" * 50)
    for param, info in recommended.items():
        print(f"\n{param}:")
        print(f"  ช่วง: {info['min']} - {info['max']} (step: {info['step']})")
        print(f"  คำอธิบาย: {info['description']}")
        print(f"  ผลกระทบ: {info['impact']}")
        if 'current_value' in info:
            print(f"  ค่าปัจจุบัน: {info['current_value']}")
    
    print(f"\n✅ ระบบพร้อมใช้งาน!")
    print(f"📁 ผลลัพธ์จะถูกบันทึกใน: {optimizer.base_dir}")