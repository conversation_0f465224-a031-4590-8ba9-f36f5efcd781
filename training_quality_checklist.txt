
🔍 TRAINING QUALITY CHECKLIST
============================

ก่อนการเทรน:
□ ตรวจสอบข้อมูลไม่มี Missing Values
□ ตรวจสอบ Data Distribution
□ ตรวจสอบ Feature Correlation
□ ตรวจสอบ Target Variable Balance
□ ตรวจสอบ Time Series Order

ระหว่างการเทรน:
□ ใช้ TimeSeriesSplit สำหรับ CV
□ ใช้ Early Stopping
□ ตรวจสอบ Validation Score
□ ตรวจสอบ Feature Importance
□ ตรวจสอบ Model Complexity

หลังการเทรน:
□ ตรวจสอบ Out-of-Sample Performance
□ ตรวจสอบ Win Rate > 40%
□ ตรวจสอบ Expectancy > 0
□ ตรวจสอบ Max Drawdown < 20%
□ เปรียบเทียบกับ Baseline Model

เงื่อนไขการยอมรับโมเดล:
□ AUC > 0.6
□ F1 Score > 0.3
□ Win Rate > 40%
□ Expectancy > 0
□ Max Drawdown < 20%
□ Profit Factor > 1.2

หากไม่ผ่านเงื่อนไข:
□ หยุดการเทรนซ้ำ
□ วิเคราะห์สาเหตุ
□ แก้ไขปัญหาก่อนเทรนใหม่
□ ปรับ Parameters
□ เพิ่ม/ลด Features
