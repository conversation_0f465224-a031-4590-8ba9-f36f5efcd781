"""
🔧 Fix Model Protection Flexibility
===================================

แก้ไขระบบป้องกันให้ยืดหยุ่นกว่านี้
เพื่อให้สามารถบันทึกโมเดลได้ในโหมด Development
"""

import os
import re

def add_development_mode_to_lightgbm():
    """เพิ่มโหมด Development ใน LightGBM_10_4.py"""
    
    print("🔧 เพิ่มโหมด Development ใน LightGBM_10_4.py")
    print("="*50)
    
    # อ่านไฟล์
    with open('LightGBM_10_4.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # ค้นหาตำแหน่งที่ต้องแก้ไข
    pattern = r'(# ตรวจสอบด้วย Model Protection System\s+should_save_by_protection = True\s+protection_reason = "no_protection_system")'
    
    if re.search(pattern, content):
        # เพิ่มโหมด Development
        replacement = '''# ตรวจสอบด้วย Model Protection System
                should_save_by_protection = True
                protection_reason = "no_protection_system"
                
                # โหมด Development - เปลี่ยนเป็น False เมื่อใช้งานจริง
                DEVELOPMENT_MODE = True  # True = บันทึกทุกโมเดล, False = ใช้ระบบป้องกัน'''
        
        content = re.sub(pattern, replacement, content)
        
        # แก้ไขการเรียกใช้ Model Protection System
        old_protection_call = '''if MODEL_PROTECTION_AVAILABLE and protection_system:
                    print(f"🛡️ เรียกใช้ Model Protection System...")
                    should_save_by_protection, protection_reason = protection_system.should_save_model(
                        current_performance=current_performance,
                        symbol=symbol,
                        timeframe=timeframe
                    )
                    print(f"🛡️ ผลการตัดสินใจ: {should_save_by_protection} ({protection_reason})")
                else:
                    print(f"⚠️ Model Protection System ไม่พร้อมใช้งาน")'''
        
        new_protection_call = '''if DEVELOPMENT_MODE:
                    print(f"🔧 Development Mode: บันทึกโมเดลทุกครั้งเพื่อการทดสอบ")
                    should_save_by_protection = True
                    protection_reason = "development_mode"
                elif MODEL_PROTECTION_AVAILABLE and protection_system:
                    print(f"🛡️ เรียกใช้ Model Protection System...")
                    should_save_by_protection, protection_reason = protection_system.should_save_model(
                        current_performance=current_performance,
                        symbol=symbol,
                        timeframe=timeframe
                    )
                    print(f"🛡️ ผลการตัดสินใจ: {should_save_by_protection} ({protection_reason})")
                else:
                    print(f"⚠️ Model Protection System ไม่พร้อมใช้งาน")'''
        
        content = content.replace(old_protection_call, new_protection_call)
        
        # บันทึกไฟล์
        with open('LightGBM_10_4.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ เพิ่มโหมด Development สำเร็จ")
        return True
    else:
        print("❌ ไม่พบตำแหน่งที่ต้องแก้ไข")
        return False

def create_flexible_protection_system():
    """สร้างระบบป้องกันที่ยืดหยุ่น"""
    
    print("\n🔧 สร้างระบบป้องกันที่ยืดหยุ่น")
    print("="*50)
    
    flexible_code = '''"""
🛡️ Flexible Model Protection System
====================================

ระบบป้องกันที่ยืดหยุ่น สำหรับการพัฒนาและใช้งานจริง
"""

import os
import json
from datetime import datetime

class FlexibleModelProtection:
    def __init__(self, mode="development"):
        """
        mode: "development" หรือ "production"
        - development: บันทึกโมเดลทุกครั้ง แต่แสดงคำเตือน
        - production: ใช้ระบบป้องกันเต็มรูปแบบ
        """
        self.mode = mode
        self.protection_log = "flexible_protection.log"
        
    def should_save_model(self, current_performance, symbol, timeframe):
        """ตัดสินใจว่าควรบันทึกโมเดลหรือไม่"""
        
        print(f"🛡️ Flexible Protection ({self.mode} mode)")
        
        current_profit = current_performance.get('total_profit', 0)
        current_win_rate = current_performance.get('win_rate', 0)
        current_expectancy = current_performance.get('expectancy', 0)
        
        print(f"📊 โมเดลปัจจุบัน:")
        print(f"   💰 Total Profit: ${current_profit:,.2f}")
        print(f"   🎯 Win Rate: {current_win_rate:.1%}")
        print(f"   📈 Expectancy: {current_expectancy:.2f}")
        
        if self.mode == "development":
            return self._development_mode_decision(current_performance, symbol, timeframe)
        else:
            return self._production_mode_decision(current_performance, symbol, timeframe)
    
    def _development_mode_decision(self, current_performance, symbol, timeframe):
        """โหมด Development - บันทึกทุกครั้ง แต่แสดงคำเตือน"""
        
        current_profit = current_performance.get('total_profit', 0)
        current_win_rate = current_performance.get('win_rate', 0)
        current_expectancy = current_performance.get('expectancy', 0)
        
        warnings = []
        
        if current_profit < 0:
            warnings.append(f"⚠️ กำไรติดลบ (${current_profit:,.0f})")
        
        if current_win_rate < 0.15:
            warnings.append(f"⚠️ Win Rate ต่ำ ({current_win_rate:.1%})")
        
        if current_expectancy < 0:
            warnings.append(f"⚠️ Expectancy ติดลบ ({current_expectancy:.2f})")
        
        if warnings:
            print("🔧 Development Mode - คำเตือน:")
            for warning in warnings:
                print(f"   {warning}")
            print("💡 ในโหมด Production โมเดลนี้จะไม่ถูกบันทึก")
        else:
            print("✅ โมเดลมีคุณภาพดี")
        
        print("🔧 Development Mode: บันทึกโมเดลเพื่อการทดสอบ")
        
        # บันทึก log
        self._log_decision({
            'mode': 'development',
            'decision': True,
            'reason': 'development_mode_always_save',
            'warnings': warnings,
            'performance': current_performance
        })
        
        return True, "development_mode"
    
    def _production_mode_decision(self, current_performance, symbol, timeframe):
        """โหมด Production - ใช้ระบบป้องกันเต็มรูปแบบ"""
        
        current_profit = current_performance.get('total_profit', 0)
        current_win_rate = current_performance.get('win_rate', 0)
        current_expectancy = current_performance.get('expectancy', 0)
        
        # โหลดประสิทธิภาพโมเดลที่ดีที่สุด
        best_performance = self._load_best_performance(symbol, timeframe)
        
        if best_performance is None:
            # โมเดลแรก - ตรวจสอบคุณภาพ
            if current_profit < 0:
                return False, "first_model_negative_profit"
            elif current_win_rate < 0.15:
                return False, "first_model_low_win_rate"
            elif current_expectancy < 0:
                return False, "first_model_negative_expectancy"
            else:
                self._save_best_performance(current_performance, symbol, timeframe)
                return True, "first_model_good_quality"
        
        # เปรียบเทียบกับโมเดลที่ดีที่สุด
        best_profit = best_performance.get('total_profit', 0)
        profit_improvement = current_profit - best_profit
        
        if current_profit < 0:
            return False, "negative_profit"
        elif current_win_rate < 0.15:
            return False, "low_win_rate"
        elif current_expectancy < 0:
            return False, "negative_expectancy"
        elif profit_improvement < 500:  # ต้องดีขึ้นอย่างน้อย $500
            return False, "insufficient_improvement"
        else:
            self._save_best_performance(current_performance, symbol, timeframe)
            return True, "improved"
    
    def _load_best_performance(self, symbol, timeframe):
        """โหลดประสิทธิภาพโมเดลที่ดีที่สุด"""
        performance_file = f"best_performance_{symbol}_{timeframe}.json"
        if os.path.exists(performance_file):
            try:
                with open(performance_file, 'r') as f:
                    return json.load(f)
            except:
                return None
        return None
    
    def _save_best_performance(self, performance, symbol, timeframe):
        """บันทึกประสิทธิภาพโมเดลที่ดีที่สุด"""
        performance_file = f"best_performance_{symbol}_{timeframe}.json"
        try:
            with open(performance_file, 'w') as f:
                json.dump(performance, f, indent=2)
        except Exception as e:
            print(f"⚠️ ไม่สามารถบันทึกไฟล์ประสิทธิภาพ: {e}")
    
    def _log_decision(self, decision_data):
        """บันทึก log การตัดสินใจ"""
        try:
            with open(self.protection_log, 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now().isoformat()}] {decision_data}\\n")
        except:
            pass
'''
    
    with open('flexible_model_protection.py', 'w', encoding='utf-8') as f:
        f.write(flexible_code)
    
    print("✅ สร้างระบบป้องกันที่ยืดหยุ่นสำเร็จ")
    return True

def create_simple_fix():
    """สร้างการแก้ไขแบบง่าย"""
    
    print("\n🔧 สร้างการแก้ไขแบบง่าย")
    print("="*50)
    
    simple_fix = '''"""
🔧 Simple Model Protection Fix
==============================

แก้ไขปัญหาการไม่บันทึกโมเดลโดยการปรับระบบป้องกัน
"""

def apply_simple_fix():
    """แก้ไขปัญหาแบบง่าย"""
    
    print("🔧 แก้ไขปัญหาการไม่บันทึกโมเดล")
    print("="*50)
    
    # อ่านไฟล์ LightGBM_10_4.py
    with open('LightGBM_10_4.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # ค้นหาและแทนที่ DEVELOPMENT_MODE
    if 'DEVELOPMENT_MODE = False' in content:
        content = content.replace('DEVELOPMENT_MODE = False', 'DEVELOPMENT_MODE = True')
        print("✅ เปลี่ยน DEVELOPMENT_MODE เป็น True")
    elif 'DEVELOPMENT_MODE = True' in content:
        print("✅ DEVELOPMENT_MODE เป็น True อยู่แล้ว")
    else:
        # เพิ่ม DEVELOPMENT_MODE ใหม่
        import_section = "# =============================================\\n# การตั้งค่าหลัก\\n# ============================================="
        if import_section in content:
            new_setting = import_section + "\\n\\n# โหมด Development - เปลี่ยนเป็น False เมื่อใช้งานจริง\\nDEVELOPMENT_MODE = True  # True = บันทึกทุกโมเดล, False = ใช้ระบบป้องกัน\\n"
            content = content.replace(import_section, new_setting)
            print("✅ เพิ่ม DEVELOPMENT_MODE = True")
    
    # บันทึกไฟล์
    with open('LightGBM_10_4.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ แก้ไขเสร็จสิ้น")
    print("💡 ตอนนี้โมเดลจะถูกบันทึกทุกครั้งในโหมด Development")

if __name__ == "__main__":
    apply_simple_fix()
'''
    
    with open('simple_model_fix.py', 'w', encoding='utf-8') as f:
        f.write(simple_fix)
    
    print("✅ สร้างการแก้ไขแบบง่ายสำเร็จ")
    return True

def run_all_fixes():
    """รันการแก้ไขทั้งหมด"""
    
    print("🚀 เริ่มแก้ไขปัญหาการไม่บันทึกโมเดล")
    print("="*60)
    
    results = []
    
    # แก้ไข 1: เพิ่มโหมด Development
    try:
        success1 = add_development_mode_to_lightgbm()
        results.append(("add_development_mode", success1))
    except Exception as e:
        print(f"❌ เพิ่มโหมด Development ล้มเหลว: {e}")
        results.append(("add_development_mode", False))
    
    # แก้ไข 2: สร้างระบบป้องกันที่ยืดหยุ่น
    try:
        success2 = create_flexible_protection_system()
        results.append(("flexible_protection", success2))
    except Exception as e:
        print(f"❌ สร้างระบบป้องกันที่ยืดหยุ่นล้มเหลว: {e}")
        results.append(("flexible_protection", False))
    
    # แก้ไข 3: สร้างการแก้ไขแบบง่าย
    try:
        success3 = create_simple_fix()
        results.append(("simple_fix", success3))
    except Exception as e:
        print(f"❌ สร้างการแก้ไขแบบง่ายล้มเหลว: {e}")
        results.append(("simple_fix", False))
    
    # สรุปผล
    print("\\n📊 สรุปผลการแก้ไข")
    print("="*60)
    
    passed = 0
    for fix_name, success in results:
        status = "✅ สำเร็จ" if success else "❌ ล้มเหลว"
        print(f"{status} {fix_name}")
        if success:
            passed += 1
    
    total = len(results)
    score = passed / total * 100
    
    print(f"\\n🎯 คะแนนรวม: {passed}/{total} ({score:.1f}%)")
    
    if score >= 80:
        print("🟢 การแก้ไขสำเร็จ!")
        print("💡 ตอนนี้สามารถบันทึกโมเดลได้แล้ว")
    else:
        print("🔴 การแก้ไขมีปัญหา")
        print("💡 ต้องแก้ไขเพิ่มเติม")
    
    return score

if __name__ == "__main__":
    score = run_all_fixes()
    
    print("\\n" + "="*60)
    print("🎉 การแก้ไขเสร็จสิ้น!")
    print(f"📊 คะแนนรวม: {score:.1f}%")
    
    if score >= 80:
        print("\\n🚀 แนะนำ: ลองรัน LightGBM_10_4.py ได้เลย!")
        print("💡 โมเดลจะถูกบันทึกในโหมด Development")
    else:
        print("\\n🔧 แนะนำ: ตรวจสอบและแก้ไขปัญหาที่เหลือ")
