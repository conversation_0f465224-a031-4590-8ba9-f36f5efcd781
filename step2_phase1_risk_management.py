"""
🧪 Step 2: Phase 1 - Risk Management Optimization
=================================================

ปรับปรุง Stop Loss และ Take Profit เพื่อเพิ่ม Win Rate และลด Drawdown
"""

from Parameter_Testing_Integration import ParameterTester
import json
from datetime import datetime

def main():
    print("🚀 Step 2: Phase 1 - Risk Management Optimization")
    print("="*60)
    
    # โหลดผลลัพธ์ baseline
    try:
        with open('baseline_result.json', 'r') as f:
            baseline = json.load(f)
        baseline_score = baseline['performance_score']
        print(f"📊 Baseline Score: {baseline_score:.2f}")
    except:
        baseline_score = 0
        print("⚠️ ไม่พบ baseline result - จะใช้ค่า 0")
    
    # สร้างระบบทดสอบ
    print("\n🔧 เริ่มต้นระบบทดสอบ...")
    tester = ParameterTester()
    
    # กำหนดพารามิเตอร์พื้นฐาน (จาก baseline)
    base_params = {
        'input_initial_nbar_sl': 4,
        'input_rsi_level_in': 35,
        'input_volume_spike': 1.25
    }
    
    # Phase 1 Configuration: ทดสอบ SL และ TP
    print("\n📋 Phase 1 Test Configuration:")
    print("   🎯 เป้าหมาย: เพิ่ม Win Rate และลด Drawdown")
    print("   🔧 พารามิเตอร์ที่ทดสอบ: Stop Loss ATR และ Take Profit Ratio")
    
    phase1_config = {
        'base_parameters': base_params,
        'parameters_to_test': {
            'input_stop_loss_atr': {
                'min': 0.8,    # ลดจาก 1.0 เพื่อลด SL
                'max': 2.0,    # เพิ่มขึ้นเพื่อทดสอบ SL ที่กว้างขึ้น
                'step': 0.2
            },
            'input_take_profit': {
                'min': 1.5,    # ลดจาก 2.0 เพื่อเพิ่มโอกาส TP
                'max': 3.0,    # เพิ่มขึ้นเพื่อทดสอบ TP ที่ไกลขึ้น
                'step': 0.3
            }
        },
        'max_combinations': 30  # จำกัดจำนวนการทดสอบ
    }
    
    print(f"\n📊 จำนวนการทดสอบ:")
    sl_count = len([x for x in [0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0]])
    tp_count = len([x for x in [1.5, 1.8, 2.1, 2.4, 2.7, 3.0]])
    total_tests = sl_count * tp_count
    print(f"   Stop Loss ATR: {sl_count} ค่า (0.8-2.0)")
    print(f"   Take Profit: {tp_count} ค่า (1.5-3.0)")
    print(f"   รวม: {total_tests} combinations")
    
    # รัน Phase 1 Testing
    print(f"\n🧪 เริ่มทดสอบ Phase 1...")
    print("   (อาจใช้เวลา 5-10 นาที...)")
    
    try:
        phase1_results = tester.run_batch_test(
            symbol="GOLD",
            timeframe="H1",
            test_config=phase1_config
        )
        
        print(f"\n✅ ทดสอบเสร็จ - ได้ผลลัพธ์ {len(phase1_results)} รายการ")
        
        # แสดงผลลัพธ์ Top 5
        print(f"\n🏆 Top 5 ผลลัพธ์ Phase 1:")
        print(f"{'Rank':<4} {'Score':<8} {'Win%':<8} {'Profit':<12} {'SL ATR':<8} {'TP':<6} {'Drawdown':<12}")
        print("="*70)
        
        for i, result in enumerate(phase1_results[:5]):
            rank = i + 1
            score = result['performance_score']
            win_rate = result['results'].get('win_rate', 0)
            profit = result['results'].get('total_profit', 0)
            sl_atr = result['parameters']['input_stop_loss_atr']
            tp = result['parameters']['input_take_profit']
            drawdown = result['results'].get('max_drawdown', 0)
            
            print(f"{rank:<4} {score:<8.2f} {win_rate:<8.1f} ${profit:<11.0f} {sl_atr:<8.1f} {tp:<6.1f} ${drawdown:<11.0f}")
        
        # เลือกผลลัพธ์ที่ดีที่สุด
        best_result = phase1_results[0]
        best_params = best_result['parameters']
        best_score = best_result['performance_score']
        
        print(f"\n🎉 พารามิเตอร์ที่ดีที่สุดจาก Phase 1:")
        print(f"   Stop Loss ATR: {best_params['input_stop_loss_atr']}")
        print(f"   Take Profit: {best_params['input_take_profit']}")
        print(f"   คะแนน: {best_score:.2f}")
        
        # เปรียบเทียบกับ baseline
        improvement = best_score - baseline_score
        print(f"\n📈 การเปรียบเทียบกับ Baseline:")
        print(f"   Baseline Score: {baseline_score:.2f}")
        print(f"   Phase 1 Score: {best_score:.2f}")
        print(f"   การปรับปรุง: {improvement:+.2f} ({(improvement/baseline_score*100):+.1f}%)")
        
        # ตรวจสอบว่าบรรลุเป้าหมายหรือไม่
        best_win_rate = best_result['results'].get('win_rate', 0)
        best_drawdown = best_result['results'].get('max_drawdown', 0)
        baseline_win_rate = 39.37  # จาก baseline
        baseline_drawdown = 692378.57  # จาก baseline
        
        print(f"\n🎯 การประเมินเป้าหมาย Phase 1:")
        
        if best_win_rate > baseline_win_rate:
            print(f"   ✅ Win Rate: {best_win_rate:.1f}% (เพิ่มขึ้นจาก {baseline_win_rate:.1f}%)")
        else:
            print(f"   ⚠️ Win Rate: {best_win_rate:.1f}% (ไม่เพิ่มขึ้นจาก {baseline_win_rate:.1f}%)")
            
        if best_drawdown < baseline_drawdown:
            print(f"   ✅ Drawdown: ${best_drawdown:.0f} (ลดลงจาก ${baseline_drawdown:.0f})")
        else:
            print(f"   ⚠️ Drawdown: ${best_drawdown:.0f} (ไม่ลดลงจาก ${baseline_drawdown:.0f})")
        
        # บันทึกผลลัพธ์
        phase1_result = {
            'phase': 1,
            'focus': 'Risk Management',
            'best_parameters': best_params,
            'best_score': best_score,
            'improvement_from_baseline': improvement,
            'all_results': phase1_results[:10],  # เก็บ top 10
            'test_date': datetime.now().isoformat()
        }
        
        with open('phase1_result.json', 'w') as f:
            json.dump(phase1_result, f, indent=2)
        
        print(f"\n💾 บันทึกผลลัพธ์: phase1_result.json")
        
        # แนะนำขั้นตอนต่อไป
        print(f"\n🎯 ขั้นตอนต่อไป:")
        if improvement > 5:  # ถ้าปรับปรุงได้มากกว่า 5 คะแนน
            print("   👉 รัน step3_phase2_entry_quality.py")
            print("      เพื่อปรับปรุง Entry Conditions")
        else:
            print("   👉 ลองปรับ configuration ใน Phase 1 อีกครั้ง")
            print("      หรือรัน step3_phase2_entry_quality.py")
            
        return phase1_result
        
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n✅ Step 2 (Phase 1) เสร็จสมบูรณ์!")
        print(f"   การปรับปรุง: {result['improvement_from_baseline']:+.2f} คะแนน")
    else:
        print(f"\n❌ Step 2 (Phase 1) ล้มเหลว - กรุณาตรวจสอบข้อผิดพลาด")
