เนื่องจากมีขอสงสัย
ถ้าการตั้งค่า ส่งผลโดยตรง หรือทางอ้อม ต่อการเทรนโมเดล เช่น
input_initial_nbar_sl = 4
input_stop_loss_atr = 1.00
input_take_profit = 2.0 << สมมุติมา 3 ค่าก่อน อนาคตอาจทดสอบมากกว่านี้
อาจกำหนดเป็นขอบเขตการทดสอบ หรือ ช่วยแนะนำ

เช่น
input_initial_nbar_sl ทดสอบ 2 - 6
input_stop_loss_atr ทดสอบ 1.0 - 3.0
input_take_profit ทดสอบ 1.0 - 3.0 step 0.5

หรือช่วยแนะนำ ค่าที่อาจส่งผลอย่างมากจ่อผลลัพธ์ ของการเทรนโมเดล
แต่เนื่องจากมีการทดสอบหลายคู่ หรือหลายช่วงเวลา 
ต้องการหาแนวทางการสรุป หรือวัดผลอย่างไรว่าภาพรวม หรือ
สรุปเป็นรายตัว และบันทึกไว้ เพื่อเรียกใช้ภายหลัง .. (ตามความคิดแจ่ละสินทรัพย์ และช่วงเวลา มีพฤติกรรมต่างกัน)
ดังนั้นจึงสมมุติว่าค่าดังกล่าวอาจจะไม่เหมือนกัน

จึงต้องการทดสอบ
+ กำหนดวิธีการทดสอบ
+ กำหนดวิธีการให้คะแนนเพื่อใช้ในการเปรียบเทียบ 
+ การจัดลำดับการทดสอบ ขั้นตอนไหนถึงจะเหมาะสม 
+ การบันทึก และเรียกใช้งานถายหลัง

เนื่องจากปัจจุบัน ไม่มีโอกาศใช้งานจริง ถ้าได้แบบนี้

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
📈 สถิติสำหรับ Buy Trades:
Win%                0.00                
Expectancy          0.00                
📈 สถิติสำหรับ Sell Trades:
Win%                43.14               
Expectancy          -38.82              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                43.14               
Expectancy          -38.82              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    15.38          26                  
Tuesday   24.24          33                  
Wednesday 17.39          23                  
Thursday  9.38           32                  
Friday    12.00          25                  
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         0.00           2                   
5         0.00           5                   
6         0.00           1                   
10        30.00          10                  
11        33.33          12                  
12        25.00          4                   
13        22.22          9                   
14        0.00           2                   
15        13.33          15                  
16        15.79          19                  
17        30.77          13                  
18        5.00           20                  
19        0.00           12                  
20        16.67          12                  
21        0.00           3                   
========================================

📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              22        15.83%
SL Hit              117       84.17%
Technical Exit      0         0.00%
SL + Tech Exit      117       84.17%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 508.14
ขาดทุนเฉลี่ยเมื่อ SL Hit: -112.47
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:4.52

============================================================
📈 FINANCIAL ANALYSIS SUMMARY
============================================================
💰 Account Balance: $1,000.00
📊 Total Trades: 139
💵 Total Profit (1.0 lot): $-1,980.00 << ค่านี้ควรจะเป็น บวกบ่าง ไม่มากก็น้อย
📉 Max Drawdown (1.0 lot): $5,348.00
🎯 Recommended Lot Size: 0.0037
⚠️ Max Risk: 2.00%
============================================================

