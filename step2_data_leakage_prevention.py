
# ==============================================
# Data Leakage Prevention System
# ==============================================

def create_safe_features(df):
    """สร้าง features ที่ปลอดภัยจาก data leakage"""

    print("🔍 สร้าง safe features...")
    print(f"📊 คอลัมน์ที่มีอยู่: {list(df.columns)}")

    # ตรวจสอบและสร้าง safe features เฉพาะที่มีอยู่
    safe_features_created = 0

    # Basic price features
    if 'Volume' in df.columns:
        df['Volume_MA20_safe'] = df['Volume'].rolling(20).mean().shift(1)
        safe_features_created += 1

    if 'Close' in df.columns:
        df['Close_MA5_safe'] = df['Close'].rolling(5).mean().shift(1)
        df['Close_MA10_safe'] = df['Close'].rolling(10).mean().shift(1)
        df['Close_MA20_safe'] = df['Close'].rolling(20).mean().shift(1)
        safe_features_created += 3

    # Technical indicators (ตรวจสอบก่อนใช้)
    if 'RSI14' in df.columns:
        df['RSI14_safe'] = df['RSI14'].shift(1)
        safe_features_created += 1
        print("✅ สร้าง RSI14_safe")
    else:
        print("⚠️ ไม่พบ RSI14 - ข้าม")

    # MACD indicators
    if 'MACD_12_26_9' in df.columns:
        df['MACD_safe'] = df['MACD_12_26_9'].shift(1)
        safe_features_created += 1
        print("✅ สร้าง MACD_safe")
    else:
        print("⚠️ ไม่พบ MACD_12_26_9 - ข้าม")

    if 'MACDs_12_26_9' in df.columns:
        df['MACD_signal_safe'] = df['MACDs_12_26_9'].shift(1)
        safe_features_created += 1
        print("✅ สร้าง MACD_signal_safe")
    else:
        print("⚠️ ไม่พบ MACDs_12_26_9 - ข้าม")

    # ATR
    if 'ATR' in df.columns:
        df['ATR_safe'] = df['ATR'].shift(1)
        safe_features_created += 1
        print("✅ สร้าง ATR_safe")
    else:
        print("⚠️ ไม่พบ ATR - ข้าม")

    # EMA indicators
    for ema_period in ['EMA50', 'EMA100', 'EMA200']:
        if ema_period in df.columns:
            df[f'{ema_period}_safe'] = df[ema_period].shift(1)
            safe_features_created += 1
            print(f"✅ สร้าง {ema_period}_safe")
        else:
            print(f"⚠️ ไม่พบ {ema_period} - ข้าม")

    # ลบ features ที่อาจมี look-ahead bias
    dangerous_features = [
        'Close_Lag_1', 'High_Lag_1', 'Low_Lag_1', 'Open_Lag_1',
        'Volume_Lag_1', 'RSI14_Lag_1', 'EMA50_Lag_1'
    ]

    removed_features = 0
    for feature in dangerous_features:
        if feature in df.columns:
            df = df.drop(feature, axis=1)
            removed_features += 1
            print(f"⚠️ ลบ feature ที่อันตราย: {feature}")

    print(f"📊 สร้าง safe features: {safe_features_created} features")
    print(f"📊 ลบ dangerous features: {removed_features} features")

    # ไม่ลบ NaN rows ที่นี่ เพราะอาจทำให้ข้อมูลหาย
    # ให้ระบบหลักจัดการ NaN แทน

    print(f"✅ สร้าง safe features เสร็จสิ้น ({len(df)} rows)")

    return df

def validate_no_data_leakage(X_train, X_val, X_test):
    """ตรวจสอบว่าไม่มี data leakage"""

    print("🔍 ตรวจสอบ data leakage...")

    try:
        # ตรวจสอบ index (ถ้าเป็น time series)
        if hasattr(X_train.index, 'max') and hasattr(X_val.index, 'min'):
            train_max_idx = X_train.index.max()
            val_min_idx = X_val.index.min()
            val_max_idx = X_val.index.max()
            test_min_idx = X_test.index.min()

            print(f"📊 Index ranges:")
            print(f"   Train: {X_train.index.min()} → {train_max_idx}")
            print(f"   Val: {val_min_idx} → {val_max_idx}")
            print(f"   Test: {test_min_idx} → {X_test.index.max()}")

            if train_max_idx >= val_min_idx:
                print("⚠️ Warning: Training data overlaps with validation data!")
                print("💡 This might be OK if using stratified split instead of time series split")

            if val_max_idx >= test_min_idx:
                print("⚠️ Warning: Validation data overlaps with test data!")
                print("💡 This might be OK if using stratified split instead of time series split")

        # ตรวจสอบ features ที่อาจมี look-ahead bias
        suspicious_features = []
        for col in X_train.columns:
            if any(keyword in col.lower() for keyword in ['future', 'next', 'lead', 'forward']):
                suspicious_features.append(col)

        if suspicious_features:
            print(f"⚠️ พบ features ที่น่าสงสัย: {suspicious_features}")
        else:
            print("✅ ไม่พบ features ที่น่าสงสัย")

        print("✅ การตรวจสอบ data leakage เสร็จสิ้น")

    except Exception as e:
        print(f"⚠️ ไม่สามารถตรวจสอบ data leakage ได้: {e}")
        print("💡 ข้ามการตรวจสอบและดำเนินการต่อ")

    return True
