#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบฟังก์ชันหลักของ WebRequest Server
"""

import sys
import os
import pandas as pd
import numpy as np

# เพิ่ม path ปัจจุบันเข้าไปใน sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_enhanced_model_decision():
    """ทดสอบ enhanced_model_decision functions"""
    
    print("🔍 ทดสอบ Enhanced Model Decision Functions")
    
    # Import functions
    try:
        from WebRequest_Server_03_Target import (
            load_multi_model_components, 
            enhanced_model_decision_buy,
            enhanced_model_decision_sell
        )
        print("✅ Import functions สำเร็จ")
    except ImportError as e:
        print(f"❌ ไม่สามารถ import functions: {e}")
        return
    
    # ทดสอบการโหลดโมเดล
    symbol = "GOLD"
    timeframe = 60
    
    print(f"\n🔍 ทดสอบการโหลด Multi-Model components สำหรับ {symbol} M{timeframe}")
    
    try:
        scenario_models = load_multi_model_components(symbol, timeframe)
        
        if scenario_models:
            print(f"✅ โหลด Multi-Model components สำเร็จ: {len(scenario_models)} scenarios")
            
            # แสดงรายละเอียดโมเดล
            for scenario_name, model_info in scenario_models.items():
                print(f"   📊 {scenario_name}:")
                print(f"      Model: {model_info.get('model') is not None}")
                print(f"      Scaler: {model_info.get('scaler') is not None}")
                print(f"      Features: {len(model_info.get('features', []))} features")
            
            # สร้างข้อมูลทดสอบ
            print(f"\n🔍 ทดสอบ Enhanced Model Decision")
            
            # สร้าง prediction_row ตัวอย่าง
            features = scenario_models['trend_following']['features']
            prediction_row = pd.Series(np.random.random(len(features)), index=features)
            
            # ทดสอบ BUY decision
            print(f"\n🟢 ทดสอบ Enhanced BUY Decision")
            try:
                should_trade_buy, confidence_buy, details_buy = enhanced_model_decision_buy(
                    scenario_models, prediction_row, symbol, timeframe
                )
                print(f"   Should Trade: {should_trade_buy}")
                print(f"   Confidence: {confidence_buy:.4f}")
                print(f"   Details: {details_buy}")
            except Exception as e:
                print(f"   ❌ Error in BUY decision: {e}")
            
            # ทดสอบ SELL decision
            print(f"\n🔴 ทดสอบ Enhanced SELL Decision")
            try:
                should_trade_sell, confidence_sell, details_sell = enhanced_model_decision_sell(
                    scenario_models, prediction_row, symbol, timeframe
                )
                print(f"   Should Trade: {should_trade_sell}")
                print(f"   Confidence: {confidence_sell:.4f}")
                print(f"   Details: {details_sell}")
            except Exception as e:
                print(f"   ❌ Error in SELL decision: {e}")
                
        else:
            print("❌ ไม่สามารถโหลด Multi-Model components ได้")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_model_decision()
