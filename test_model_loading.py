#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการโหลดโมเดล Multi-Model Architecture
"""

import sys
import os

# เพิ่ม path ปัจจุบันเข้าไปใน sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_model_loading():
    """ทดสอบการโหลดโมเดลและ threshold"""
    
    print("🔍 ทดสอบการโหลดโมเดล Multi-Model Architecture")
    
    # Import functions
    try:
        from LightGBM_07_target import load_scenario_models, load_scenario_threshold, load_scenario_nbars
        print("✅ Import functions สำเร็จ")
    except ImportError as e:
        print(f"❌ ไม่สามารถ import functions: {e}")
        return
    
    # ทดสอบการโหลดโมเดล
    symbol = "GOLD"
    timeframe = 60
    
    print(f"\n🔍 ทดสอบการโหลดโมเดลสำหรับ {symbol} M{timeframe}")
    
    # โหลดโมเดล
    try:
        models_path = r"D:\test_gold\LightGBM\Multi\models"
        scenario_models = load_scenario_models(symbol, timeframe, base_folder=models_path)
        
        if scenario_models:
            print(f"✅ โหลดโมเดลสำเร็จ: {len(scenario_models)} scenarios")
            for scenario_name, model_info in scenario_models.items():
                print(f"   📊 {scenario_name}: {len(model_info['features'])} features")
                print(f"      Model: {model_info.get('model') is not None}")
                print(f"      Scaler: {model_info.get('scaler') is not None}")
        else:
            print("❌ ไม่สามารถโหลดโมเดลได้")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการโหลดโมเดล: {e}")
    
    # ทดสอบการโหลด threshold
    print(f"\n🔍 ทดสอบการโหลด threshold")
    
    scenarios_to_test = ["trend_following", "counter_trend", "enhanced"]
    
    for scenario in scenarios_to_test:
        try:
            if scenario == "enhanced":
                # ทดสอบการคำนวณค่าเฉลี่ยสำหรับ enhanced
                tf_data = load_scenario_threshold(symbol, timeframe, "trend_following")
                ct_data = load_scenario_threshold(symbol, timeframe, "counter_trend")
                tf_threshold = tf_data['best_threshold']
                ct_threshold = ct_data['best_threshold']
                enhanced_threshold = (tf_threshold + ct_threshold) / 2
                print(f"✅ {scenario} threshold (calculated): {enhanced_threshold:.4f}")
                
                tf_nbars = load_scenario_nbars(symbol, timeframe, "trend_following")
                ct_nbars = load_scenario_nbars(symbol, timeframe, "counter_trend")
                enhanced_nbars = int((tf_nbars + ct_nbars) / 2)
                print(f"✅ {scenario} nBars_SL (calculated): {enhanced_nbars}")
            else:
                # ทดสอบการโหลดปกติ
                threshold_data = load_scenario_threshold(symbol, timeframe, scenario)
                threshold = threshold_data['best_threshold']
                print(f"✅ {scenario} threshold: {threshold:.4f}")
                
                nbars = load_scenario_nbars(symbol, timeframe, scenario)
                print(f"✅ {scenario} nBars_SL: {nbars}")
                
        except Exception as e:
            print(f"⚠️ {scenario}: {e}")

if __name__ == "__main__":
    test_model_loading()
