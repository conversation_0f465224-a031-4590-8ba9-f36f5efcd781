#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการแก้ไข feature selection กับข้อมูลจริง
"""

import sys
import os
import pandas as pd

# เพิ่ม path สำหรับ import
sys.path.append('.')

def test_real_feature_selection():
    """ทดสอบ select_features กับข้อมูลจริง"""
    print("🧪 ทดสอบ select_features กับข้อมูลจริง")
    
    try:
        from LightGBM_07_target import select_features
        
        # ทดสอบกับ GOLD
        print("\n📊 ทดสอบ GOLD:")
        gold_path = 'CSV_Files_Fixed/GOLD_H1_FIXED.csv'
        if os.path.exists(gold_path):
            df_gold = pd.read_csv(gold_path, sep=',', skiprows=1)  # ข้าม header แปลกๆ
            print(f'   โหลดข้อมูล GOLD: {df_gold.shape}')
            print(f'   คอลัมน์: {list(df_gold.columns)}')
            
            # แก้ไขชื่อคอลัมน์
            df_gold.columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol', 'Vol', 'Spread']

            # เพิ่มคอลัมน์ Target และ features พื้นฐาน
            df_gold['Target'] = (df_gold['Close'].shift(-1) > df_gold['Close']).astype(int)
            df_gold['Volume_Change_1'] = df_gold['Vol'].pct_change()
            df_gold['Price_Range'] = df_gold['High'] - df_gold['Low']
            df_gold['Hour'] = pd.to_datetime(df_gold['Date'] + ' ' + df_gold['Time']).dt.hour
            df_gold['DayOfWeek'] = pd.to_datetime(df_gold['Date']).dt.dayofweek
            df_gold['IsMorning'] = (df_gold['Hour'] >= 6) & (df_gold['Hour'] < 12)
            df_gold['IsAfternoon'] = (df_gold['Hour'] >= 12) & (df_gold['Hour'] < 18)
            df_gold['IsEvening'] = (df_gold['Hour'] >= 18) & (df_gold['Hour'] < 22)
            df_gold['IsNight'] = (df_gold['Hour'] >= 22) | (df_gold['Hour'] < 6)
            
            # ลบ NaN
            df_gold = df_gold.dropna()
            
            if len(df_gold) > 100:  # ต้องมีข้อมูลเพียงพอ
                features_gold = select_features(df_gold, 'GOLD', 'M60')
                print(f'   ✅ GOLD features: {len(features_gold)} features')
                print(f'   🔍 Top 10: {features_gold[:10]}')
            else:
                print(f'   ⚠️ ข้อมูล GOLD ไม่เพียงพอ: {len(df_gold)} rows')
        else:
            print(f'   ❌ ไม่พบไฟล์: {gold_path}')
        
        # ทดสอบกับ USDJPY
        print("\n📊 ทดสอบ USDJPY:")
        usdjpy_path = 'CSV_Files_Fixed/USDJPY_H1_FIXED.csv'
        if os.path.exists(usdjpy_path):
            df_usdjpy = pd.read_csv(usdjpy_path, sep=',', skiprows=1)  # ข้าม header แปลกๆ
            print(f'   โหลดข้อมูล USDJPY: {df_usdjpy.shape}')
            print(f'   คอลัมน์: {list(df_usdjpy.columns)}')
            
            # แก้ไขชื่อคอลัมน์
            df_usdjpy.columns = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'TickVol', 'Vol', 'Spread']

            # เพิ่มคอลัมน์ Target และ features พื้นฐาน
            df_usdjpy['Target'] = (df_usdjpy['Close'].shift(-1) > df_usdjpy['Close']).astype(int)
            df_usdjpy['Volume_Change_1'] = df_usdjpy['Vol'].pct_change()
            df_usdjpy['Price_Range'] = df_usdjpy['High'] - df_usdjpy['Low']
            df_usdjpy['Hour'] = pd.to_datetime(df_usdjpy['Date'] + ' ' + df_usdjpy['Time']).dt.hour
            df_usdjpy['DayOfWeek'] = pd.to_datetime(df_usdjpy['Date']).dt.dayofweek
            df_usdjpy['IsMorning'] = (df_usdjpy['Hour'] >= 6) & (df_usdjpy['Hour'] < 12)
            df_usdjpy['IsAfternoon'] = (df_usdjpy['Hour'] >= 12) & (df_usdjpy['Hour'] < 18)
            df_usdjpy['IsEvening'] = (df_usdjpy['Hour'] >= 18) & (df_usdjpy['Hour'] < 22)
            df_usdjpy['IsNight'] = (df_usdjpy['Hour'] >= 22) | (df_usdjpy['Hour'] < 6)
            
            # ลบ NaN
            df_usdjpy = df_usdjpy.dropna()
            
            if len(df_usdjpy) > 100:  # ต้องมีข้อมูลเพียงพอ
                features_usdjpy = select_features(df_usdjpy, 'USDJPY', 'M60')
                print(f'   ✅ USDJPY features: {len(features_usdjpy)} features')
                print(f'   🔍 Top 10: {features_usdjpy[:10]}')
            else:
                print(f'   ⚠️ ข้อมูล USDJPY ไม่เพียงพอ: {len(df_usdjpy)} rows')
        else:
            print(f'   ❌ ไม่พบไฟล์: {usdjpy_path}')
            
    except Exception as e:
        print(f'❌ เกิดข้อผิดพลาด: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_feature_selection()
