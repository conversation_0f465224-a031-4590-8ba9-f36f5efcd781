#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบการวิเคราะห์แบบเต็มรูปแบบ (จำลอง Multi-Model Analysis)
"""

import sys
import os
import pandas as pd
import numpy as np

# เพิ่ม path ปัจจุบันเข้าไปใน sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_full_multi_model_analysis():
    """ทดสอบการวิเคราะห์ Multi-Model แบบเต็มรูปแบบ"""
    
    print("🔍 ทดสอบการวิเคราะห์ Multi-Model แบบเต็มรูปแบบ")
    
    # Import functions
    try:
        from WebRequest_Server_03_Target import load_multi_model_components
        print("✅ Import functions สำเร็จ")
    except ImportError as e:
        print(f"❌ ไม่สามารถ import functions: {e}")
        return
    
    # ทดสอบการโหลดโมเดล
    symbol = "GOLD"
    timeframe = 60
    
    print(f"\n🔍 ทดสอบการโหลด Multi-Model components สำหรับ {symbol} M{timeframe}")
    
    try:
        scenario_models = load_multi_model_components(symbol, timeframe)
        
        if scenario_models:
            print(f"✅ โหลด Multi-Model components สำเร็จ: {len(scenario_models)} scenarios")
            
            # สร้างข้อมูลทดสอบที่สมจริงมากขึ้น
            print(f"\n🔍 สร้างข้อมูลทดสอบ")
            
            # ใช้ features จาก trend_following model
            features = scenario_models['trend_following']['features']
            print(f"📊 จำนวน features: {len(features)}")
            
            # สร้าง prediction_row ที่มีค่าสมจริง
            prediction_row = pd.Series(index=features)
            
            # ใส่ค่าทดสอบที่สมจริง
            for i, feature in enumerate(features):
                if 'close' in feature.lower():
                    prediction_row[feature] = 2650.0  # ราคา Gold
                elif 'ema' in feature.lower():
                    prediction_row[feature] = 2640.0  # EMA
                elif 'rsi' in feature.lower():
                    prediction_row[feature] = 65.0    # RSI
                elif 'volume' in feature.lower():
                    prediction_row[feature] = 1000.0  # Volume
                else:
                    prediction_row[feature] = np.random.uniform(-1, 1)  # ค่าสุ่มสำหรับ features อื่นๆ
            
            # เพิ่มข้อมูลที่จำเป็นสำหรับการกำหนด scenario
            prediction_row['close'] = 2650.0
            prediction_row['EMA_200'] = 2640.0  # close > EMA200 = uptrend
            
            print(f"📊 ข้อมูลทดสอบ: Close={prediction_row.get('close', 0):.2f}, EMA200={prediction_row.get('EMA_200', 0):.2f}")
            
            # จำลองการวิเคราะห์แบบเต็มรูปแบบ
            print(f"\n🔄 เริ่มการวิเคราะห์ Multi-Model")
            
            analysis_results = {
                'trend_following': {'buy': None, 'sell': None},
                'counter_trend': {'buy': None, 'sell': None}
            }
            
            # คำนวณ confidence สำหรับ trend_following
            if 'trend_following' in scenario_models:
                print(f"\n📊 คำนวณ Trend Following confidence")
                tf_model = scenario_models['trend_following']['model']
                tf_features = scenario_models['trend_following']['features']
                tf_scaler = scenario_models['trend_following']['scaler']
                
                # เตรียมข้อมูล
                tf_feature_values = []
                for feature in tf_features:
                    if feature in prediction_row.index:
                        tf_feature_values.append(prediction_row[feature])
                    else:
                        tf_feature_values.append(0.0)
                
                tf_feature_array = np.array(tf_feature_values).reshape(1, -1)
                if tf_scaler is not None:
                    tf_scaled_features = tf_scaler.transform(tf_feature_array)
                else:
                    tf_scaled_features = tf_feature_array
                
                # ทำนาย
                tf_prediction_proba = tf_model.predict_proba(tf_scaled_features)[0]
                print(f"   📈 Prediction probabilities: {tf_prediction_proba}")
                
                # คำนวณ confidence
                if len(tf_prediction_proba) >= 5:
                    tf_buy_conf = tf_prediction_proba[3] + tf_prediction_proba[4]
                    tf_sell_conf = tf_prediction_proba[0] + tf_prediction_proba[1]
                else:
                    tf_buy_conf = tf_prediction_proba[1] if len(tf_prediction_proba) > 1 else 0.0
                    tf_sell_conf = tf_prediction_proba[0] if len(tf_prediction_proba) > 0 else 0.0
                
                analysis_results['trend_following']['buy'] = {
                    'should_trade': tf_buy_conf > 0.5,
                    'confidence': tf_buy_conf,
                    'details': f'TF_BUY_Prob:{tf_buy_conf:.4f}'
                }
                analysis_results['trend_following']['sell'] = {
                    'should_trade': tf_sell_conf > 0.5,
                    'confidence': tf_sell_conf,
                    'details': f'TF_SELL_Prob:{tf_sell_conf:.4f}'
                }
                
                print(f"   🟢 BUY: {tf_buy_conf:.4f} (Should trade: {tf_buy_conf > 0.5})")
                print(f"   🔴 SELL: {tf_sell_conf:.4f} (Should trade: {tf_sell_conf > 0.5})")

            # คำนวณ confidence สำหรับ counter_trend
            if 'counter_trend' in scenario_models:
                print(f"\n📊 คำนวณ Counter Trend confidence")
                ct_model = scenario_models['counter_trend']['model']
                ct_features = scenario_models['counter_trend']['features']
                ct_scaler = scenario_models['counter_trend']['scaler']
                
                # เตรียมข้อมูล
                ct_feature_values = []
                for feature in ct_features:
                    if feature in prediction_row.index:
                        ct_feature_values.append(prediction_row[feature])
                    else:
                        ct_feature_values.append(0.0)
                
                ct_feature_array = np.array(ct_feature_values).reshape(1, -1)
                if ct_scaler is not None:
                    ct_scaled_features = ct_scaler.transform(ct_feature_array)
                else:
                    ct_scaled_features = ct_feature_array
                
                # ทำนาย
                ct_prediction_proba = ct_model.predict_proba(ct_scaled_features)[0]
                print(f"   📈 Prediction probabilities: {ct_prediction_proba}")
                
                # คำนวณ confidence
                if len(ct_prediction_proba) >= 5:
                    ct_buy_conf = ct_prediction_proba[3] + ct_prediction_proba[4]
                    ct_sell_conf = ct_prediction_proba[0] + ct_prediction_proba[1]
                else:
                    ct_buy_conf = ct_prediction_proba[1] if len(ct_prediction_proba) > 1 else 0.0
                    ct_sell_conf = ct_prediction_proba[0] if len(ct_prediction_proba) > 0 else 0.0
                
                analysis_results['counter_trend']['buy'] = {
                    'should_trade': ct_buy_conf > 0.5,
                    'confidence': ct_buy_conf,
                    'details': f'CT_BUY_Prob:{ct_buy_conf:.4f}'
                }
                analysis_results['counter_trend']['sell'] = {
                    'should_trade': ct_sell_conf > 0.5,
                    'confidence': ct_sell_conf,
                    'details': f'CT_SELL_Prob:{ct_sell_conf:.4f}'
                }
                
                print(f"   🟢 BUY: {ct_buy_conf:.4f} (Should trade: {ct_buy_conf > 0.5})")
                print(f"   🔴 SELL: {ct_sell_conf:.4f} (Should trade: {ct_sell_conf > 0.5})")
            
            # แสดงสรุปผลการวิเคราะห์
            print(f"\n📋 สรุปผลการวิเคราะห์:")
            for scenario in ['trend_following', 'counter_trend']:
                if scenario in analysis_results:
                    print(f"   📊 {scenario.upper()}:")
                    for action in ['buy', 'sell']:
                        if analysis_results[scenario][action]:
                            result = analysis_results[scenario][action]
                            print(f"      {action.upper()}: confidence={result['confidence']:.4f}, should_trade={result['should_trade']}")
                        else:
                            print(f"      {action.upper()}: No data")
            
            # จำลองการส่งข้อมูลไปยัง MT5
            print(f"\n📤 จำลองข้อมูลที่จะส่งไปยัง MT5:")
            
            # ดึงค่า confidence
            tf_buy_conf = analysis_results['trend_following']['buy']['confidence'] if analysis_results['trend_following']['buy'] else 0.0
            tf_sell_conf = analysis_results['trend_following']['sell']['confidence'] if analysis_results['trend_following']['sell'] else 0.0
            ct_buy_conf = analysis_results['counter_trend']['buy']['confidence'] if analysis_results['counter_trend']['buy'] else 0.0
            ct_sell_conf = analysis_results['counter_trend']['sell']['confidence'] if analysis_results['counter_trend']['sell'] else 0.0
            
            print(f"   trend_following_buy_confidence: {tf_buy_conf:.4f}")
            print(f"   trend_following_sell_confidence: {tf_sell_conf:.4f}")
            print(f"   counter_trend_buy_confidence: {ct_buy_conf:.4f}")
            print(f"   counter_trend_sell_confidence: {ct_sell_conf:.4f}")
            
            # ตรวจสอบว่าค่าไม่เป็น 0.0 ทั้งหมด
            total_confidence = tf_buy_conf + tf_sell_conf + ct_buy_conf + ct_sell_conf
            if total_confidence > 0:
                print(f"✅ การคำนวณ confidence สำเร็จ! (Total: {total_confidence:.4f})")
            else:
                print(f"⚠️ ค่า confidence ทั้งหมดเป็น 0.0 - อาจมีปัญหาในการคำนวณ")
                
        else:
            print("❌ ไม่สามารถโหลด Multi-Model components ได้")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_multi_model_analysis()
