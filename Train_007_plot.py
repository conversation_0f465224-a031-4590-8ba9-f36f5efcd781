import os
import pandas as pd
import numpy as np
import joblib
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import lightgbm as lgb
from sklearn.metrics import accuracy_score, f1_score
import mplfinance as mpf

# ====================================================================
# Configuration
# ====================================================================
TEST_GROUPS = {
    "M60": [
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv"
    ]
}
OUTPUT_DIR = "LightGBM_Data"
HORIZONS = [10, 20]
TARGETS = ['Target_Buy', 'Target_Sell']

# ====================================================================
# Feature Engineering Helper Functions
# ====================================================================

# --- EMA Calculation (Classic, seeded with SMA) ---
def classic_ema(series, span):
    sma = series.rolling(window=span, min_periods=span).mean()
    ema = pd.Series(index=series.index, dtype=float)
    alpha = 2 / (span + 1)

    for i in range(len(series)):
        if i < span-1:
            ema.iloc[i] = np.nan
        elif i == span-1:
            ema.iloc[i] = sma.iloc[i]
        else:
            ema.iloc[i] = alpha * series.iloc[i] + (1 - alpha) * ema.iloc[i-1]
    return ema

def classic_macd_ema(series: pd.Series, span: int):
    s = pd.to_numeric(series, errors='coerce').copy()
    alpha = 2.0 / (span + 1.0)
    out = pd.Series(index=s.index, dtype='float64')

    # หาตำแหน่งเริ่มต้นที่มี value จริง
    valid_idx = s.first_valid_index()
    if valid_idx is None:
        return out

    start = s.index.get_loc(valid_idx)
    n = len(s)

    # ถ้ามีข้อมูลมากพอสำหรับ seed SMA
    if n - start >= span:
        # seed position at index start+span-1
        seed_pos = start + span - 1
        seed = s.iloc[start: start + span].mean()
        out.iloc[seed_pos] = seed
        # recursive
        for i in range(seed_pos + 1, n):
            out.iloc[i] = alpha * s.iloc[i] + (1 - alpha) * out.iloc[i - 1]
        # ข้อดี: ค่าก่อน seed_pos จะยังคง NaN
    else:
        # fallback: ถ้าไม่พอข้อมูล ให้ใช้ pandas ewm (จะ seed ด้วยค่าที่มี)
        out = s.ewm(span=span, adjust=False).mean()

    return out

def mt5_like_macd_seeded(price: pd.Series, fast=12, slow=26, signal=9):
    price = pd.to_numeric(price, errors='coerce')
    ema_fast = classic_macd_ema(price, fast)
    ema_slow = classic_macd_ema(price, slow)
    macd_line = ema_fast - ema_slow

    # สำหรับ signal line เรามักจะใช้ EMA บน macd_line (และสามารถ seed ด้วย SMA ของ macd ส่วนเริ่ม)
    signal_line = classic_macd_ema(macd_line, signal)
    hist = macd_line - signal_line
    return ema_fast, ema_slow, macd_line, signal_line, hist

# ====================================================================
# Data Processing Functions
# ====================================================================

def load_and_clean_data(symbol, timeframe, file_path: str) -> pd.DataFrame:
    """Loads data from a CSV file, cleans it, and standardizes the DateTime column."""
    print(f"--- Loading and Cleaning {os.path.basename(file_path)} ---")
    
    df = pd.read_csv(file_path)
    print(f"Initial rows: {len(df)}")
    
    # Drop the first row if it's not header-related
    if "Date" not in df.columns and "Time" not in df.columns:
        df = df.iloc[1:].reset_index(drop=True)

    # Combine Date and Time into a single DateTime column
    if 'Date' in df.columns and 'Time' in df.columns:
        df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'], errors='coerce')
        df.drop(['Date', 'Time'], axis=1, inplace=True)
        # Reorder columns to have DateTime first
        cols = ['DateTime'] + [col for col in df.columns if col != 'DateTime']
        df = df[cols]
        print(f"DateTime column created. Date range: {df['DateTime'].min()} to {df['DateTime'].max()}")
    else:
        print("⚠️ 'Date' or 'Time' column not found.")

    # Ensure numeric types for price columns
    for col in ['Open', 'High', 'Low', 'Close']:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # ตรวจสอบว่า row แรกมีปัญหา
    # print(df.head(5))

    # ถ้ารู้ว่าแถวแรกไม่ใช่ข้อมูลที่ต้องใช้:
    df = df.iloc[1:].reset_index(drop=True)

    # รายชื่อคอลัมน์ที่ไม่ใช้ ต้องการลบออก
    drop_cols = ["Vol","Col_8"]
    df = df.drop(columns=drop_cols, errors="ignore")

    # เปลี่ยนชื่อคอลัมน์ 'TickVol' เป็น 'Volume'
    if 'TickVol' in df.columns:
        df = df.rename(columns={'TickVol': 'Volume'})

    # ตรวจสอบว่า row แรกมีปัญหา
    # print(df.head(5))

    # ==============================================
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_data = "01_Data"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_data}.csv"        # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name) # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

    # ✅ บันทึกไฟล์ CSV
    df.to_csv(new_file_path, index=False)

    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    return df

def create_features(symbol, timeframe, df: pd.DataFrame) -> pd.DataFrame:
    """Creates technical indicators and price action features."""
    print("--- Creating Features ---")
    df_feat = df.copy()

    # Price Action
    df_feat["Bar_CL"] = 0.0
    df_feat.loc[df_feat['Close'] > df_feat['Open'], "Bar_CL"] = 1.0
    df_feat.loc[df_feat['Close'] < df_feat['Open'], "Bar_CL"] = -1.0
    df_feat['Price_Range'] = df_feat["High"] - df_feat["Low"]
    df_feat['Price_Move'] = df_feat["Close"] - df_feat["Open"]

    # EMAs
    df_feat['EMA50'] = classic_ema(df_feat['Close'], 50)
    df_feat['EMA100'] = classic_ema(df_feat['Close'], 100)
    df_feat['EMA200'] = classic_ema(df_feat['Close'], 200)

    # MACD
    ema12, ema26, macd_line, macd_signal, macd_hist = mt5_like_macd_seeded(df['Close'])

    df_feat['EMA12'] = ema12
    df_feat['EMA26'] = ema26
    df_feat['MACD'] = macd_line
    df_feat['MACD_SIGNAL'] = macd_signal
    df_feat['MACD_HIST'] = macd_hist

    # รายชื่อคอลัมน์ที่ไม่ใช้ ต้องการลบออก
    drop_cols = ["EMA12","EMA26"]
    df_feat = df_feat.drop(columns=drop_cols, errors="ignore")

    # ==============================================
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_features = "02a_Features"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_features}.csv"        # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name) # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

    # ✅ บันทึกไฟล์ CSV
    df_feat.to_csv(new_file_path, index=False)

    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    # ลบแถวที่มี NaN
    df_feat = df_feat.dropna().reset_index(drop=True)

    # ==============================================
    # กำหนด suffix ที่ต้องการเติมท้าย
    suffix_dropna = "02b_Dropna"

    # สร้างชื่อไฟล์ใหม่
    new_file_name = f"{timeframe}_{symbol}_{suffix_dropna}.csv"          # 'GOLD_H1_FIXED_cleaned.csv'
    new_file_path = os.path.join(OUTPUT_DIR, new_file_name) # LightGBM_Data/GOLD_H1_FIXED_cleaned.csv

    # ✅ บันทึกไฟล์ CSV
    df_feat.to_csv(new_file_path, index=False)

    print(f"✅ Saved cleaned file to: {new_file_path}")
    # ==============================================

    print(f"Features created. DF shape: {df_feat.shape}")
    return df_feat

def preprocess_for_training(df: pd.DataFrame) -> pd.DataFrame:
    """Prepares the dataframe for training by cleaning and adding scenario features."""
    print("--- Preprocessing for Training ---")
    
    # 1. Drop rows with NaN values from feature creation
    df_processed = df.dropna().reset_index(drop=True)
    print(f"Rows after dropping NaN: {len(df_processed)}")

    # 2. Drop unnecessary columns
    drop_cols = ["Vol", "Col_8", "Bar_CLp"] # Assuming Bar_CLp might exist from old code
    df_processed = df_processed.drop(columns=drop_cols, errors="ignore")

    # 3. Rename and ensure numeric types for volume
    if 'TickVol' in df_processed.columns:
        df_processed = df_processed.rename(columns={'TickVol': 'Volume'})
    if 'Volume' in df_processed.columns:
        df_processed['Volume'] = pd.to_numeric(df_processed['Volume'], errors='coerce').fillna(0)

    # 4. Create Market Scenario features
    conditions = [
        (df_processed['Close'] > df_processed['EMA200']) & (df_processed['Low'] > df_processed['EMA200']) & (df_processed['MACD'] > df_processed['MACD_SIGNAL']),
        (df_processed['Close'] < df_processed['EMA200']) & (df_processed['High'] < df_processed['EMA200']) & (df_processed['MACD'] < df_processed['MACD_SIGNAL']),
    ]
    choices = ['Uptrend', 'Downtrend']
    df_processed['Market_Scenario'] = np.select(conditions, choices, default='Natural')
    df_processed = pd.get_dummies(df_processed, columns=['Market_Scenario'], prefix='Scenario')
    
    print("Preprocessing complete.")
    return df_processed

# ====================================================================
# Model Training and Evaluation Function
# ====================================================================

def train_evaluate_and_save(df: pd.DataFrame, symbol: str, group_name: str):
    """Loops through horizons and targets to train, evaluate, and save models and scalers."""
    print(f"\n{'='*60}")
    print(f"STARTING TRAINING FOR: {symbol} - {group_name}")
    print(f"{'='*60}")

    os.makedirs(OUTPUT_DIR, exist_ok=True)

    for horizon in HORIZONS:
        print(f"\n--- Processing Horizon: {horizon} bars ---")
        
        # 1. Create Targets for the current horizon
        df_horizon = df.copy()
        df_horizon['Next_Close'] = df_horizon['Close'].shift(-horizon)
        df_horizon['Target_Buy'] = ((df_horizon['Close'] > df_horizon['Open']) & (df_horizon['Next_Close'] > df_horizon['Close'])).astype(int)
        df_horizon['Target_Sell'] = ((df_horizon['Close'] < df_horizon['Open']) & (df_horizon['Next_Close'] < df_horizon['Close'])).astype(int)
        df_horizon.dropna(subset=['Next_Close', 'Target_Buy', 'Target_Sell'], inplace=True)

        for target_col in TARGETS:
            print(f"\n🎯 Training for Target: {target_col} (Horizon: {horizon})")

            # 2. Define Features (X) and Target (y)
            feature_columns = [col for col in df_horizon.columns if col not in ['DateTime', 'Next_Close', 'Target_Buy', 'Target_Sell']]
            X = df_horizon[feature_columns]
            y = df_horizon[target_col]

            if y.nunique() < 2:
                print(f"⚠️ Skipping {target_col} for Horizon {horizon} due to single class in target.")
                continue

            # 3. Split Data (Train 60%, Validation 20%, Test 20%)
            # First, split into training (60%) and a temporary set (40%)
            X_train, X_temp, y_train, y_temp = train_test_split(X, y, test_size=0.4, random_state=42, stratify=y)

            # Now, split the temporary set into validation (50% of 40% = 20%) and test (50% of 40% = 20%)
            X_val, X_test, y_val, y_test = train_test_split(X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp)

            # 4. Scale Features and Save Scaler
            scaler = StandardScaler()
            numeric_features = X_train.select_dtypes(include=np.number).columns.tolist()
            
            X_train = X_train.copy()
            X_val = X_val.copy()
            X_test = X_test.copy()
            
            scaler.fit(X_train[numeric_features])
            
            scaler_filename = f"{symbol}_{group_name}_{target_col}_h{horizon}_scaler.joblib"
            scaler_path = os.path.join(OUTPUT_DIR, scaler_filename)
            joblib.dump(scaler, scaler_path)
            print(f"💾 Saved Scaler to: {scaler_path}")

            X_train.loc[:, numeric_features] = scaler.transform(X_train[numeric_features])
            X_val.loc[:, numeric_features] = scaler.transform(X_val[numeric_features])
            X_test.loc[:, numeric_features] = scaler.transform(X_test[numeric_features])

            # 5. Train Model
            print("Training model with validation set for early stopping...")
            model = lgb.LGBMClassifier(objective='binary', random_state=42)
            model.fit(X_train, y_train,
                    eval_set=[(X_val, y_val)],
                    eval_metric='logloss',
                    callbacks=[lgb.early_stopping(10, verbose=False)])

            # 6. Evaluate Model on Test Set
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            f1 = f1_score(y_test, y_pred, average='weighted')
            print(f"📊 Test Set Accuracy: {accuracy:.4f}, F1-Score: {f1:.4f}")

            # 7. Save Model
            model_filename = f"{symbol}_{group_name}_{target_col}_h{horizon}_model.joblib"
            model_path = os.path.join(OUTPUT_DIR, model_filename)
            joblib.dump(model, model_path)
            print(f"💾 Saved Model to: {model_path}")

            # 8. Test and Visualize
            test_and_visualize_model(df_horizon, symbol, group_name, target_col, horizon, X_val, X_test)


# ====================================================================
# Model Testing and Visualization Function
# ====================================================================

def test_and_visualize_model(df_horizon, symbol, group_name, target_col, horizon, X_val, X_test):
    """Loads a saved model, makes predictions, and visualizes the results."""
    print(f"\n--- Testing and Visualizing: {target_col} h{horizon} ---")

    try:
        # 1. Load Model and Scaler
        model_filename = f"{symbol}_{group_name}_{target_col}_h{horizon}_model.joblib"
        model_path = os.path.join(OUTPUT_DIR, model_filename)
        model = joblib.load(model_path)

        scaler_filename = f"{symbol}_{group_name}_{target_col}_h{horizon}_scaler.joblib"
        scaler_path = os.path.join(OUTPUT_DIR, scaler_filename)
        scaler = joblib.load(scaler_path)
        print(f"✅ Loaded model and scaler from {model_path}")

        # 2. Combine Validation and Test sets for visualization
        X_vis = pd.concat([X_val, X_test]).sort_index()

        # Ensure data is scaled (already done, but good practice to be explicit)
        numeric_features = X_vis.select_dtypes(include=np.number).columns.tolist()
        X_vis_scaled = X_vis.copy()
        X_vis_scaled.loc[:, numeric_features] = scaler.transform(X_vis[numeric_features])

        # 3. Make Predictions
        predictions = model.predict(X_vis_scaled)
        X_vis['Prediction'] = predictions

        # 4. Prepare data for plotting
        plot_df = X_vis.copy()
        plot_df['DateTime'] = df_horizon.loc[plot_df.index, 'DateTime']
        plot_df['DateTime'] = pd.to_datetime(plot_df['DateTime'])
        plot_df.set_index('DateTime', inplace=True)


        # Take a sample for cleaner visualization (e.g., last 200 bars)
        plot_df = plot_df.tail(200)

        # 5. Create plot
        buy_signals = pd.Series(np.nan, index=plot_df.index)
        sell_signals = pd.Series(np.nan, index=plot_df.index)

        buy_mask = (plot_df['Prediction'] == 1) & (target_col == 'Target_Buy')
        sell_mask = (plot_df['Prediction'] == 1) & (target_col == 'Target_Sell')

        buy_signals.loc[buy_mask] = plot_df.loc[buy_mask, 'Low'] * 0.98
        sell_signals.loc[sell_mask] = plot_df.loc[sell_mask, 'High'] * 1.02

        # Add plot markers
        add_plots = []
        if not buy_signals.dropna().empty:
            add_plots.append(mpf.make_addplot(buy_signals, type='scatter', marker='^', color='lime', markersize=100))
        if not sell_signals.dropna().empty:
            add_plots.append(mpf.make_addplot(sell_signals, type='scatter', marker='v', color='red', markersize=100))

        # Generate and save the plot
        chart_title = f"{symbol} {group_name} - {target_col} (h{horizon}) Predictions"
        save_path = os.path.join(OUTPUT_DIR, f"{symbol}_{group_name}_{target_col}_h{horizon}_prediction_chart.png")

        mpf.plot(plot_df, 
                type='candle', 
                style='yahoo', 
                title=chart_title, 
                ylabel='Price', 
                addplot=add_plots if add_plots else None,
                volume=True, 
                panel_ratios=(3, 1),
                savefig=save_path)

        print(f"📈 Saved prediction chart to: {save_path}")

    except FileNotFoundError as e:
        print(f"❌ Error loading files for visualization: {e}. Skipping.")
    except Exception as e:
        print(f"❌ An error occurred during visualization: {e}")
        import traceback
        traceback.print_exc()

# ====================================================================
# Main Execution
# ====================================================================

def main():
    """Main function to orchestrate the data processing and model training pipeline."""
    for group_name, group_files in TEST_GROUPS.items():
        for file_path in group_files:
            
            file_name = os.path.basename(file_path)  # GOLD_H1_FIXED.csv
            symbol = file_name.split('_')[0] # GOLD
            timeframe = group_name # M60
            print(f"symbol {symbol} timeframe {timeframe}")

            try:
                # Step 1: Load and clean the raw data
                df_clean = load_and_clean_data(symbol, timeframe, file_path)
                
                # Step 2: Create technical features
                df_with_features = create_features(symbol, timeframe, df_clean)
                
                # Step 3: Final preprocessing before training
                df_ready_to_train = preprocess_for_training(df_with_features)
                
                # Step 4: Train, evaluate, and save models for all horizons
                train_evaluate_and_save(df_ready_to_train, symbol, group_name)

            except Exception as e:
                print(f"❌ An error occurred while processing {file_path}: {e}")
                import traceback
                traceback.print_exc()

if __name__ == "__main__":
    main()
