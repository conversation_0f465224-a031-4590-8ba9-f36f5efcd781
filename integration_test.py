"""
🧪 Integration Test for Training Protection Systems
==================================================

ทดสอบระบบป้องกันที่เพิ่มเข้าไปใน LightGBM_10_4.py
"""

import os
import sys
import importlib.util

def test_protection_systems():
    """ทดสอบระบบป้องกันทั้งหมด"""
    
    print("🧪 ทดสอบระบบป้องกันการเทรน")
    print("="*50)
    
    # ทดสอบการ import ไฟล์ป้องกัน
    protection_files = [
        'model_protection_system.py',
        'training_prevention_system.py', 
        'step1_overfitting_prevention.py',
        'step2_data_leakage_prevention.py',
        'step3_model_versioning.py',
        'step4_conservative_thresholds.py',
        'step5_safe_oversampling.py'
    ]
    
    available_systems = []
    
    for file in protection_files:
        if os.path.exists(file):
            try:
                # ทดสอบการ import
                spec = importlib.util.spec_from_file_location("test_module", file)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                print(f"✅ {file} - พร้อมใช้งาน")
                available_systems.append(file)
                
            except Exception as e:
                print(f"❌ {file} - มีปัญหา: {e}")
        else:
            print(f"⚠️ {file} - ไม่พบไฟล์")
    
    print(f"\n📊 สรุป: {len(available_systems)}/{len(protection_files)} ระบบพร้อมใช้งาน")
    
    return available_systems

def test_lightgbm_integration():
    """ทดสอบการรวมเข้ากับ LightGBM_10_4.py"""
    
    print("\n🔗 ทดสอบการรวมเข้ากับ LightGBM_10_4.py")
    print("="*50)
    
    if not os.path.exists('LightGBM_10_4.py'):
        print("❌ ไม่พบไฟล์ LightGBM_10_4.py")
        return False
    
    # อ่านไฟล์และตรวจสอบการเพิ่มโค้ด
    with open('LightGBM_10_4.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # ตรวจสอบการ import
    imports_to_check = [
        'from model_protection_system import ModelProtectionSystem',
        'from training_prevention_system import TrainingPreventionSystem',
        'from step1_overfitting_prevention import',
        'from step2_data_leakage_prevention import'
    ]
    
    import_results = []
    for import_line in imports_to_check:
        if import_line in content:
            print(f"✅ พบ import: {import_line}")
            import_results.append(True)
        else:
            print(f"⚠️ ไม่พบ import: {import_line}")
            import_results.append(False)
    
    # ตรวจสอบการใช้งาน
    usage_checks = [
        'protection_system = ModelProtectionSystem',
        'prevention_system = TrainingPreventionSystem',
        'can_train, reason = prevention_system.can_train_now',
        'should_save_by_protection, protection_reason = protection_system.should_save_model',
        'create_safe_features',
        'validate_no_data_leakage'
    ]
    
    usage_results = []
    for usage in usage_checks:
        if usage in content:
            print(f"✅ พบการใช้งาน: {usage}")
            usage_results.append(True)
        else:
            print(f"⚠️ ไม่พบการใช้งาน: {usage}")
            usage_results.append(False)
    
    import_score = sum(import_results) / len(import_results) * 100
    usage_score = sum(usage_results) / len(usage_results) * 100
    
    print(f"\n📊 คะแนนการรวม:")
    print(f"   Import: {import_score:.1f}%")
    print(f"   Usage: {usage_score:.1f}%")
    print(f"   รวม: {(import_score + usage_score) / 2:.1f}%")
    
    return (import_score + usage_score) / 2 > 70

def test_model_backup_system():
    """ทดสอบระบบ backup โมเดล"""
    
    print("\n💾 ทดสอบระบบ backup โมเดล")
    print("="*50)
    
    backup_dirs = [
        'model_backups',
        'model_recovery_backup'
    ]
    
    backup_results = []
    for backup_dir in backup_dirs:
        if os.path.exists(backup_dir):
            files_count = len([f for f in os.listdir(backup_dir) if os.path.isfile(os.path.join(backup_dir, f))])
            dirs_count = len([d for d in os.listdir(backup_dir) if os.path.isdir(os.path.join(backup_dir, d))])
            
            print(f"✅ {backup_dir}: {files_count} ไฟล์, {dirs_count} โฟลเดอร์")
            backup_results.append(True)
        else:
            print(f"⚠️ {backup_dir}: ไม่พบโฟลเดอร์")
            backup_results.append(False)
    
    return all(backup_results)

def create_test_summary():
    """สร้างสรุปผลการทดสอบ"""
    
    print("\n📋 สร้างสรุปผลการทดสอบ")
    print("="*50)
    
    # รันการทดสอบทั้งหมด
    available_systems = test_protection_systems()
    integration_success = test_lightgbm_integration()
    backup_success = test_model_backup_system()
    
    # สร้างรายงาน
    report = f"""
# 🧪 รายงานการทดสอบระบบป้องกันการเทรน
# ================================================

## 📊 สรุปผลการทดสอบ

### ✅ ระบบป้องกันที่พร้อมใช้งาน ({len(available_systems)}/7):
{chr(10).join([f"- {system}" for system in available_systems])}

### 🔗 การรวมเข้ากับ LightGBM_10_4.py:
- สถานะ: {"✅ สำเร็จ" if integration_success else "❌ ล้มเหลว"}

### 💾 ระบบ Backup:
- สถานะ: {"✅ พร้อมใช้งาน" if backup_success else "❌ ไม่พร้อม"}

## 🎯 คะแนนรวม:
- ระบบป้องกัน: {len(available_systems)/7*100:.1f}%
- การรวมระบบ: {"100%" if integration_success else "0%"}
- ระบบ Backup: {"100%" if backup_success else "0%"}

## 📋 สถานะการพร้อมใช้งาน:
{"🟢 พร้อมใช้งาน" if len(available_systems) >= 5 and integration_success else "🟡 ต้องแก้ไข"}

## 🚀 ขั้นตอนต่อไป:
1. ทดสอบการเทรนด้วยระบบป้องกัน
2. ตรวจสอบ log การทำงาน
3. ทดสอบการกู้คืนโมเดล
4. ตรวจสอบการป้องกัน Data Leakage

## ⚠️ คำเตือน:
- อย่าเทรนซ้ำจนกว่าจะแน่ใจว่าระบบทำงานถูกต้อง
- ใช้โมเดลจากครั้งที่ 2 ที่ให้ผล +$5,940
- ตรวจสอบ log ทุกครั้งหลังการเทรน
"""
    
    with open('integration_test_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ สร้างรายงาน: integration_test_report.txt")
    
    # แสดงสรุปสั้นๆ
    overall_score = (len(available_systems)/7 + int(integration_success) + int(backup_success)) / 3 * 100
    
    print(f"\n🎯 คะแนนรวม: {overall_score:.1f}%")
    
    if overall_score >= 80:
        print("🟢 ระบบพร้อมใช้งาน!")
        print("💡 สามารถเริ่มทดสอบการเทรนได้")
    elif overall_score >= 60:
        print("🟡 ระบบใช้งานได้แต่ต้องระวัง")
        print("💡 แนะนำให้แก้ไขปัญหาก่อนใช้งานจริง")
    else:
        print("🔴 ระบบยังไม่พร้อม")
        print("💡 ต้องแก้ไขปัญหาก่อนใช้งาน")
    
    return overall_score

if __name__ == "__main__":
    print("🚀 เริ่มทดสอบระบบป้องกันการเทรน")
    print("="*60)
    
    score = create_test_summary()
    
    print("\n" + "="*60)
    print("🎉 การทดสอบเสร็จสิ้น!")
    print(f"📊 คะแนนรวม: {score:.1f}%")
