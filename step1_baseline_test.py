"""
🧪 Step 1: Baseline Parameter Testing
====================================

ทดสอบพารามิเตอร์เดิมเพื่อเป็น baseline สำหรับการเปรียบเทียบ
"""

from Parameter_Testing_Integration import ParameterTester
import json
from datetime import datetime

def main():
    print("🚀 Step 1: ทดสอบพารามิเตอร์เดิม (Baseline)")
    print("="*60)
    
    # สร้างระบบทดสอบ
    print("🔧 เริ่มต้นระบบทดสอบ...")
    tester = ParameterTester()
    
    # กำหนดพารามิเตอร์เดิม
    current_params = {
        'input_initial_nbar_sl': 4,      # จำนวน bars สำหรับ SL
        'input_stop_loss_atr': 1.00,     # ตัวคูณ ATR สำหรับ SL
        'input_take_profit': 2.0,        # อัตราส่วน TP ต่อ SL
        'input_rsi_level_in': 35,        # RSI สำหรับ entry
        'input_volume_spike': 1.25       # ตัวคูณ volume
    }
    
    print("\n📋 พารามิเตอร์เดิม:")
    for key, value in current_params.items():
        print(f"   {key}: {value}")
    
    # ทดสอบค่าเดิม
    print("\n🧪 กำลังทดสอบพารามิเตอร์เดิม...")
    print("   (อาจใช้เวลาสักครู่...)")
    
    try:
        result = tester.run_single_test(
            symbol="GOLD",
            timeframe="H1",
            parameters=current_params
        )
        
        # คำนวณคะแนน
        score = tester.optimizer.calculate_performance_score(result)
        
        print(f"\n📊 ผลลัพธ์ Baseline:")
        print("="*40)
        print(f"   🏆 คะแนนรวม: {score:.2f}")
        print(f"   📈 Win Rate: {result.get('win_rate', 0):.2f}%")
        print(f"   💰 Total Profit: ${result.get('total_profit', 0):.2f}")
        print(f"   📊 Expectancy: {result.get('expectancy', 0):.2f}")
        print(f"   🔴 SL Hit Rate: {result.get('sl_hit_rate', 0):.2f}%")
        print(f"   🟢 TP Hit Rate: {result.get('tp_hit_rate', 0):.2f}%")
        print(f"   📉 Max Drawdown: ${result.get('max_drawdown', 0):.2f}")
        print(f"   🎯 Profit Factor: {result.get('profit_factor', 0):.2f}")
        
        # วิเคราะห์ปัญหา
        print(f"\n🔍 การวิเคราะห์ปัญหา:")
        problems = []
        
        if result.get('sl_hit_rate', 0) > 70:
            problems.append("🔴 SL Hit Rate สูงเกินไป - SL แคบเกินไป")
        
        if result.get('tp_hit_rate', 0) < 25:
            problems.append("🔴 TP Hit Rate ต่ำเกินไป - TP ไกลเกินไป")
        
        if result.get('expectancy', 0) < 0:
            problems.append("🔴 Expectancy เป็นลบ - ขาดทุนเฉลี่ยมากกว่ากำไร")
        
        if result.get('win_rate', 0) < 50:
            problems.append("🟡 Win Rate ต่ำ - ควรปรับ entry conditions")
            
        if result.get('profit_factor', 0) < 1.0:
            problems.append("🔴 Profit Factor < 1.0 - ขาดทุนมากกว่ากำไร")
        
        if problems:
            print("   ปัญหาที่พบ:")
            for problem in problems:
                print(f"     {problem}")
        else:
            print("   ✅ ไม่พบปัญหาสำคัญ")
        
        # บันทึกผลลัพธ์
        baseline_result = {
            'parameters': current_params,
            'results': result,
            'performance_score': score,
            'problems': problems,
            'test_date': datetime.now().isoformat()
        }
        
        with open('baseline_result.json', 'w') as f:
            json.dump(baseline_result, f, indent=2)
        
        print(f"\n💾 บันทึกผลลัพธ์: baseline_result.json")
        
        # แนะนำขั้นตอนต่อไป
        print(f"\n🎯 ขั้นตอนต่อไป:")
        if result.get('sl_hit_rate', 0) > 70 or result.get('tp_hit_rate', 0) < 25:
            print("   👉 รัน step2_phase1_risk_management.py")
            print("      เพื่อปรับ Stop Loss และ Take Profit")
        elif result.get('win_rate', 0) < 50:
            print("   👉 รัน step3_phase2_entry_quality.py") 
            print("      เพื่อปรับ Entry Conditions")
        else:
            print("   👉 รัน step4_phase3_fine_tuning.py")
            print("      เพื่อ Fine-tuning")
            
        return baseline_result
        
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n✅ Step 1 เสร็จสมบูรณ์!")
        print(f"   คะแนน Baseline: {result['performance_score']:.2f}")
    else:
        print(f"\n❌ Step 1 ล้มเหลว - กรุณาตรวจสอบข้อผิดพลาด")
