#!/usr/bin/env python3
"""
Quick Test for Financial Analysis System
ทดสอบระบบวิเคราะห์ทางการเงินแบบเร็ว

วิธีใช้:
1. python quick_test_financial.py
2. ดูผลลัพธ์ใน Financial_Analysis_Results/
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_financial_system():
    """ทดสอบระบบวิเคราะห์ทางการเงินแบบเร็ว"""
    
    print("🧪 ทดสอบระบบวิเคราะห์ทางการเงินแบบเร็ว")
    print("=" * 60)
    
    try:
        # Import ระบบวิเคราะห์ทางการเงิน
        from financial_analysis_system import FinancialAnalysisSystem
        from financial_integration import integrate_with_trade_cycles
        
        print("✅ โหลดโมดูลสำเร็จ")
        
    except ImportError as e:
        print(f"❌ ไม่สามารถโหลดโมดูลได้: {e}")
        print("💡 ตรวจสอบว่าไฟล์ financial_analysis_system.py และ financial_integration.py อยู่ในโฟลเดอร์เดียวกัน")
        return False
    
    # สร้างระบบวิเคราะห์ทางการเงิน
    financial_system = FinancialAnalysisSystem(base_currency='USD', leverage=500)
    print("✅ สร้างระบบวิเคราะห์ทางการเงินสำเร็จ")
    
    # ทดสอบการคำนวณ pips value
    print("\n📊 ทดสอบการคำนวณ Pips Value:")
    test_symbols = {
        'GOLD': 2650.0,
        'EURUSD': 1.0850,
        'USDJPY': 148.50
    }
    
    for symbol, price in test_symbols.items():
        try:
            result = financial_system.calculate_pips_value_and_margin(symbol, price, 1.0)
            print(f"   {symbol}: Pips Value = ${result['pips_value_per_pip']:.2f}/pip, Margin = ${result['margin_required']:.2f}")
        except Exception as e:
            print(f"   ❌ {symbol}: Error - {e}")
    
    # สร้างข้อมูลการเทรดจำลอง
    print("\n🔄 สร้างข้อมูลการเทรดจำลอง...")
    
    test_symbols_list = ['EURUSD', 'GOLD']
    test_timeframes = ['M30', 'M60']
    
    for symbol in test_symbols_list:
        for timeframe in test_timeframes:
            # สร้างข้อมูลจำลอง
            trade_data = create_sample_trade_data(symbol, timeframe)
            
            # ประมวลผลด้วยระบบวิเคราะห์ทางการเงิน
            success = integrate_with_trade_cycles(
                symbol=symbol,
                timeframe=timeframe,
                trade_cycles_df=trade_data,
                financial_system=financial_system
            )
            
            if success:
                print(f"   ✅ {symbol} {timeframe}: สำเร็จ")
            else:
                print(f"   ❌ {symbol} {timeframe}: ไม่สำเร็จ")
    
    # รันการวิเคราะห์ทั้งหมด
    print("\n🚀 รันการวิเคราะห์ทั้งหมด...")
    
    results = financial_system.run_complete_analysis(account_balance=1000)
    
    if results:
        print("\n🎉 การทดสอบสำเร็จ!")
        
        # แสดงผลสรุป
        combined = results['combined_analysis']
        risk = results['risk_analysis']
        
        print(f"\n📈 ผลการทดสอบ:")
        print(f"   💰 ยอดเงินในบัญชี: ${risk['account_balance']:,.2f}")
        print(f"   📊 จำนวนการเทรด: {combined['total_trades']}")
        print(f"   💵 กำไรรวม (1.0 lot): ${combined['total_profit_usd']:,.2f}")
        print(f"   📉 Drawdown สูงสุด (1.0 lot): ${combined['max_drawdown_usd']:,.2f}")
        print(f"   🎯 ขนาดล็อตที่แนะนำ: {risk['recommended_lot_size']:.4f}")
        print(f"   ⚠️ ความเสี่ยงสูงสุด: {risk['max_risk_percentage']:.2f}%")
        
        print(f"\n📁 ไฟล์ผลลัพธ์:")
        results_folder = financial_system.results_folder
        if os.path.exists(results_folder):
            files = os.listdir(results_folder)
            for file in sorted(files):
                print(f"   📄 {file}")
        
        return True
        
    else:
        print("❌ การทดสอบไม่สำเร็จ")
        return False

def create_sample_trade_data(symbol: str, timeframe: str) -> pd.DataFrame:
    """สร้างข้อมูลการเทรดจำลอง"""
    
    np.random.seed(42)
    num_trades = 20  # จำนวนการเทรดจำลอง
    
    trades = []
    base_time = datetime.now() - timedelta(days=30)
    
    for i in range(num_trades):
        # สร้างเวลาการเทรด
        entry_time = base_time + timedelta(hours=i*2)
        exit_time = entry_time + timedelta(hours=1)
        
        # สร้างราคาและกำไร/ขาดทุน
        direction = np.random.choice(['BUY', 'SELL'])
        
        if symbol == 'GOLD':
            entry_price = 2650 + np.random.normal(0, 10)
            profit_pips = np.random.normal(5, 10)  # เฉลี่ย 5 pips
            exit_price = entry_price + (profit_pips * 0.01)
        else:  # EURUSD
            entry_price = 1.0850 + np.random.normal(0, 0.01)
            profit_pips = np.random.normal(3, 8)   # เฉลี่ย 3 pips
            exit_price = entry_price + (profit_pips * 0.0001)
        
        trades.append({
            'entry_time': entry_time,
            'exit_time': exit_time,
            'direction': direction,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'profit_pips': profit_pips
        })
    
    return pd.DataFrame(trades)

def check_requirements():
    """ตรวจสอบไฟล์ที่จำเป็น"""
    
    print("🔍 ตรวจสอบไฟล์ที่จำเป็น:")
    
    required_files = [
        'financial_analysis_system.py',
        'financial_integration.py'
    ]
    
    all_exist = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - ไม่พบไฟล์")
            all_exist = False
    
    if not all_exist:
        print("\n💡 วิธีแก้ไข:")
        print("   1. ตรวจสอบว่าไฟล์อยู่ในโฟลเดอร์เดียวกัน")
        print("   2. ดาวน์โหลดไฟล์ที่ขาดหายไป")
        return False
    
    return True

def show_usage_guide():
    """แสดงคู่มือการใช้งาน"""
    
    guide = """
📖 คู่มือการใช้งานระบบวิเคราะห์ทางการเงิน

🎯 วัตถุประสงค์:
   - คำนวณ DDmax, Profit, Risk Management
   - หาทุนที่ต้องใช้และขนาดล็อตที่เหมาะสม
   - สร้างกราฟและรายงานการวิเคราะห์

🚀 วิธีใช้งานกับ LightGBM_09_MM.py:

   1. ตั้งค่าใน LightGBM_09_MM.py:
      ENABLE_FINANCIAL_ANALYSIS = True
      ACCOUNT_BALANCE = 1000

   2. รันคำสั่ง:
      python LightGBM_09_MM.py

   3. ดูผลลัพธ์ใน:
      Financial_Analysis_Results/

📁 ไฟล์ที่จะได้:
   - complete_financial_analysis.json
   - risk_management_table.csv
   - financial_analysis_report.txt
   - trading_performance_analysis.png
   - [Symbol]_[Timeframe]_financial_analysis.json

💡 เคล็ดลับ:
   - อัปเดตราคาใน financial_integration.py
   - ปรับ TEST_GROUPS ตามข้อมูลที่มี
   - ตรวจสอบ Leverage ใน financial_analysis_system.py
"""
    
    print(guide)

if __name__ == "__main__":
    print("🧪 Quick Test - Financial Analysis System")
    print("=" * 80)
    
    # ตรวจสอบไฟล์ที่จำเป็น
    if not check_requirements():
        sys.exit(1)
    
    print()
    
    # รันการทดสอบ
    success = test_financial_system()
    
    print("\n" + "=" * 80)
    
    if success:
        print("🎉 การทดสอบเสร็จสมบูรณ์!")
        print("📁 ตรวจสอบผลลัพธ์ในโฟลเดอร์ Financial_Analysis_Results/")
    else:
        print("❌ การทดสอบไม่สำเร็จ")
    
    print("\n" + "=" * 80)
    
    # แสดงคู่มือการใช้งาน
    show_usage_guide()
    
    print("🏁 เสร็จสิ้น")
