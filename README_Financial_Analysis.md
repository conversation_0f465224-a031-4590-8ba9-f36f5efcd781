# 📊 Financial Analysis System for Trading

ระบบวิเคราะห์ทางการเงินสำหรับการเทรด - คำนวณ DDmax, Profit, Risk Management และทุนที่ต้องใช้

## 🎯 ความสามารถหลัก

### 1. **การคำนวณทางการเงิน**
- ✅ คำนวณ **Pips Value** สำหรับแต่ละสัญลักษณ์
- ✅ คำนวณ **Margin Required** ตาม Leverage
- ✅ รองรับ 3 ประเภทสัญลักษณ์:
  - 🥇 **GOLD** (Contract Size: 100 oz)
  - 💱 **Major USD Quote** (EURUSD, GBPUSD, AUDUSD, NZDUSD)
  - 💱 **Major USD Base** (USDJPY, USDCAD)

### 2. **การวิเคราะห์ความเสี่ยง**
- 📈 คำนวณ **Maximum Drawdown (DDmax)**
- 💰 คำนวณ **Total Profit** ที่ขนาดล็อต 1.0
- 🎯 แนะนำ **ขนาดล็อตที่เหมาะสม** ตามระดับความเสี่ยง
- 📊 สร้าง **ตารางการจัดการความเสี่ยง**

### 3. **การสร้างรายงานและกราฟ**
- 📈 กราฟ **Cumulative Profit** ตามเวลา
- 📉 กราฟ **Drawdown** ตามเวลา
- 📄 รายงาน **JSON, CSV, TXT**
- 🖼️ กราฟ **PNG** ความละเอียดสูง

## 📁 โครงสร้างไฟล์

```
Financial_Analysis_Results/
├── complete_financial_analysis.json    # ข้อมูลการวิเคราะห์ทั้งหมด
├── risk_management_table.csv          # ตารางการจัดการความเสี่ยง
├── financial_analysis_report.txt      # รายงานสรุป
├── trading_performance_analysis.png   # กราฟผลการเทรด
└── [Symbol]_[Timeframe]_financial_analysis.json  # ข้อมูลแต่ละสัญลักษณ์
```

## 🚀 วิธีการใช้งาน

### 1. **การผสานรวมกับ create_trade_cycles_with_model()**

```python
# Import modules
from financial_analysis_system import FinancialAnalysisSystem
from financial_integration import integrate_with_trade_cycles

# สร้างระบบวิเคราะห์ทางการเงิน
financial_system = FinancialAnalysisSystem(base_currency='USD', leverage=500)

# ในลูปของ create_trade_cycles_with_model()
for symbol in TEST_GROUPS:
    for timeframe in TIMEFRAMES:
        
        # เรียกใช้ create_trade_cycles_with_model() ตามปกติ
        trade_cycles_df = create_trade_cycles_with_model(symbol, timeframe)
        
        # เพิ่มการวิเคราะห์ทางการเงิน
        integrate_with_trade_cycles(symbol, timeframe, trade_cycles_df, financial_system)

# รันการวิเคราะห์ทั้งหมดเมื่อเสร็จ
results = financial_system.run_complete_analysis(account_balance=1000)
```

### 2. **การทดสอบระบบ**

```bash
python test_financial_analysis.py
```

## 📊 ตัวอย่างผลลัพธ์

### **การคำนวณ Pips Value**
```
📊 GOLD @ 2650.0
Lot Size: 1.0
  Pips Value per Point: $1.0000
  Pips Value per Pip: $1.0000
  Margin Required: $530.00

📊 EURUSD @ 1.085
Lot Size: 1.0
  Pips Value per Point: $1.0000
  Pips Value per Pip: $10.0000
  Margin Required: $217.00
```

### **ตารางการจัดการความเสี่ยง**
| Risk % | Risk Amount | Max Lot Size | Status |
|--------|-------------|--------------|---------|
| 0.1%   | $1.00       | 0.0003       | Safe    |
| 2.0%   | $20.00      | 0.0053       | Safe    |
| 5.0%   | $50.00      | 0.0133       | Moderate|
| 10.0%  | $100.00     | 0.0267       | High Risk|

### **สรุปผลการวิเคราะห์**
```
💰 Account Balance: $1,000.00
📊 Total Trades: 672
💵 Total Profit (1.0 lot): $20,676.66
📉 Max Drawdown (1.0 lot): $3,747.22
🎯 Recommended Lot Size: 0.0053
⚠️ Max Risk: 2.00%
```

## ⚙️ การตั้งค่า

### **1. อัปเดตราคาปัจจุบัน**
แก้ไขใน `financial_integration.py`:
```python
CURRENT_PRICES = {
    'GOLD': 2650.0,      # อัปเดตจากราคาจริง
    'EURUSD': 1.0850,    # อัปเดตจากราคาจริง
    'GBPUSD': 1.2650,    # อัปเดตจากราคาจริง
    # ... อื่นๆ
}
```

### **2. การตั้งค่า Leverage**
```python
financial_system = FinancialAnalysisSystem(
    base_currency='USD', 
    leverage=500  # ปรับตามโบรกเกอร์
)
```

### **3. ระดับความเสี่ยงที่แนะนำ**
- 🟢 **Safe**: ≤ 2% ต่อวัน
- 🟡 **Moderate**: 2-5% ต่อสัปดาห์  
- 🔴 **High Risk**: > 10% ของทั้งหมด

## 🧮 สูตรการคำนวณ

### **1. Pips Value**
```
# GOLD
Pips Value = (Point / Current Price) × Current Price × Contract Size × Lot Size

# Major USD Quote (EURUSD, GBPUSD)
Pips Value = (Point / Current Price) × Current Price × Contract Size × Lot Size

# Major USD Base (USDJPY, USDCAD)
Pips Value = (Point / Current Price) × Contract Size × Lot Size
```

### **2. Margin Required**
```
# GOLD, Major USD Quote
Margin = (Lot Size × Contract Size × Current Price) / Leverage

# Major USD Base
Margin = (Lot Size × Contract Size) / Leverage
```

### **3. Risk Management**
```
Recommended Lot Size = (Account Balance × Risk %) / Max Drawdown (1.0 lot)
```

## 📋 Checklist การใช้งาน

- [ ] ✅ Import modules ที่จำเป็น
- [ ] ✅ สร้าง FinancialAnalysisSystem
- [ ] ✅ อัปเดตราคาปัจจุบันใน CURRENT_PRICES
- [ ] ✅ เพิ่ม integrate_with_trade_cycles() ในลูป
- [ ] ✅ เรียก run_complete_analysis() เมื่อเสร็จ
- [ ] ✅ ตรวจสอบผลลัพธ์ใน Financial_Analysis_Results/
- [ ] ✅ ปรับขนาดล็อตตามคำแนะนำ

## 🎯 ประโยชน์ที่ได้รับ

1. **📊 การวิเคราะห์ที่ครอบคลุม**: DDmax, Profit, Risk ทั้งหมดในที่เดียว
2. **💰 การจัดการทุน**: คำนวณทุนที่ต้องใช้และขนาดล็อตที่เหมาะสม
3. **⚠️ การควบคุมความเสี่ยง**: ตารางความเสี่ยงแบบละเอียด
4. **📈 การติดตามผล**: กราฟและรายงานที่ชัดเจน
5. **🔄 การผสานรวม**: ใช้งานร่วมกับระบบเดิมได้ง่าย

## 🚨 ข้อควรระวัง

- ⚠️ อัปเดตราคาปัจจุบันให้ถูกต้อง
- ⚠️ ตรวจสอบ Leverage ของโบรกเกอร์
- ⚠️ ทดสอบด้วยข้อมูลจริงก่อนใช้งาน
- ⚠️ ปรับระดับความเสี่ยงตามความเหมาะสม

---

**🎉 พร้อมใช้งาน!** ทดสอบด้วย `python test_financial_analysis.py`
