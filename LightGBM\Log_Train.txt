
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters
🛡️ เริ่มใช้งาน Model Protection System (min profit: $5,000)
🚫 เริ่มใช้งาน Training Prevention System
🔧 โหลดการตั้งค่าป้องกัน Overfitting
🛡️ ระบบป้องกันโมเดล: FLEXIBLE
   - Accuracy ≥ 45.0%
   - Win Rate ≥ 35.0%
   - Expectancy ≥ 10.0

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-26 18:59:23
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
✅ เปิดใช้งานระบบวิเคราะห์ทางการเงิน (Account Balance: $1,000.00)
📁 จำนวนไฟล์ทั้งหมด: 1
   - M60: 1 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 18:59:23
📊 ประมวลผลกลุ่ม M60 (1 ไฟล์)

🏗️ เปิดใช้งาน main

🔍 ตรวจสอบการป้องกันการเทรนสำหรับ GOLD M60
⚠️ เทรนเกินจำนวนที่กำหนดแล้ววันนี้ (66/3)
🚫 ไม่สามารถเทรนได้: daily_limit_exceeded
💡 ข้ามการเทรนและใช้โมเดลเดิม

🔍 กำลังค้นหาพารามิเตอร์ที่เหมาะสมสำหรับ GOLD M30...
📁 ใช้ไฟล์: multi_asset_results_20250924_135729.json (Modified: 2025-09-24 13:57)
======================================================================
🎯 PARAMETER OPTIMIZATION RESULTS
======================================================================
📊 Source: GOLD_M30 specific

🔸 GOLD_M30
   Score: 65.87
   Win Rate: 53.3%
   Total Profit: $121,493
   Total Trades: 15
   Expectancy: 8099.52
   Max Drawdown: $86,950
   
   Best Parameters:
     SL ATR: 1.0
     TP Ratio: 2.0
     RSI Level: 25
     Volume Spike: 1.0
======================================================================

🔄 Updated Global Parameters:
   input_stop_loss_atr: 1.0 (unchanged)
   input_take_profit: 2.0 (unchanged)
   input_rsi_level_in: 35 → 25
   input_volume_spike: 1.25 → 1.0
   input_rsi_level_over: 70 (unchanged)
   input_rsi_level_out: 30 → 35
   input_pull_back: 0.5 → 0.45
   input_initial_nbar_sl: 4 (unchanged)

============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/GOLD_H1_FIXED.csv main_round 1 symbol GOLD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ GOLD MM60, ใช้ค่า default: 0.3

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 10

📂 โหลดข้อมูลจาก LightGBM/Data_Trained/M60_GOLD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 10 confidence 0.3

🏗️ เปิดใช้งาน load and process data

🔍 กำลังตรวจสอบและจัดการ Missing Values...
✅ ไม่พบ Missing Values ในข้อมูล

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M60_GOLD_features.pkl
⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: LightGBM/Multi/models/trend_following\M60_GOLD_features.pkl
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ GOLD MM60

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ GOLD MM60
❌ trend_following: ไม่พบไฟล์โมเดล
❌ trend_following_Buy: ไม่พบไฟล์โมเดล
❌ trend_following_Sell: ไม่พบไฟล์โมเดล
✅ counter_trend: พร้อมใช้งาน
❌ counter_trend_Buy: ไม่พบไฟล์โมเดล
❌ counter_trend_Sell: ไม่พบไฟล์โมเดล

📊 สรุป: 1/6 โมเดลพร้อมใช้งาน
⚠️ โมเดลที่ขาดหายไป: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend_Buy', 'counter_trend_Sell']
🔍 กำลังโหลดโมเดลสำหรับ GOLD MM60
📁 Base folder: LightGBM/Multi/models
🎯 Strategy: use_available
📋 จะโหลด 1 scenarios: ['counter_trend']

🔍 โหลด counter_trend:
  📄 Model: LightGBM/Multi/models\counter_trend\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend\M60_GOLD_scaler.pkl
✅ โหลดโมเดล counter_trend สำเร็จ
  📊 Features: 50 features

📊 สรุปการโหลดโมเดล: 1/6 โมเดล
⚠️ โหลดโมเดลได้ไม่ครบ - อาจส่งผลต่อประสิทธิภาพการทำนาย
📋 โมเดลที่โหลดได้: ['counter_trend']
📋 โมเดลที่ขาดหายไป: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend_Buy', 'counter_trend_Sell']
✅ โหลดโมเดล Multi-Model สำเร็จ: ['counter_trend']
⚠️ ไม่มีโมเดลสำหรับ trend_following - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่มีโมเดลสำหรับ trend_following_Buy - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่มีโมเดลสำหรับ trend_following_Sell - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend_Buy - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend_Sell - ใช้ค่าเริ่มต้น: 0.3000
🎯 Scenario Thresholds: {'trend_following': 0.3, 'counter_trend': 0.3, 'trend_following_Buy': 0.3, 'trend_following_Sell': 0.3, 'counter_trend_Buy': 0.3, 'counter_trend_Sell': 0.3}

🏗️ เปิดใช้งาน try trade with threshold adjustment

📂 พบ threshold ที่บันทึกไว้สำหรับ GOLD_M60
   ค่า threshold: 0.24
   จำนวน trades: 217
   ⏱️ ข้อมูลบันทึกเมื่อ 1 วัน 5 ชั่วโมงที่แล้ว (ยังใช้ได้)

🔍 ทดสอบ threshold ที่บันทึกไว้: 0.2400

🏗️ เปิดใช้งาน create trade cycles with multi model

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 35864 (49.9%)
  downtrend: 30186 (42.0%)
  sideways: 5778 (8.0%)
📊 โหลด threshold สำหรับ counter_trend: 0.3000
🎯 Scenario Thresholds: {'counter_trend': 0.3}
📊 ใช้ Multi-Model: ['counter_trend']
✅ ใช้ Multi-Model Architecture พร้อม 2 scenarios

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 0.8000

🔄 ใช้ Multi-Model Architecture: ['counter_trend']
📋 Scenario Model Mapping: {'trend_following': None, 'counter_trend': 'counter_trend', 'trend_following_Buy': None, 'trend_following_Sell': None, 'counter_trend_Buy': 'counter_trend', 'counter_trend_Sell': 'counter_trend'}
⚠️ ขาดโมเดลหลักสำหรับ scenario: ['trend_following']
❌ ไม่พบโมเดลใดสำหรับ trend_following - จะใช้ Technical Only
🔍 ตรวจสอบโมเดลที่โหลดมาแล้ว: ['counter_trend']
📊 Scenario trend_following ใช้: ❌ Technical Only (ไม่มีโมเดล None)
📊 Scenario counter_trend ใช้: ✅ โมเดลตัวเอง
📊 Scenario trend_following_Buy ใช้: ❌ Technical Only (ไม่มีโมเดล None)
📊 Scenario trend_following_Sell ใช้: ❌ Technical Only (ไม่มีโมเดล None)
📊 Scenario counter_trend_Buy ใช้: ✅ โมเดล counter_trend
📊 Scenario counter_trend_Sell ใช้: ✅ โมเดล counter_trend
model_features: None

🤖 สถานะการใช้งานโมเดลตาม Scenario:
   trend_following: ❌ ใช้ Technical Only
   counter_trend: ✅ ใช้โมเดลตัวเอง
   trend_following_Buy: ❌ ใช้ Technical Only
   trend_following_Sell: ❌ ใช้ Technical Only
   counter_trend_Buy: ✅ ใช้โมเดล counter_trend
   counter_trend_Sell: ✅ ใช้โมเดล counter_trend
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 71828 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 71778
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
💰 เริ่มการวิเคราะห์ทางการเงินสำหรับ GOLD M60
🔄 กำลังประมวลผล GOLD M60...
💾 บันทึกการวิเคราะห์ GOLD M60 ที่: Financial_Analysis_Results/GOLD_M60_financial_analysis.json
✅ ประมวลผล GOLD M60 สำเร็จ: 7242 รายการ
✅ วิเคราะห์ทางการเงิน GOLD M60 สำเร็จ
✅ ยืนยัน threshold ที่บันทึกไว้ใช้ได้ดี: พบ 7242 trades
🎉 สำเร็จ! ใช้ threshold สุดท้าย: 0.2400 (Multi-Model)

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 71828 ตัวอย่างข้อมูล df
         Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2013.07.18  12:00:00  1281.04  1282.29  1278.50  1280.54    3360  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
1  2013.07.18  13:00:00  1280.53  1282.77  1280.18  1280.85    2748  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
2  2013.07.18  14:00:00  1281.10  1282.35  1280.29  1281.04    2718  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
3  2013.07.18  15:00:00  1281.14  1287.04  1277.57  1285.03    4864  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
4  2013.07.18  16:00:00  1285.12  1288.40  1283.80  1286.97    4524  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
จำนวนการซื้อขายที่พบ: 7242 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  ATR at Entry  BB Width at Entry  RSI14 at Entry  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2013-07-23 05:00:00      1335.25 2013-07-23 06:00:00     1335.25        Buy     0.0          1  ...      3.694286          33.189229       69.858470  0.000000    0.005542               2946.95                      1
1 2013-07-23 10:00:00      1331.38 2013-07-23 10:00:00     1331.38        Buy     0.0          1  ...      2.937857          21.773529       58.292709  0.000000    0.003868               2877.15                      1
2 2013-07-23 15:00:00      1329.05 2013-07-23 16:00:00     1329.05        Buy     0.0          1  ...      3.494286          10.598560       52.449350  0.000000    0.003837               2542.40                      1
3 2013-07-24 09:00:00      1341.46 2013-07-24 11:00:00     1341.46        Buy     0.0          2  ...      3.806429          24.058898       58.508645  0.000000    0.004689               2928.40                      0
4 2013-07-24 17:00:00      1338.31 2013-07-24 17:00:00     1334.32        Buy  -399.0          2  ...      3.985000          11.108790       49.217878  0.002981    0.005963               2771.40                      1

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                29.91               
Expectancy          -39.99              
📈 สถิติสำหรับ Sell Trades:
Win%                32.34               
Expectancy          -12.65              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                31.05               
Expectancy          -27.15              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    14.78          1441                
Tuesday   15.91          1527                
Wednesday 15.57          1496                
Thursday  16.90          1349                
Friday    13.65          1429                
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         19.29          648                 
5         16.15          576                 
6         18.51          497                 
7         18.55          399                 
8         14.81          351                 
9         10.99          373                 
10        17.74          389                 
11        14.45          422                 
12        13.32          398                 
13        14.86          424                 
14        14.99          387                 
15        15.68          421                 
16        13.44          305                 
17        13.31          338                 
18        12.67          300                 
19        14.33          328                 
20        12.78          399                 
21        14.98          287                 
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2013-07-23 05:00:00
1   2013-07-23 10:00:00
2   2013-07-23 15:00:00
3   2013-07-24 09:00:00
4   2013-07-24 17:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2013-07-23 04:55:00
1   2013-07-23 09:55:00
2   2013-07-23 14:55:00
3   2013-07-24 08:55:00
4   2013-07-24 16:55:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 0   2013-07-18 12:00:00
1   2013-07-18 13:00:00
2   2013-07-18 14:00:00
3   2013-07-18 15:00:00
4   2013-07-18 16:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 313 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 7242
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2013-07-23 05:00:00      1335.25 2013-07-23 06:00:00     1335.25        Buy     0.0          1           5
1 2013-07-23 10:00:00      1331.38 2013-07-23 10:00:00     1331.38        Buy     0.0          1          10
2 2013-07-23 15:00:00      1329.05 2013-07-23 16:00:00     1329.05        Buy     0.0          1          15
3 2013-07-24 09:00:00      1341.46 2013-07-24 11:00:00     1341.46        Buy     0.0          2           9
4 2013-07-24 17:00:00      1338.31 2013-07-24 17:00:00     1334.32        Buy  -399.0          2          17

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...
🔍 ตรวจสอบ columns ข้อมูล df หลัง merge : จำนวน 332
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 7242/7242 valid values

🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 534 samples (7.4%)
  Class 1 (weak_sell): 1777 samples (24.5%)
  Class 2 (no_trade): 2467 samples (34.1%)
  Class 3 (weak_buy): 1902 samples (26.3%)
  Class 4 (strong_buy): 562 samples (7.8%)
✅ Multi-class Target ถูกต้อง: 5 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0     534
1    1777
2    2467
3    1902
4     562
Name: count, dtype: int64
Class 0 (strong_sell): 534 trades, Profit range: 74.0 to 4536.0
Class 1 (weak_sell): 1777 trades, Profit range: -17.0 to 58.0
Class 2 (no_trade): 2467 trades, Profit range: -2862.0 to -30.0
Class 3 (weak_buy): 1902 trades, Profit range: -4.0 to 40.0
Class 4 (strong_buy): 562 trades, Profit range: 103.0 to 5065.0
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    6157
1    1085
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    3240
1     554
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    2917
1     531
Name: count, dtype: int64
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 7242
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time        EMA50       EMA100       EMA200      RSI14
0 2013-07-23 05:00:00  1314.329568  1302.348973  1287.682560  69.858470
1 2013-07-23 10:00:00  1317.757103  1305.294244  1289.908778  58.292709
2 2013-07-23 15:00:00  1319.872307  1307.594651  1291.839113  52.449350
3 2013-07-24 09:00:00  1330.338942  1317.382448  1299.741175  58.508645
4 2013-07-24 17:00:00  1333.002343  1320.747392  1302.850104  49.217878

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    6157
1    1085
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
⚠️ Feature 'Volume_MA_20' not found in DataFrame. Skipping.
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20'] = 416 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target                     1.000000
ADX_14_x_RollingVol15      0.091043
Rolling_Close_15           0.072119
Volume_Lag_3               0.071335
Volume_MA_10               0.064955
                             ...   
H4_Volume_TrendStrength    0.000182
H4_Volume_Spike            0.000182
RSI_Divergence_i2               NaN
RSI_Divergence_i6               NaN
RSI_Divergence_i4               NaN
Name: Target, Length: 301, dtype: float64

📊 ค่า VIF ของ Features:
                   feature        VIF
0    ADX_14_x_RollingVol15  23.751490
1         Rolling_Close_15  21.885063
2             Volume_Lag_3  80.245121
3             Volume_MA_10  13.779301
4          Rolling_Close_5  25.218994
..                     ...        ...
185            RSI14_Lag_5  21.394194
186            Slope_EMA50        inf
187            Price_EMA50        inf
188          H12_MACD_deep   2.900222
189           DMP_14_Lag_5  15.117509

[190 rows x 2 columns]

🚫 Features ถูกตัดออกเนื่องจาก VIF สูง: ['ADX_14_x_RollingVol15', 'Rolling_Close_15', 'Volume_Lag_3', 'Volume_MA_10', 'Rolling_Close_5', 'ADX_14_Lag_1', 'Volume_MA_3', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'RSI_x_VolumeSpike', 'Volume_TrendStrength', 'Volume_Spike', 'High_Lag_15', 'High_Lag_20', 'Open_Lag_15', 'Close_Lag_15', 'Close_Lag_20', 'Low_Lag_15', 'Low_Lag_20', 'Open_Lag_20', 'Open_Lag_30', 'Low_Lag_30', 'High_Lag_30', 'Close_Lag_30', 'High_Lag_1', 'Close_Lag_1', 'EMA50_Lag_5', 'Close_MA_20', 'EMA50_Lag_3', 'EMA50_Lag_1', 'EMA50_Lag_2', 'High_Lag_2', 'High_Lag_50', 'EMA100_Lag_5', 'EMA100_Lag_3', 'EMA100_Lag_1', 'EMA100_Lag_2', 'Close_Lag_50', 'Close_MA_3', 'Low_Lag_10', 'Open_Lag_10', 'Open_Lag_50', 'Low_Lag_1', 'Close_Lag_3', 'Open_Lag_1', 'Close_Lag_2', 'Low_Lag_3', 'Open_Lag_2', 'High_Lag_10', 'Close_MA_5', 'Low_Lag_50', 'Close_Lag_10', 'Open_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'Close_MA_10', 'EMA200_Lag_3', 'EMA200_Lag_5', 'Low_Lag_5', 'Low_Lag_2', 'Close_Lag_5', 'Open_Lag_3', 'High_Lag_5', 'High_Lag_3', 'H2_Volume_Spike', 'H2_Volume_TrendStrength', 'BB_width_Lag_5', 'ADX_14_Lag_5', 'STOCHd_14_3_3_Lag_5', 'Price_EMA50_x_RSI_trend', 'BB_width_Lag_1', 'Close_Std_20', 'BB_width', 'BB_width_Lag_3', 'STOCHk_14_3_3_Lag_5', 'BB_width_Lag_2', 'EMA50_x_RollingVol5', 'STOCHd_14_3_3_Lag_3', 'RSI14_x_BBwidth', 'H12_Volume_Spike', 'H12_Volume_TrendStrength', 'STOCHd_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_2', 'ADX_14_x_ATR', 'ATR_ROC_i8', 'STOCHd_14_3_3_Lag_1', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'Volume_Lag_2', 'D1_Volume_TrendStrength', 'DMP_14_Lag_3', 'D1_Volume_Spike', 'RSI_ROC_i8', 'ATR_Lag_5', 'Close_Return_2', 'RSI_ROC_i6', 'EMA_diff_100_200', 'STOCHk_14_3_3_Lag_1', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_5', 'ATR_ROC_i6', 'Close_Return_5', 'Bar_CL', 'Slope_EMA200', 'EMA_diff_50_200', 'MACD_12_26_9_Lag_1', 'RSI14_Lag_5', 'Slope_EMA50', 'Price_EMA50', 'DMP_14_Lag_5']

🔍 เริ่มการคัดเลือก features ด้วย RFE และ Feature Importance
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004620 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6537
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 76
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004397 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6534
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 75
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003580 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6531
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 74
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003793 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6529
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 73
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003460 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6526
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 72
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003080 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6523
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 71
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003493 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6520
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 70
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003043 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6518
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 69
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003654 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6516
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 68
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003375 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6513
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 67
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003208 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6510
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 66
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.004055 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6507
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 65
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002830 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6504
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 64
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003160 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6501
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 63
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.001721 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6498
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 62
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003040 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6495
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 61
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003015 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6492
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 60
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002870 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6489
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 59
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002621 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6486
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 58
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002535 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6483
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 57
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002878 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6481
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 56
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002750 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6478
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 55
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002556 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6475
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 54
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.003196 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6472
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 53
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010

✅ RFE เลือก 53 features จากทั้งหมด 76 features
📋 Top 10 features จาก RFE:
1. ADX_zone_25
2. Volume_MA_5
3. ADX_Deep
4. Volume_MA20
5. Hour
6. D1_Price_Range
7. Volume_Lag_30
8. Volume_Lag_5
9. Price_Range
10. H4_Bar_TL
[LightGBM] [Info] Number of positive: 1085, number of negative: 6157
[LightGBM] [Info] Auto-choosing col-wise multi-threading, the overhead of testing was 0.002087 seconds.
You can set `force_col_wise=true` to remove the overhead.
[LightGBM] [Info] Total Bins 6537
[LightGBM] [Info] Number of data points in the train set: 7242, number of used features: 76
[LightGBM] [Info] [binary:BoostFromScore]: pavg=0.149820 -> initscore=-1.736010
[LightGBM] [Info] Start training from score -1.736010

✅ Feature Importance เลือก 25 features จากทั้งหมด 76 features
📋 Top 10 features จาก Feature Importance:
1. Volume_MA_5
2. Volume_MA20
3. D1_Price_Range
4. Volume_Lag_30
5. Volume_Lag_5
6. Price_Range
7. H12_Price_Move
8. H12_Price_Range
9. Volume_Change_2
10. Volume_Lag_10

✅ รวมทั้งสองวิธีได้ 53 features ที่สำคัญ

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

featuresasset_feature_importance : len  6
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
👍 เพิ่ม Feature ที่จำเป็น 'DayOfWeek' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsMorning' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsNight' เข้าไปในรายการ

🔍 จำนวน features ที่เลือกได้: 58
⚠️ Features มากเกินไป (58 > 50)
✂️ ตัด features ที่มี correlation ต่ำสุด
✅ เหลือ 50 features

✅ Final selected features for training: 50 features
📋 Top 10 features:
1. ADX_zone_25 (corr: 0.0506)
2. Volume_MA_5 (corr: 0.0493)
3. ADX_Deep (corr: 0.0484)
4. Volume_MA20 (corr: 0.0482)
5. Hour (corr: 0.0472)
6. D1_Price_Range (corr: 0.0462)
7. Volume_Lag_30 (corr: 0.0408)
8. Volume_Lag_5 (corr: 0.0366)
9. Price_Range (corr: 0.0348)
10. IsEvening (corr: 0.0322)
... และอีก 40 features
💾 บันทึก features (pkl): LightGBM/Multi\feature_selected\GOLD_M60_selected_features.pkl
📝 บันทึก features (txt): LightGBM/Multi\feature_selected\GOLD_M60_selected_features.txt

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 50
1. ADX_zone_25
2. Volume_MA_5
3. ADX_Deep
4. Volume_MA20
5. Hour
6. D1_Price_Range
7. Volume_Lag_30
8. Volume_Lag_5
9. Price_Range
10. IsEvening
11. H4_Bar_TL
12. H4_Volume_Momentum
13. H12_Bar_CL_OC
14. H12_Price_Move
15. H2_Bar_SW
... และอีก 35 features

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.85018
1    0.14982
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.18
⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                        count         mean           std          min          25%          50%          75%            max
ADX_zone_25            7242.0     0.513946      0.499840     0.000000     0.000000     1.000000     1.000000       1.000000
Volume_MA_5            7242.0  5582.835377   6029.052974   198.400000  2005.000000  3542.900000  6802.350000   64506.200000
ADX_Deep               7242.0    -0.289975      0.957100    -1.000000    -1.000000    -1.000000     1.000000       1.000000
Volume_MA20            7242.0  5789.971762   4243.330635   570.050000  2636.587500  4460.150000  7935.837500   29664.550000
Hour                   7242.0    10.630765      5.285296     3.000000     6.000000    10.000000    15.000000      20.000000
D1_Price_Range         7242.0    22.981161     16.313794     0.000000    12.690000    18.680000    27.880000     133.230000
Volume_Lag_30          7242.0  5210.341895  12358.337689     1.000000  1491.250000  2978.000000  5267.000000  233517.000000
Volume_Lag_5           7242.0  5757.966722  14584.289357     1.000000  1426.000000  2900.500000  5191.750000  235825.000000
Price_Range            7242.0     3.719426      2.898098     0.380000     1.950000     2.930000     4.490000      39.400000
IsEvening              7242.0     0.188484      0.391125     0.000000     0.000000     0.000000     0.000000       1.000000
H4_Bar_TL              7242.0     0.144849      0.351973     0.000000     0.000000     0.000000     0.000000       1.000000
H4_Volume_Momentum     7242.0     0.262358      0.965037    -1.000000    -1.000000     1.000000     1.000000       1.000000
H12_Bar_CL_OC          7242.0    -0.017260      0.837826    -1.000000    -1.000000     0.000000     1.000000       1.000000
H12_Price_Move         7242.0    -0.265918     11.039545   -96.760000    -4.770000    -0.100000     4.635000      77.300000
H2_Bar_SW              7242.0     0.011323      0.708528    -1.000000     0.000000     0.000000     1.000000       1.000000
H12_Price_Range        7242.0    16.201286     11.458245     0.000000     8.940000    13.170000    19.740000     121.050000
H12_Volume_Momentum    7242.0     0.038940      0.999311    -1.000000    -1.000000     1.000000     1.000000       1.000000
H8_Bar_SW              7242.0     0.009666      0.667816    -1.000000     0.000000     0.000000     0.000000       1.000000
IsNight                7242.0     0.129108      0.335343     0.000000     0.000000     0.000000     0.000000       1.000000
EMA_Cross_EMA5         7242.0     0.006076      1.000051    -1.000000    -1.000000     1.000000     1.000000       1.000000
Volume_Change_2        7242.0     0.408738      1.124674    -0.973456    -0.282127     0.072542     0.713493      26.125926
H2_Bar_CL_OC           7242.0     0.003866      0.871204    -1.000000    -1.000000     0.000000     1.000000       1.000000
Bar_CL_OC              7242.0     0.021265      0.691128    -1.000000     0.000000     0.000000     0.000000       1.000000
STO_overbought         7242.0     0.133941      0.340612     0.000000     0.000000     0.000000     0.000000       1.000000
BB_Break_HL            7242.0    -0.003866      0.444067    -1.000000     0.000000     0.000000     0.000000       1.000000
H8_Volume_Momentum     7242.0     0.007457      1.000041    -1.000000    -1.000000     1.000000     1.000000       1.000000
Volume_Lag_10          7242.0  5818.988677  10942.538181     1.000000  1597.000000  3398.500000  6437.750000  160237.000000
ATR_ROC_i2             7242.0    -0.029180      0.133365    -1.182904    -0.082254    -0.008192     0.046996       0.454461
ATR_x_PriceRange       7242.0    20.822919     42.617634     0.425850     5.338529    10.328286    20.512864    1080.648357
D1_Volume_Momentum     7242.0    -0.035349      0.999444    -1.000000    -1.000000    -1.000000     1.000000       1.000000
EMA_Cross_EMA10        7242.0    -0.012704      0.999988    -1.000000    -1.000000    -1.000000     1.000000       1.000000
STO_cross              7242.0    -0.001381      1.000068    -1.000000    -1.000000    -1.000000     1.000000       1.000000
H4_Bar_longwick        7242.0     0.023088      2.071409   -33.000000    -0.715816    -0.005713     0.714828      43.659574
H8_Price_Range         7242.0    12.612416      9.356687     0.000000     6.732500    10.070000    15.530000     108.620000
IsMorning              7242.0     0.218448      0.413222     0.000000     0.000000     0.000000     0.000000       1.000000
Bar_SW                 7242.0     0.007042      0.566115    -1.000000     0.000000     0.000000     0.000000       1.000000
MACD_line_x_PriceMove  7242.0    -0.050875      2.261175   -31.650000    -0.970000     0.210000     0.980000      21.450000
D1_MACD_line           7242.0     0.087269      0.993477    -1.000000    -1.000000     1.000000     1.000000       1.000000
H4_MACD_deep           7242.0    -0.024579      0.999767    -1.000000    -1.000000    -1.000000     1.000000       1.000000
H8_Price_Move          7242.0    -0.012473      8.458109   -64.280000    -3.400000     0.190000     3.587500      87.440000
H12_Bar_SW             7242.0     0.001657      0.642591    -1.000000     0.000000     0.000000     0.000000       1.000000
H4_Price_Move          7242.0    -0.005884      5.625226   -57.870000    -2.310000     0.070000     2.410000      56.480000
Close_Std_10           7242.0     3.075079      2.681880     0.306777     1.488164     2.314812     3.772896      33.883056
H2_Price_Strangth      7242.0    -0.015742      0.568982    -1.000000     0.000000     0.000000     0.000000       1.000000
Volume_Change_1        7242.0     0.120355      0.637195    -0.794566    -0.237880    -0.035958     0.305603      26.148148
H2_Bar_OSB             7242.0    -0.006352      0.412099    -1.000000     0.000000     0.000000     0.000000       1.000000
H4_Price_Strangth      7242.0    -0.009804      0.563629    -1.000000     0.000000     0.000000     0.000000       1.000000
DMN_14_Lag_2           7242.0    21.415720      7.296379     2.355939    16.312386    20.996599    25.957175      63.306758
EMA_diff_x_BBwidth     7242.0    57.325486    742.342404 -7135.771526   -85.782179     9.124790   132.138326   21250.585813
H2_Bar_longwick        7242.0    -0.004430      2.190876   -38.000000    -0.652218     0.072210     0.646409      52.546512

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
⚠️ พบ 1 คู่ features ที่มีความสัมพันธ์สูง (>0.8)

คู่ features ที่มีความสัมพันธ์สูง:
       Feature 1        Feature 2  Correlation
     Price_Range ATR_x_PriceRange     0.841535
ATR_x_PriceRange      Price_Range     0.841535

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...

📊 ใช้ Target Column: Target_Multiclass
📊 การกระจายของ Target ทั้งหมด:
Target_Multiclass
2    2467
3    1902
1    1777
4     562
0     534
Name: count, dtype: int64

🔄 ใช้ Stratified Time Series Split เพื่อรักษา positive samples ใน validation set
📊 Total positive samples: 1777
📊 Total negative samples: 534
📊 Positive distribution - Train: 1067, Val: 355, Test: 355

📊 การกระจายของ Target ในชุดข้อมูลหลังแบ่ง:
Train: 1389 samples, positive: 1067 (76.8%)
Val: 461 samples, positive: 355 (77.0%)
Test: 461 samples, positive: 355 (77.0%)
Train distribution: Target_Multiclass
1    0.768179
0    0.231821
Name: proportion, dtype: float64
Val distribution: Target_Multiclass
1    0.770065
0    0.229935
Name: proportion, dtype: float64
Test distribution: Target_Multiclass
1    0.770065
0    0.229935
Name: proportion, dtype: float64

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 1 : จำนวน 338

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 2 : จำนวน 338

🏗️ เปิดใช้งาน analyze time filters (Enhanced)
📊 Adaptive Thresholds - Win Rate: 0.300, Expectancy: 193.110

📊 Enhanced Time Filter Analysis for GOLD:
📅 Recommended Days: []
⏰ Recommended Hours: []
📈 Performance Improvement: {'error': 'No trades match the filter criteria'}
✅ บันทึก time filter ที่: LightGBM/Multi/thresholds/M60_GOLD_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
✅ ทำ Feature Scaling เรียบร้อยแล้ว
train_data : (      ADX_zone_25  Volume_MA_5  ADX_Deep  Volume_MA20      Hour  D1_Price_Range  Volume_Lag_30  ...  H2_Price_Strangth  Volume_Change_1  H2_Bar_OSB  H4_Price_Strangth  DMN_14_Lag_2  EMA_diff_x_BBwidth  H2_Bar_longwick
27      -1.251985    -0.421347 -0.796315     0.298097 -0.249636        1.269701      -0.166568  ...           0.198166         0.467263   -0.196330           0.066670     -0.640223            0.012523        -0.811393
28      -1.251985    -0.162651  1.255784     0.216438  1.259588        1.269701      -0.092190  ...           0.198166         0.039678   -0.196330           0.066670      0.598345            0.171803         0.125439
30      -1.251985    -0.620137 -0.796315    -0.537205 -1.192901        0.374888      -0.228044  ...           0.198166        -0.247620    2.260443           0.066670      0.484324            0.055817         0.039783
31       0.798731    -0.317323  1.255784    -0.603779  0.504976        0.374888      -0.242148  ...           2.045499        -0.181690   -0.196330           1.813924      0.921016           -0.207836        -0.890098
32       0.798731    -0.476390 -0.796315    -0.850663  1.636894        0.718818      -0.210082  ...           0.198166        -0.765275   -0.196330           0.066670     -0.321336           -0.321134        -0.325911
...           ...          ...       ...          ...       ...             ...            ...  ...                ...              ...         ...                ...           ...                 ...              ...
4433     0.798731    -0.479115 -0.796315    -0.337009 -1.004248        0.901205      -0.065058  ...           0.198166        -0.235660   -0.196330          -1.680584      0.327748           -0.222476        -0.418751
4436     0.798731    -0.344743 -0.796315    -0.474831  0.316323        0.901205      -0.161003  ...          -1.649167        -0.309964   -0.196330           0.066670      0.316790           -0.064322        -0.073228
4438     0.798731    -0.512071 -0.796315    -0.479894 -1.192901        0.742640      -0.089218  ...           0.198166        -0.781067    2.260443           0.066670      0.053378           -1.416098        -0.022322
4440     0.798731    -0.457027 -0.796315    -0.527856 -0.438289        0.742640      -0.252963  ...           0.198166         0.259402   -0.196330           1.813924     -0.558250           -0.805421        -0.646260
4441    -1.251985    -0.470766 -0.796315    -0.527846 -0.249636        0.742640      -0.199456  ...           0.198166        -0.235162   -0.196330           1.813924     -0.697119           -0.693490        -0.646260

[1389 rows x 50 columns], 27      0
28      1
30      0
31      1
32      1
       ..
4433    1
4436    1
4438    1
4440    1
4441    1
Name: Target_Multiclass, Length: 1389, dtype: int32)
val_data : (      ADX_zone_25  Volume_MA_5  ADX_Deep  Volume_MA20      Hour  D1_Price_Range  Volume_Lag_30  ...  H2_Price_Strangth  Volume_Change_1  H2_Bar_OSB  H4_Price_Strangth  DMN_14_Lag_2  EMA_diff_x_BBwidth  H2_Bar_longwick
3843    -1.251985    -0.230027 -0.796315    -0.724631  1.636894       -0.519924      -0.099970  ...           0.198166        -0.744386   -0.196330           0.066670     -0.962306            0.386154        -0.088544
3853     0.798731    -0.215116 -0.796315    -0.671447  1.636894        0.890038      -0.216786  ...           2.045499        -0.445778   -0.196330           0.066670      0.141906            0.297489         1.024957
3855     0.798731    -0.544118 -0.796315    -0.497085 -0.060983        1.000959      -0.213560  ...           0.198166        -0.440454   -0.196330           0.066670      0.572855           -0.673312        -0.144972
3857    -1.251985    -0.461421 -0.796315    -0.723273  0.316323       -0.100807      -0.272569  ...           0.198166        -0.581601   -0.196330           0.066670     -0.004956           -0.064102         0.453496
3858     0.798731    -0.663609  1.255784    -0.720957 -1.192901        0.006392      -0.206287  ...           0.198166        -0.750320   -0.196330           0.066670      1.291508           -0.754084        -0.530003
...           ...          ...       ...          ...       ...             ...            ...  ...                ...              ...         ...                ...           ...                 ...              ...
5618     0.798731    -0.307246 -0.796315    -0.121727 -0.438289        0.638419      -0.292113  ...           0.198166        -0.462797   -0.196330           0.066670      0.179169           -0.604575        -0.132366
5619     0.798731     0.013729 -0.796315    -0.302648  0.693629        0.638419      -0.189210  ...           0.198166        -0.173510   -0.196330           1.813924     -0.841118           -0.467532        -0.233481
5621    -1.251985    -0.392932 -0.796315    -0.012539 -1.381554        0.381588       0.049671  ...           0.198166         1.507815   -0.196330           0.066670      0.588734           -0.841351        -0.362017
5622    -1.251985    -0.072395 -0.796315    -0.295584  0.504976        0.381588      -0.199709  ...           0.198166        -0.503826   -2.653102           0.066670     -0.181968           -1.314728        -0.735636
5623    -1.251985     0.597326  1.255784    -0.030993  1.259588        0.381588       0.080978  ...           0.198166        -0.292469    2.260443           0.066670     -1.006743           -1.010558         0.058000

[461 rows x 50 columns], 3843    0
3853    0
3855    0
3857    0
3858    0
       ..
5618    1
5619    1
5621    1
5622    1
5623    1
Name: Target_Multiclass, Length: 461, dtype: int32)
test_data : (      ADX_zone_25  Volume_MA_5  ADX_Deep  Volume_MA20      Hour  D1_Price_Range  Volume_Lag_30  ...  H2_Price_Strangth  Volume_Change_1  H2_Bar_OSB  H4_Price_Strangth  DMN_14_Lag_2  EMA_diff_x_BBwidth  H2_Bar_longwick
5451    -1.251985    -0.530174  1.255784    -0.041615 -1.381554        0.319055       0.005715  ...           0.198166         1.450786   -0.196330           1.813924      0.144875           -0.241874         0.265747
5456    -1.251985    -0.506388 -0.796315    -0.819281 -0.249636       -0.589901      -0.286041  ...           0.198166         0.635001   -0.196330           0.066670     -0.972684            0.149873        -0.283316
5462    -1.251985    -0.394543 -0.796315    -0.580113 -0.249636       -0.414214      -0.264853  ...           2.045499        -0.024859   -0.196330           0.066670     -0.565752           -0.491046         0.404488
5470    -1.251985    -0.403360 -0.796315    -0.678743  0.693629        0.206646      -0.231396  ...          -1.649167         0.129173   -0.196330           0.066670     -0.544801           -0.255210        -0.018735
5471    -1.251985     0.382278 -0.796315    -0.326197  1.448241        0.206646      -0.162141  ...           0.198166        -0.725991   -0.196330           0.066670      1.130328           -1.546909        -0.172593
...           ...          ...       ...          ...       ...             ...            ...  ...                ...              ...         ...                ...           ...                 ...              ...
7218     0.798731     1.629153  1.255784     1.148295  1.825547        1.650852       0.020072  ...           0.198166         0.054791   -0.196330           0.066670      1.010458           -6.927016        -0.113416
7220     0.798731     0.242604 -0.796315     0.948858  0.127670        4.007737       0.312967  ...           0.198166        -0.358617   -0.196330           0.066670     -0.163164           -3.975132        -0.360864
7234     0.798731    -0.064808  1.255784     0.738113 -1.192901        3.012426       0.364512  ...           0.198166         0.349137    2.260443           0.066670      1.240956           -1.275577         0.026813
7235     0.798731     0.812374 -0.796315     0.569394  1.448241        3.012426       0.316825  ...           0.198166        -0.739631   -0.196330           0.066670      0.180637           -1.180347        -0.101152
7237    -1.251985    -0.131540  1.255784     0.439288 -1.004248        1.190790       0.199502  ...           2.045499        -0.812596   -0.196330           0.066670     -1.197539           -1.560657        -0.420588

[461 rows x 50 columns], 5451    0
5456    0
5462    0
5470    0
5471    0
       ..
7218    1
7220    1
7234    0
7235    1
7237    1
Name: Target_Multiclass, Length: 461, dtype: int32)
df :              Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0      2013.07.18  12:00:00  1281.04  1282.29  1278.50  1280.54    3360  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
1      2013.07.18  13:00:00  1280.53  1282.77  1280.18  1280.85    2748  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
2      2013.07.18  14:00:00  1281.10  1282.35  1280.29  1281.04    2718  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
3      2013.07.18  15:00:00  1281.14  1287.04  1277.57  1285.03    4864  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
4      2013.07.18  16:00:00  1285.12  1288.40  1283.80  1286.97    4524  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
...           ...       ...      ...      ...      ...      ...     ...  ...               ...              ...                 ...                      ...           ...           ...             ...
71823  2025.07.11  19:00:00  3358.83  3359.63  3352.79  3353.84   11242  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
71824  2025.07.11  20:00:00  3353.65  3354.09  3349.24  3353.38    9305  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
71825  2025.07.11  21:00:00  3353.37  3358.48  3353.32  3356.09    7062  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
71826  2025.07.11  22:00:00  3356.09  3359.72  3355.59  3356.40    7058  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
71827  2025.07.11  23:00:00  3356.47  3358.82  3353.52  3354.98    3542  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0

[71828 rows x 332 columns]
trade_df :               Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  D1_MACD_signal  RR_Ratio  Target Main_Target  Target_Buy  Target_Sell  Target_Multiclass
0    2013-07-23 05:00:00      1335.25 2013-07-23 06:00:00     1335.25        Buy     0.0          1  ...             0.0       inf       0           0           0           -1                  3
1    2013-07-23 10:00:00      1331.38 2013-07-23 10:00:00     1331.38        Buy     0.0          1  ...             0.0       inf       0           0           0           -1                  3
2    2013-07-23 15:00:00      1329.05 2013-07-23 16:00:00     1329.05        Buy     0.0          1  ...             0.0       inf       0           0           0           -1                  3
3    2013-07-24 09:00:00      1341.46 2013-07-24 11:00:00     1341.46        Buy     0.0          2  ...             0.0       inf       0           0           0           -1                  3
4    2013-07-24 17:00:00      1338.31 2013-07-24 17:00:00     1334.32        Buy  -399.0          2  ...             0.0  2.000000       0           0           0           -1                  2
...                  ...          ...                 ...         ...        ...     ...        ...  ...             ...       ...     ...         ...         ...          ...                ...
7237 2025-07-10 06:00:00      3319.19 2025-07-10 08:00:00     3319.19       Sell     0.0          3  ...            -1.0       inf       0           0          -1            0                  1
7238 2025-07-10 09:00:00      3321.14 2025-07-10 09:00:00     3326.53       Sell  -539.0          3  ...            -1.0  2.001855       0           0          -1            0                  2
7239 2025-07-10 11:00:00      3321.08 2025-07-10 11:00:00     3327.70       Sell  -662.0          3  ...            -1.0  2.001511       0           0          -1            0                  2
7240 2025-07-10 15:00:00      3320.46 2025-07-10 16:00:00     3328.05       Sell  -759.0          3  ...            -1.0  2.001318       0           0          -1            0                  2
7241 2025-07-10 20:00:00      3315.05 2025-07-10 22:00:00     3323.99       Sell  -894.0          3  ...            -1.0  2.000000       0           0          -1            0                  2

[7242 rows x 338 columns]
stats : {'buy': {'win_rate': 29.91, 'expectancy': -39.995}, 'sell': {'win_rate': 32.34, 'expectancy': -12.654}, 'buy_sell': {'win_rate': 31.05, 'expectancy': -27.153}}
features : 50
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening']

✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)
Train: 2013-08-05 ถึง 2020-11-12 (2657 วัน, 1389 records)
Val: 2019-10-30 ถึง 2022-10-21 (1088 วัน, 461 records)
Test: 2022-07-01 ถึง 2025-07-10 (1106 วัน, 461 records)

✅ ข้อมูล df หลังจาก load and process data
จำนวน columns ใน df: 332

✅ ข้อมูล trade_df หลังจาก load and process data
จำนวน columns ใน trade_df: 338
🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนวิเคราะห์ SL/TP + เวลา : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

🏗️ เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              1085      14.98%
SL Hit              6051      83.55%
Technical Exit      106       1.46%
SL + Tech Exit      6157      85.02%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 763.81
ขาดทุนเฉลี่ยเมื่อ SL Hit: -150.88
กำไร/ขาดทุนเฉลี่ยเมื่อออกด้วยสัญญาณเทคนิค: -122.76
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:5.06

ผลรวม SL + Technical Exit:
- จำนวนเทรดรวม: 6157
- อัตราส่วน: 85.02%
- กำไร/ขาดทุนเฉลี่ย: -150.39
- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:5.08

การออกด้วยสัญญาณเทคนิค:
- กำไรเฉลี่ยเมื่อชน TP: 78.54
- ขาดทุนเฉลี่ยเมื่อชน SL: -188.19
- อัตราการชน TP: 24.53%
- อัตราการชน SL: 75.47%

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
          Profit                        Target
           count       mean      sum      mean
DayOfWeek                                     
Monday      1441 -20.969466 -30217.0  0.145038
Tuesday     1527  -8.383759 -12802.0  0.154551
Wednesday   1496 -11.927139 -17843.0  0.153743
Thursday    1349   7.234248   9759.0  0.163084
Friday      1429 -32.282715 -46132.0  0.132960

📊 ประสิทธิภาพตามชั่วโมง:
     Profit                        Target
      count       mean      sum      mean
Hour                                     
3       648  38.052469  24658.0  0.191358
4       576 -15.375000  -8856.0  0.159722
5       497  -5.595573  -2781.0  0.179074
6       399   0.721805    288.0  0.182957
7       351 -68.131054 -23914.0  0.142450
8       373 -49.533512 -18476.0  0.109920
9       389  23.884319   9291.0  0.174807
10      422  -1.741706   -735.0  0.144550
11      398 -23.479899  -9345.0  0.128141
12      424  -0.875000   -371.0  0.146226
13      387 -32.248062 -12480.0  0.147287
14      421 -42.641330 -17952.0  0.156770
15      305 -17.695082  -5397.0  0.134426
16      338  27.926036   9439.0  0.127219
17      300   5.440000   1632.0  0.126667
18      328 -10.548780  -3460.0  0.128049
19      399 -47.644110 -19010.0  0.122807
20      287 -68.871080 -19766.0  0.132404
💾 บันทึกกราฟวิเคราะห์เวลา ที่: LightGBM/Multi/results\M60_GOLD_time_analysis.png

============================================================
🤖 ขั้นตอนที่ 2: เทรนโมเดล LightGBM
============================================================
🔧 โหมดการทำงาน: Development
🔧 เทรนโมเดลใหม่: ใช่
🔧 ทดสอบ Optimal Parameters: ใช่
============================================================
🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)

🔄 เทรนโมเดลใหม่ (TRAIN_NEW_MODEL = True)
🔍 Debug: เตรียม merge df และ trade_df
🔍 Debug: df.shape = (71828, 332), trade_df.shape = (7242, 338)

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🚀 ใช้ Logic การ Merge ใหม่โดยอ้างอิงจาก Timestamp ที่ถูกต้อง
🔍 เตรียม Merge ข้อมูล 10 คอลัมน์จาก trade_df
✅ คำนวณ Timeframe จากข้อมูลได้: 0 days 01:00:00
✅ Merge สำเร็จ พบข้อมูลที่ตรงกัน 7242 รายการ
✅ จัดการค่าว่างหลังการ Merge...
✅ เติมค่าว่างตามที่กำหนดเรียบร้อยแล้ว

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 342
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'Entry Time', 'Entry Price', 'Exit Price', 'Trade Type', 'Profit', 'Exit Condition', 'Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
features : 50
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening']
✅ ใช้ Final selected feature: 50 feature

🏗️ เปิดใช้งาน train all scenario models
🚀 เริ่มเทรนโมเดลทั้ง 2 scenarios สำหรับ GOLD_M60
============================================================
📁 ใช้ results_dir: LightGBM/Multi/results
🔍 Debug: USE_MULTICLASS_TARGET = True
🔍 Debug: target_column = Target_Multiclass
✅ สร้างโฟลเดอร์ทั้งหมดเรียบร้อยแล้ว
🔍 Debug: ตรวจสอบคอลัมน์ใน df ก่อนเพิ่ม market_scenario
🔍 Debug: df.columns[:10] = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour']
🔍 Debug: มีคอลัมน์ Close? True
🔍 Debug: มีคอลัมน์ EMA200? True

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 35864 (49.9%)
  downtrend: 30186 (42.0%)
  sideways: 5778 (8.0%)

================================================================================
📊 กำลังเทรน trend_following...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ trend_following

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 71828 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 66050/71828 rows (92.0%)
📊 ข้อมูลหลังกรอง trend_following: 66050 samples

🛠️ Features used for training (Selected: 50 total):
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
📊 Class distribution สำหรับ trend_following: {0.0: 60466, 2.0: 2031, 3.0: 1622, 1.0: 1477, 4.0: 454}
✅ เตรียมข้อมูล trend_following: 66050 samples, 50 features

✅ ข้อมูลพร้อม: X.shape=(66050, 50), y.shape=(66050,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ trend_following

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 66050 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 66050 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 60466, 2.0: 2031, 3.0: 1622, 1.0: 1477, 4.0: 454}
📈 Train: 39630, Val: 13210, Test: 13210
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 1 → 71827
   Val: 0 → 71822
   Test: 2 → 71825
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 36280, 2.0: 1219, 3.0: 973, 1.0: 886, 4.0: 272}
🔄 ใช้ Safe Oversampling System...
📊 Class distribution: {0.0: 36280, 1.0: 886, 2.0: 1219, 3.0: 973, 4.0: 272}
📊 Imbalance ratio: 0.007
⚠️ Imbalance รุนแรง (0.007) - ใช้ SMOTE
📊 Original: Counter({0.0: 36280, 2.0: 1219, 3.0: 973, 1.0: 886, 4.0: 272})
📊 Balanced: Counter({0.0: 36280, 1.0: 36280, 2.0: 36280, 3.0: 36280, 4.0: 36280})
📊 Train class distribution หลังปรับสมดุล: {0.0: 36280, 1.0: 36280, 2.0: 36280, 3.0: 36280, 4.0: 36280}
📈 จำนวนข้อมูล: เดิม 39630 → หลังปรับสมดุล 181400
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ trend_following

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 1.0, 1.0: 1.0, 2.0: 1.0, 3.0: 1.0, 4.0: 1.0}
📊 trend_following Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.037 - 0.087 (base: 0.062)
   num_leaves: 21 - 27 (base: 24)
   max_depth: 4 - 8 (base: 6)
   Strategy: Stability-focused
✅ โหลด parameters สำเร็จสำหรับ trend_following: {'reg_lambda': 0.0, 'reg_alpha': 0.005, 'num_leaves': 22, 'min_data_in_leaf': 10, 'max_depth': 7, 'learning_rate': 0.035, 'feature_fraction': 0.84, 'bagging_freq': 1, 'bagging_fraction': 0.87}
✅ Best CV score สำหรับ trend_following: 0.8067

--- Features (Columns) ---
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
--- Sample Data (First 5 rows) ---
   ADX_zone_25  Volume_MA_5  ADX_Deep  Volume_MA20  Hour  D1_Price_Range  Volume_Lag_30  ...  H2_Price_Strangth  Volume_Change_1  H2_Bar_OSB  H4_Price_Strangth  DMN_14_Lag_2  EMA_diff_x_BBwidth  H2_Bar_longwick
0            1       3132.6         1      8341.80     7           10.98          990.0  ...               -1.0        -0.042908         0.0                0.0     10.325876           10.877801        -0.371841
1            0        990.8        -1       790.40    12            5.51          589.0  ...                0.0        -0.176923        -1.0                0.0     16.707992           -8.189740        -0.811321
2            0      11418.6        -1     17472.35     9           22.45         4747.0  ...               -1.0         0.413858         0.0                0.0     20.091324           62.217022         0.878378
3            1       3675.4        -1      7574.60     9           34.60         1974.0  ...                1.0         1.074568         0.0                0.0     16.186532         -223.574680        -0.939086
4            0       9513.6        -1      4885.65     1           12.96         2933.0  ...                0.0        -0.981498         0.0                0.0     24.630537          -65.097901         0.397727

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following
📊 Class distribution: {0.0: 36280, 1.0: 36280, 2.0: 36280, 3.0: 36280, 4.0: 36280}
📊 Imbalance ratio: 1.000
✅ ไม่ต้อง oversample - imbalance ยอมรับได้หรือมีข้อมูลเพียงพอ
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's multi_logloss: 0.324651

Accuracy: 0.9075
Classification Report:
              precision    recall  f1-score   support

         0.0       0.94      0.98      0.96     12093
         1.0       0.34      0.27      0.30       296
         2.0       0.10      0.01      0.02       406
         3.0       0.33      0.31      0.32       324
         4.0       0.11      0.09      0.10        91

    accuracy                           0.91     13210
   macro avg       0.36      0.33      0.34     13210
weighted avg       0.88      0.91      0.89     13210

Confusion Matrix:
[[11796   108    31   119    39]
 [  213    79     4     0     0]
 [  288    42     4    62    10]
 [  208     0     2   101    13]
 [   58     0     0    25     8]]
📊 Test Set Accuracy: 0.9075, F1-Score: 0.8918
🔍 ประเมินผลแบบ Multiclass
✅ คำนวณ Multiclass AUC สำเร็จ: 0.9019
✅ trend_following - Accuracy: 0.907, F1: 0.892, AUC: 0.902

🔍 ตรวจสอบคุณภาพโมเดล trend_following...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (13210, 50)
   y_val shape: (13210,), unique: [0. 1. 2. 3. 4.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (13210, 50)
   y_val shape: (13210,), unique: [0. 1. 2. 3. 4.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (13210, 5)
   Multi-class classification detected (5 classes)
   Using model.predict() to get actual class values instead of argmax
   Final y_pred shape: (13210,), unique: [0. 1. 2. 3. 4.], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (13210,), unique values: [0. 1. 2. 3. 4.]
   y_pred shape: (13210,), unique values: [0. 1. 2. 3. 4.]
   y_val sample: [2. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   Multi-class classification metrics calculated
   Final calculated metrics: Acc=0.9072, F1=0.8911, Prec=0.8790, Rec=0.9072
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.9072
   AUC: 0.9063
   F1: 0.8911
   Precision: 0.8790
   Recall: 0.9072
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 45.06
   Trades: 1321
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 225.30
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (trend_following) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 19:04:16
📊 โมเดล: GOLD MM60 (trend_following)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================

================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (trend_following)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล trend_following - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ trend_following
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_GOLD_M60 symbol GOLD timeframe M60 (trend_following)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
           Feature   Gain  Split
EMA_diff_x_BBwidth 0.2715 0.0664
         Bar_CL_OC 0.1484 0.0671
            Bar_SW 0.0767 0.0610
          ADX_Deep 0.0521 0.0138
         STO_cross 0.0433 0.0350
              Hour 0.0292 0.0331
      H2_Bar_CL_OC 0.0261 0.0212
      H4_MACD_deep 0.0220 0.0202
     H12_Bar_CL_OC 0.0195 0.0274
D1_Volume_Momentum 0.0168 0.0143
      D1_MACD_line 0.0164 0.0194
      DMN_14_Lag_2 0.0146 0.0296
   EMA_Cross_EMA10 0.0146 0.0210
     Volume_Lag_10 0.0142 0.0379
       Volume_MA20 0.0136 0.0340

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_feature_importance.csv (ขนาด: 2778 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
              Feature  Importance
   EMA_diff_x_BBwidth    0.098003
            Bar_CL_OC    0.078398
               Bar_SW    0.055271
         H2_Bar_CL_OC    0.032018
            STO_cross    0.031330
        Volume_Lag_10    0.028592
          Volume_MA20    0.028018
          Volume_MA_5    0.026770
        Volume_Lag_30    0.025577
                 Hour    0.025181
         Volume_Lag_5    0.025121
         DMN_14_Lag_2    0.021583
MACD_line_x_PriceMove    0.021465
         D1_MACD_line    0.021428
        H12_Bar_CL_OC    0.021328

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.93      0.99      0.96     12093
         1.0       0.41      0.20      0.27       296
         2.0       0.29      0.04      0.07       406
         3.0       0.34      0.22      0.26       324
         4.0       0.25      0.01      0.02        91

    accuracy                           0.91     13210
   macro avg       0.45      0.29      0.32     13210
weighted avg       0.88      0.91      0.89     13210

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง trade_df สำหรับ trend_following: 13210 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 345 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 271626 bytes
✅ เทรน trend_following สำเร็จ

================================================================================
📊 กำลังเทรน counter_trend...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ counter_trend

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 71828 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5778/71828 rows (8.0%)
📊 ข้อมูลหลังกรอง counter_trend: 5778 samples

🛠️ Features used for training (Selected: 50 total):
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
📊 Class distribution สำหรับ counter_trend: {0.0: 4654, 2.0: 436, 1.0: 300, 3.0: 280, 4.0: 108}
✅ เตรียมข้อมูล counter_trend: 5778 samples, 50 features

✅ ข้อมูลพร้อม: X.shape=(5778, 50), y.shape=(5778,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ counter_trend

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 4654, 2.0: 436, 1.0: 300, 3.0: 280, 4.0: 108}
📈 Train: 3466, Val: 1156, Test: 1156
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 220 → 71803
   Val: 196 → 71805
   Test: 225 → 71792
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 2792, 2.0: 262, 1.0: 180, 3.0: 168, 4.0: 64}
🔄 ใช้ Safe Oversampling System...
📊 Class distribution: {1.0: 180, 0.0: 2792, 2.0: 262, 3.0: 168, 4.0: 64}
📊 Imbalance ratio: 0.023
⚠️ Imbalance รุนแรง (0.023) - ใช้ SMOTE
📊 Original: Counter({0.0: 2792, 2.0: 262, 1.0: 180, 3.0: 168, 4.0: 64})
📊 Balanced: Counter({1.0: 2792, 0.0: 2792, 2.0: 2792, 3.0: 2792, 4.0: 2792})
📊 Train class distribution หลังปรับสมดุล: {1.0: 2792, 0.0: 2792, 2.0: 2792, 3.0: 2792, 4.0: 2792}
📈 จำนวนข้อมูล: เดิม 3466 → หลังปรับสมดุล 13960
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ counter_trend

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 1.0, 1.0: 1.0, 2.0: 1.0, 3.0: 1.0, 4.0: 1.0}
📊 counter_trend Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.022 - 0.102 (base: 0.062)
   num_leaves: 14 - 34 (base: 24)
   max_depth: 5 - 7 (base: 6)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ counter_trend: {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 42, 'min_data_in_leaf': 8, 'max_depth': 7, 'learning_rate': 0.1, 'feature_fraction': 0.88, 'bagging_freq': 1, 'bagging_fraction': 0.8}
✅ Best CV score สำหรับ counter_trend: 0.9633

--- Features (Columns) ---
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
--- Sample Data (First 5 rows) ---
   ADX_zone_25  Volume_MA_5  ADX_Deep  Volume_MA20  Hour  D1_Price_Range  Volume_Lag_30  ...  H2_Price_Strangth  Volume_Change_1  H2_Bar_OSB  H4_Price_Strangth  DMN_14_Lag_2  EMA_diff_x_BBwidth  H2_Bar_longwick
0            1      10961.0        -1      19174.0    15            9.43        16133.0  ...                1.0         0.924630         0.0                0.0     10.985438           12.721477        -2.448718
1            1       8786.6         1      13955.0    15           12.61        10836.0  ...               -1.0         1.580779         0.0                0.0     27.010713           11.768378         2.333333
2            0       1701.6         1       1244.0    22           22.74         4149.0  ...               -1.0         0.077372         0.0                0.0     24.289299          -12.442089        -0.580645
3            0       1366.8         1        989.6    14            8.88         1358.0  ...                1.0         2.392610         0.0                0.0     18.498624           14.018330        -1.245283
4            1       3563.8         1       5124.6    11           27.74         5231.0  ...               -1.0        -0.111310         0.0                0.0     13.792904          -50.195009         0.360704

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend
📊 Class distribution: {0.0: 2792, 1.0: 2792, 2.0: 2792, 3.0: 2792, 4.0: 2792}
📊 Imbalance ratio: 1.000
✅ ไม่ต้อง oversample - imbalance ยอมรับได้หรือมีข้อมูลเพียงพอ
Training until validation scores don't improve for 50 rounds
Early stopping, best iteration is:
[39]	valid_0's multi_logloss: 0.61158

Accuracy: 0.7751
Classification Report:
              precision    recall  f1-score   support

         0.0       0.82      0.95      0.88       931
         1.0       0.18      0.10      0.13        60
         2.0       0.18      0.03      0.06        87
         3.0       0.17      0.07      0.10        56
         4.0       0.20      0.09      0.12        22

    accuracy                           0.78      1156
   macro avg       0.31      0.25      0.26      1156
weighted avg       0.70      0.78      0.73      1156

Confusion Matrix:
[[881  24  11  11   4]
 [ 53   6   1   0   0]
 [ 70   3   3   8   3]
 [ 50   0   1   4   1]
 [ 18   0   1   1   2]]
📊 Test Set Accuracy: 0.7751, F1-Score: 0.7267
🔍 ประเมินผลแบบ Multiclass
✅ คำนวณ Multiclass AUC สำเร็จ: 0.8072
✅ counter_trend - Accuracy: 0.775, F1: 0.727, AUC: 0.807

🔍 ตรวจสอบคุณภาพโมเดล counter_trend...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (1156, 50)
   y_val shape: (1156,), unique: [0. 1. 2. 3. 4.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (1156, 50)
   y_val shape: (1156,), unique: [0. 1. 2. 3. 4.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (1156, 5)
   Multi-class classification detected (5 classes)
   Using model.predict() to get actual class values instead of argmax
   Final y_pred shape: (1156,), unique: [0. 1. 2. 3. 4.], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (1156,), unique values: [0. 1. 2. 3. 4.]
   y_pred shape: (1156,), unique values: [0. 1. 2. 3. 4.]
   y_val sample: [1. 0. 0. 0. 0. 0. 0. 0. 1. 0.]
   y_pred sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   Multi-class classification metrics calculated
   Final calculated metrics: Acc=0.7889, F1=0.7383, Prec=0.7072, Rec=0.7889
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.7889
   AUC: 0.8267
   F1: 0.7383
   Precision: 0.7072
   Recall: 0.7889
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 37.79
   Trades: 115
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 188.94
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (counter_trend) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 19:08:45
📊 โมเดล: GOLD MM60 (counter_trend)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================

================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (counter_trend)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล counter_trend - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ counter_trend
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_GOLD_M60 symbol GOLD timeframe M60 (counter_trend)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
              Feature   Gain  Split
            Bar_CL_OC 0.1934 0.0355
               Bar_SW 0.0541 0.0258
MACD_line_x_PriceMove 0.0406 0.0370
   EMA_diff_x_BBwidth 0.0362 0.0343
         Volume_Lag_5 0.0336 0.0487
        Volume_Lag_10 0.0322 0.0499
          Volume_MA_5 0.0302 0.0397
              IsNight 0.0292 0.0040
             ADX_Deep 0.0286 0.0119
        Volume_Lag_30 0.0268 0.0449
       STO_overbought 0.0263 0.0120
          Volume_MA20 0.0254 0.0408
                 Hour 0.0236 0.0231
           ATR_ROC_i2 0.0200 0.0298
   D1_Volume_Momentum 0.0195 0.0127

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_counter_trend_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_counter_trend_feature_importance.csv (ขนาด: 2828 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
              Feature  Importance
            Bar_CL_OC    0.095098
               Bar_SW    0.059733
   EMA_diff_x_BBwidth    0.041996
MACD_line_x_PriceMove    0.038986
        Volume_Lag_10    0.030582
          Volume_MA20    0.028969
          Volume_MA_5    0.028077
                 Hour    0.027651
         Volume_Lag_5    0.026879
        Volume_Lag_30    0.026221
         H4_MACD_deep    0.023527
           ATR_ROC_i2    0.022439
          Price_Range    0.021970
      Volume_Change_1    0.020195
       D1_Price_Range    0.019979

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.82      0.96      0.88       931
         1.0       0.25      0.05      0.08        60
         2.0       0.38      0.07      0.12        87
         3.0       0.15      0.07      0.10        56
         4.0       0.00      0.00      0.00        22

    accuracy                           0.79      1156
   macro avg       0.32      0.23      0.24      1156
weighted avg       0.71      0.79      0.73      1156

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง trade_df สำหรับ counter_trend: 1156 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 337 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 268465 bytes
✅ เทรน counter_trend สำเร็จ

================================================================================
📊 กำลังเทรน Target_Buy...
================================================================================

================================================================================
📊 กำลังเทรน trend_following สำหรับ Target_Buy...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 68380 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 63201/68380 rows (92.4%)
📊 ข้อมูลหลังกรอง trend_following: 63201 samples

🛠️ Features used for training (Selected: 50 total):
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
📊 Class distribution สำหรับ trend_following: {0.0: 62752, 1.0: 449}
✅ เตรียมข้อมูล trend_following: 63201 samples, 50 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 63201 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 63201 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 62752, 1.0: 449}
📈 Train: 37920, Val: 12640, Test: 12641
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 1 → 71827
   Val: 0 → 71825
   Test: 2 → 71824
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 37651, 1.0: 269}
🔄 ใช้ Safe Oversampling System...
📊 Class distribution: {0.0: 37651, 1.0: 269}
📊 Imbalance ratio: 0.007
⚠️ Imbalance รุนแรง (0.007) - ใช้ SMOTE
📊 Original: Counter({0.0: 37651, 1.0: 269})
📊 Balanced: Counter({0.0: 37651, 1.0: 37651})
📊 Train class distribution หลังปรับสมดุล: {0.0: 37651, 1.0: 37651}
📈 จำนวนข้อมูล: เดิม 37920 → หลังปรับสมดุล 75302
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following_Buy (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following_Buy:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Buy_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Buy_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ trend_following_Buy

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 37651, 1.0: 37651}
   Imbalance ratio: 1.0:1
  🎯 Auto Class Weight: balanced
📊 trend_following_Buy Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.015 - 0.085 (base: 0.050)
   num_leaves: 12 - 28 (base: 20)
   max_depth: 3 - 7 (base: 5)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ trend_following_Buy: {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ trend_following_Buy: 0.9996

--- Features (Columns) ---
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
--- Sample Data (First 5 rows) ---
   ADX_zone_25  Volume_MA_5  ADX_Deep  Volume_MA20  Hour  D1_Price_Range  Volume_Lag_30  ...  H2_Price_Strangth  Volume_Change_1  H2_Bar_OSB  H4_Price_Strangth  DMN_14_Lag_2  EMA_diff_x_BBwidth  H2_Bar_longwick
0            1       4118.2         1      3370.75    13           19.48         3281.0  ...                0.0        -0.139217         0.0                0.0     11.815493           60.143198         0.025678
1            1       6122.6         1      4750.00    16           47.61         6011.0  ...                0.0         0.591687         0.0                0.0     23.616194         -299.050366         0.284855
2            1       3633.2         1      6040.95    10           24.72         1544.0  ...               -1.0         0.112329         0.0                0.0     35.108926         -379.325558        -0.669323
3            1       1888.2        -1      2978.30     6           25.58         3007.0  ...                0.0        -0.314940        -1.0                1.0     11.146068          261.824388        -0.762774
4            1       2739.8         1      4470.70     7           17.06         1480.0  ...               -1.0        -0.320017         0.0                0.0     29.521635         -215.721167         0.894977

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following_Buy
📊 Class distribution: {0.0: 37651, 1.0: 37651}
📊 Imbalance ratio: 1.000
✅ ไม่ต้อง oversample - imbalance ยอมรับได้หรือมีข้อมูลเพียงพอ
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's binary_logloss: 0.0341359

Accuracy: 0.9923
Classification Report:
              precision    recall  f1-score   support

         0.0       0.99      1.00      1.00     12551
         1.0       0.11      0.01      0.02        90

    accuracy                           0.99     12641
   macro avg       0.55      0.51      0.51     12641
weighted avg       0.99      0.99      0.99     12641

Confusion Matrix:
[[12543     8]
 [   89     1]]
📊 Test Set Accuracy: 0.9923, F1-Score: 0.9892
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.9155
✅ trend_following_Buy - Accuracy: 0.992, F1: 0.989, AUC: 0.915

🔍 ตรวจสอบคุณภาพโมเดล trend_following_Buy...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (12640, 50)
   y_val shape: (12640,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following_Buy)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (12640, 50)
   y_val shape: (12640,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (12640, 2)
   Binary classification detected
   Final y_pred shape: (12640,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (12640,), unique values: [0. 1.]
   y_pred shape: (12640,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification: negative=0.0, positive=1.0
   Binary metrics calculated with pos_label=1.0
   ⚠️ All metrics are 0, trying weighted average...
   Final calculated metrics: Acc=0.9924, F1=0.9891, Prec=0.9858, Rec=0.9924
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.9924
   AUC: 0.9287
   F1: 0.9891
   Precision: 0.9858
   Recall: 0.9924
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.55
   Trades: 1264
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 247.77
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (trend_following_Buy) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 19:09:09
📊 โมเดล: GOLD MM60 (trend_following_Buy)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================

================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (trend_following_Buy)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล trend_following_Buy - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ trend_following_Buy
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_Buy_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_Buy_GOLD_M60 symbol GOLD timeframe M60 (trend_following_Buy)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
           Feature   Gain  Split
EMA_diff_x_BBwidth 0.2044 0.0507
         Bar_CL_OC 0.1820 0.0422
         STO_cross 0.1138 0.0301
            Bar_SW 0.0536 0.0318
      H4_MACD_deep 0.0463 0.0340
      D1_MACD_line 0.0455 0.0223
          ADX_Deep 0.0414 0.0268
      H2_Bar_CL_OC 0.0341 0.0157
    EMA_Cross_EMA5 0.0296 0.0209
              Hour 0.0252 0.0314
     H12_Bar_CL_OC 0.0202 0.0209
D1_Volume_Momentum 0.0172 0.0216
        H12_Bar_SW 0.0168 0.0272
 H2_Price_Strangth 0.0157 0.0298
        H2_Bar_OSB 0.0143 0.0465

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_Buy_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_Buy_feature_importance.csv (ขนาด: 2840 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following_Buy

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
           Feature  Importance
         Bar_CL_OC    0.117113
EMA_diff_x_BBwidth    0.113278
            Bar_SW    0.075455
         STO_cross    0.068469
      D1_MACD_line    0.055454
      H2_Bar_CL_OC    0.054973
     H12_Bar_CL_OC    0.047116
      H4_MACD_deep    0.046586
        H12_Bar_SW    0.032024
              Hour    0.030920
         H2_Bar_SW    0.027283
 H2_Price_Strangth    0.025871
   EMA_Cross_EMA10    0.022568
    EMA_Cross_EMA5    0.022535
          ADX_Deep    0.019912

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.99      1.00      1.00     12551
         1.0       0.00      0.00      0.00        90

    accuracy                           0.99     12641
   macro avg       0.50      0.50      0.50     12641
weighted avg       0.99      0.99      0.99     12641

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following_Buy
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following_Buy
✅ สร้าง trade_df สำหรับ trend_following_Buy: 12641 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following_Buy
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following_Buy/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following_Buy/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following_Buy/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 356 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following_Buy

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following_Buy
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following_Buy/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following_Buy/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 273064 bytes
✅ เทรน trend_following_Buy สำเร็จ

================================================================================
📊 กำลังเทรน counter_trend สำหรับ Target_Buy...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 68380 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5179/68380 rows (7.6%)
📊 ข้อมูลหลังกรอง counter_trend: 5179 samples

🛠️ Features used for training (Selected: 50 total):
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
📊 Class distribution สำหรับ counter_trend: {0.0: 5074, 1.0: 105}
✅ เตรียมข้อมูล counter_trend: 5179 samples, 50 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 5179 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 5179 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 5074, 1.0: 105}
📈 Train: 3107, Val: 1036, Test: 1036
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 196 → 71805
   Val: 244 → 71741
   Test: 225 → 71792
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 3044, 1.0: 63}
🔄 ใช้ Safe Oversampling System...
📊 Class distribution: {0.0: 3044, 1.0: 63}
📊 Imbalance ratio: 0.021
⚠️ Imbalance รุนแรง (0.021) - ใช้ SMOTE
📊 Original: Counter({0.0: 3044, 1.0: 63})
📊 Balanced: Counter({0.0: 3044, 1.0: 3044})
📊 Train class distribution หลังปรับสมดุล: {0.0: 3044, 1.0: 3044}
📈 จำนวนข้อมูล: เดิม 3107 → หลังปรับสมดุล 6088
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend_Buy (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend_Buy:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Buy_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Buy_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ counter_trend_Buy

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 3044, 1.0: 3044}
   Imbalance ratio: 1.0:1
  🎯 Auto Class Weight: balanced
📊 counter_trend_Buy Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.015 - 0.085 (base: 0.050)
   num_leaves: 12 - 28 (base: 20)
   max_depth: 3 - 7 (base: 5)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ counter_trend_Buy: {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ counter_trend_Buy: 0.9993

--- Features (Columns) ---
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
--- Sample Data (First 5 rows) ---
   ADX_zone_25  Volume_MA_5  ADX_Deep  Volume_MA20  Hour  D1_Price_Range  Volume_Lag_30  ...  H2_Price_Strangth  Volume_Change_1  H2_Bar_OSB  H4_Price_Strangth  DMN_14_Lag_2  EMA_diff_x_BBwidth  H2_Bar_longwick
0            0       6025.0         1      7220.70    15           84.93         5546.0  ...               -1.0         0.803819         0.0               -1.0     20.546222          609.096457         1.360097
1            0       3864.8        -1      6908.05     2           15.71         8904.0  ...                0.0        -0.310298        -1.0               -1.0     17.665997          -29.339911         1.152542
2            1       1183.4        -1      4990.30     0           10.48         4193.0  ...               -1.0       143.671018         0.0                0.0     33.224341            8.631985        -0.144000
3            0      16334.0         1     23394.05     0           11.78        20776.0  ...                1.0        20.128476         0.0               -1.0     19.080839          -32.407133        -0.143251
4            0       4091.6         1      2972.90    21           11.73         3585.0  ...                0.0         0.075845         0.0                0.0     14.896255         -102.656806        -0.629139

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend_Buy
📊 Class distribution: {0.0: 3044, 1.0: 3044}
📊 Imbalance ratio: 1.000
✅ ไม่ต้อง oversample - imbalance ยอมรับได้หรือมีข้อมูลเพียงพอ
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[73]	valid_0's binary_logloss: 0.0928656

Accuracy: 0.9778
Classification Report:
              precision    recall  f1-score   support

         0.0       0.98      1.00      0.99      1015
         1.0       0.25      0.05      0.08        21

    accuracy                           0.98      1036
   macro avg       0.62      0.52      0.53      1036
weighted avg       0.97      0.98      0.97      1036

Confusion Matrix:
[[1012    3]
 [  20    1]]
📊 Test Set Accuracy: 0.9778, F1-Score: 0.9703
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.8608
✅ counter_trend_Buy - Accuracy: 0.978, F1: 0.970, AUC: 0.861

🔍 ตรวจสอบคุณภาพโมเดล counter_trend_Buy...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (1036, 50)
   y_val shape: (1036,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend_Buy)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (1036, 50)
   y_val shape: (1036,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (1036, 2)
   Binary classification detected
   Final y_pred shape: (1036,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (1036,), unique values: [0. 1.]
   y_pred shape: (1036,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification: negative=0.0, positive=1.0
   Binary metrics calculated with pos_label=1.0
   Final calculated metrics: Acc=0.9778, F1=0.0800, Prec=0.2500, Rec=0.0476
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.9778
   AUC: 0.8098
   F1: 0.0800
   Precision: 0.2500
   Recall: 0.0476
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 48.74
   Trades: 103
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 243.70
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (counter_trend_Buy) ผ่านเกณฑ์คุณภาพ
⚠️ คำเตือน:
   - Recall 0.0476 < 0.150 (FLEXIBLE)

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 19:10:16
📊 โมเดล: GOLD MM60 (counter_trend_Buy)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)
🏷️ ประเภท: declined
================================================================================

================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (counter_trend_Buy)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ดีขึ้น - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)
🏷️ ประเภท: declined
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล counter_trend_Buy - โมเดลไม่ดีขึ้น - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น - auc: -0.0704 (-8.00%), f1: -0.8886 (-91.74%), precision: -0.7096 (-73.95%), recall: -0.9301 (-95.13%)
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ counter_trend_Buy
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_Buy_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_Buy_GOLD_M60 symbol GOLD timeframe M60 (counter_trend_Buy)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
              Feature   Gain  Split
            Bar_CL_OC 0.2510 0.0480
         H4_MACD_deep 0.1760 0.0379
              IsNight 0.0414 0.0142
            STO_cross 0.0409 0.0178
      EMA_Cross_EMA10 0.0403 0.0190
        H12_Bar_CL_OC 0.0341 0.0350
       EMA_Cross_EMA5 0.0340 0.0219
             ADX_Deep 0.0293 0.0213
   H8_Volume_Momentum 0.0293 0.0154
            H2_Bar_SW 0.0282 0.0314
   D1_Volume_Momentum 0.0275 0.0237
   EMA_diff_x_BBwidth 0.0205 0.0415
            H4_Bar_TL 0.0202 0.0267
MACD_line_x_PriceMove 0.0197 0.0237
               Bar_SW 0.0171 0.0101

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_counter_trend_Buy_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_counter_trend_Buy_feature_importance.csv (ขนาด: 2840 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend_Buy

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
           Feature  Importance
         Bar_CL_OC    0.122186
      H4_MACD_deep    0.070051
            Bar_SW    0.058472
           IsNight    0.046699
     H12_Bar_CL_OC    0.040062
         H8_Bar_SW    0.038842
D1_Volume_Momentum    0.034832
H8_Volume_Momentum    0.033673
         H2_Bar_SW    0.031585
          ADX_Deep    0.028383
      H2_Bar_CL_OC    0.027021
 H2_Price_Strangth    0.026935
EMA_diff_x_BBwidth    0.026773
        H12_Bar_SW    0.024789
H4_Volume_Momentum    0.023815

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.98      1.00      0.99      1015
         1.0       0.00      0.00      0.00        21

    accuracy                           0.98      1036
   macro avg       0.49      0.50      0.49      1036
weighted avg       0.96      0.98      0.97      1036

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend_Buy
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend_Buy
✅ สร้าง trade_df สำหรับ counter_trend_Buy: 1036 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend_Buy
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend_Buy/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend_Buy/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend_Buy/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 348 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend_Buy

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend_Buy
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend_Buy/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend_Buy/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 273809 bytes
✅ เทรน counter_trend_Buy สำเร็จ

================================================================================
📊 กำลังเทรน Target_Sell...
================================================================================

================================================================================
📊 กำลังเทรน trend_following สำหรับ Target_Sell...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 68034 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 62881/68034 rows (92.4%)
📊 ข้อมูลหลังกรอง trend_following: 62881 samples

🛠️ Features used for training (Selected: 50 total):
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
📊 Class distribution สำหรับ trend_following: {0.0: 62448, 1.0: 433}
✅ เตรียมข้อมูล trend_following: 62881 samples, 50 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 62881 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 62881 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 62448, 1.0: 433}
📈 Train: 37728, Val: 12576, Test: 12577
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 1 → 71827
   Val: 0 → 71821
   Test: 2 → 71824
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 37468, 1.0: 260}
🔄 ใช้ Safe Oversampling System...
📊 Class distribution: {0.0: 37468, 1.0: 260}
📊 Imbalance ratio: 0.007
⚠️ Imbalance รุนแรง (0.007) - ใช้ SMOTE
📊 Original: Counter({0.0: 37468, 1.0: 260})
📊 Balanced: Counter({0.0: 37468, 1.0: 37468})
📊 Train class distribution หลังปรับสมดุล: {0.0: 37468, 1.0: 37468}
📈 จำนวนข้อมูล: เดิม 37728 → หลังปรับสมดุล 74936
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following_Sell (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following_Sell:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Sell_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Sell_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 37468, 1.0: 37468}
   Imbalance ratio: 1.0:1
  🎯 Auto Class Weight: balanced
📊 trend_following_Sell Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.015 - 0.085 (base: 0.050)
   num_leaves: 12 - 28 (base: 20)
   max_depth: 3 - 7 (base: 5)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ trend_following_Sell: {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 30, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 3, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ trend_following_Sell: 0.9996

--- Features (Columns) ---
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
--- Sample Data (First 5 rows) ---
   ADX_zone_25  Volume_MA_5  ADX_Deep  Volume_MA20  Hour  D1_Price_Range  Volume_Lag_30  ...  H2_Price_Strangth  Volume_Change_1  H2_Bar_OSB  H4_Price_Strangth  DMN_14_Lag_2  EMA_diff_x_BBwidth  H2_Bar_longwick
0            1       4507.4         1       3746.0    16           27.84         5076.0  ...                0.0         0.145602         0.0                0.0     24.531490         -135.795989         0.790984
1            1      29348.8         1      15494.3     3           24.65         5993.0  ...                0.0         0.529909        -1.0               -1.0      6.493744          324.186886        -1.451677
2            1       2921.2         1       5094.4    10           48.10         2462.0  ...                0.0         0.216682         0.0                0.0      8.066433         1274.860017        -1.118343
3            1       2455.0         1       2700.0     5           33.50         1753.0  ...                1.0        -0.451593         0.0                1.0      8.477572          491.612783        -3.981982
4            0       2092.6         1       2239.7    12            8.15         2400.0  ...                0.0        -0.207819         0.0                0.0     11.722613          -22.646659         0.276836

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following_Sell
📊 Class distribution: {0.0: 37468, 1.0: 37468}
📊 Imbalance ratio: 1.000
✅ ไม่ต้อง oversample - imbalance ยอมรับได้หรือมีข้อมูลเพียงพอ
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's binary_logloss: 0.0324867

Accuracy: 0.9932
Classification Report:
              precision    recall  f1-score   support

         0.0       0.99      1.00      1.00     12490
         1.0       0.67      0.02      0.04        87

    accuracy                           0.99     12577
   macro avg       0.83      0.51      0.52     12577
weighted avg       0.99      0.99      0.99     12577

Confusion Matrix:
[[12489     1]
 [   85     2]]
📊 Test Set Accuracy: 0.9932, F1-Score: 0.9900
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.9299
✅ trend_following_Sell - Accuracy: 0.993, F1: 0.990, AUC: 0.930

🔍 ตรวจสอบคุณภาพโมเดล trend_following_Sell...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (12576, 50)
   y_val shape: (12576,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following_Sell)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (12576, 50)
   y_val shape: (12576,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (12576, 2)
   Binary classification detected
   Final y_pred shape: (12576,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (12576,), unique values: [0. 1.]
   y_pred shape: (12576,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification: negative=0.0, positive=1.0
   Binary metrics calculated with pos_label=1.0
   ⚠️ All metrics are 0, trying weighted average...
   Final calculated metrics: Acc=0.9928, F1=0.9896, Prec=0.9864, Rec=0.9928
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.9928
   AUC: 0.9270
   F1: 0.9896
   Precision: 0.9864
   Recall: 0.9928
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.59
   Trades: 1257
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 247.97
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (trend_following_Sell) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 19:10:30
📊 โมเดล: GOLD MM60 (trend_following_Sell)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================

================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (trend_following_Sell)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล trend_following_Sell - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ trend_following_Sell
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_Sell_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_Sell_GOLD_M60 symbol GOLD timeframe M60 (trend_following_Sell)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
           Feature   Gain  Split
EMA_diff_x_BBwidth 0.2527 0.0594
         Bar_CL_OC 0.1774 0.0420
      D1_MACD_line 0.0731 0.0291
         STO_cross 0.0615 0.0227
    STO_overbought 0.0481 0.0457
      H2_Bar_CL_OC 0.0449 0.0140
      H4_MACD_deep 0.0396 0.0344
            Bar_SW 0.0278 0.0208
     H12_Bar_CL_OC 0.0271 0.0268
         H4_Bar_TL 0.0263 0.0510
              Hour 0.0250 0.0261
          ADX_Deep 0.0205 0.0261
 H2_Price_Strangth 0.0174 0.0216
         H8_Bar_SW 0.0172 0.0306
         H2_Bar_SW 0.0171 0.0265

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_Sell_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_Sell_feature_importance.csv (ขนาด: 2848 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
           Feature  Importance
EMA_diff_x_BBwidth    0.139284
         Bar_CL_OC    0.112313
      D1_MACD_line    0.059948
      H2_Bar_CL_OC    0.059113
         STO_cross    0.057586
            Bar_SW    0.056455
      H4_MACD_deep    0.037708
         H2_Bar_SW    0.034889
     H12_Bar_CL_OC    0.030717
    EMA_Cross_EMA5    0.029760
    STO_overbought    0.027511
         H8_Bar_SW    0.026855
 H2_Price_Strangth    0.025442
          ADX_Deep    0.024763
        H12_Bar_SW    0.023972

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.99      1.00      1.00     12490
         1.0       0.00      0.00      0.00        87

    accuracy                           0.99     12577
   macro avg       0.50      0.50      0.50     12577
weighted avg       0.99      0.99      0.99     12577

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง trade_df สำหรับ trend_following_Sell: 12577 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following_Sell
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following_Sell/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 358 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following_Sell
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 273766 bytes
✅ เทรน trend_following_Sell สำเร็จ

================================================================================
📊 กำลังเทรน counter_trend สำหรับ Target_Sell...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 68034 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5153/68034 rows (7.6%)
📊 ข้อมูลหลังกรอง counter_trend: 5153 samples

🛠️ Features used for training (Selected: 50 total):
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
📊 Class distribution สำหรับ counter_trend: {0.0: 5055, 1.0: 98}
✅ เตรียมข้อมูล counter_trend: 5153 samples, 50 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 5153 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 5153 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 5055, 1.0: 98}
📈 Train: 3091, Val: 1031, Test: 1031
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 237 → 71805
   Val: 196 → 71803
   Test: 225 → 71794
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 3033, 1.0: 58}
🔄 ใช้ Safe Oversampling System...
📊 Class distribution: {0.0: 3033, 1.0: 58}
📊 Imbalance ratio: 0.019
⚠️ Imbalance รุนแรง (0.019) - ใช้ SMOTE
📊 Original: Counter({0.0: 3033, 1.0: 58})
📊 Balanced: Counter({0.0: 3033, 1.0: 3033})
📊 Train class distribution หลังปรับสมดุล: {0.0: 3033, 1.0: 3033}
📈 จำนวนข้อมูล: เดิม 3091 → หลังปรับสมดุล 6066
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend_Sell (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend_Sell:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Sell_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Sell_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 3033, 1.0: 3033}
   Imbalance ratio: 1.0:1
  🎯 Auto Class Weight: balanced
📊 counter_trend_Sell Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.015 - 0.085 (base: 0.050)
   num_leaves: 12 - 28 (base: 20)
   max_depth: 3 - 7 (base: 5)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ counter_trend_Sell: {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ counter_trend_Sell: 0.9994

--- Features (Columns) ---
['ADX_zone_25', 'Volume_MA_5', 'ADX_Deep', 'Volume_MA20', 'Hour', 'D1_Price_Range', 'Volume_Lag_30', 'Volume_Lag_5', 'Price_Range', 'IsEvening', 'H4_Bar_TL', 'H4_Volume_Momentum', 'H12_Bar_CL_OC', 'H12_Price_Move', 'H2_Bar_SW', 'H12_Price_Range', 'H12_Volume_Momentum', 'H8_Bar_SW', 'IsNight', 'EMA_Cross_EMA5', 'Volume_Change_2', 'H2_Bar_CL_OC', 'Bar_CL_OC', 'STO_overbought', 'BB_Break_HL', 'H8_Volume_Momentum', 'Volume_Lag_10', 'ATR_ROC_i2', 'ATR_x_PriceRange', 'D1_Volume_Momentum', 'EMA_Cross_EMA10', 'STO_cross', 'H4_Bar_longwick', 'H8_Price_Range', 'IsMorning', 'Bar_SW', 'MACD_line_x_PriceMove', 'D1_MACD_line', 'H4_MACD_deep', 'H8_Price_Move', 'H12_Bar_SW', 'H4_Price_Move', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Change_1', 'H2_Bar_OSB', 'H4_Price_Strangth', 'DMN_14_Lag_2', 'EMA_diff_x_BBwidth', 'H2_Bar_longwick']
--- Sample Data (First 5 rows) ---
   ADX_zone_25  Volume_MA_5  ADX_Deep  Volume_MA20  Hour  D1_Price_Range  Volume_Lag_30  ...  H2_Price_Strangth  Volume_Change_1  H2_Bar_OSB  H4_Price_Strangth  DMN_14_Lag_2  EMA_diff_x_BBwidth  H2_Bar_longwick
0            0       4525.0        -1      2768.25    13           12.02          277.0  ...                0.0         0.009396         0.0                0.0     25.578671            0.768822         5.921569
1            0       5675.6        -1      4444.30    16           14.94         5010.0  ...                0.0         0.216795         0.0                0.0     17.313564          -51.698976         0.655296
2            0       1366.2        -1      1482.90    12            7.49         1039.0  ...               -1.0        -0.073109         0.0                0.0     30.885105           15.162078         0.272727
3            1      16774.2        -1      7891.35     1           17.05         5088.0  ...                0.0        -0.986876         0.0                1.0     15.994628          -26.161076        -0.973913
4            0       4310.8         1      2785.90    17           13.06         3662.0  ...                0.0         0.024440        -1.0                1.0     16.682336         -106.110400        -2.841530

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend_Sell
📊 Class distribution: {0.0: 3033, 1.0: 3033}
📊 Imbalance ratio: 1.000
✅ ไม่ต้อง oversample - imbalance ยอมรับได้หรือมีข้อมูลเพียงพอ
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[72]	valid_0's binary_logloss: 0.0881306

Accuracy: 0.9806
Classification Report:
              precision    recall  f1-score   support

         0.0       0.98      1.00      0.99      1011
         1.0       0.00      0.00      0.00        20

    accuracy                           0.98      1031
   macro avg       0.49      0.50      0.50      1031
weighted avg       0.96      0.98      0.97      1031

Confusion Matrix:
[[1011    0]
 [  20    0]]
📊 Test Set Accuracy: 0.9806, F1-Score: 0.9710
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.8119
✅ counter_trend_Sell - Accuracy: 0.981, F1: 0.971, AUC: 0.812

🔍 ตรวจสอบคุณภาพโมเดล counter_trend_Sell...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (1031, 50)
   y_val shape: (1031,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend_Sell)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (1031, 50)
   y_val shape: (1031,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (1031, 2)
   Binary classification detected
   Final y_pred shape: (1031,), unique: [0], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (1031,), unique values: [0. 1.]
   y_pred shape: (1031,), unique values: [0]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification: negative=0.0, positive=1.0
   Binary metrics calculated with pos_label=1.0
   ⚠️ All metrics are 0, trying weighted average...
   Final calculated metrics: Acc=0.9806, F1=0.9710, Prec=0.9616, Rec=0.9806
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.9806
   AUC: 0.8046
   F1: 0.9710
   Precision: 0.9616
   Recall: 0.9806
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 48.84
   Trades: 103
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 244.19
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (counter_trend_Sell) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 19:11:36
📊 โมเดล: GOLD MM60 (counter_trend_Sell)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น - auc: -0.0512 (-5.99%)
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น - auc: -0.0512 (-5.99%)
🏷️ ประเภท: declined
================================================================================

================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (counter_trend_Sell)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ดีขึ้น - auc: -0.0512 (-5.99%)
🏷️ ประเภท: declined
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล counter_trend_Sell - โมเดลไม่ดีขึ้น - auc: -0.0512 (-5.99%)
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น - auc: -0.0512 (-5.99%)
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ counter_trend_Sell
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_Sell_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_Sell_GOLD_M60 symbol GOLD timeframe M60 (counter_trend_Sell)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
           Feature   Gain  Split
         Bar_CL_OC 0.2849 0.0692
       Price_Range 0.0839 0.0402
       BB_Break_HL 0.0789 0.0132
D1_Volume_Momentum 0.0713 0.0349
H8_Volume_Momentum 0.0554 0.0171
         H2_Bar_SW 0.0458 0.0369
   EMA_Cross_EMA10 0.0417 0.0237
    EMA_Cross_EMA5 0.0392 0.0191
      H4_MACD_deep 0.0317 0.0218
      H2_Bar_CL_OC 0.0253 0.0237
    STO_overbought 0.0221 0.0283
         STO_cross 0.0201 0.0270
          ADX_Deep 0.0168 0.0165
            Bar_SW 0.0157 0.0132
        H2_Bar_OSB 0.0154 0.0264

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_counter_trend_Sell_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_counter_trend_Sell_feature_importance.csv (ขนาด: 2804 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
           Feature  Importance
         Bar_CL_OC    0.116972
       Price_Range    0.068393
D1_Volume_Momentum    0.057134
    EMA_Cross_EMA5    0.056374
      H2_Bar_CL_OC    0.046634
         H2_Bar_SW    0.044262
            Bar_SW    0.037787
   EMA_Cross_EMA10    0.037110
      H4_MACD_deep    0.032010
         STO_cross    0.031505
H8_Volume_Momentum    0.031293
       BB_Break_HL    0.028551
     H12_Bar_CL_OC    0.028074
          ADX_Deep    0.024256
           IsNight    0.023935

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.98      1.00      0.99      1011
         1.0       0.00      0.00      0.00        20

    accuracy                           0.98      1031
   macro avg       0.49      0.50      0.50      1031
weighted avg       0.96      0.98      0.97      1031

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend_Sell
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend_Sell
✅ สร้าง trade_df สำหรับ counter_trend_Sell: 1031 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend_Sell
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend_Sell/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 351 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend_Sell
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 270834 bytes
✅ เทรน counter_trend_Sell สำเร็จ

================================================================================

✅ เทรนเสร็จสิ้น: 6 โมเดล
================================================================================
🔍 Debug: ผลลัพธ์การเทรน:
  ✅ trend_following: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ trend_following_Buy: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend_Buy: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ trend_following_Sell: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend_Sell: มีผลลัพธ์
    📊 Feature Importance: True

📊 บันทึก Random Forest Feature Importance

🏗️ เปิดใช้งาน save combined random forest importance
📊 รวบรวม RF Feature Importance จาก trend_following
📊 รวบรวม RF Feature Importance จาก counter_trend
📊 รวบรวม RF Feature Importance จาก trend_following_Buy
📊 รวบรวม RF Feature Importance จาก counter_trend_Buy
📊 รวบรวม RF Feature Importance จาก trend_following_Sell
📊 รวบรวม RF Feature Importance จาก counter_trend_Sell
🔍 Debug: คอลัมน์ใน combined_rf_df: ['Feature', 'Importance', 'Scenario']
💾 บันทึก Random Forest Feature Importance: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv
📊 รวมจาก 6 scenarios, 50 features

🏆 Top 10 Random Forest Features:
  Bar_CL_OC: Importance=0.1070
  EMA_diff_x_BBwidth: Importance=0.0710
  Bar_SW: Importance=0.0572
  H2_Bar_CL_OC: Importance=0.0396
  H4_MACD_deep: Importance=0.0382
  STO_cross: Importance=0.0368
  H12_Bar_CL_OC: Importance=0.0306
  D1_MACD_line: Importance=0.0296
  H2_Bar_SW: Importance=0.0280
  EMA_Cross_EMA5: Importance=0.0262

📊 สร้าง Combined Feature Importance จากทุก scenarios

🏗️ เปิดใช้งาน create combined feature importance
📊 รวบรวม Feature Importance จาก trend_following
📊 รวบรวม Feature Importance จาก counter_trend
📊 รวบรวม Feature Importance จาก trend_following_Buy
📊 รวบรวม Feature Importance จาก counter_trend_Buy
📊 รวบรวม Feature Importance จาก trend_following_Sell
📊 รวบรวม Feature Importance จาก counter_trend_Sell
💾 บันทึก Combined Feature Importance: LightGBM/Multi/results/M60\M60_GOLD_feature_importance.csv
📊 รวมจาก 6 scenarios, 50 features

🏆 Top 10 Features (Combined):
  Bar_CL_OC: Gain=0.2062, Split=0.0507, Count=6
  EMA_diff_x_BBwidth: Gain=0.1313, Split=0.0453, Count=6
  H4_MACD_deep: Gain=0.0556, Split=0.0269, Count=6
  STO_cross: Gain=0.0493, Split=0.0246, Count=6
  Bar_SW: Gain=0.0408, Split=0.0271, Count=6
  ADX_Deep: Gain=0.0314, Split=0.0194, Count=6
  D1_MACD_line: Gain=0.0284, Split=0.0183, Count=6
  D1_Volume_Momentum: Gain=0.0275, Split=0.0210, Count=6
  H2_Bar_CL_OC: Gain=0.0248, Split=0.0163, Count=6
  EMA_Cross_EMA10: Gain=0.0229, Split=0.0181, Count=6

🔍 ทำ Cross-Validation และ Threshold Optimization สำหรับ Multi-Model
📊 ทำ Time Series Cross-Validation...

🏗️ เปิดใช้งาน time series cv
🔁 เริ่มทำ Time Series Cross-Validation (n_splits=5, original=5)
📊 CV Configuration: splits=5, test_size=10261, total_samples=71828

📊 Fold 1/5:
  - Train size: 20523 ตัวอย่าง (28.6% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9066900550601764, 2.0: 0.03483896116552161, 1.0: 0.026019587779564392, 3.0: 0.024996345563514107, 4.0: 0.007455050431223505}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.22058254514187448, 1.0: 7.686516853932584, 2.0: 5.7406993006993, 3.0: 8.001169590643276, 4.0: 26.827450980392157}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 368 trees)
  📊 ผลลัพธ์ Fold 1:
    - Accuracy:  0.8892
    - AUC:       0.8952
    - F1 Score:  0.8815
    - Precision: 0.8756
    - Recall:    0.8892

📊 Fold 2/5:
  - Train size: 30784 ตัวอย่าง (42.9% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.909043659043659, 2.0: 0.034141112266112265, 1.0: 0.025500259875259876, 3.0: 0.02351871101871102, 4.0: 0.007796257796257797}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.22001143510577473, 1.0: 7.843057324840764, 2.0: 5.858039961941008, 3.0: 8.503867403314917, 4.0: 25.653333333333332}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 750 trees)
  📊 ผลลัพธ์ Fold 2:
    - Accuracy:  0.8851
    - AUC:       0.8880
    - F1 Score:  0.8764
    - Precision: 0.8684
    - Recall:    0.8851

📊 Fold 3/5:
  - Train size: 41045 ตัวอย่าง (57.1% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9076379583384091, 2.0: 0.03518089901327811, 3.0: 0.02555731514191741, 1.0: 0.02358387136070167, 4.0: 0.008039956145693751}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.22035217694744189, 1.0: 8.480371900826446, 2.0: 5.684903047091413, 3.0: 7.825548141086749, 4.0: 24.875757575757575}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 917 trees)
  📊 ผลลัพธ์ Fold 3:
    - Accuracy:  0.8819
    - AUC:       0.8989
    - F1 Score:  0.8787
    - Precision: 0.8780
    - Recall:    0.8819

📊 Fold 4/5:
  - Train size: 51306 ตัวอย่าง (71.4% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9068724905469145, 2.0: 0.03498616146259697, 3.0: 0.025727985030990528, 1.0: 0.02481191283670526, 4.0: 0.007601450122792655}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.2205381705639615, 1.0: 8.06064414768264, 2.0: 5.716545961002786, 3.0: 7.7736363636363635, 4.0: 26.310769230769232}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 997 trees)
  📊 ผลลัพธ์ Fold 4:
    - Accuracy:  0.8859
    - AUC:       0.9088
    - F1 Score:  0.8881
    - Precision: 0.8925
    - Recall:    0.8859

📊 Fold 5/5:
  - Train size: 61567 ตัวอย่าง (85.7% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9072392677895625, 2.0: 0.034482758620689655, 3.0: 0.025598128867737588, 1.0: 0.02517582471129014, 4.0: 0.007504020010720028}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.22044901174448583, 1.0: 7.944129032258065, 2.0: 5.8, 3.0: 7.813071065989848, 4.0: 26.652380952380952}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 996 trees)
  📊 ผลลัพธ์ Fold 5:
    - Accuracy:  0.8784
    - AUC:       0.9048
    - F1 Score:  0.8783
    - Precision: 0.8802
    - Recall:    0.8784

📊 สรุปการทำ Time Series CV:
   - จำนวน folds ที่วางแผน: 5
   - จำนวน folds ที่สำเร็จ: 5

✅ การทำ Time Series Cross-Validation เสร็จสมบูรณ์
📌 ผลลัพธ์เฉลี่ย:
  - Accuracy:  0.8841
  - AUC:       0.8991
  - F1 Score:  0.8806
  - Precision: 0.8789
  - Recall:    0.8841
✅ Time Series CV เสร็จสิ้น: AUC=0.899, F1=0.881
🔍 Debug: กำลังบันทึก CV results ที่ LightGBM/Multi/results\multi_scenario_cv_results.json
💾 บันทึก CV Results: LightGBM/Multi/results\multi_scenario_cv_results.json
📁 ขนาดไฟล์: 166 bytes

📊 สร้าง Performance Analysis และ Comparison Plots

🏗️ เปิดใช้งาน create multi scenario performance analysis
🔍 Debug: กำลังบันทึกไฟล์ที่ LightGBM/Multi/results\multi_scenario_performance_analysis.txt
💾 บันทึก Multi-Scenario Performance Analysis: LightGBM/Multi/results\multi_scenario_performance_analysis.txt
📁 ขนาดไฟล์: 1640 bytes

🏗️ เปิดใช้งาน create performance comparison plots
🔍 Debug: สร้างโฟลเดอร์ plots ที่ LightGBM/Multi/results/plots
🔍 Debug: จำนวน scenarios: 6
🔍 Debug: scenarios: ['trend_following', 'counter_trend', 'trend_following_Buy', 'counter_trend_Buy', 'trend_following_Sell', 'counter_trend_Sell']
🔍 Debug: metrics data: {'AUC': [0.9018918928467085, 0.8071977715649311, 0.9154649031949645, 0.8608022519352567, 0.929855608624831, 0.8119188921859546], 'F1_Score': [0.8917678896315042, 0.7267246474600223, 0.9891997610878993, 0.9703431521409049, 0.9899825695907063, 0.9709970351047024], 'Accuracy': [0.9074943224829675, 0.7750865051903114, 0.9923265564433194, 0.9777992277992278, 0.9931621213325912, 0.9806013579049466]}
🔍 Debug: สร้างกราฟสำหรับ AUC
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_auc_comparison.png
💾 บันทึก AUC Comparison Plot: LightGBM/Multi/results/plots\performance_auc_comparison.png
📁 ขนาดไฟล์: 167213 bytes
🔍 Debug: สร้างกราฟสำหรับ F1_Score
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_f1_score_comparison.png
💾 บันทึก F1_Score Comparison Plot: LightGBM/Multi/results/plots\performance_f1_score_comparison.png
📁 ขนาดไฟล์: 169050 bytes
🔍 Debug: สร้างกราฟสำหรับ Accuracy
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_accuracy_comparison.png
💾 บันทึก Accuracy Comparison Plot: LightGBM/Multi/results/plots\performance_accuracy_comparison.png
📁 ขนาดไฟล์: 172725 bytes
🔍 Debug: สร้างกราฟรวมทุก metrics
🔍 Debug: กำลังบันทึกกราฟรวมที่ LightGBM/Multi/results/plots\performance_combined_comparison.png
💾 บันทึก Combined Performance Comparison Plot: LightGBM/Multi/results/plots\performance_combined_comparison.png
📁 ขนาดไฟล์: 265263 bytes

🏗️ เปิดใช้งาน create final and training results
💾 บันทึก Final Results: LightGBM/Multi/results/trend_following\final_results.csv
💾 บันทึก Training Results: LightGBM/Multi/results/trend_following\training_results.csv

🏗️ เปิดใช้งาน create group feature importance comparison
💾 บันทึก Feature Importance Comparison: LightGBM/Multi/results/trend_following\M60_GOLD_feature_importance_comparison.csv

🏗️ เปิดใช้งาน create feature importance comparison plot
💾 บันทึก Feature Importance Comparison Plot: LightGBM/Multi/results/trend_following\M60_GOLD_feature_importance_comparison.png
💾 บันทึก Final Results: LightGBM/Multi/results/counter_trend\final_results.csv
💾 บันทึก Training Results: LightGBM/Multi/results/counter_trend\training_results.csv

🏗️ เปิดใช้งาน create group feature importance comparison
💾 บันทึก Feature Importance Comparison: LightGBM/Multi/results/counter_trend\M60_GOLD_feature_importance_comparison.csv

🏗️ เปิดใช้งาน create feature importance comparison plot
💾 บันทึก Feature Importance Comparison Plot: LightGBM/Multi/results/counter_trend\M60_GOLD_feature_importance_comparison.png

🏗️ เริ่มบันทึกผลลัพธ์ลงระบบสรุป Multi-Model
🔍 Debug: จำนวน results = 6
🔍 Debug: ตรวจสอบ trend_following
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 13210
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ trend_following
   📊 Test stats: {'buy': {'count': 418, 'win_rate': 62.44, 'expectancy': 4.93}, 'sell': {'count': 12792, 'win_rate': 62.27, 'expectancy': 4.75}, 'buy_sell': {'count': 13210, 'win_rate': 62.27, 'expectancy': 4.76}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9074943224829675, 'auc': 0.9018918928467085, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=trend_following
🔍 Debug: train_val_stats={'buy': {'count': 418, 'win_rate': 62.44, 'expectancy': 4.93}, 'sell': {'count': 12792, 'win_rate': 62.27, 'expectancy': 4.75}, 'buy_sell': {'count': 13210, 'win_rate': 62.27, 'expectancy': 4.76}}
🔍 Debug: test_stats={'buy': {'count': 418, 'win_rate': 62.44, 'expectancy': 4.93}, 'sell': {'count': 12792, 'win_rate': 62.27, 'expectancy': 4.75}, 'buy_sell': {'count': 13210, 'win_rate': 62.27, 'expectancy': 4.76}}
🔍 Debug: model_metrics={'accuracy': 0.9074943224829675, 'auc': 0.9018918928467085, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 84.0/100
📈 Win Rate เฉลี่ย: 62.3%, Expectancy เฉลี่ย: 4.76
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 84.0)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 84.04
✅ บันทึกสรุปสำหรับ trend_following เรียบร้อย
🔍 Debug: ตรวจสอบ counter_trend
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 1156
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ counter_trend
   📊 Test stats: {'buy': {'count': 51, 'win_rate': 60.78, 'expectancy': 5.64}, 'sell': {'count': 1105, 'win_rate': 60.0, 'expectancy': 4.65}, 'buy_sell': {'count': 1156, 'win_rate': 60.03, 'expectancy': 4.69}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.7750865051903114, 'auc': 0.8071977715649311, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=counter_trend
🔍 Debug: train_val_stats={'buy': {'count': 51, 'win_rate': 60.78, 'expectancy': 5.64}, 'sell': {'count': 1105, 'win_rate': 60.0, 'expectancy': 4.65}, 'buy_sell': {'count': 1156, 'win_rate': 60.03, 'expectancy': 4.69}}
🔍 Debug: test_stats={'buy': {'count': 51, 'win_rate': 60.78, 'expectancy': 5.64}, 'sell': {'count': 1105, 'win_rate': 60.0, 'expectancy': 4.65}, 'buy_sell': {'count': 1156, 'win_rate': 60.03, 'expectancy': 4.69}}
🔍 Debug: model_metrics={'accuracy': 0.7750865051903114, 'auc': 0.8071977715649311, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 81.2/100
📈 Win Rate เฉลี่ย: 60.0%, Expectancy เฉลี่ย: 4.69
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 81.2)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 81.25
✅ บันทึกสรุปสำหรับ counter_trend เรียบร้อย
🔍 Debug: ตรวจสอบ trend_following_Buy
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 12641
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ trend_following_Buy
   📊 Test stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 12641, 'win_rate': 62.34, 'expectancy': 4.7}, 'buy_sell': {'count': 12641, 'win_rate': 62.34, 'expectancy': 4.7}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9923265564433194, 'auc': 0.9154649031949645, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=trend_following_Buy
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 12641, 'win_rate': 62.34, 'expectancy': 4.7}, 'buy_sell': {'count': 12641, 'win_rate': 62.34, 'expectancy': 4.7}}
🔍 Debug: test_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 12641, 'win_rate': 62.34, 'expectancy': 4.7}, 'buy_sell': {'count': 12641, 'win_rate': 62.34, 'expectancy': 4.7}}
🔍 Debug: model_metrics={'accuracy': 0.9923265564433194, 'auc': 0.9154649031949645, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 80.2/100
📈 Win Rate เฉลี่ย: 62.3%, Expectancy เฉลี่ย: 4.70
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 80.2)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 80.16
✅ บันทึกสรุปสำหรับ trend_following_Buy เรียบร้อย
🔍 Debug: ตรวจสอบ counter_trend_Buy
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 1036
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ counter_trend_Buy
   📊 Test stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1036, 'win_rate': 62.16, 'expectancy': 4.75}, 'buy_sell': {'count': 1036, 'win_rate': 62.16, 'expectancy': 4.75}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9777992277992278, 'auc': 0.8608022519352567, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=counter_trend_Buy
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1036, 'win_rate': 62.16, 'expectancy': 4.75}, 'buy_sell': {'count': 1036, 'win_rate': 62.16, 'expectancy': 4.75}}
🔍 Debug: test_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1036, 'win_rate': 62.16, 'expectancy': 4.75}, 'buy_sell': {'count': 1036, 'win_rate': 62.16, 'expectancy': 4.75}}
🔍 Debug: model_metrics={'accuracy': 0.9777992277992278, 'auc': 0.8608022519352567, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 79.5/100
📈 Win Rate เฉลี่ย: 62.2%, Expectancy เฉลี่ย: 4.75
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 79.5)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 79.47
✅ บันทึกสรุปสำหรับ counter_trend_Buy เรียบร้อย
🔍 Debug: ตรวจสอบ trend_following_Sell
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 12577
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ trend_following_Sell
   📊 Test stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 12577, 'win_rate': 62.92, 'expectancy': 4.89}, 'buy_sell': {'count': 12577, 'win_rate': 62.92, 'expectancy': 4.89}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9931621213325912, 'auc': 0.929855608624831, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=trend_following_Sell
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 12577, 'win_rate': 62.92, 'expectancy': 4.89}, 'buy_sell': {'count': 12577, 'win_rate': 62.92, 'expectancy': 4.89}}
🔍 Debug: test_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 12577, 'win_rate': 62.92, 'expectancy': 4.89}, 'buy_sell': {'count': 12577, 'win_rate': 62.92, 'expectancy': 4.89}}
🔍 Debug: model_metrics={'accuracy': 0.9931621213325912, 'auc': 0.929855608624831, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 80.5/100
📈 Win Rate เฉลี่ย: 62.9%, Expectancy เฉลี่ย: 4.89
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 80.5)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 80.55
✅ บันทึกสรุปสำหรับ trend_following_Sell เรียบร้อย
🔍 Debug: ตรวจสอบ counter_trend_Sell
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 1031
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ counter_trend_Sell
   📊 Test stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1031, 'win_rate': 63.92, 'expectancy': 5.46}, 'buy_sell': {'count': 1031, 'win_rate': 63.92, 'expectancy': 5.46}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9806013579049466, 'auc': 0.8119188921859546, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=counter_trend_Sell
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1031, 'win_rate': 63.92, 'expectancy': 5.46}, 'buy_sell': {'count': 1031, 'win_rate': 63.92, 'expectancy': 5.46}}
🔍 Debug: test_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1031, 'win_rate': 63.92, 'expectancy': 5.46}, 'buy_sell': {'count': 1031, 'win_rate': 63.92, 'expectancy': 5.46}}
🔍 Debug: model_metrics={'accuracy': 0.9806013579049466, 'auc': 0.8119188921859546, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 50, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 79.8/100
📈 Win Rate เฉลี่ย: 63.9%, Expectancy เฉลี่ย: 5.46
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 79.8)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 79.84
✅ บันทึกสรุปสำหรับ counter_trend_Sell เรียบร้อย
📊 บันทึกสรุปเสร็จสิ้น: 6/6 scenarios
✅ เทรนโมเดลสำเร็จ: 6 scenarios
🔍 Debug Multi-Model: จำนวน scenarios = 6
🔍 Debug Scenario 'trend_following': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'trend_following' metrics: {'accuracy': 0.9074943224829675, 'f1_score': 0.8917678896315042, 'auc': 0.9018918928467085, 'train_samples': 181400, 'test_samples': 13210, 'scenario': 'trend_following'}
🔍 Debug Scenario 'trend_following' cv_results: {'best_score': 0.8066794903961417, 'best_params': {'reg_lambda': 0.0, 'reg_alpha': 0.005, 'num_leaves': 22, 'min_data_in_leaf': 10, 'max_depth': 7, 'learning_rate': 0.035, 'feature_fraction': 0.84, 'bagging_freq': 1, 'bagging_fraction': 0.87}, 'scenario': 'trend_following'}
🔍 Debug Scenario 'counter_trend': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'counter_trend' metrics: {'accuracy': 0.7750865051903114, 'f1_score': 0.7267246474600223, 'auc': 0.8071977715649311, 'train_samples': 13960, 'test_samples': 1156, 'scenario': 'counter_trend'}
🔍 Debug Scenario 'counter_trend' cv_results: {'best_score': 0.9633080534488986, 'best_params': {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 42, 'min_data_in_leaf': 8, 'max_depth': 7, 'learning_rate': 0.1, 'feature_fraction': 0.88, 'bagging_freq': 1, 'bagging_fraction': 0.8}, 'scenario': 'counter_trend'}
🔍 Debug Scenario 'trend_following_Buy': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'trend_following_Buy' metrics: {'accuracy': 0.9923265564433194, 'f1_score': 0.9891997610878993, 'auc': 0.9154649031949645, 'train_samples': 75302, 'test_samples': 12641, 'scenario': 'trend_following_Buy'}
🔍 Debug Scenario 'trend_following_Buy' cv_results: {'best_score': 0.9996149314530852, 'best_params': {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}, 'scenario': 'trend_following_Buy'}
🔍 Debug Scenario 'counter_trend_Buy': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'counter_trend_Buy' metrics: {'accuracy': 0.9777992277992278, 'f1_score': 0.9703431521409049, 'auc': 0.8608022519352567, 'train_samples': 6088, 'test_samples': 1036, 'scenario': 'counter_trend_Buy'}
🔍 Debug Scenario 'counter_trend_Buy' cv_results: {'best_score': 0.9993118749850698, 'best_params': {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}, 'scenario': 'counter_trend_Buy'}
🔍 Debug Scenario 'trend_following_Sell': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'trend_following_Sell' metrics: {'accuracy': 0.9931621213325912, 'f1_score': 0.9899825695907063, 'auc': 0.929855608624831, 'train_samples': 74936, 'test_samples': 12577, 'scenario': 'trend_following_Sell'}
🔍 Debug Scenario 'trend_following_Sell' cv_results: {'best_score': 0.9995765839588957, 'best_params': {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 30, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 3, 'bagging_fraction': 0.9}, 'scenario': 'trend_following_Sell'}
🔍 Debug Scenario 'counter_trend_Sell': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'counter_trend_Sell' metrics: {'accuracy': 0.9806013579049466, 'f1_score': 0.9709970351047024, 'auc': 0.8119188921859546, 'train_samples': 6066, 'test_samples': 1031, 'scenario': 'counter_trend_Sell'}
🔍 Debug Scenario 'counter_trend_Sell' cv_results: {'best_score': 0.9993665820276701, 'best_params': {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}, 'scenario': 'counter_trend_Sell'}
🔍 Debug avg_metrics['accuracy'] = 0.9377 (จาก 6 scenarios)
🔍 Debug avg_metrics['f1_score'] = 0.9232 (จาก 6 scenarios)
🔍 Debug avg_metrics['auc'] = 0.8712 (จาก 6 scenarios)
🔍 Debug avg_metrics['train_samples'] = 59625.3333 (จาก 6 scenarios)
🔍 Debug avg_metrics['test_samples'] = 6941.8333 (จาก 6 scenarios)
⚠️ ไม่มีค่าตัวเลขสำหรับ scenario: ['trend_following', 'counter_trend', 'trend_following_Buy', 'counter_trend_Buy', 'trend_following_Sell', 'counter_trend_Sell']
🔍 Debug avg_cv_results['best_score'] = 0.9613 (จาก 6 scenarios)
⚠️ ไม่มีค่าตัวเลขสำหรับ best_params: [{'reg_lambda': 0.0, 'reg_alpha': 0.005, 'num_leaves': 22, 'min_data_in_leaf': 10, 'max_depth': 7, 'learning_rate': 0.035, 'feature_fraction': 0.84, 'bagging_freq': 1, 'bagging_fraction': 0.87}, {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 42, 'min_data_in_leaf': 8, 'max_depth': 7, 'learning_rate': 0.1, 'feature_fraction': 0.88, 'bagging_freq': 1, 'bagging_fraction': 0.8}, {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}, {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}, {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 30, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 3, 'bagging_fraction': 0.9}, {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}]
⚠️ ไม่มีค่าตัวเลขสำหรับ scenario: ['trend_following', 'counter_trend', 'trend_following_Buy', 'counter_trend_Buy', 'trend_following_Sell', 'counter_trend_Sell']
🔍 Debug Final avg_metrics: {'accuracy': 0.9377450151922272, 'f1_score': 0.9231691758359566, 'auc': 0.8711885533921078, 'train_samples': 59625.333333333336, 'test_samples': 6941.833333333333}
🔍 Debug Final avg_cv_results: {'best_score': 0.9613095860449602}

==================================================
🎯 เริ่มทดสอบ Optimal Parameters
==================================================
🔍 Debug validation data:
   - X_val shape: (461, 50)
   - X_val index range: 3843 - 5623
   - combined_df shape: (71828, 342)
   - combined_df index range: 0 - 71827
✅ พบ indices ที่ตรงกัน: 461 จาก 461
📊 ข้อมูล validation สำหรับ optimization:
   - จำนวน samples: 461
   - จำนวน features: 50
   - Index range: 3843 - 5623
✅ ข้อมูล validation เพียงพอสำหรับการทดสอบ (461 samples)

💾 การบันทึก Artifacts สำหรับการทดสอบแบบ Offline...
✅ บันทึก Models Artifact สำเร็จที่: LightGBM/Data_Trained\M60_GOLD_scenario_results.pkl
✅ บันทึก Validation Set Artifact สำเร็จที่: LightGBM/Data_Trained\M60_GOLD_validation_set.csv
⚠️ ไม่พบโมเดล trend_following: LightGBM/Multi/models/trend_following/M60_GOLD_trained.pkl
⚠️ ไม่พบโมเดล trend_following_Buy: LightGBM/Multi/models/trend_following_Buy/M60_GOLD_trained.pkl
⚠️ ไม่พบโมเดล counter_trend_Buy: LightGBM/Multi/models/counter_trend_Buy/M60_GOLD_trained.pkl
⚠️ ไม่พบโมเดล trend_following_Sell: LightGBM/Multi/models/trend_following_Sell/M60_GOLD_trained.pkl
⚠️ ไม่พบโมเดล counter_trend_Sell: LightGBM/Multi/models/counter_trend_Sell/M60_GOLD_trained.pkl
📋 จะทดสอบ 1 scenarios: ['counter_trend']

📌 Running scenario: counter_trend
🏗️ เปิดใช้งาน find optimal threshold multi model (Enhanced)

======================================================================
🎯 เริ่มการหา Optimal Threshold
📊 วิธีการ: YOUDEN
======================================================================
📊 จำนวนข้อมูล: 461 samples
📊 Positive samples: 6.0 (1.3%)
🎯 Min threshold: 0.3
เทียบค่า threshold : 0.0160 / 0.6500-0.3000

📈 ผลลัพธ์สุดท้าย:
   🎯 Best Threshold: 0.3000
   📊 Accuracy: 0.9393
   📊 Precision: 0.0000
   📊 Recall: 0.0000
   📊 F1 Score: 0.0000
   📊 ROC AUC: 0.6894
   🔧 Method: youden
Best threshold: 0.3
Metrics: {'Accuracy': 0.9392624728850325, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': 0.6893772893772894}
💾 Saved: LightGBM/Multi/thresholds/M60_GOLD_counter_trend_optimal_threshold.pkl

🔍 ทดสอบการเรียกใช้งาน threshold
⚠️ trend_data is None หรือไม่มี best_threshold - ใช้ค่า default
⚠️ counter_data is None หรือไม่มี best_threshold - ใช้ค่า default
Best threshold: 0.3
Metrics: None
⚠️ counter_all_th_df is None - ไม่มีข้อมูล threshold

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ GOLD MM60, ใช้ค่า default: 0.3

✅ symbol GOLD M60 : threshold 0.3 : trend 0.3000 counter 0.3000

🎯 ทดสอบ Optimal nBars SL...
🏗️ เปิดใช้งาน find optimal nbars sl multi model (Enhanced)

======================================================================
🎯 เริ่มการทดสอบ Optimal nBars_SL สำหรับ GOLD MM60
🚀 ใช้ Enhanced Optimal Parameter Finder
======================================================================
📊 ข้อมูล validation: 461 samples
🔧 จำนวน scenarios: 6
🎯 nBars_SL เริ่มต้น: 4

🔍 ทดสอบ Optimal nBars_SL สำหรับ trend_following
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 10
📋 nBars_SL เดิม: 10
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for trend_following...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 10
   📊 Best Score: 0.0024
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ trend_following:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.002    0.769                  
2        0.002    0.606                  
2        0.002    0.636                  
2        0.002    0.462                  
2        0.002    0.800                  
2        0.002    0.571                  
3        0.002    0.523                  
3        0.002    0.533                  
3        0.002    0.360                  
3        0.002    0.720                  
3        0.002    0.368                  
4        0.002    0.698                  
4        0.002    0.690                  
4        0.002    0.310                  
6        0.002    0.530                  
6        0.002    0.710                  
7        0.002    0.508                  
7        0.002    0.774                  
7        0.002    0.337                  
7        0.002    0.469                  
9        0.002    0.312                  
9        0.002    0.537                  
9        0.002    0.737                  
10       0.002    0.690        🏆 BEST    
10       0.002    0.371        🏆 BEST    
10       0.002    0.796        🏆 BEST    
10       0.002    0.779        🏆 BEST    
10       0.002    0.609        🏆 BEST    
10       0.002    0.658        🏆 BEST    
10       0.002    0.417        🏆 BEST    
10       0.002    0.451        🏆 BEST    
10       0.002    0.471        🏆 BEST    
11       0.002    0.328                  
11       0.002    0.662                  
12       0.002    0.500                  
12       0.002    0.569                  
12       0.002    0.466                  
12       0.002    0.791                  
13       0.002    0.362                  
13       0.002    0.300                  
13       0.002    0.734                  
13       0.002    0.709                  
13       0.002    0.321                  
13       0.002    0.702                  
14       0.002    0.553                  
14       0.002    0.586                  
15       0.002    0.750                  
15       0.002    0.737                  
15       0.002    0.633                  
15       0.002    0.522                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 10
   🔸 nBars_SL ใหม่: 10
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0024
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Trend Following: nBars_SL = 10 เหมาะสำหรับการติดตาม trend
   📈 SL ห่าง - เหมาะกับ strong trend, ลด whipsaw
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ trend_following: 10
📁 ไฟล์: M60_GOLD_trend_following_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ trend_following: 10

🔍 ทดสอบ Optimal nBars_SL สำหรับ counter_trend
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend: 10
📋 nBars_SL เดิม: 10
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for counter_trend...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 10
   📊 Best Score: 0.0025
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ counter_trend:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.002    0.769                  
2        0.002    0.606                  
2        0.002    0.426                  
2        0.002    0.452                  
2        0.002    0.554                  
2        0.002    0.420                  
3        0.002    0.523                  
3        0.002    0.533                  
3        0.002    0.396                  
3        0.002    0.568                  
3        0.002    0.482                  
3        0.002    0.360                  
3        0.002    0.720                  
3        0.002    0.368                  
4        0.002    0.698                  
4        0.002    0.690                  
4        0.002    0.310                  
6        0.002    0.530                  
6        0.002    0.710                  
7        0.002    0.636                  
7        0.002    0.508                  
7        0.002    0.774                  
7        0.002    0.337                  
9        0.002    0.312                  
9        0.002    0.537                  
9        0.002    0.737                  
10       0.002    0.690        🏆 BEST    
10       0.002    0.371        🏆 BEST    
10       0.002    0.796        🏆 BEST    
10       0.002    0.779        🏆 BEST    
10       0.002    0.609        🏆 BEST    
10       0.002    0.658        🏆 BEST    
10       0.002    0.417        🏆 BEST    
10       0.002    0.451        🏆 BEST    
10       0.002    0.471        🏆 BEST    
11       0.002    0.328                  
11       0.002    0.662                  
12       0.002    0.500                  
12       0.002    0.466                  
12       0.002    0.791                  
13       0.002    0.362                  
13       0.002    0.300                  
13       0.002    0.709                  
13       0.002    0.739                  
13       0.002    0.321                  
13       0.002    0.702                  
13       0.002    0.585                  
14       0.002    0.439                  
15       0.002    0.737                  
15       0.002    0.421                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 10
   🔸 nBars_SL ใหม่: 10
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0025
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 10 เหมาะสำหรับการเทรด reversal
   🎯 SL ห่าง - รอ reversal ที่แข็งแกร่งกว่า
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ counter_trend: 10
📁 ไฟล์: M60_GOLD_counter_trend_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ counter_trend: 10

🔍 ทดสอบ Optimal nBars_SL สำหรับ trend_following_Buy
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following_Buy: 11
📋 nBars_SL เดิม: 11
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for trend_following_Buy...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 11
   📊 Best Score: 0.0024
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ trend_following_Buy:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.002    0.769                  
2        0.002    0.606                  
2        0.002    0.322                  
2        0.002    0.316                  
2        0.002    0.301                  
2        0.002    0.307                  
2        0.002    0.318                  
3        0.002    0.523                  
3        0.002    0.533                  
3        0.002    0.360                  
3        0.002    0.720                  
3        0.002    0.368                  
4        0.002    0.698                  
4        0.002    0.690                  
4        0.002    0.310                  
4        0.002    0.326                  
6        0.002    0.530                  
6        0.002    0.710                  
7        0.002    0.508                  
7        0.002    0.774                  
7        0.002    0.337                  
8        0.002    0.572                  
9        0.002    0.312                  
9        0.002    0.537                  
9        0.002    0.737                  
10       0.002    0.690                  
10       0.002    0.371                  
10       0.002    0.796                  
10       0.002    0.779                  
10       0.002    0.609                  
10       0.002    0.658                  
10       0.002    0.417                  
10       0.002    0.451                  
10       0.002    0.471                  
11       0.002    0.328        🏆 BEST    
11       0.002    0.662        🏆 BEST    
12       0.002    0.500                  
12       0.002    0.466                  
12       0.002    0.791                  
13       0.002    0.362                  
13       0.002    0.300                  
13       0.002    0.709                  
13       0.002    0.321                  
13       0.002    0.702                  
14       0.002    0.325                  
15       0.002    0.304                  
15       0.002    0.737                  
15       0.002    0.314                  
15       0.002    0.308                  
15       0.002    0.327                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 11
   🔸 nBars_SL ใหม่: 11
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0024
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 11 เหมาะสำหรับการเทรด reversal
   🎯 SL ห่าง - รอ reversal ที่แข็งแกร่งกว่า
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ trend_following_Buy: 11
📁 ไฟล์: M60_GOLD_trend_following_Buy_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ trend_following_Buy: 11

🔍 ทดสอบ Optimal nBars_SL สำหรับ counter_trend_Buy
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend_Buy: 13
📋 nBars_SL เดิม: 13
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for counter_trend_Buy...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 13
   📊 Best Score: 0.0024
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ counter_trend_Buy:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.002    0.769                  
2        0.002    0.606                  
2        0.002    0.300                  
3        0.002    0.523                  
3        0.002    0.533                  
3        0.002    0.301                  
3        0.002    0.300                  
3        0.002    0.360                  
3        0.002    0.300                  
3        0.002    0.720                  
3        0.002    0.368                  
4        0.002    0.698                  
4        0.002    0.690                  
4        0.002    0.310                  
5        0.002    0.300                  
6        0.002    0.530                  
6        0.002    0.300                  
6        0.002    0.710                  
7        0.002    0.508                  
7        0.002    0.774                  
7        0.002    0.337                  
9        0.002    0.312                  
9        0.002    0.537                  
9        0.002    0.737                  
10       0.002    0.690                  
10       0.002    0.371                  
10       0.002    0.796                  
10       0.002    0.779                  
10       0.002    0.609                  
10       0.002    0.658                  
10       0.002    0.417                  
10       0.002    0.451                  
10       0.002    0.471                  
11       0.002    0.328                  
11       0.002    0.662                  
12       0.002    0.500                  
12       0.002    0.466                  
12       0.002    0.791                  
12       0.002    0.300                  
13       0.002    0.362        🏆 BEST    
13       0.002    0.300        🏆 BEST    
13       0.002    0.709        🏆 BEST    
13       0.002    0.321        🏆 BEST    
13       0.002    0.702        🏆 BEST    
13       0.002    0.300        🏆 BEST    
14       0.002    0.300                  
14       0.002    0.301                  
15       0.002    0.300                  
15       0.002    0.737                  
15       0.002    0.301                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 13
   🔸 nBars_SL ใหม่: 13
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0024
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 13 เหมาะสำหรับการเทรด reversal
   🎯 SL ห่าง - รอ reversal ที่แข็งแกร่งกว่า
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ counter_trend_Buy: 13
📁 ไฟล์: M60_GOLD_counter_trend_Buy_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ counter_trend_Buy: 13

🔍 ทดสอบ Optimal nBars_SL สำหรับ trend_following_Sell
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following_Sell: 10
📋 nBars_SL เดิม: 10
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for trend_following_Sell...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 10
   📊 Best Score: 0.0024
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ trend_following_Sell:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.002    0.769                  
2        0.002    0.606                  
2        0.002    0.356                  
2        0.002    0.303                  
2        0.002    0.331                  
2        0.002    0.369                  
2        0.002    0.317                  
3        0.002    0.523                  
3        0.002    0.533                  
3        0.002    0.360                  
3        0.002    0.720                  
3        0.002    0.368                  
4        0.002    0.698                  
4        0.002    0.690                  
4        0.002    0.310                  
4        0.002    0.342                  
4        0.002    0.367                  
6        0.002    0.530                  
6        0.002    0.710                  
7        0.002    0.508                  
7        0.002    0.774                  
7        0.002    0.337                  
9        0.002    0.312                  
9        0.002    0.537                  
9        0.002    0.737                  
10       0.002    0.690        🏆 BEST    
10       0.002    0.371        🏆 BEST    
10       0.002    0.796        🏆 BEST    
10       0.002    0.779        🏆 BEST    
10       0.002    0.609        🏆 BEST    
10       0.002    0.658        🏆 BEST    
10       0.002    0.417        🏆 BEST    
10       0.002    0.451        🏆 BEST    
10       0.002    0.471        🏆 BEST    
11       0.002    0.328                  
11       0.002    0.662                  
12       0.002    0.500                  
12       0.002    0.466                  
12       0.002    0.791                  
13       0.002    0.362                  
13       0.002    0.300                  
13       0.002    0.709                  
13       0.002    0.321                  
13       0.002    0.702                  
14       0.002    0.350                  
15       0.002    0.336                  
15       0.002    0.320                  
15       0.002    0.368                  
15       0.002    0.305                  
15       0.002    0.737                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 10
   🔸 nBars_SL ใหม่: 10
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0024
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 10 เหมาะสำหรับการเทรด reversal
   🎯 SL ห่าง - รอ reversal ที่แข็งแกร่งกว่า
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ trend_following_Sell: 10
📁 ไฟล์: M60_GOLD_trend_following_Sell_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ trend_following_Sell: 10

🔍 ทดสอบ Optimal nBars_SL สำหรับ counter_trend_Sell
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend_Sell: 4
📋 nBars_SL เดิม: 4
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for counter_trend_Sell...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 4
   📊 Best Score: 0.0024
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ counter_trend_Sell:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.002    0.769                  
2        0.002    0.606                  
2        0.002    0.300                  
2        0.002    0.336                  
2        0.002    0.321                  
3        0.002    0.523                  
3        0.002    0.533                  
3        0.002    0.300                  
3        0.002    0.300                  
3        0.002    0.360                  
3        0.002    0.720                  
3        0.002    0.368                  
4        0.002    0.698        🏆 BEST    
4        0.002    0.690        🏆 BEST    
4        0.002    0.310        🏆 BEST    
5        0.002    0.300                  
6        0.002    0.530                  
6        0.002    0.710                  
6        0.002    0.300                  
7        0.002    0.508                  
7        0.002    0.774                  
7        0.002    0.337                  
9        0.002    0.312                  
9        0.002    0.537                  
9        0.002    0.737                  
9        0.002    0.345                  
10       0.002    0.690                  
10       0.002    0.371                  
10       0.002    0.796                  
10       0.002    0.779                  
10       0.002    0.609                  
10       0.002    0.658                  
10       0.002    0.417                  
10       0.002    0.451                  
10       0.002    0.471                  
11       0.002    0.328                  
11       0.002    0.662                  
12       0.002    0.500                  
12       0.002    0.466                  
12       0.002    0.791                  
12       0.002    0.761                  
13       0.002    0.362                  
13       0.002    0.300                  
13       0.002    0.709                  
13       0.002    0.300                  
13       0.002    0.321                  
13       0.002    0.702                  
14       0.002    0.797                  
14       0.002    0.300                  
15       0.002    0.737                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 4
   🔸 nBars_SL ใหม่: 4
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0024
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 4 เหมาะสำหรับการเทรด reversal
   ⚡ SL ใกล้ - เหมาะกับ quick reversal, cut loss เร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ counter_trend_Sell: 4
📁 ไฟล์: M60_GOLD_counter_trend_Sell_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ counter_trend_Sell: 4

======================================================================
📋 สรุปผลการทดสอบ Optimal nBars_SL สำหรับ GOLD MM60
======================================================================
🔸 trend_following:
   nBars_SL: 10 → 10 (+0 bars)
   Best Score: 0.0024
🔸 counter_trend:
   nBars_SL: 10 → 10 (+0 bars)
   Best Score: 0.0025
🔸 trend_following_Buy:
   nBars_SL: 11 → 11 (+0 bars)
   Best Score: 0.0024
🔸 counter_trend_Buy:
   nBars_SL: 13 → 13 (+0 bars)
   Best Score: 0.0024
🔸 trend_following_Sell:
   nBars_SL: 10 → 10 (+0 bars)
   Best Score: 0.0024
🔸 counter_trend_Sell:
   nBars_SL: 4 → 4 (+0 bars)
   Best Score: 0.0024

💡 คำแนะนำการใช้งาน:
   🔄 Trend Following (10 bars): เหมาะกับ strong trend, ระวัง drawdown
   ⚡ Counter Trend (10 bars): รอ strong reversal, ระวัง false signal
   ⚡ Counter Trend (11 bars): รอ strong reversal, ระวัง false signal
   ⚡ Counter Trend (13 bars): รอ strong reversal, ระวัง false signal
   ⚡ Counter Trend (10 bars): รอ strong reversal, ระวัง false signal
   ⚡ Counter Trend (4 bars): เน้น quick exit, เหมาะกับ scalping

✅ การทดสอบ Optimal nBars_SL เสร็จสิ้น
💾 บันทึกผลลัพธ์แล้ว: 6 scenarios
✅ ผลการทดสอบ Optimal nBars SL:
   - trend_following: 10
   - counter_trend: 10
   - trend_following_Buy: 11
   - counter_trend_Buy: 13
   - trend_following_Sell: 10
   - counter_trend_Sell: 4
✅ ทดสอบ Optimal Parameters เสร็จสิ้น

✅ ข้อมูล df และ trade_df หลังจาก train and evaluate
จำนวน columns ใน df: 332
จำนวน columns ใน trade_df: 338

[INFO] จำนวน Features หลัง train and evaluate (fallback): 50
🔍 Debug ไฟล์ CSV_Files_Fixed/GOLD_H1_FIXED.csv:
   metrics: {'accuracy': 0.9377450151922272, 'f1_score': 0.9231691758359566, 'auc': 0.8711885533921078, 'train_samples': 59625.333333333336, 'test_samples': 6941.833333333333}
   cv_results: {'best_score': 0.9613095860449602}

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.0000    | 0.9377 |
| AUC         | 0.5000    | 0.8712 |
| F1 Score    | 0.0000    | 0.0000 |

🔍 ตรวจสอบเงื่อนไข Performance Tracking:
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
   training_success: True
   เงื่อนไขรวม: True
✅ เข้าเงื่อนไข Performance Tracking - จะบันทึกไฟล์
🔍 Debug metrics from result_dict: {'accuracy': 0.9377450151922272, 'f1_score': 0.9231691758359566, 'auc': 0.8711885533921078, 'train_samples': 59625.333333333336, 'test_samples': 6941.833333333333}
🔍 Debug cv_results from result_dict: {'best_score': 0.9613095860449602}
🔍 Final model_metrics: {'avg_accuracy': 0.9377450151922272, 'avg_f1_score': 0.5, 'avg_auc': 0.8711885533921078, 'total_train_samples': 1389, 'total_test_samples': 461}
⚠️ trend_data is None หรือไม่มี best_threshold - ใช้ค่า default
⚠️ counter_data is None หรือไม่มี best_threshold - ใช้ค่า default

🏗️ เปิดใช้งาน load time filters
กำลังโหลด time filters จาก: LightGBM/Multi/thresholds/M60_GOLD_time_filters.pkl
✅ โหลด time filters สำเร็จ (M60_GOLD)
🔍 Debug loaded time_filters: {'days': [], 'hours': [], 'detailed_stats': {'days': {'Monday': {'win_rate': 0.0, 'expectancy': 164.93577981651333, 'total_trades': 109.0, 'day_index': 0, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Tuesday': {'win_rate': 0.0, 'expectancy': 234.96330275229263, 'total_trades': 109.0, 'day_index': 1, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Wednesday': {'win_rate': 0.0, 'expectancy': 321.03529411764583, 'total_trades': 85.0, 'day_index': 2, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Thursday': {'win_rate': 0.0, 'expectancy': 202.84931506849182, 'total_trades': 73.0, 'day_index': 3, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Friday': {'win_rate': 0.0, 'expectancy': 200.15294117646914, 'total_trades': 85.0, 'day_index': 4, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}}, 'hours': {3: {'win_rate': 0.0, 'expectancy': 357.9787234042549, 'total_trades': 47.0, 'strong_buy_rate': 0.0}, 4: {'win_rate': 0.0, 'expectancy': 192.5263157894715, 'total_trades': 38.0, 'strong_buy_rate': 0.0}, 5: {'win_rate': 0.0, 'expectancy': 142.7037037037036, 'total_trades': 27.0, 'strong_buy_rate': 0.0}, 6: {'win_rate': 0.0, 'expectancy': 198.64999999999782, 'total_trades': 20.0, 'strong_buy_rate': 0.0}, 7: {'win_rate': 0.0, 'expectancy': 73.85714285714172, 'total_trades': 21.0, 'strong_buy_rate': 0.0}, 8: {'win_rate': 0.0, 'expectancy': 356.1739130434766, 'total_trades': 23.0, 'strong_buy_rate': 0.0}, 9: {'win_rate': 0.0, 'expectancy': 290.0799999999963, 'total_trades': 25.0, 'strong_buy_rate': 0.0}, 10: {'win_rate': 0.0, 'expectancy': 83.23999999999978, 'total_trades': 25.0, 'strong_buy_rate': 0.0}, 11: {'win_rate': 0.0, 'expectancy': 257.7272727272719, 'total_trades': 33.0, 'strong_buy_rate': 0.0}, 12: {'win_rate': 0.0, 'expectancy': 209.00000000000023, 'total_trades': 27.0, 'strong_buy_rate': 0.0}, 13: {'win_rate': 0.0, 'expectancy': 189.36363636363674, 'total_trades': 22.0, 'strong_buy_rate': 0.0}, 14: {'win_rate': 0.0, 'expectancy': 196.70370370370165, 'total_trades': 27.0, 'strong_buy_rate': 0.0}, 15: {'win_rate': 0.0, 'expectancy': 120.14999999999986, 'total_trades': 20.0, 'strong_buy_rate': 0.0}, 16: {'win_rate': 0.0, 'expectancy': 311.3928571428565, 'total_trades': 28.0, 'strong_buy_rate': 0.0}, 17: {'win_rate': 0.0, 'expectancy': 191.11999999999898, 'total_trades': 25.0, 'strong_buy_rate': 0.0}, 18: {'win_rate': 0.0, 'expectancy': 192.72222222222, 'total_trades': 18.0, 'strong_buy_rate': 0.0}, 19: {'win_rate': 0.0, 'expectancy': 103.0666666666654, 'total_trades': 15.0, 'strong_buy_rate': 0.0}, 20: {'win_rate': 0.0, 'expectancy': 355.75000000000045, 'total_trades': 20.0, 'strong_buy_rate': 0.0}}}, 'adaptive_settings': {'min_win_rate': 0.3, 'min_expectancy': 0.0, 'adaptive_threshold': True, 'confidence_level': 0.8}, 'time_blocks': [], 'performance_comparison': {'filtered_vs_all': {'error': 'No trades match the filter criteria'}, 'best_day': {'win_rate': 0.0, 'total': 109.0, 'expectancy': 164.93577981651333, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'best_hour': {'win_rate': 0.0, 'total': 47.0, 'expectancy': 357.9787234042549, 'strong_buy_rate': 0.0}}}
   ⚠️ time_filters ว่าง - สร้าง default
   ✅ สร้าง default time_filters: {'days': [0, 1, 2, 3, 4], 'hours': [8, 9, 10, 11, 12, 13, 14, 15, 16, 17], 'source': 'default'}

🎯 กำลังเรียกใช้ record_model_performance...

🏗️ เปิดใช้งาน record model performance
   Symbol: GOLD, Timeframe: M60
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
🔍 Debug trade_df:
   Shape: (7242, 338)
   Columns: ['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
   ใช้ Trade Type แทน Signal
   Trade Type counts: {'Buy': 3794, 'Sell': 3448}
🔍 calculate_trade_metrics: input shape = (3794, 338)
   📊 Total trades: 3794
   💰 Winning trades: 568/3794
   💰 Total profit: -75951.00
   ✅ Calculated metrics: {'count': 3794, 'win_rate': 14.971006852925672, 'expectancy': -20.018713758564623, 'trade_accuracy': 0.14971006852925672, 'trade_f1_score': 0.17965208223510806, 'trade_auc': 0.5598840274117027}
🔍 calculate_trade_metrics: input shape = (3448, 338)
   📊 Total trades: 3448
   💰 Winning trades: 544/3448
   💰 Total profit: -21284.00
   ✅ Calculated metrics: {'count': 3448, 'win_rate': 15.777262180974477, 'expectancy': -6.1728538283077174, 'trade_accuracy': 0.15777262180974477, 'trade_f1_score': 0.18932714617169372, 'trade_auc': 0.5631090487238979}

🏗️ เปิดใช้งาน format time filters display
🔍 Debug _compare_with_previous:
   Key: GOLD_M60
   Current F1: 0.5
   Current AUC: 0.8711885533921078
   Previous F1: 0.5
   Previous AUC: 0.8711885533921078
   Summary length: 10
⚠️ ⚠️ โมเดลไม่ดีขึ้น! F1 เปลี่ยน 0.0000, AUC เปลี่ยน 0.0000
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ (Multi-Model)
   - Symbol: GOLD, Timeframe: M60
   - Buy_sell stats (calculated): {'win_rate': 0.1535487434410384, 'expectancy': -13.42653962993637, 'profit_factor': 0.8952358069757326, 'count': 7242, 'max_drawdown': inf}
   - Performance data prepared: {'symbol': 'GOLD', 'timeframe': 'M60', 'entry_config': 'config_1_enhanced_signal', 'entry_config_name': 'Enhanced MACD Signal', 'entry_config_description': 'ใช้ macd_signal พร้อมเงื่อนไข volume และ pullback เพิ่มเติม', 'win_rate': 0.1535487434410384, 'expectancy': -13.42653962993637, 'profit_factor': 0.8952358069757326, 'num_trades': 7242, 'max_drawdown': inf, 'model_accuracy': 0.9377450151922272, 'model_auc': 0.8711885533921078, 'model_f1': 0, 'training_date': '2025-09-26T19:17:06.669610', 'training_type': 'multi_model', 'num_features': 50}

🏗️ เปิดใช้งาน save entry config performance

🏗️ เปิดใช้งาน get entry config results folder

🏗️ เปิดใช้งาน get entry config folder name
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models\M60_GOLD
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results\M60_GOLD
path บันทึกไฟล์ performance_summary.json LightGBM/Entry_config_1_enhanced_signal\results\M60_GOLD
✅ บันทึกผลการประเมิน config_1_enhanced_signal สำหรับ GOLD MM60 ที่: LightGBM/Entry_config_1_enhanced_signal\results\M60_GOLD\performance_summary.json
   📁 ขนาดไฟล์: 757 bytes
✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น (Multi-Model)

✅ เสร็จสิ้นการประมวลผลกลุ่ม M60
📊 ประมวลผล: 1 ไฟล์
📈 ผลลัพธ์: 1 รายการ

================================================================================
🔍 การวิเคราะห์เกณฑ์คุณภาพโมเดล (จาก main function)
================================================================================

================================================================================
📊 การวิเคราะห์เกณฑ์คุณภาพโมเดล (MODEL_QUALITY_THRESHOLDS)
================================================================================
Metric          Current    Recommended  New        Change     Data Points 
--------------------------------------------------------------------------------
Accuracy        0.450      0.978        0.978      +117.3%    5           
AUC             0.480      0.814        0.814      +69.6%     6           
F1 Score        0.080      0.891        0.891      +1013.9%   5           
Precision       0.080      0.879        0.879      +998.7%    5           
Recall          0.300      0.907        0.907      +202.4%    5           
Win Rate        35.0%      0.7%         0.7%       -98.1%     6           
Expectancy      10.000     48.741       48.741     +387.4%    5           
Min Trades      8.000    N/A          8.000    N/A        0           

💡 คำแนะนำ:
   - เกณฑ์ใหม่อิงจาก percentile 25% ของข้อมูลจริง
   - ค่าที่แนะนำจะไม่เข้มงวดเกินไปและสมจริงกว่า
   - สามารถปรับ THRESHOLD_PERCENTILE เพื่อเปลี่ยนความเข้มงวด
================================================================================
💾 บันทึกผลการวิเคราะห์เกณฑ์ที่: LightGBM/Multi/threshold_analysis.json
✅ การวิเคราะห์เกณฑ์เสร็จสิ้น

📊 ไม่มีการปรับ threshold ในรอบนี้
🔍 Debug: round_results = <class 'dict'>
✅ กลุ่ม M60 สำเร็จ
📊 ผลลัพธ์กลุ่ม M60: สำเร็จ 1, ผิดพลาด 0
⏱️ เวลาที่ใช้: 1063.6 วินาที (17.7 นาที)
📈 เฉลี่ยต่อไฟล์: 1063.6 วินาที/ไฟล์

────────────────────────────────────────────────────────────
📋 สรุปรอบที่ 1:
   ⏱️ เวลาที่ใช้: 1063.6 วินาที (17.7 นาที)
   ✅ ไฟล์สำเร็จ: 1
   ❌ ไฟล์ผิดพลาด: 0
   ⏰ เวลาสิ้นสุด: 19:17:07
   📊 M60: 1063.6s (1063.6s/ไฟล์)

============================================================
⏭️ ข้ามการวิเคราะห์ Feature Importance ข้าม Assets
============================================================
💡 หมายเหตุ: การวิเคราะห์จะทำงานเฉพาะเมื่อเทรนโมเดลใหม่และมีผลลัพธ์
============================================================

================================================================================
🎉 สรุปการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-26 18:59:23
⏰ เวลาสิ้นสุด: 2025-09-26 19:17:07
⏱️ เวลาที่ใช้ทั้งหมด: 1063.7 วินาที (17.7 นาที)

🚀 ประสิทธิภาพการทำงาน:
   📈 ประมวลผลได้: 3 ไฟล์/ชั่วโมง
================================================================================
💾 บันทึกผลการทดสอบลงใน 'LightGBM/Multi_Time\time_test.txt' เรียบร้อยแล้ว

================================================================================
💰 เริ่มการวิเคราะห์ทางการเงินรวม
================================================================================

================================================================================
🚀 เริ่มการวิเคราะห์ทางการเงินทั้งหมด
================================================================================
🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์
📊 พบข้อมูลการเทรด: 7242 รายการ
📊 บันทึกกราฟที่: Financial_Analysis_Results/trading_performance_analysis.png
💾 บันทึกการวิเคราะห์สมบูรณ์:
   📄 Summary: Financial_Analysis_Results/complete_financial_analysis.json
   📊 Risk Table: Financial_Analysis_Results/risk_management_table.csv
   📝 Report: Financial_Analysis_Results/financial_analysis_report.txt

============================================================
📈 FINANCIAL ANALYSIS SUMMARY
============================================================
💰 Account Balance: $1,000.00
📊 Total Trades: 7242
💵 Total Profit (1.0 lot): $-97,235.00
📉 Max Drawdown (1.0 lot): $119,267.00
🎯 Recommended Lot Size: 0.0002
⚠️ Max Risk: 2.00%
============================================================

🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
📁 ผลลัพธ์บันทึกที่: Financial_Analysis_Results

📈 สรุปผลการวิเคราะห์:
   💰 ยอดเงินในบัญชี: $1,000.00
   📊 จำนวนการเทรดทั้งหมด: 7242
   💵 กำไรรวม (1.0 lot): $-97,235.00
   📉 Drawdown สูงสุด (1.0 lot): $119,267.00
   🎯 ขนาดล็อตที่แนะนำ: 0.0002
   ⚠️ ความเสี่ยงสูงสุด: 2.00%
🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
