
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters
🛡️ เริ่มใช้งาน Model Protection System (min profit: $5,000)
🚫 เริ่มใช้งาน Training Prevention System
🔧 โหลดการตั้งค่าป้องกัน Overfitting

🛡️ ระบบป้องกันโมเดล: FLEXIBLE
   🟡 เกณฑ์ยืดหยุ่น - สมดุลระหว่างคุณภาพและการยอมรับ
   - Accuracy ≥ 45.0%
   - Win Rate ≥ 35.0%
   - Expectancy ≥ 10.0

💡 แนวคิดการบันทึกโมเดล:
   🆕 โมเดลแรก: บันทึกเสมอ (ตามเกณฑ์ของแต่ละโหมด)
   🔄 โมเดลถัดไป: เปรียบเทียบกับโมเดลก่อนหน้า

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-26 21:36:16
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
✅ เปิดใช้งานระบบวิเคราะห์ทางการเงิน (Account Balance: $1,000.00)
📁 จำนวนไฟล์ทั้งหมด: 1
   - M60: 1 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 21:36:16
📊 ประมวลผลกลุ่ม M60 (1 ไฟล์)

🏗️ เปิดใช้งาน main

🔍 ตรวจสอบการป้องกันการเทรนสำหรับ GOLD M60
⚠️ เทรนเกินจำนวนที่กำหนดแล้ววันนี้ (87/3)
🚫 ไม่สามารถเทรนได้: daily_limit_exceeded
💡 ข้ามการเทรนและใช้โมเดลเดิม

🔍 กำลังค้นหาพารามิเตอร์ที่เหมาะสมสำหรับ GOLD M30...
📁 ใช้ไฟล์: multi_asset_results_20250924_135729.json (Modified: 2025-09-24 13:57)
======================================================================
🎯 PARAMETER OPTIMIZATION RESULTS
======================================================================
📊 Source: GOLD_M30 specific

🔸 GOLD_M30
   Score: 65.87
   Win Rate: 53.3%
   Total Profit: $121,493
   Total Trades: 15
   Expectancy: 8099.52
   Max Drawdown: $86,950
   
   Best Parameters:
     SL ATR: 1.0
     TP Ratio: 2.0
     RSI Level: 25
     Volume Spike: 1.0
======================================================================

🔄 Updated Global Parameters:
   input_stop_loss_atr: 1.0 (unchanged)
   input_take_profit: 2.0 (unchanged)
   input_rsi_level_in: 35 → 25
   input_volume_spike: 1.25 → 1.0
   input_rsi_level_over: 70 (unchanged)
   input_rsi_level_out: 30 → 35
   input_pull_back: 0.5 → 0.45
   input_initial_nbar_sl: 4 (unchanged)

============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/GOLD_H1_FIXED.csv main_round 1 symbol GOLD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ GOLD MM60, ใช้ค่า default: 0.3

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 4
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend: 4
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ GOLD MM60, ใช้ค่า default: 4

📂 โหลดข้อมูลจาก LightGBM/Data_Trained/M60_GOLD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 4 confidence 0.3

🏗️ เปิดใช้งาน load and process data

🔍 กำลังตรวจสอบและจัดการ Missing Values...
✅ ไม่พบ Missing Values ในข้อมูล

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M60_GOLD_features.pkl
✅ โหลดรายชื่อ Features ที่ Model ใช้สำเร็จ: 20 features
1. DayOfWeek
2. Hour
3. IsMorning
4. IsAfternoon
5. IsEvening
6. IsNight
7. H2_Volume_TrendStrength
8. H2_Volume_Spike
9. ATR_ROC_i6
10. ATR_ROC_i4
... และอีก 10 features

First 5 rows of df:
         Date      Time     Open     High      Low  ...  D1_Volume_Momentum  D1_Volume_TrendStrength D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2013.07.18  12:00:00  1281.04  1282.29  1278.50  ...                -1.0                      0.0          0.0           0.0             0.0
1  2013.07.18  13:00:00  1280.53  1282.77  1280.18  ...                -1.0                      0.0          0.0           0.0             0.0
2  2013.07.18  14:00:00  1281.10  1282.35  1280.29  ...                -1.0                      0.0          0.0           0.0             0.0
3  2013.07.18  15:00:00  1281.14  1287.04  1277.57  ...                -1.0                      0.0          0.0           0.0             0.0
4  2013.07.18  16:00:00  1285.12  1288.40  1283.80  ...                -1.0                      0.0          0.0           0.0             0.0

[5 rows x 332 columns]
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ GOLD MM60

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ GOLD MM60
✅ trend_following: พร้อมใช้งาน
✅ trend_following_Buy: พร้อมใช้งาน
✅ trend_following_Sell: พร้อมใช้งาน
✅ counter_trend: พร้อมใช้งาน
✅ counter_trend_Buy: พร้อมใช้งาน
✅ counter_trend_Sell: พร้อมใช้งาน

📊 สรุป: 6/6 โมเดลพร้อมใช้งาน
🔍 กำลังโหลดโมเดลสำหรับ GOLD MM60
📁 Base folder: LightGBM/Multi/models
🎯 Strategy: use_available
📋 จะโหลด 6 scenarios: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']

🔍 โหลด trend_following:
  📄 Model: LightGBM/Multi/models\trend_following\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following\M60_GOLD_scaler.pkl
✅ โหลดโมเดล trend_following สำเร็จ
  📊 Features: 20 features

🔍 โหลด trend_following_Buy:
  📄 Model: LightGBM/Multi/models\trend_following_Buy\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following_Buy\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following_Buy\M60_GOLD_scaler.pkl
✅ โหลดโมเดล trend_following_Buy สำเร็จ
  📊 Features: 45 features

🔍 โหลด trend_following_Sell:
  📄 Model: LightGBM/Multi/models\trend_following_Sell\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following_Sell\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following_Sell\M60_GOLD_scaler.pkl
✅ โหลดโมเดล trend_following_Sell สำเร็จ
  📊 Features: 45 features

🔍 โหลด counter_trend:
  📄 Model: LightGBM/Multi/models\counter_trend\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend\M60_GOLD_scaler.pkl
✅ โหลดโมเดล counter_trend สำเร็จ
  📊 Features: 20 features

🔍 โหลด counter_trend_Buy:
  📄 Model: LightGBM/Multi/models\counter_trend_Buy\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend_Buy\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend_Buy\M60_GOLD_scaler.pkl
✅ โหลดโมเดล counter_trend_Buy สำเร็จ
  📊 Features: 45 features

🔍 โหลด counter_trend_Sell:
  📄 Model: LightGBM/Multi/models\counter_trend_Sell\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend_Sell\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend_Sell\M60_GOLD_scaler.pkl
✅ โหลดโมเดล counter_trend_Sell สำเร็จ
  📊 Features: 45 features

📊 สรุปการโหลดโมเดล: 6/6 โมเดล
✅ โหลดโมเดลครบทุก scenarios
✅ โหลดโมเดล Multi-Model สำเร็จ: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following_Buy - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่พบไฟล์ threshold สำหรับ trend_following_Sell - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend_Buy - ใช้ค่าเริ่มต้น: 0.3000
⚠️ ไม่พบไฟล์ threshold สำหรับ counter_trend_Sell - ใช้ค่าเริ่มต้น: 0.3000
🎯 Scenario Thresholds: {'trend_following': 0.3, 'counter_trend': 0.3, 'trend_following_Buy': 0.3, 'trend_following_Sell': 0.3, 'counter_trend_Buy': 0.3, 'counter_trend_Sell': 0.3}

🏗️ เปิดใช้งาน try trade with threshold adjustment

📂 พบ threshold ที่บันทึกไว้สำหรับ GOLD_M60
   ค่า threshold: 0.24
   จำนวน trades: 217
   ⏱️ ข้อมูลบันทึกเมื่อ 1 วัน 8 ชั่วโมงที่แล้ว (ยังใช้ได้)

🔍 ทดสอบ threshold ที่บันทึกไว้: 0.2400

🏗️ เปิดใช้งาน create trade cycles with multi model

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 35864 (49.9%)
  downtrend: 30186 (42.0%)
  sideways: 5778 (8.0%)
📊 โหลด threshold สำหรับ trend_following: 0.6500
📊 โหลด threshold สำหรับ trend_following_Buy: 0.6500
📊 โหลด threshold สำหรับ trend_following_Sell: 0.6500
📊 โหลด threshold สำหรับ counter_trend: 0.6500
📊 โหลด threshold สำหรับ counter_trend_Buy: 0.6500
📊 โหลด threshold สำหรับ counter_trend_Sell: 0.6500
🎯 Scenario Thresholds: {'trend_following': 0.65, 'trend_following_Buy': 0.65, 'trend_following_Sell': 0.65, 'counter_trend': 0.65, 'counter_trend_Buy': 0.65, 'counter_trend_Sell': 0.65}
📊 ใช้ Multi-Model: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
✅ ใช้ Multi-Model Architecture พร้อม 2 scenarios

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 0.8000

🔄 ใช้ Multi-Model Architecture: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
📋 Scenario Model Mapping: {'trend_following': 'trend_following', 'counter_trend': 'counter_trend', 'trend_following_Buy': 'trend_following_Buy', 'trend_following_Sell': 'trend_following_Sell', 'counter_trend_Buy': 'counter_trend_Buy', 'counter_trend_Sell': 'counter_trend_Sell'}
🔍 ตรวจสอบโมเดลที่โหลดมาแล้ว: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
📊 Scenario trend_following ใช้: ✅ โมเดลตัวเอง
📊 Scenario counter_trend ใช้: ✅ โมเดลตัวเอง
📊 Scenario trend_following_Buy ใช้: ✅ โมเดลตัวเอง
📊 Scenario trend_following_Sell ใช้: ✅ โมเดลตัวเอง
📊 Scenario counter_trend_Buy ใช้: ✅ โมเดลตัวเอง
📊 Scenario counter_trend_Sell ใช้: ✅ โมเดลตัวเอง
✅ Features สะอาด: 20 features (ไม่มี raw price หรือ data leakage)

🤖 สถานะการใช้งานโมเดลตาม Scenario:
   trend_following: ✅ ใช้โมเดลตัวเอง
   counter_trend: ✅ ใช้โมเดลตัวเอง
   trend_following_Buy: ✅ ใช้โมเดลตัวเอง
   trend_following_Sell: ✅ ใช้โมเดลตัวเอง
   counter_trend_Buy: ✅ ใช้โมเดลตัวเอง
   counter_trend_Sell: ✅ ใช้โมเดลตัวเอง
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 71828 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 71778
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
💰 เริ่มการวิเคราะห์ทางการเงินสำหรับ GOLD M60
🔄 กำลังประมวลผล GOLD M60...
💾 บันทึกการวิเคราะห์ GOLD M60 ที่: Financial_Analysis_Results/GOLD_M60_financial_analysis.json
✅ ประมวลผล GOLD M60 สำเร็จ: 172 รายการ
✅ วิเคราะห์ทางการเงิน GOLD M60 สำเร็จ
✅ ยืนยัน threshold ที่บันทึกไว้ใช้ได้ดี: พบ 172 trades
🎉 สำเร็จ! ใช้ threshold สุดท้าย: 0.2400 (Multi-Model)

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 71828 ตัวอย่างข้อมูล df
         Date      Time     Open     High      Low  ...  D1_Volume_Momentum  D1_Volume_TrendStrength D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2013.07.18  12:00:00  1281.04  1282.29  1278.50  ...                -1.0                      0.0          0.0           0.0             0.0
1  2013.07.18  13:00:00  1280.53  1282.77  1280.18  ...                -1.0                      0.0          0.0           0.0             0.0
2  2013.07.18  14:00:00  1281.10  1282.35  1280.29  ...                -1.0                      0.0          0.0           0.0             0.0
3  2013.07.18  15:00:00  1281.14  1287.04  1277.57  ...                -1.0                      0.0          0.0           0.0             0.0
4  2013.07.18  16:00:00  1285.12  1288.40  1283.80  ...                -1.0                      0.0          0.0           0.0             0.0

[5 rows x 332 columns]
จำนวนการซื้อขายที่พบ: 172 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price  ...  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2013-09-06 10:00:00      1370.46 2013-09-06 10:00:00     1370.46  ...  0.000000    0.004334               1778.15                      1
1 2013-09-06 18:00:00      1384.17 2013-09-06 18:00:00     1387.08  ...  0.004898    0.009796               1664.95                      1
2 2013-09-10 10:00:00      1378.60 2013-09-10 12:00:00     1372.91  ...  0.000000    0.004127               1411.30                      1
3 2013-09-11 05:00:00      1363.81 2013-09-11 05:00:00     1363.81  ...  0.000000    0.004062               1808.00                      1
4 2013-09-13 15:00:00      1313.28 2013-09-13 15:00:00     1313.28  ...  0.000000    0.006153               1723.30                      1

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                0.00                
Expectancy          -223.00             
📈 สถิติสำหรับ Sell Trades:
Win%                36.51               
Expectancy          -64.98              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                35.94               
Expectancy          -67.45              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    12.50          32                  
Tuesday   23.08          39                  
Wednesday 12.00          25                  
Thursday  7.89           38                  
Friday    10.53          38                  
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         0.00           2                   
5         0.00           6                   
6         0.00           1                   
10        36.36          11                  
11        27.27          11                  
12        25.00          4                   
13        16.67          12                  
14        0.00           7                   
15        11.76          17                  
16        10.53          19                  
17        20.00          20                  
18        3.85           26                  
19        10.53          19                  
20        15.38          13                  
21        0.00           4                   
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2013-09-06 10:00:00
1   2013-09-06 18:00:00
2   2013-09-10 10:00:00
3   2013-09-11 05:00:00
4   2013-09-13 15:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2013-09-06 09:55:00
1   2013-09-06 17:55:00
2   2013-09-10 09:55:00
3   2013-09-11 04:55:00
4   2013-09-13 14:55:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 0   2013-07-18 12:00:00
1   2013-07-18 13:00:00
2   2013-07-18 14:00:00
3   2013-07-18 15:00:00
4   2013-07-18 16:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 313 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 172
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2013-09-06 10:00:00      1370.46 2013-09-06 10:00:00     1370.46       Sell     0.0          4          10
1 2013-09-06 18:00:00      1384.17 2013-09-06 18:00:00     1387.08       Sell  -291.0          4          18
2 2013-09-10 10:00:00      1378.60 2013-09-10 12:00:00     1372.91       Sell   569.0          1          10
3 2013-09-11 05:00:00      1363.81 2013-09-11 05:00:00     1363.81       Sell     0.0          2           5
4 2013-09-13 15:00:00      1313.28 2013-09-13 15:00:00     1313.28       Sell     0.0          4          15

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...
🔍 ตรวจสอบ columns ข้อมูล df หลัง merge : จำนวน 332
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 172/172 valid values

🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 21 samples (12.2%)
  Class 1 (weak_sell): 106 samples (61.6%)
  Class 2 (no_trade): 41 samples (23.8%)
  Class 3 (weak_buy): 4 samples (2.3%)
✅ Multi-class Target ถูกต้อง: 3 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0     21
1    106
2     41
3      4
Name: count, dtype: int64
Class 0 (strong_sell): 21 trades, Profit range: 189.0 to 840.0
Class 1 (weak_sell): 106 trades, Profit range: 0.0 to 47.0
Class 2 (no_trade): 41 trades, Profit range: -1869.0 to -34.0
Class 3 (weak_buy): 4 trades, Profit range: 0.0 to 0.0
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    151
1     21
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    5
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    146
1     21
Name: count, dtype: int64
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 172
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time        EMA50       EMA100       EMA200      RSI14
0 2013-09-06 10:00:00  1382.660969  1389.290940  1390.883269  37.032786
1 2013-09-06 18:00:00  1380.618031  1387.163062  1389.642881  63.301091
2 2013-09-10 10:00:00  1384.192859  1386.333276  1388.417466  34.910969
3 2013-09-11 05:00:00  1374.225914  1379.807159  1384.505772  36.355958
4 2013-09-13 15:00:00  1336.530333  1351.149339  1365.421223  32.673525

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    151
1     21
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
⚠️ Feature 'Volume_MA_20' not found in DataFrame. Skipping.
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20'] = 416 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target                  1.000000
ATR_ROC_i6              0.289950
ATR_ROC_i8              0.275864
ATR_ROC_i2              0.263479
ATR_ROC_i4              0.260157
                          ...   
D1_Bar_DTB                   NaN
RSI_Oversold                 NaN
H12_Bar_DTB                  NaN
Volume_Spike                 NaN
Volume_TrendStrength         NaN
Name: Target, Length: 301, dtype: float64

📊 ค่า VIF ของ Features:
                     feature  VIF
0                 ATR_ROC_i6  inf
1                 ATR_ROC_i8  inf
2                 ATR_ROC_i2  inf
3                 ATR_ROC_i4  inf
4    H2_Volume_TrendStrength  inf
..                       ...  ...
275      STOCHd_14_3_3_Lag_5  inf
276      STOCHk_14_3_3_Lag_5  inf
277          Volume_Change_3  inf
278               H8_Bar_OSB  inf
279                H8_Bar_TL  inf

[280 rows x 2 columns]

🚫 Features ถูกตัดออกเนื่องจาก VIF สูง: ['ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'IsMorning', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3', 'High_Lag_3', 'Open_Lag_1', 'Close_Lag_2', 'Low_Lag_2', 'Close_MA_5', 'Low_Lag_1', 'Close_MA_10', 'Open_Lag_3', 'Low_Lag_3', 'High_Lag_5', 'High_Lag_10', 'Close_Lag_5', 'Close_MA_20', 'Open_Lag_5', 'Close_Lag_10', 'Low_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'Open_Lag_10', 'Close_Lag_15', 'Low_Lag_10', 'Low_Lag_15', 'High_Lag_15', 'EMA50_Lag_1', 'EMA50_Lag_2', 'Close_Lag_20', 'EMA50_Lag_3', 'Open_Lag_15', 'EMA100_Lag_1', 'EMA50_Lag_5', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'High_Lag_20', 'High_Lag_30', 'Close_Lag_30', 'Low_Lag_20', 'Close_Lag_50', 'Low_Lag_50', 'Open_Lag_30', 'Open_Lag_20', 'Open_Lag_50', 'Low_Lag_30', 'High_Lag_50', 'ATR_Lag_1', 'H4_Bar_TL', 'H4_Price_Range', 'ADX_14_Lag_5', 'Volume_MA_10', 'ATR_Lag_2', 'RSI14_x_BBwidth', 'Volume_MA_3', 'Volume_MA_5', 'H2_Price_Range', 'Volume_Lag_1', 'H4_Volume_Spike', 'H4_Volume_TrendStrength', 'ADX_14_Lag_3', 'ADX_14_Lag_2', 'BB_width', 'H8_MACD_line', 'MA_Cross_100_200', 'Close_Std_10', 'ATR_Lag_3', 'Volume_Lag_3', 'Volume_Lag_2', 'ADX_14_Lag_1', 'Close_Std_5', 'EMA50_x_RollingVol5', 'Volume_Lag_10', 'H8_Price_Range', 'MACD_signal_x_RSI14', 'H4_MACD_deep', 'RSI14_x_Volume', 'Close_Std_20', 'BB_width_Lag_1', 'H4_Volume_Momentum', 'Volume_Momentum', 'H12_MACD_line', 'ATR_x_PriceRange', 'H8_MACD_deep', 'RSI_signal_EMA8', 'Bar_CL_OC', 'ATR_Lag_5', 'RSI14_x_PullBack_50_Up', 'BB_Outside', 'ADX_14_x_ATR', 'BB_width_Lag_2', 'Bar_CL_HL', 'H8_Price_Strangth', 'BB_width_Lag_3', 'MACD_line_x_PriceMove', 'Slope_EMA100', 'EMA_Cross_EMA5', 'Volume_Lag_30', 'EMA_diff_x_RSI14', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14_x_PullBack_50_Down', 'DMP_14_Lag_3', 'Volume_Lag_5', 'H12_MACD_deep', 'EMA_diff_x_BBwidth', 'Hour', 'ATR_Deep', 'EMA_diff_x_ATR', 'H12_Price_Range', 'D1_Price_Strangth', 'H12_Bar_longwick', 'H4_Bar_CL_HL', 'BB_width_Lag_5', 'H2_Bar_TL', 'Bar_OSB', 'H8_Volume_Momentum', 'H12_Bar_CL_OC', 'RSI14_x_PriceMove', 'Volume_Change_1', 'H2_Price_Move', 'Close_Std_3', 'DayOfWeek', 'PullBack_50_Down', 'PullBack_50_Up', 'EMA_diff_100_200', 'Volume_MA20', 'DMP_14_Lag_2', 'MACD_signal_x_ADX', 'ADX_14_x_BBwidth', 'D1_Price_Move', 'DMP_14_Lag_1', 'RSI14_Lag_3', 'Volume_Lag_50', 'EMA_diff_50_200', 'DMN_14_Lag_5', 'Volume_Lag_15', 'Price_Move', 'IsEvening', 'DMN_14_Lag_2', 'Bar_longwick', 'H2_MACD_line', 'DMN_14_Lag_3', 'D1_Bar_longwick', 'H4_Bar_SW', 'RSI_x_VolumeSpike', 'RSI14', 'RSI_Zone', 'PullBack_100_Down', 'PullBack_100_Up', 'Price_EMA50', 'Slope_EMA50', 'H8_Bar_CL_OC', 'DMN_14_Lag_1', 'RSI14_Lag_1', 'H8_Bar_SW', 'Dist_EMA50', 'EMA_Cross_EMA15', 'ADX_cross', 'EMA_Cross_EMA10', 'D1_MACD_line', 'D1_Bar_TL', 'ADX_zone_15', 'RSI_signal_EMA4', 'RSI14_Lag_2', 'H12_Bar_FVG', 'H4_MACD_signal', 'H12_MACD_signal', 'H2_Bar_longwick', 'H2_Bar_FVG', 'Volume_Change_2', 'Price_EMA50_x_RSI_trend', 'RSI_Overbought', 'RSI_trend', 'RSI_counter', 'IsNight', 'D1_MACD_signal', 'EMA_diff_50_100', 'H4_Bar_OSB', 'H2_Price_Strangth', 'H4_Bar_CL_OC', 'RSI14_x_StochD', 'D1_Price_Range', 'RSI_signal_EMA12', 'Bar_SW', 'H8_Volume_Spike', 'H8_Volume_TrendStrength', 'Bar_CL', 'Slope_EMA200', 'H2_Bar_CL_OC', 'DMP_14_Lag_5', 'Close_Return_5', 'H4_Price_Strangth', 'H12_Volume_Momentum', 'H8_Bar_longwick', 'Dist_EMA100', 'RSI14_x_StochK', 'BB_Break_HL', 'D1_Bar_FVG', 'H12_Bar_SW', 'Volume_Lag_20', 'H2_MACD_signal', 'Bar_FVG', 'H4_Price_Move', 'H12_Volume_Spike', 'H12_Volume_TrendStrength', 'Price_Strangth', 'H12_Bar_CL_HL', 'RSI14_Lag_5', 'RSI_ROC_i4', 'H12_Bar_OSB', 'H2_Bar_CL_HL', 'ADX_Deep', 'H4_Bar_longwick', 'H4_MACD_line', 'D1_Bar_CL_OC', 'H8_Bar_DTB', 'D1_Bar_SW', 'Close_Return_2', 'D1_Bar_CL_HL', 'MA_Cross_50_100', 'Bar_TL', 'H2_Bar_SW', 'H8_Bar_CL_HL', 'ADX_14_x_RollingVol15', 'H8_Bar_FVG', 'STO_zone', 'STO_overbought', 'ADX_zone_25', 'H4_Bar_FVG', 'MACD_12_26_9_Lag_1', 'Close_Return_1', 'RSI_ROC_i6', 'Volume_Change_5', 'Momentum5_x_Volatility10', 'D1_Volume_Momentum', 'H12_Price_Strangth', 'Bar_DTB', 'IsAfternoon', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_5', 'D1_MACD_deep', 'Close_Return_3', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACD_12_26_9_Lag_2', 'STO_Oversold', 'STOCHk_14_3_3_Lag_2', 'H8_Price_Move', 'STOCHk_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_1', 'MACD_12_26_9_Lag_3', 'STO_cross', 'MACD_12_26_9_Lag_5', 'H2_Bar_OSB', 'STOCHk_14_3_3_Lag_3', 'H2_MACD_deep', 'H2_Volume_Momentum', 'Dist_EMA200', 'STOCHd_14_3_3_Lag_2', 'RSI_ROC_i8', 'STOCHd_14_3_3_Lag_5', 'STOCHk_14_3_3_Lag_5', 'Volume_Change_3', 'H8_Bar_OSB', 'H8_Bar_TL']

🔍 เริ่มการคัดเลือก features ด้วย RFE และ Feature Importance
⚠️ มี features ไม่เพียงพอสำหรับการใช้ RFE หรือไม่พบคอลัมน์ Target
⚠️ ใช้ features ที่เลือกจาก correlation แทน

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

featuresasset_feature_importance : len  6
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
👍 เพิ่ม Feature ที่จำเป็น 'DayOfWeek' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Hour' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsMorning' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsNight' เข้าไปในรายการ

🔍 จำนวน features ที่เลือกได้: 6
⚠️ Features น้อยเกินไป (6 < 20)
🔄 ลด correlation threshold เพื่อเพิ่ม features
➕ เพิ่ม feature: ATR_ROC_i6 (corr: 0.2899)
➕ เพิ่ม feature: ATR_ROC_i8 (corr: 0.2759)
➕ เพิ่ม feature: ATR_ROC_i2 (corr: 0.2635)
➕ เพิ่ม feature: ATR_ROC_i4 (corr: 0.2602)
➕ เพิ่ม feature: H2_Volume_TrendStrength (corr: 0.2561)
➕ เพิ่ม feature: H2_Volume_Spike (corr: 0.2561)
➕ เพิ่ม feature: Price_Range (corr: 0.2200)
➕ เพิ่ม feature: RSI14_x_ATR (corr: 0.2186)
➕ เพิ่ม feature: High_Lag_1 (corr: 0.2154)
➕ เพิ่ม feature: High_Lag_2 (corr: 0.2152)
➕ เพิ่ม feature: Close_Lag_1 (corr: 0.2151)
➕ เพิ่ม feature: Close_Lag_3 (corr: 0.2150)
➕ เพิ่ม feature: Open_Lag_2 (corr: 0.2150)
➕ เพิ่ม feature: Close_MA_3 (corr: 0.2150)

✅ Final selected features for training: 20 features
📋 Top 10 features:
1. DayOfWeek (corr: 0.1029)
2. Hour (corr: 0.1128)
3. IsMorning (corr: 0.2392)
4. IsAfternoon (corr: 0.0272)
5. IsEvening (corr: 0.0900)
6. IsNight (corr: 0.0709)
7. ATR_ROC_i6 (corr: 0.2899)
8. ATR_ROC_i8 (corr: 0.2759)
9. ATR_ROC_i2 (corr: 0.2635)
10. ATR_ROC_i4 (corr: 0.2602)
... และอีก 10 features
💾 บันทึก features (pkl): LightGBM/Multi\feature_selected\GOLD_M60_selected_features.pkl
📝 บันทึก features (txt): LightGBM/Multi\feature_selected\GOLD_M60_selected_features.txt

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 20
1. DayOfWeek
2. Hour
3. IsMorning
4. IsAfternoon
5. IsEvening
6. IsNight
7. ATR_ROC_i6
8. ATR_ROC_i8
9. ATR_ROC_i2
10. ATR_ROC_i4
11. H2_Volume_TrendStrength
12. H2_Volume_Spike
13. Price_Range
14. RSI14_x_ATR
15. High_Lag_1
... และอีก 5 features

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.877907
1    0.122093
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.14
⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                         count         mean         std          min          25%          50%          75%          max
DayOfWeek                172.0     2.063953    1.443470     0.000000     1.000000     2.000000     3.000000     4.000000
Hour                     172.0    14.401163    3.856581     3.000000    12.000000    15.000000    17.000000    20.000000
IsMorning                172.0     0.151163    0.359253     0.000000     0.000000     0.000000     0.000000     1.000000
IsAfternoon              172.0     0.319767    0.467748     0.000000     0.000000     0.000000     1.000000     1.000000
IsEvening                172.0     0.453488    0.499285     0.000000     0.000000     0.000000     1.000000     1.000000
IsNight                  172.0     0.034884    0.184021     0.000000     0.000000     0.000000     0.000000     1.000000
ATR_ROC_i6               172.0     0.030544    0.329486    -1.128948    -0.122667     0.104557     0.262006     0.605651
ATR_ROC_i8               172.0    -0.032355    0.367224    -1.242698    -0.244022     0.021502     0.279294     0.591162
ATR_ROC_i2               172.0     0.061177    0.139079    -0.710329     0.017091     0.071284     0.131371     0.423569
ATR_ROC_i4               172.0     0.054661    0.279234    -1.209173    -0.032782     0.117638     0.213622     0.588505
H2_Volume_TrendStrength  172.0     0.465116    0.887834    -1.000000    -1.000000     1.000000     1.000000     1.000000
H2_Volume_Spike          172.0     0.732558    0.443917     0.000000     0.000000     1.000000     1.000000     1.000000
Price_Range              172.0     5.723430    4.345174     0.930000     2.757500     4.705000     7.110000    30.390000
RSI14_x_ATR              172.0   222.727587  151.919589    40.095481   111.953923   190.109786   287.701566  1162.719856
High_Lag_1               172.0  1645.949826  457.617577  1149.630000  1279.125000  1674.985000  1869.205000  3248.620000
High_Lag_2               172.0  1643.305523  456.506811  1145.050000  1274.550000  1664.890000  1867.875000  3234.330000
Close_Lag_1              172.0  1644.151337  456.922355  1147.710000  1278.297500  1674.090000  1867.847500  3240.830000
Close_Lag_3              172.0  1639.896337  455.344090  1144.900000  1273.340000  1662.070000  1867.135000  3224.800000
Open_Lag_2               172.0  1639.888198  455.338905  1144.900000  1273.390000  1662.055000  1867.102500  3224.740000
Close_MA_3               172.0  1641.794903  456.018405  1145.253333  1275.664167  1666.610000  1867.573333  3233.053333

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
⚠️ พบ 19 คู่ features ที่มีความสัมพันธ์สูง (>0.8)

คู่ features ที่มีความสัมพันธ์สูง:
  Feature 1   Feature 2  Correlation
Close_Lag_3  Open_Lag_2     1.000000
 Open_Lag_2 Close_Lag_3     1.000000
Close_Lag_1  High_Lag_1     0.999993
 High_Lag_1 Close_Lag_1     0.999993
 High_Lag_2  Close_MA_3     0.999987
 Close_MA_3  High_Lag_2     0.999987
 Close_MA_3  Open_Lag_2     0.999976
 Open_Lag_2  Close_MA_3     0.999976
Close_Lag_3  Close_MA_3     0.999975
 Close_MA_3 Close_Lag_3     0.999975
 Open_Lag_2  High_Lag_2     0.999966
 High_Lag_2  Open_Lag_2     0.999966
 High_Lag_2 Close_Lag_3     0.999966
Close_Lag_3  High_Lag_2     0.999966
Close_Lag_1  Close_MA_3     0.999966
 Close_MA_3 Close_Lag_1     0.999966
 High_Lag_1  Close_MA_3     0.999964
 Close_MA_3  High_Lag_1     0.999964
 High_Lag_2  High_Lag_1     0.999945
 High_Lag_1  High_Lag_2     0.999945
Close_Lag_1  High_Lag_2     0.999938
 High_Lag_2 Close_Lag_1     0.999938
 Open_Lag_2  High_Lag_1     0.999895
 High_Lag_1  Open_Lag_2     0.999895
Close_Lag_3  High_Lag_1     0.999895
 High_Lag_1 Close_Lag_3     0.999895
Close_Lag_1 Close_Lag_3     0.999894
Close_Lag_3 Close_Lag_1     0.999894
 Open_Lag_2 Close_Lag_1     0.999894
Close_Lag_1  Open_Lag_2     0.999894
 ATR_ROC_i8  ATR_ROC_i6     0.939424
 ATR_ROC_i6  ATR_ROC_i8     0.939424
 ATR_ROC_i6  ATR_ROC_i4     0.926136
 ATR_ROC_i4  ATR_ROC_i6     0.926136
 ATR_ROC_i4  ATR_ROC_i8     0.832763
 ATR_ROC_i8  ATR_ROC_i4     0.832763

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...

📊 ใช้ Target Column: Target_Multiclass
📊 การกระจายของ Target ทั้งหมด:
Target_Multiclass
1    106
2     41
0     21
3      4
Name: count, dtype: int64

🔄 ใช้ Stratified Time Series Split เพื่อรักษา positive samples ใน validation set
📊 Total positive samples: 106
📊 Total negative samples: 21
📊 Positive distribution - Train: 64, Val: 21, Test: 21

📊 การกระจายของ Target ในชุดข้อมูลหลังแบ่ง:
Train: 77 samples, positive: 64 (83.1%)
Val: 25 samples, positive: 21 (84.0%)
Test: 25 samples, positive: 21 (84.0%)
Train distribution: Target_Multiclass
1    0.831169
0    0.168831
Name: proportion, dtype: float64
Val distribution: Target_Multiclass
1    0.84
0    0.16
Name: proportion, dtype: float64
Test distribution: Target_Multiclass
1    0.84
0    0.16
Name: proportion, dtype: float64

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 1 : จำนวน 338

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 2 : จำนวน 338

🏗️ เปิดใช้งาน analyze time filters (Enhanced)
📊 Adaptive Thresholds - Win Rate: 0.300, Expectancy: 83.543

📊 Enhanced Time Filter Analysis for GOLD:
📅 Recommended Days: []
⏰ Recommended Hours: []
📈 Performance Improvement: {'error': 'No trades match the filter criteria'}
✅ บันทึก time filter ที่: LightGBM/Multi/thresholds/M60_GOLD_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
✅ ทำ Feature Scaling เรียบร้อยแล้ว
train_data : (     DayOfWeek      Hour  IsMorning  IsAfternoon  IsEvening   IsNight  ...  High_Lag_1  High_Lag_2  Close_Lag_1  Close_Lag_3  Open_Lag_2  Close_MA_3
0     1.339689 -1.475412   1.952562    -0.612372  -0.912871 -0.201347  ...   -0.174057   -0.168909    -0.173754    -0.161068   -0.161262   -0.166517
2    -0.765537 -1.475412   1.952562    -0.612372  -0.912871 -0.201347  ...   -0.141782   -0.138262    -0.140488    -0.130213   -0.130407   -0.136326
3    -0.063795 -2.860860  -0.512148    -0.612372  -0.912871 -0.201347  ...   -0.196469   -0.189194    -0.189874    -0.182802   -0.183392   -0.185513
4     1.339689 -0.089964  -0.512148     1.632993  -0.912871 -0.201347  ...   -0.399394   -0.400277    -0.397255    -0.403111   -0.403108   -0.398670
5     1.339689  0.741305  -0.512148    -0.612372   1.095445 -0.201347  ...   -0.375168   -0.361129    -0.378370    -0.389389   -0.389425   -0.379225
..         ...       ...        ...          ...        ...       ...  ...         ...         ...          ...          ...         ...         ...
107   0.637947 -0.089964  -0.512148     1.632993  -0.912871 -0.201347  ...    1.423580    1.430190     1.432236     1.439910    1.439924    1.437736
108  -1.467279  0.741305  -0.512148    -0.612372   1.095445 -0.201347  ...    1.298621    1.322196     1.281392     1.319781    1.319715    1.298122
111  -0.765537  1.018394  -0.512148    -0.612372   1.095445 -0.201347  ...    1.429735    1.427066     1.434765     1.429440    1.428938    1.431347
113  -1.467279 -0.921233   1.952562    -0.612372  -0.912871 -0.201347  ...    1.350625    1.378823     1.339667     1.387361    1.387930    1.365856
114   1.339689 -1.198322   1.952562    -0.612372  -0.912871 -0.201347  ...    1.547987    1.539962     1.555108     1.530572    1.530705    1.542644

[77 rows x 20 columns], 0      1
2      0
3      1
4      1
5      1
      ..
107    1
108    1
111    1
113    1
114    1
Name: Target_Multiclass, Length: 77, dtype: int32)
val_data : (     DayOfWeek      Hour  IsMorning  IsAfternoon  IsEvening   IsNight  ...  High_Lag_1  High_Lag_2  Close_Lag_1  Close_Lag_3  Open_Lag_2  Close_MA_3
69   -0.765537 -0.921233   1.952562    -0.612372  -0.912871 -0.201347  ...   -0.519224   -0.523811    -0.513253    -0.526928   -0.526767   -0.519605
70   -1.467279 -1.198322   1.952562    -0.612372  -0.912871 -0.201347  ...   -0.412533   -0.402373    -0.407764    -0.399224   -0.399221   -0.401838
77   -0.765537  1.295484  -0.512148    -0.612372   1.095445 -0.201347  ...    0.272039    0.275520     0.276132     0.205743    0.205750    0.255018
109  -0.765537  0.187125  -0.512148     1.632993  -0.912871 -0.201347  ...    1.248708    1.253272     1.256422     1.262077    1.262089    1.260631
115   1.339689 -0.644143  -0.512148     1.632993  -0.912871 -0.201347  ...    1.552840    1.562186     1.561390     1.572611    1.572348    1.561033
116   1.339689 -0.089964  -0.512148     1.632993  -0.912871 -0.201347  ...    1.575054    1.568513     1.569687     1.578917    1.579328    1.574762
117   1.339689  1.295484  -0.512148    -0.612372   1.095445 -0.201347  ...    1.438100    1.418168     1.446815     1.425157    1.417318    1.431254
118  -0.765537  0.187125  -0.512148     1.632993  -0.912871 -0.201347  ...    1.547158    1.559022     1.541122     1.566742    1.566796    1.556677
119  -1.467279 -0.089964  -0.512148     1.632993  -0.912871 -0.201347  ...    2.174044    2.184956     2.176659     2.189992    2.190050    2.181679
120  -0.765537  1.295484  -0.512148    -0.612372   1.095445 -0.201347  ...    2.031724    2.029273     2.034230     2.001252    2.001388    2.023135
121   0.637947 -0.089964  -0.512148     1.632993  -0.912871 -0.201347  ...    2.094145    2.106897     2.089227     2.102305    2.102283    2.099014
122   0.637947  0.741305  -0.512148    -0.612372   1.095445 -0.201347  ...    2.103378    2.106264     2.110443     2.091954    2.092289    2.105060
123   0.637947  1.018394  -0.512148    -0.612372   1.095445 -0.201347  ...    2.105587    2.116506     2.109257     2.116067    2.116283    2.117350
124   1.339689  0.187125  -0.512148     1.632993  -0.912871 -0.201347  ...    1.987415    1.992260     1.995788     1.995858    1.995439    1.995109
125  -0.765537  0.187125  -0.512148     1.632993  -0.912871 -0.201347  ...    1.656966    1.646769     1.660241     1.653318    1.653453    1.653506
126   0.637947 -0.367054  -0.512148     1.632993  -0.912871 -0.201347  ...    1.634041    1.618298     1.640328     1.593234    1.593289    1.620385
127  -0.063795 -0.367054  -0.512148     1.632993  -0.912871 -0.201347  ...    1.617825    1.616558     1.623814     1.623177    1.623153    1.624306
128   0.637947  0.464215  -0.512148    -0.612372   1.095445 -0.201347  ...    1.148094    1.071886     1.154490     1.075121    1.075292    1.103645
131  -1.467279  1.018394  -0.512148    -0.612372   1.095445 -0.201347  ...    1.278024    1.283642     1.276335     1.265170    1.265143    1.271496
133   1.339689  1.018394  -0.512148    -0.612372   1.095445 -0.201347  ...    1.190944    1.182766     1.196408     1.179466    1.179319    1.188448
134   1.339689  1.295484  -0.512148    -0.612372   1.095445 -0.201347  ...    1.189523    1.202063     1.192260     1.192315    1.192407    1.198072
138   1.339689  0.741305  -0.512148    -0.612372   1.095445 -0.201347  ...    0.895374    0.867842     0.901555     0.839464    0.839435    0.870106
139  -0.765537 -1.475412   1.952562    -0.612372  -0.912871 -0.201347  ...    0.912420    0.902087     0.919650     0.897843    0.897735    0.909445
140  -1.467279  0.187125  -0.512148     1.632993  -0.912871 -0.201347  ...    1.806940    1.814117     1.809189     1.827583    1.827282    1.819483
141  -0.063795 -1.198322   1.952562    -0.612372  -0.912871 -0.201347  ...    1.855551    1.836776     1.864857     1.846024    1.846001    1.850360

[25 rows x 20 columns], 69     0
70     0
77     0
109    0
115    1
116    1
117    1
118    1
119    1
120    1
121    1
122    1
123    1
124    1
125    1
126    1
127    1
128    1
131    1
133    1
134    1
138    1
139    1
140    1
141    1
Name: Target_Multiclass, dtype: int32)
test_data : (     DayOfWeek      Hour  IsMorning  IsAfternoon  IsEvening   IsNight  ...  High_Lag_1  High_Lag_2  Close_Lag_1  Close_Lag_3  Open_Lag_2  Close_MA_3
112  -0.063795 -1.198322   1.952562    -0.612372  -0.912871 -0.201347  ...    1.436995    1.444702     1.441204     1.444947    1.445040    1.445221
136  -0.765537  1.295484  -0.512148    -0.612372   1.095445 -0.201347  ...    1.048348    1.017395     1.055599     1.013451    1.013422    1.029732
137  -1.467279  0.464215  -0.512148    -0.612372   1.095445 -0.201347  ...    0.994410    0.967017     0.996494     0.974267    0.974199    0.981865
142  -0.765537 -1.475412   1.952562    -0.612372  -0.912871 -0.201347  ...    1.671407    1.683624     1.676084     1.696072    1.696047    1.689110
143  -0.765537 -0.644143  -0.512148     1.632993  -0.912871 -0.201347  ...    1.659964    1.668360     1.663757     1.678740    1.678438    1.672740
144   0.637947  0.187125  -0.512148     1.632993  -0.912871 -0.201347  ...    1.626781    1.637041     1.629740     1.645704    1.645679    1.639381
145   0.637947  0.741305  -0.512148    -0.612372   1.095445 -0.201347  ...    1.640552    1.626523     1.647440     1.611993    1.611849    1.632319
146  -0.765537  0.187125  -0.512148     1.632993  -0.912871 -0.201347  ...    2.148989    2.144621     2.156391     2.147318    2.147297    2.152531
147   0.637947 -0.089964  -0.512148     1.632993  -0.912871 -0.201347  ...    2.025056    2.038408     2.031860     2.045948    2.045966    2.041986
148  -0.765537  0.187125  -0.512148     1.632993  -0.912871 -0.201347  ...    1.925902    1.941091     1.935143     1.954969    1.955026    1.946635
150  -1.467279  0.187125  -0.512148     1.632993  -0.912871 -0.201347  ...    1.713270    1.731985     1.720847     1.743742    1.743837    1.733689
152   1.339689  0.464215  -0.512148    -0.612372   1.095445 -0.201347  ...    2.458842    2.456145     2.453023     2.459320    2.459340    2.457686
153   0.637947  0.741305  -0.512148    -0.612372   1.095445 -0.201347  ...    2.336487    2.334074     2.328452     2.326025    2.326321    2.333293
154   1.339689 -0.089964  -0.512148     1.632993  -0.912871 -0.201347  ...    2.334475    2.346649     2.342003     2.354104    2.354043    2.351642
156  -1.467279  0.741305  -0.512148    -0.612372   1.095445 -0.201347  ...    3.712142    3.699826     3.715880     3.702411    3.702478    3.708376
157  -0.765537  1.295484  -0.512148    -0.612372   1.095445 -0.201347  ...    3.732383    3.728456     3.737017     3.741238    3.740829    3.739002
158  -1.467279 -2.860860  -0.512148    -0.612372  -0.912871 -0.201347  ...    3.927772    3.946182     3.924841     3.964878    3.964867    3.950509
159  -1.467279 -2.860860  -0.512148    -0.612372  -0.912871 -0.201347  ...    3.888315    3.894894     3.893788     3.903485    3.903711    3.900530
160  -0.063795 -2.860860  -0.512148    -0.612372  -0.912871 -0.201347  ...    3.832129    3.858119     3.840451     3.875327    3.875355    3.855937
161   0.637947  0.741305  -0.512148    -0.612372   1.095445 -0.201347  ...    4.781416    4.797830     4.789648     4.765248    4.765282    4.787691
162   0.637947  1.018394  -0.512148    -0.612372   1.095445 -0.201347  ...    5.066095    5.065381     5.074427     5.048735    5.048770    5.065414
163  -1.467279 -3.137950  -0.512148    -0.612372  -0.912871  4.966555  ...    5.010895    5.028131     5.015717     5.048854    5.049761    5.037480
164   0.637947 -3.137950  -0.512148    -0.612372  -0.912871  4.966555  ...    4.822529    4.839983     4.825008     4.855990    4.855428    4.845603
168   0.637947  0.464215  -0.512148    -0.612372   1.095445 -0.201347  ...    7.167317    7.169687     7.160488     7.183889    7.184214    7.151266
169  -1.467279  0.741305  -0.512148    -0.612372   1.095445 -0.201347  ...    7.230408    7.198316     7.216037     7.191583    7.191392    7.209390

[25 rows x 20 columns], 112    0
136    0
137    0
142    1
143    1
144    1
145    1
146    1
147    1
148    1
150    1
152    1
153    1
154    0
156    1
157    1
158    1
159    1
160    1
161    1
162    1
163    1
164    1
168    1
169    1
Name: Target_Multiclass, dtype: int32)
df :              Date      Time     Open     High      Low  ...  D1_Volume_Momentum  D1_Volume_TrendStrength D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0      2013.07.18  12:00:00  1281.04  1282.29  1278.50  ...                -1.0                      0.0          0.0           0.0             0.0
1      2013.07.18  13:00:00  1280.53  1282.77  1280.18  ...                -1.0                      0.0          0.0           0.0             0.0
2      2013.07.18  14:00:00  1281.10  1282.35  1280.29  ...                -1.0                      0.0          0.0           0.0             0.0
3      2013.07.18  15:00:00  1281.14  1287.04  1277.57  ...                -1.0                      0.0          0.0           0.0             0.0
4      2013.07.18  16:00:00  1285.12  1288.40  1283.80  ...                -1.0                      0.0          0.0           0.0             0.0
...           ...       ...      ...      ...      ...  ...                 ...                      ...          ...           ...             ...
71823  2025.07.11  19:00:00  3358.83  3359.63  3352.79  ...                -1.0                     -1.0         -1.0          -1.0            -1.0
71824  2025.07.11  20:00:00  3353.65  3354.09  3349.24  ...                -1.0                     -1.0         -1.0          -1.0            -1.0
71825  2025.07.11  21:00:00  3353.37  3358.48  3353.32  ...                -1.0                     -1.0         -1.0          -1.0            -1.0
71826  2025.07.11  22:00:00  3356.09  3359.72  3355.59  ...                -1.0                     -1.0         -1.0          -1.0            -1.0
71827  2025.07.11  23:00:00  3356.47  3358.82  3353.52  ...                -1.0                     -1.0         -1.0          -1.0            -1.0

[71828 rows x 332 columns]
trade_df :              Entry Time  Entry Price           Exit Time  Exit Price Trade Type  ...  Target  Main_Target  Target_Buy  Target_Sell  Target_Multiclass
0   2013-09-06 10:00:00      1370.46 2013-09-06 10:00:00     1370.46       Sell  ...       0            0          -1            0                  1
1   2013-09-06 18:00:00      1384.17 2013-09-06 18:00:00     1387.08       Sell  ...       0            0          -1            0                  2
2   2013-09-10 10:00:00      1378.60 2013-09-10 12:00:00     1372.91       Sell  ...       1            1          -1            1                  0
3   2013-09-11 05:00:00      1363.81 2013-09-11 05:00:00     1363.81       Sell  ...       0            0          -1            0                  1
4   2013-09-13 15:00:00      1313.28 2013-09-13 15:00:00     1313.28       Sell  ...       0            0          -1            0                  1
..                  ...          ...                 ...         ...        ...  ...     ...          ...         ...          ...                ...
167 2025-04-09 16:00:00      3047.37 2025-04-09 16:00:00     3066.06       Sell  ...       0            0          -1            0                  2
168 2025-05-01 17:00:00      3223.90 2025-05-01 17:00:00     3223.90       Sell  ...       0            0          -1            0                  1
169 2025-05-12 18:00:00      3237.64 2025-05-12 22:00:00     3237.64       Sell  ...       0            0          -1            0                  1
170 2025-05-16 18:00:00      3173.21 2025-05-16 19:00:00     3188.32       Sell  ...       0            0          -1            0                  2
171 2025-05-20 06:00:00      3206.63 2025-05-20 07:00:00     3217.48       Sell  ...       0            0          -1            0                  2

[172 rows x 338 columns]
stats : {'buy': {'win_rate': 0.0, 'expectancy': -223.0}, 'sell': {'win_rate': 36.51, 'expectancy': -64.984}, 'buy_sell': {'win_rate': 35.94, 'expectancy': -67.453}}
features : 20
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4']

✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)
Train: 2013-09-06 ถึง 2021-11-26 (3004 วัน, 77 records)
Val: 2019-01-22 ถึง 2023-02-08 (1479 วัน, 25 records)
Test: 2021-09-22 ถึง 2025-05-12 (1329 วัน, 25 records)

✅ ข้อมูล df หลังจาก load and process data
จำนวน columns ใน df: 332

✅ ข้อมูล trade_df หลังจาก load and process data
จำนวน columns ใน trade_df: 338
🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนวิเคราะห์ SL/TP + เวลา : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

🏗️ เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              21        12.21%
SL Hit              138       80.23%
Technical Exit      13        7.56%
SL + Tech Exit      151       87.79%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 496.29
ขาดทุนเฉลี่ยเมื่อ SL Hit: -95.62
กำไร/ขาดทุนเฉลี่ยเมื่อออกด้วยสัญญาณเทคนิค: -118.69
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:5.19

ผลรวม SL + Technical Exit:
- จำนวนเทรดรวม: 151
- อัตราส่วน: 87.79%
- กำไร/ขาดทุนเฉลี่ย: -97.61
- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:5.08

การออกด้วยสัญญาณเทคนิค:
- กำไรเฉลี่ยเมื่อชน TP: 42.50
- ขาดทุนเฉลี่ยเมื่อชน SL: -148.00
- อัตราการชน TP: 15.38%
- อัตราการชน SL: 84.62%

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
          Profit                       Target
           count       mean     sum      mean
DayOfWeek                                    
Monday        32   9.468750   303.0  0.125000
Tuesday       39  13.641026   532.0  0.205128
Wednesday     25 -57.920000 -1448.0  0.120000
Thursday      38 -28.210526 -1072.0  0.078947
Friday        38 -69.263158 -2632.0  0.078947

📊 ประสิทธิภาพตามชั่วโมง:
     Profit                         Target
      count         mean     sum      mean
Hour                                      
3         2     0.000000     0.0  0.000000
4         6   -86.666667  -520.0  0.000000
5         1 -1085.000000 -1085.0  0.000000
9        11   123.727273  1361.0  0.363636
10       11    56.909091   626.0  0.272727
11        4   -26.250000  -105.0  0.250000
12       12    13.666667   164.0  0.166667
13        7     0.000000     0.0  0.000000
14       17   -36.235294  -616.0  0.117647
15       19   -75.578947 -1436.0  0.105263
16       20   108.150000  2163.0  0.200000
17       26  -127.076923 -3304.0  0.038462
18       19   -52.736842 -1002.0  0.000000
19       13   -24.692308  -321.0  0.153846
20        4   -60.500000  -242.0  0.000000
💾 บันทึกกราฟวิเคราะห์เวลา ที่: LightGBM/Multi/results\M60_GOLD_time_analysis.png

============================================================
🤖 ขั้นตอนที่ 2: เทรนโมเดล LightGBM
============================================================
🔧 โหมดการทำงาน: Development
🔧 เทรนโมเดลใหม่: ใช่
🔧 ทดสอบ Optimal Parameters: ใช่
============================================================
🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)

🔄 เทรนโมเดลใหม่ (TRAIN_NEW_MODEL = True)
🔍 Debug: เตรียม merge df และ trade_df
🔍 Debug: df.shape = (71828, 332), trade_df.shape = (172, 338)

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🚀 ใช้ Logic การ Merge ใหม่โดยอ้างอิงจาก Timestamp ที่ถูกต้อง
🔍 เตรียม Merge ข้อมูล 10 คอลัมน์จาก trade_df
✅ คำนวณ Timeframe จากข้อมูลได้: 0 days 01:00:00
✅ Merge สำเร็จ พบข้อมูลที่ตรงกัน 172 รายการ
✅ จัดการค่าว่างหลังการ Merge...
✅ เติมค่าว่างตามที่กำหนดเรียบร้อยแล้ว

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 342
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'Entry Time', 'Entry Price', 'Exit Price', 'Trade Type', 'Profit', 'Exit Condition', 'Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
features : 20
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4']
✅ ใช้ Final selected feature: 20 feature

🏗️ เปิดใช้งาน train all scenario models
🚀 เริ่มเทรนโมเดลทั้ง 2 scenarios สำหรับ GOLD_M60
============================================================
📁 ใช้ results_dir: LightGBM/Multi/results
🔍 Debug: USE_MULTICLASS_TARGET = True
🔍 Debug: target_column = Target_Multiclass
✅ สร้างโฟลเดอร์ทั้งหมดเรียบร้อยแล้ว
🔍 Debug: ตรวจสอบคอลัมน์ใน df ก่อนเพิ่ม market_scenario
🔍 Debug: df.columns[:10] = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour']
🔍 Debug: มีคอลัมน์ Close? True
🔍 Debug: มีคอลัมน์ EMA200? True

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 35864 (49.9%)
  downtrend: 30186 (42.0%)
  sideways: 5778 (8.0%)

================================================================================
📊 กำลังเทรน trend_following...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ trend_following

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 71828 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 66050/71828 rows (92.0%)
📊 ข้อมูลหลังกรอง trend_following: 66050 samples

🛠️ Features used for training (Selected: 20 total):
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
📊 Class distribution สำหรับ trend_following: {0.0: 65927, 1.0: 90, 2.0: 29, 3.0: 4}
✅ เตรียมข้อมูล trend_following: 66050 samples, 20 features

✅ ข้อมูลพร้อม: X.shape=(66050, 20), y.shape=(66050,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ trend_following

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 66050 samples, 20 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 66050 samples, 20 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 65927, 1.0: 90, 2.0: 29, 3.0: 4}
📈 Train: 39630, Val: 13210, Test: 13210
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 0 → 71825
   Val: 24 → 71827
   Test: 2 → 71818
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 39557, 1.0: 54, 2.0: 17, 3.0: 2}
⚠️ ไม่สามารถใช้ SMOTE ได้เนื่องจากมีข้อมูลไม่เพียงพอในบาง class
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ trend_following

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 4
  📊 Classes: [0. 1. 2. 3.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 4 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 0.2504613595570948, 1.0: 183.47222222222223, 2.0: 582.7941176470588, 3.0: 4953.75}
📊 trend_following Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.037 - 0.087 (base: 0.062)
   num_leaves: 21 - 27 (base: 24)
   max_depth: 4 - 8 (base: 6)
   Strategy: Stability-focused
✅ โหลด parameters สำเร็จสำหรับ trend_following: {'reg_lambda': 0.0, 'reg_alpha': 0.005, 'num_leaves': 22, 'min_data_in_leaf': 10, 'max_depth': 7, 'learning_rate': 0.035, 'feature_fraction': 0.84, 'bagging_freq': 1, 'bagging_fraction': 0.87}
✅ Best CV score สำหรับ trend_following: 0.8058

--- Features (Columns) ---
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
--- Sample Data (First 5 rows) ---
       DayOfWeek  Hour  IsMorning  IsAfternoon  IsEvening  IsNight  ...  High_Lag_1  High_Lag_2  Close_Lag_1  Close_Lag_3  Open_Lag_2   Close_MA_3
36212          1     4          0            0          0        0  ...     1386.56     1388.49      1385.95      1387.10     1387.10  1386.173333
44111          0     6          0            0          0        0  ...     1884.46     1885.54      1882.37      1881.20     1881.20  1882.446667
67719          1    18          0            0          1        0  ...     2770.50     2771.74      2764.25      2760.45     2760.45  2764.743333
21556          4    20          0            0          0        1  ...     1207.30     1204.22      1205.16      1203.60     1203.58  1204.043333
54483          3    18          0            0          1        0  ...     1787.72     1784.12      1785.77      1780.67     1780.74  1783.293333

[5 rows x 20 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following
📊 Class distribution: {0.0: 39557, 1.0: 54, 2.0: 17, 3.0: 2}
📊 Imbalance ratio: 0.000
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 39557, 1.0: 54, 2.0: 17, 3.0: 2}
🔄 Oversampling class 1.0: 54 -> 200 (+146 samples)
🔄 Oversampling class 2.0: 17 -> 200 (+183 samples)
🔄 Oversampling class 3.0: 2 -> 200 (+198 samples)
📊 Class distribution หลัง oversample: {0.0: 39557, 1.0: 200, 2.0: 200, 3.0: 200}
📊 Class distribution หลัง oversample: {0.0: 39557, 1.0: 200, 2.0: 200, 3.0: 200}
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's multi_logloss: 0.104516

Accuracy: 0.9756
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      0.98      0.99     13185
         1.0       0.00      0.06      0.01        18
         2.0       0.00      0.00      0.00         6
         3.0       0.00      0.00      0.00         1

    accuracy                           0.98     13210
   macro avg       0.25      0.26      0.25     13210
weighted avg       1.00      0.98      0.99     13210

Confusion Matrix:
[[12887   262    34     2]
 [   17     1     0     0]
 [    6     0     0     0]
 [    1     0     0     0]]
📊 Test Set Accuracy: 0.9756, F1-Score: 0.9858
🔍 ประเมินผลแบบ Multiclass
✅ คำนวณ Multiclass AUC สำเร็จ: 0.7498
✅ trend_following - Accuracy: 0.976, F1: 0.986, AUC: 0.750

🔍 ตรวจสอบคุณภาพโมเดล trend_following...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (13210, 20)
   y_val shape: (13210,), unique: [0. 1. 2. 3.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (13210, 20)
   y_val shape: (13210,), unique: [0. 1. 2. 3.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (13210, 4)
   Multi-class classification detected (4 classes)
   Using model.predict() to get actual class values instead of argmax
   Final y_pred shape: (13210,), unique: [0. 1. 2. 3.], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (13210,), unique values: [0. 1. 2. 3.]
   y_pred shape: (13210,), unique values: [0. 1. 2. 3.]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   Multi-class classification metrics calculated
   Final calculated metrics: Acc=0.9765, F1=0.9862, Prec=0.9962, Rec=0.9765
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.9765
   AUC: 0.7798
   F1: 0.9862
   Precision: 0.9962
   Recall: 0.9765
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 48.98
   Trades: 1321
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 244.92
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (trend_following) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🔄 พบโมเดลก่อนหน้า - ทำการเปรียบเทียบ...
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
📊 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 21:45:59
📊 โมเดล: GOLD MM60 (trend_following)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================


================================================================================
📋 สรุปการตัดสินใจบันทึกโมเดล: GOLD MM60 (trend_following)
================================================================================
❌ ตัดสินใจ: ไม่บันทึกโมเดル
🏷️ ประเภท: declined
📝 เหตุผล: โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ

📊 Metrics ปัจจุบัน:
   - Accuracy: 0.9765
   - AUC: 0.7798
   - F1: 0.9862
   - Win Rate: 65.00%
   - Expectancy: 48.98

📈 เปรียบเทียบกับโมเดลก่อนหน้า:
   - Accuracy: 0.9765 (↗️ +0.0053, +0.5%)
   - Auc: 0.7798 (↗️ +0.0075, +1.0%)
   - F1: 0.9862 (↗️ +0.0031, +0.3%)
   - Win_Rate: 0.6500 (➡️ ไม่เปลี่ยนแปลง)
   - Expectancy: 48.9848 (↗️ +0.0955, +0.2%)
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล trend_following - โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ trend_following
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_GOLD_M60 symbol GOLD timeframe M60 (trend_following)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                Feature   Gain  Split
            RSI14_x_ATR 0.3452 0.1291
             ATR_ROC_i2 0.1891 0.1432
            Price_Range 0.1453 0.1381
             ATR_ROC_i4 0.0468 0.1094
             ATR_ROC_i8 0.0465 0.0837
                   Hour 0.0453 0.0643
             ATR_ROC_i6 0.0328 0.0830
             High_Lag_2 0.0237 0.0419
            Close_Lag_1 0.0234 0.0382
             Open_Lag_2 0.0199 0.0132
            Close_Lag_3 0.0193 0.0304
                IsNight 0.0155 0.0159
              DayOfWeek 0.0125 0.0237
H2_Volume_TrendStrength 0.0090 0.0092
             High_Lag_1 0.0080 0.0331

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_feature_importance.csv (ขนาด: 1090 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
    Feature  Importance
RSI14_x_ATR    0.161974
 High_Lag_2    0.084693
 Open_Lag_2    0.083767
 Close_MA_3    0.081330
Close_Lag_1    0.073516
 High_Lag_1    0.073233
Price_Range    0.072168
Close_Lag_3    0.070711
 ATR_ROC_i2    0.069084
 ATR_ROC_i8    0.058911
 ATR_ROC_i4    0.056371
 ATR_ROC_i6    0.049923
       Hour    0.019477
  DayOfWeek    0.013981
IsAfternoon    0.013186

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00     13185
         1.0       0.00      0.00      0.00        18
         2.0       0.00      0.00      0.00         6
         3.0       0.00      0.00      0.00         1

    accuracy                           1.00     13210
   macro avg       0.25      0.25      0.25     13210
weighted avg       1.00      1.00      1.00     13210

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง trade_df สำหรับ trend_following: 13210 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 344 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 269994 bytes
✅ เทรน trend_following สำเร็จ

================================================================================
📊 กำลังเทรน counter_trend...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ counter_trend

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 71828 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5778/71828 rows (8.0%)
📊 ข้อมูลหลังกรอง counter_trend: 5778 samples

🛠️ Features used for training (Selected: 20 total):
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
📊 Class distribution สำหรับ counter_trend: {0.0: 5750, 1.0: 16, 2.0: 12}
✅ เตรียมข้อมูล counter_trend: 5778 samples, 20 features

✅ ข้อมูลพร้อม: X.shape=(5778, 20), y.shape=(5778,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ counter_trend

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 20 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 20 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 5750, 1.0: 16, 2.0: 12}
📈 Train: 3466, Val: 1156, Test: 1156
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 219 → 71805
   Val: 196 → 71797
   Test: 225 → 71785
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 3449, 1.0: 10, 2.0: 7}
🔄 ใช้ Safe Oversampling System...
📊 Class distribution: {0.0: 3449, 1.0: 10, 2.0: 7}
📊 Imbalance ratio: 0.002
⚠️ Imbalance รุนแรง (0.002) - ใช้ SMOTE
📊 Original: Counter({0.0: 3449, 1.0: 10, 2.0: 7})
📊 Balanced: Counter({0.0: 3449, 1.0: 3449, 2.0: 3449})
📊 Train class distribution หลังปรับสมดุล: {0.0: 3449, 1.0: 3449, 2.0: 3449}
📈 จำนวนข้อมูล: เดิม 3466 → หลังปรับสมดุล 10347
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ counter_trend

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 3
  📊 Classes: [0. 1. 2.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 3 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 1.0, 1.0: 1.0, 2.0: 1.0}
📊 counter_trend Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.022 - 0.102 (base: 0.062)
   num_leaves: 14 - 34 (base: 24)
   max_depth: 5 - 7 (base: 6)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ counter_trend: {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 38, 'min_data_in_leaf': 8, 'max_depth': 8, 'learning_rate': 0.09, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.79}
✅ Best CV score สำหรับ counter_trend: 0.9627

--- Features (Columns) ---
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
--- Sample Data (First 5 rows) ---
   DayOfWeek  Hour  IsMorning  IsAfternoon  IsEvening  IsNight  ...  High_Lag_1  High_Lag_2  Close_Lag_1  Close_Lag_3  Open_Lag_2   Close_MA_3
0          3     0          0            0          0        1  ...     1250.18     1250.19      1248.98      1249.21     1249.20  1249.400000
1          0    18          0            0          1        0  ...     1214.58     1211.38      1213.18      1210.04     1210.12  1211.286667
2          4     2          0            0          0        1  ...     1807.55     1807.47      1807.40      1807.12     1807.12  1806.970000
3          4     4          0            0          0        0  ...     1200.31     1200.19      1199.17      1199.67     1199.68  1199.536667
4          3     9          1            0          0        0  ...     3384.24     3399.32      3355.33      3396.94     3396.91  3378.103333

[5 rows x 20 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend
📊 Class distribution: {0.0: 3449, 1.0: 3449, 2.0: 3449}
📊 Imbalance ratio: 1.000
✅ ไม่ต้อง oversample - imbalance ยอมรับได้หรือมีข้อมูลเพียงพอ
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[63]	valid_0's multi_logloss: 0.0364777

Accuracy: 0.9870
Classification Report:
              precision    recall  f1-score   support

         0.0       0.99      0.99      0.99      1150
         1.0       0.00      0.00      0.00         3
         2.0       0.00      0.00      0.00         3

    accuracy                           0.99      1156
   macro avg       0.33      0.33      0.33      1156
weighted avg       0.99      0.99      0.99      1156

Confusion Matrix:
[[1141    6    3]
 [   3    0    0]
 [   3    0    0]]
📊 Test Set Accuracy: 0.9870, F1-Score: 0.9883
🔍 ประเมินผลแบบ Multiclass
✅ คำนวณ Multiclass AUC สำเร็จ: 0.7587
✅ counter_trend - Accuracy: 0.987, F1: 0.988, AUC: 0.759

🔍 ตรวจสอบคุณภาพโมเดล counter_trend...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (1156, 20)
   y_val shape: (1156,), unique: [0. 1. 2.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (1156, 20)
   y_val shape: (1156,), unique: [0. 1. 2.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (1156, 3)
   Multi-class classification detected (3 classes)
   Using model.predict() to get actual class values instead of argmax
   Final y_pred shape: (1156,), unique: [0. 1. 2.], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (1156,), unique values: [0. 1. 2.]
   y_pred shape: (1156,), unique values: [0. 1. 2.]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   Multi-class classification metrics calculated
   Final calculated metrics: Acc=0.9905, F1=0.9913, Prec=0.9922, Rec=0.9905
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.9905
   AUC: 0.8489
   F1: 0.9913
   Precision: 0.9922
   Recall: 0.9905
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.38
   Trades: 115
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 246.88
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (counter_trend) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🔄 พบโมเดลก่อนหน้า - ทำการเปรียบเทียบ...
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
📊 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 21:46:19
📊 โมเดล: GOLD MM60 (counter_trend)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================


================================================================================
📋 สรุปการตัดสินใจบันทึกโมเดล: GOLD MM60 (counter_trend)
================================================================================
❌ ตัดสินใจ: ไม่บันทึกโมเดル
🏷️ ประเภท: declined
📝 เหตุผล: โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ

📊 Metrics ปัจจุบัน:
   - Accuracy: 0.9905
   - AUC: 0.8489
   - F1: 0.9913
   - Win Rate: 65.00%
   - Expectancy: 49.38

📈 เปรียบเทียบกับโมเดลก่อนหน้า:
   - Accuracy: 0.9905 (➡️ ไม่เปลี่ยนแปลง)
   - Auc: 0.8489 (➡️ ไม่เปลี่ยนแปลง)
   - F1: 0.9913 (➡️ ไม่เปลี่ยนแปลง)
   - Win_Rate: 0.6500 (➡️ ไม่เปลี่ยนแปลง)
   - Expectancy: 49.3770 (➡️ ไม่เปลี่ยนแปลง)
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล counter_trend - โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น (FLEXIBLE) - ไม่มีการปรับปรุงที่สำคัญ
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ counter_trend
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_GOLD_M60 symbol GOLD timeframe M60 (counter_trend)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                Feature   Gain  Split
            Price_Range 0.2269 0.1352
                   Hour 0.1841 0.1107
H2_Volume_TrendStrength 0.1595 0.0613
              DayOfWeek 0.0616 0.0682
            RSI14_x_ATR 0.0574 0.1063
             ATR_ROC_i6 0.0403 0.0675
             ATR_ROC_i2 0.0366 0.0965
             ATR_ROC_i8 0.0317 0.0560
              IsEvening 0.0273 0.0219
             High_Lag_2 0.0262 0.0302
            IsAfternoon 0.0233 0.0343
              IsMorning 0.0207 0.0196
             High_Lag_1 0.0194 0.0766
            Close_Lag_3 0.0190 0.0213
             Open_Lag_2 0.0188 0.0046

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_counter_trend_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_counter_trend_feature_importance.csv (ขนาด: 1087 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
                Feature  Importance
            Price_Range    0.182231
                   Hour    0.124354
H2_Volume_TrendStrength    0.087034
        H2_Volume_Spike    0.079222
              DayOfWeek    0.056279
            RSI14_x_ATR    0.045535
             ATR_ROC_i6    0.043243
             ATR_ROC_i2    0.039020
             ATR_ROC_i8    0.035957
            Close_Lag_3    0.034778
             Open_Lag_2    0.033359
             Close_MA_3    0.032904
            IsAfternoon    0.031011
             High_Lag_2    0.029843
            Close_Lag_1    0.029400

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.99      0.99      0.99      1150
         1.0       0.00      0.00      0.00         3
         2.0       0.00      0.00      0.00         3

    accuracy                           0.99      1156
   macro avg       0.33      0.33      0.33      1156
weighted avg       0.99      0.99      0.99      1156

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง trade_df สำหรับ counter_trend: 1156 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 336 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 270336 bytes
✅ เทรน counter_trend สำเร็จ

================================================================================
📊 กำลังเทรน Target_Buy...
================================================================================

================================================================================
📊 กำลังเทรน trend_following สำหรับ Target_Buy...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 71661 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 65914/71661 rows (92.0%)
📊 ข้อมูลหลังกรอง trend_following: 65914 samples

🛠️ Features used for training (Selected: 20 total):
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
📊 Class distribution สำหรับ trend_following: {0.0: 65914}
✅ เตรียมข้อมูล trend_following: 65914 samples, 20 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 65914 samples, 20 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 65914 samples, 20 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน trend_following_Buy ล้มเหลว - result เป็น None

================================================================================
📊 กำลังเทรน counter_trend สำหรับ Target_Buy...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 71661 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5747/71661 rows (8.0%)
📊 ข้อมูลหลังกรอง counter_trend: 5747 samples

🛠️ Features used for training (Selected: 20 total):
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
📊 Class distribution สำหรับ counter_trend: {0.0: 5747}
✅ เตรียมข้อมูล counter_trend: 5747 samples, 20 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 5747 samples, 20 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 5747 samples, 20 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน counter_trend_Buy ล้มเหลว - result เป็น None

================================================================================
📊 กำลังเทรน Target_Sell...
================================================================================

================================================================================
📊 กำลังเทรน trend_following สำหรับ Target_Sell...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 71823 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 66045/71823 rows (92.0%)
📊 ข้อมูลหลังกรอง trend_following: 66045 samples

🛠️ Features used for training (Selected: 20 total):
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
📊 Class distribution สำหรับ trend_following: {0.0: 66027, 1.0: 18}
✅ เตรียมข้อมูล trend_following: 66045 samples, 20 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 66045 samples, 20 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 66045 samples, 20 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 66027, 1.0: 18}
📈 Train: 39627, Val: 13209, Test: 13209
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 0 → 71827
   Val: 4 → 71825
   Test: 2 → 71817
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 39616, 1.0: 11}
🔄 ใช้ Safe Oversampling System...
📊 Class distribution: {0.0: 39616, 1.0: 11}
📊 Imbalance ratio: 0.000
⚠️ Imbalance รุนแรง (0.000) - ใช้ SMOTE
📊 Original: Counter({0.0: 39616, 1.0: 11})
📊 Balanced: Counter({0.0: 39616, 1.0: 39616})
📊 Train class distribution หลังปรับสมดุล: {0.0: 39616, 1.0: 39616}
📈 จำนวนข้อมูล: เดิม 39627 → หลังปรับสมดุล 79232
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following_Sell (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following_Sell:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Sell_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Sell_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 39616, 1.0: 39616}
   Imbalance ratio: 1.0:1
  🎯 Auto Class Weight: balanced
📊 trend_following_Sell Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.015 - 0.085 (base: 0.050)
   num_leaves: 12 - 28 (base: 20)
   max_depth: 3 - 7 (base: 5)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ trend_following_Sell: {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 30, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 3, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ trend_following_Sell: 0.9996

--- Features (Columns) ---
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
--- Sample Data (First 5 rows) ---
   DayOfWeek  Hour  IsMorning  IsAfternoon  IsEvening  IsNight  ...  High_Lag_1  High_Lag_2  Close_Lag_1  Close_Lag_3  Open_Lag_2   Close_MA_3
0          1    13          0            1          0        0  ...     1990.97     1991.10      1988.16      1990.24     1990.28  1988.983333
1          4     9          1            0          0        0  ...     1756.85     1754.26      1755.64      1754.23     1754.26  1754.326667
2          2    14          0            1          0        0  ...     1407.86     1410.83      1406.09      1410.43     1410.42  1407.860000
3          1     7          0            0          0        0  ...     2050.01     2051.56      2049.36      2050.50     2050.49  2049.760000
4          1    16          0            0          1        0  ...     1251.50     1249.25      1249.91      1247.57     1247.52  1248.680000

[5 rows x 20 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following_Sell
📊 Class distribution: {0.0: 39616, 1.0: 39616}
📊 Imbalance ratio: 1.000
✅ ไม่ต้อง oversample - imbalance ยอมรับได้หรือมีข้อมูลเพียงพอ
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's binary_logloss: 0.0114995

Accuracy: 0.9974
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00     13205
         1.0       0.00      0.00      0.00         4

    accuracy                           1.00     13209
   macro avg       0.50      0.50      0.50     13209
weighted avg       1.00      1.00      1.00     13209

Confusion Matrix:
[[13175    30]
 [    4     0]]
📊 Test Set Accuracy: 0.9974, F1-Score: 0.9984
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.8734
✅ trend_following_Sell - Accuracy: 0.997, F1: 0.998, AUC: 0.873

🔍 ตรวจสอบคุณภาพโมเดล trend_following_Sell...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (13209, 20)
   y_val shape: (13209,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following_Sell)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (13209, 20)
   y_val shape: (13209,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (13209, 2)
   Binary classification detected
   Final y_pred shape: (13209,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (13209,), unique values: [0. 1.]
   y_pred shape: (13209,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification: negative=0.0, positive=1.0
   Binary metrics calculated with pos_label=1.0
   ⚠️ All metrics are 0, trying weighted average...
   Final calculated metrics: Acc=0.9973, F1=0.9984, Prec=0.9995, Rec=0.9973
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.9973
   AUC: 0.6774
   F1: 0.9984
   Precision: 0.9995
   Recall: 0.9973
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.89
   Trades: 1320
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 249.45
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (trend_following_Sell) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🔄 พบโมเดลก่อนหน้า - ทำการเปรียบเทียบ...
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
📊 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 21:46:36
📊 โมเดล: GOLD MM60 (trend_following_Sell)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2575 (-27.55%)
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2575 (-27.55%)
🏷️ ประเภท: declined
================================================================================


================================================================================
📋 สรุปการตัดสินใจบันทึกโมเดล: GOLD MM60 (trend_following_Sell)
================================================================================
❌ ตัดสินใจ: ไม่บันทึกโมเดル
🏷️ ประเภท: declined
📝 เหตุผล: โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2575 (-27.55%)

📊 Metrics ปัจจุบัน:
   - Accuracy: 0.9973
   - AUC: 0.6774
   - F1: 0.9984
   - Win Rate: 65.00%
   - Expectancy: 49.89

📈 เปรียบเทียบกับโมเดลก่อนหน้า:
   - Accuracy: 0.9973 (↗️ +0.0056, +0.6%)
   - Auc: 0.6774 (↘️ -0.2575, -27.5%)
   - F1: 0.9984 (↗️ +0.0104, +1.1%)
   - Win_Rate: 0.6500 (➡️ ไม่เปลี่ยนแปลง)
   - Expectancy: 49.8910 (↗️ +0.3747, +0.8%)
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล trend_following_Sell - โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2575 (-27.55%)
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2575 (-27.55%)
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ trend_following_Sell
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_Sell_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_Sell_GOLD_M60 symbol GOLD timeframe M60 (trend_following_Sell)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                Feature   Gain  Split
                   Hour 0.3190 0.1374
H2_Volume_TrendStrength 0.1029 0.0746
              DayOfWeek 0.0729 0.0801
            RSI14_x_ATR 0.0704 0.1282
            IsAfternoon 0.0619 0.0287
              IsMorning 0.0526 0.0375
             ATR_ROC_i2 0.0499 0.0611
              IsEvening 0.0369 0.0358
            Price_Range 0.0328 0.0658
             ATR_ROC_i6 0.0300 0.0565
                IsNight 0.0283 0.0076
             ATR_ROC_i8 0.0259 0.0573
             Open_Lag_2 0.0255 0.0228
             High_Lag_2 0.0210 0.0438
            Close_Lag_1 0.0203 0.0405

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_Sell_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_Sell_feature_importance.csv (ขนาด: 1081 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
                Feature  Importance
                   Hour    0.141020
        H2_Volume_Spike    0.096200
H2_Volume_TrendStrength    0.086787
                IsNight    0.084873
              DayOfWeek    0.075963
             ATR_ROC_i6    0.062992
             ATR_ROC_i8    0.055875
            RSI14_x_ATR    0.051244
             ATR_ROC_i2    0.044076
            Close_Lag_3    0.041277
            IsAfternoon    0.035447
             High_Lag_2    0.034404
             ATR_ROC_i4    0.033477
             Open_Lag_2    0.032024
             Close_MA_3    0.027704

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00     13205
         1.0       0.00      0.00      0.00         4

    accuracy                           1.00     13209
   macro avg       0.50      0.50      0.50     13209
weighted avg       1.00      1.00      1.00     13209

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง trade_df สำหรับ trend_following_Sell: 13209 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following_Sell
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following_Sell/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 359 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following_Sell
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 275076 bytes
✅ เทรน trend_following_Sell สำเร็จ

================================================================================
📊 กำลังเทรน counter_trend สำหรับ Target_Sell...
================================================================================

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 71823 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5778/71823 rows (8.0%)
📊 ข้อมูลหลังกรอง counter_trend: 5778 samples

🛠️ Features used for training (Selected: 20 total):
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
📊 Class distribution สำหรับ counter_trend: {0.0: 5775, 1.0: 3}
✅ เตรียมข้อมูล counter_trend: 5778 samples, 20 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 20 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 20 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 5775, 1.0: 3}
📈 Train: 3466, Val: 1156, Test: 1156
🔍 ตรวจสอบ data leakage...
📊 Index ranges:
   Train: 196 → 71805
   Val: 744 → 71796
   Test: 225 → 71803
⚠️ Warning: Training data overlaps with validation data!
💡 This might be OK if using stratified split instead of time series split
⚠️ Warning: Validation data overlaps with test data!
💡 This might be OK if using stratified split instead of time series split
✅ ไม่พบ features ที่น่าสงสัย
✅ การตรวจสอบ data leakage เสร็จสิ้น
✅ ผ่านการตรวจสอบ Data Leakage
📊 Train class distribution ก่อนปรับสมดุล: {0.0: 3465, 1.0: 1}
⚠️ ไม่สามารถใช้ SMOTE ได้เนื่องจากมีข้อมูลไม่เพียงพอในบาง class
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend_Sell (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend_Sell:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Sell_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Sell_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 3465, 1.0: 1}
   Imbalance ratio: 3465.0:1
   Enhanced class weight: {0.0: 1, 1.0: 15}
  🎯 Auto Class Weight: {0.0: 1, 1.0: 15}
📊 counter_trend_Sell Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.015 - 0.085 (base: 0.050)
   num_leaves: 12 - 28 (base: 20)
   max_depth: 3 - 7 (base: 5)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ counter_trend_Sell: {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ counter_trend_Sell: 0.9994

--- Features (Columns) ---
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'ATR_ROC_i6', 'ATR_ROC_i8', 'ATR_ROC_i2', 'ATR_ROC_i4', 'H2_Volume_TrendStrength', 'H2_Volume_Spike', 'Price_Range', 'RSI14_x_ATR', 'High_Lag_1', 'High_Lag_2', 'Close_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_3']
--- Sample Data (First 5 rows) ---
       DayOfWeek  Hour  IsMorning  IsAfternoon  IsEvening  IsNight  ...  High_Lag_1  High_Lag_2  Close_Lag_1  Close_Lag_3  Open_Lag_2   Close_MA_3
20079          0    15          0            1          0        0  ...     1265.62     1266.13      1265.01      1266.12     1266.09  1265.256667
46527          3     9          1            0          0        0  ...     1714.96     1713.21      1714.50      1712.78     1712.73  1712.823333
59858          1     3          0            0          0        1  ...     1921.35     1921.56      1921.23      1921.43     1920.89  1921.290000
8400           3    17          0            0          1        0  ...     1196.80     1196.79      1194.53      1195.70     1195.65  1195.503333
71298          1    17          0            0          1        0  ...     3349.14     3340.23      3343.59      3333.65     3333.65  3338.120000

[5 rows x 20 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend_Sell
📊 Class distribution: {0.0: 3465, 1.0: 1}
📊 Imbalance ratio: 0.000
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 3465, 1.0: 1}
🔄 Oversampling class 1.0: 1 -> 200 (+199 samples)
📊 Class distribution หลัง oversample: {0.0: 3465, 1.0: 200}
📊 Class distribution หลัง oversample: {0.0: 3465, 1.0: 200}
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[75]	valid_0's binary_logloss: 0.00760335

Accuracy: 0.9991
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00      1155
         1.0       0.00      0.00      0.00         1

    accuracy                           1.00      1156
   macro avg       0.50      0.50      0.50      1156
weighted avg       1.00      1.00      1.00      1156

Confusion Matrix:
[[1155    0]
 [   1    0]]
📊 Test Set Accuracy: 0.9991, F1-Score: 0.9987
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.5212
✅ counter_trend_Sell - Accuracy: 0.999, F1: 0.999, AUC: 0.521

🔍 ตรวจสอบคุณภาพโมเดล counter_trend_Sell...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (1156, 20)
   y_val shape: (1156,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>

🔍 ตรวจสอบคุณภาพโมเดลก่อนบันทึก:
   💰 Total Profit: $0.00
   🎯 Win Rate: 0.0%
   📊 Expectancy: 0.00
🔧 Development Mode: ข้ามการตรวจสอบ Model Protection

🏗️ เปิดใช้งาน evaluate and decide model save
================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend_Sell)
================================================================================

📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (1156, 20)
   y_val shape: (1156,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (1156, 2)
   Binary classification detected
   Final y_pred shape: (1156,), unique: [0], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (1156,), unique values: [0. 1.]
   y_pred shape: (1156,), unique values: [0]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification: negative=0.0, positive=1.0
   Binary metrics calculated with pos_label=1.0
   ⚠️ All metrics are 0, trying weighted average...
   Final calculated metrics: Acc=0.9991, F1=0.9987, Prec=0.9983, Rec=0.9991
   ⚠️ ไม่พบข้อมูล trade_df - ข้ามการสร้างรายงาน
📈 คำนวณ Advanced Trading Metrics...
   ⚠️ ไม่พบข้อมูล trade_df - ใช้ basic trading stats เท่านั้น
   ✅ Advanced Trading Metrics added to evaluation
✅ ML Metrics:
   Accuracy: 0.9991
   AUC: 0.5368
   F1: 0.9987
   Precision: 0.9983
   Recall: 0.9991
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.95
   Trades: 115
✅ Advanced Trading Metrics:
   Sharpe Ratio: 2.000
   Max Drawdown: 249.74
   Profit Factor: 1.86
   Win Rate: 65.0%

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
🔍 ตรวจสอบ Advanced Trading Metrics...
   ✅ Advanced Trading Metrics validation completed
✅ โมเดล GOLD MM60 (counter_trend_Sell) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🔄 พบโมเดลก่อนหน้า - ทำการเปรียบเทียบ...
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
📊 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-26 21:47:08
📊 โมเดล: GOLD MM60 (counter_trend_Sell)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2896 (-35.04%)
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2896 (-35.04%)
🏷️ ประเภท: declined
================================================================================


================================================================================
📋 สรุปการตัดสินใจบันทึกโมเดล: GOLD MM60 (counter_trend_Sell)
================================================================================
❌ ตัดสินใจ: ไม่บันทึกโมเดル
🏷️ ประเภท: declined
📝 เหตุผล: โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2896 (-35.04%)

📊 Metrics ปัจจุบัน:
   - Accuracy: 0.9991
   - AUC: 0.5368
   - F1: 0.9987
   - Win Rate: 65.00%
   - Expectancy: 49.95

📈 เปรียบเทียบกับโมเดลก่อนหน้า:
   - Accuracy: 0.9991 (↗️ +0.0235, +2.4%)
   - Auc: 0.5368 (↘️ -0.2896, -35.0%)
   - F1: 0.9987 (↗️ +0.0312, +3.2%)
   - Win_Rate: 0.6500 (➡️ ไม่เปลี่ยนแปลง)
   - Expectancy: 49.9481 (↗️ +1.2106, +2.5%)
================================================================================
🛡️ Model Protection System: อนุญาตให้บันทึก (development_mode)
⏭️ ไม่บันทึกโมเดล counter_trend_Sell - โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2896 (-35.04%)
📝 บันทึกการเทรน: GOLD_M60 → rejected_โมเดลไม่ดีขึ้น (FLEXIBLE) - auc: -0.2896 (-35.04%)
💡 แนะนำ: ใช้โมเดลเดิมที่มีประสิทธิภาพดีกว่า
📊 สร้าง Feature Importance สำหรับ counter_trend_Sell
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_Sell_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_Sell_GOLD_M60 symbol GOLD timeframe M60 (counter_trend_Sell)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                Feature   Gain  Split
             ATR_ROC_i6 0.8115 0.5900
             ATR_ROC_i4 0.1053 0.0700
             ATR_ROC_i8 0.0808 0.2300
H2_Volume_TrendStrength 0.0021 0.0950
        H2_Volume_Spike 0.0003 0.0100
                   Hour 0.0000 0.0050
            RSI14_x_ATR 0.0000 0.0000
             Open_Lag_2 0.0000 0.0000
            Close_Lag_3 0.0000 0.0000
            Close_Lag_1 0.0000 0.0000
             High_Lag_2 0.0000 0.0000
             High_Lag_1 0.0000 0.0000
              DayOfWeek 0.0000 0.0000
            Price_Range 0.0000 0.0000
             ATR_ROC_i2 0.0000 0.0000

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_counter_trend_Sell_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_counter_trend_Sell_feature_importance.csv (ขนาด: 538 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
                Feature  Importance
             ATR_ROC_i6    0.179121
             ATR_ROC_i8    0.135318
             Close_MA_3    0.101389
             ATR_ROC_i4    0.090439
             Open_Lag_2    0.076063
            Close_Lag_1    0.067344
H2_Volume_TrendStrength    0.056711
            Close_Lag_3    0.055744
             High_Lag_1    0.052584
             ATR_ROC_i2    0.044215
             High_Lag_2    0.041039
        H2_Volume_Spike    0.037136
              IsMorning    0.036122
            Price_Range    0.017867
                   Hour    0.005811

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00      1155
         1.0       0.00      0.00      0.00         1

    accuracy                           1.00      1156
   macro avg       0.50      0.50      0.50      1156
weighted avg       1.00      1.00      1.00      1156

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend_Sell
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend_Sell
✅ สร้าง trade_df สำหรับ counter_trend_Sell: 1156 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend_Sell
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend_Sell/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 351 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend_Sell
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 271563 bytes
✅ เทรน counter_trend_Sell สำเร็จ

================================================================================

✅ เทรนเสร็จสิ้น: 4 โมเดล
================================================================================
🔍 Debug: ผลลัพธ์การเทรน:
  ✅ trend_following: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ trend_following_Sell: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend_Sell: มีผลลัพธ์
    📊 Feature Importance: True

📊 บันทึก Random Forest Feature Importance

🏗️ เปิดใช้งาน save combined random forest importance
📊 รวบรวม RF Feature Importance จาก trend_following
📊 รวบรวม RF Feature Importance จาก counter_trend
📊 รวบรวม RF Feature Importance จาก trend_following_Sell
📊 รวบรวม RF Feature Importance จาก counter_trend_Sell
🔍 Debug: คอลัมน์ใน combined_rf_df: ['Feature', 'Importance', 'Scenario']
💾 บันทึก Random Forest Feature Importance: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv
📊 รวมจาก 4 scenarios, 20 features

🏆 Top 10 Random Forest Features:
  ATR_ROC_i6: Importance=0.0838
  Price_Range: Importance=0.0735
  Hour: Importance=0.0727
  ATR_ROC_i8: Importance=0.0715
  RSI14_x_ATR: Importance=0.0655
  Close_MA_3: Importance=0.0608
  H2_Volume_TrendStrength: Importance=0.0586
  Open_Lag_2: Importance=0.0563
  H2_Volume_Spike: Importance=0.0544
  Close_Lag_3: Importance=0.0506

📊 สร้าง Combined Feature Importance จากทุก scenarios

🏗️ เปิดใช้งาน create combined feature importance
📊 รวบรวม Feature Importance จาก trend_following
📊 รวบรวม Feature Importance จาก counter_trend
📊 รวบรวม Feature Importance จาก trend_following_Sell
📊 รวบรวม Feature Importance จาก counter_trend_Sell
💾 บันทึก Combined Feature Importance: LightGBM/Multi/results/M60\M60_GOLD_feature_importance.csv
📊 รวมจาก 4 scenarios, 20 features

🏆 Top 10 Features (Combined):
  ATR_ROC_i6: Gain=0.2286, Split=0.1993, Count=4
  Hour: Gain=0.1371, Split=0.0794, Count=4
  RSI14_x_ATR: Gain=0.1183, Split=0.0909, Count=4
  Price_Range: Gain=0.1012, Split=0.0848, Count=4
  ATR_ROC_i2: Gain=0.0689, Split=0.0752, Count=4
  H2_Volume_TrendStrength: Gain=0.0684, Split=0.0600, Count=4
  ATR_ROC_i8: Gain=0.0462, Split=0.1068, Count=4
  ATR_ROC_i4: Gain=0.0449, Split=0.0679, Count=4
  DayOfWeek: Gain=0.0368, Split=0.0430, Count=4
  IsAfternoon: Gain=0.0227, Split=0.0202, Count=4

🔍 ทำ Cross-Validation และ Threshold Optimization สำหรับ Multi-Model
📊 ทำ Time Series Cross-Validation...

🏗️ เปิดใช้งาน time series cv
🔁 เริ่มทำ Time Series Cross-Validation (n_splits=5, original=5)
📊 CV Configuration: splits=5, test_size=10261, total_samples=71828

📊 Fold 1/5:
  - Train size: 20523 ตัวอย่าง (28.6% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9983920479462067, 1.0: 0.0011206938556741217, 2.0: 0.00048725819811918335}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 3
  📊 Classes: [0. 1. 2.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 3 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.3338701805758907, 1.0: 297.4347826086956, 2.0: 684.1}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 138 trees)
  📊 ผลลัพธ์ Fold 1:
    - Accuracy:  0.9985
    - AUC:       0.7044
    - F1 Score:  0.9979
    - Precision: 0.9973
    - Recall:    0.9985

📊 Fold 2/5:
  - Train size: 30784 ตัวอย่าง (42.9% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9984732328482329, 1.0: 0.001071985446985447, 2.0: 0.0004547817047817048}
⚠️ เตือน : ใน Fold 2 พบคลาสใน validation ที่ไม่มีใน training: {3.0}
  - Training classes: [0.0, 1.0, 2.0]
  - Validation classes: [0.0, 1.0, 2.0, 3.0]
⚠️ ข้าม Fold 2 เนื่องจากมีคลาสที่ไม่เคยเห็นใน training

📊 Fold 3/5:
  - Train size: 41045 ตัวอย่าง (57.1% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9983676452673894, 1.0: 0.0010476306492873675, 2.0: 0.0005116335729077841, 3.0: 7.309051041539774e-05}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 4
  📊 Classes: [0. 1. 2. 3.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 4 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.25040875591780953, 1.0: 238.63372093023256, 2.0: 488.6309523809524, 3.0: 3420.4166666666665}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 33 trees)
  📊 ผลลัพธ์ Fold 3:
    - Accuracy:  0.9968
    - AUC:       0.5000
    - F1 Score:  0.9952
    - Precision: 0.9936
    - Recall:    0.9968

📊 Fold 4/5:
  - Train size: 51306 ตัวอย่าง (71.4% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.998050910224925, 1.0: 0.0013058901493002769, 2.0: 0.0005847269325225119, 3.0: 5.84726932522512e-05}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 4
  📊 Classes: [0. 1. 2. 3.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 4 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.25048822403624577, 1.0: 191.4402985074627, 2.0: 427.55, 3.0: 4275.5}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 196 trees)
  📊 ผลลัพธ์ Fold 4:
    - Accuracy:  0.9969
    - AUC:       0.5000
    - F1 Score:  0.9954
    - Precision: 0.9940
    - Recall:    0.9969

📊 Fold 5/5:
  - Train size: 61567 ตัวอย่าง (85.7% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9978722367502071, 1.0: 0.0015105494826774084, 2.0: 0.000568486364448487, 3.0: 4.872740266701317e-05}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 4
  📊 Classes: [0. 1. 2. 3.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 4 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.25053307506999156, 1.0: 165.502688172043, 2.0: 439.76428571428573, 3.0: 5130.583333333333}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 103 trees)
  📊 ผลลัพธ์ Fold 5:
    - Accuracy:  0.9981
    - AUC:       0.5614
    - F1 Score:  0.9971
    - Precision: 0.9961
    - Recall:    0.9981

📊 สรุปการทำ Time Series CV:
   - จำนวน folds ที่วางแผน: 5
   - จำนวน folds ที่สำเร็จ: 4

✅ การทำ Time Series Cross-Validation เสร็จสมบูรณ์
📌 ผลลัพธ์เฉลี่ย:
  - Accuracy:  0.9976
  - AUC:       0.5665
  - F1 Score:  0.9964
  - Precision: 0.9952
  - Recall:    0.9976
✅ Time Series CV เสร็จสิ้น: AUC=0.566, F1=0.996
🔍 Debug: กำลังบันทึก CV results ที่ LightGBM/Multi/results\multi_scenario_cv_results.json
💾 บันทึก CV Results: LightGBM/Multi/results\multi_scenario_cv_results.json
📁 ขนาดไฟล์: 164 bytes

📊 สร้าง Performance Analysis และ Comparison Plots

🏗️ เปิดใช้งาน create multi scenario performance analysis
🔍 Debug: กำลังบันทึกไฟล์ที่ LightGBM/Multi/results\multi_scenario_performance_analysis.txt
💾 บันทึก Multi-Scenario Performance Analysis: LightGBM/Multi/results\multi_scenario_performance_analysis.txt
📁 ขนาดไฟล์: 1370 bytes

🏗️ เปิดใช้งาน create performance comparison plots
🔍 Debug: สร้างโฟลเดอร์ plots ที่ LightGBM/Multi/results/plots
🔍 Debug: จำนวน scenarios: 4
🔍 Debug: scenarios: ['trend_following', 'counter_trend', 'trend_following_Sell', 'counter_trend_Sell']
🔍 Debug: metrics data: {'AUC': [0.7498156383961624, 0.758657521474995, 0.8734191594093147, 0.5212121212121212], 'F1_Score': [0.9858014885113722, 0.9883133257912758, 0.9984089066108471, 0.9987026093049788], 'Accuracy': [0.9756245268735806, 0.9870242214532872, 0.9974259974259975, 0.9991349480968859]}
🔍 Debug: สร้างกราฟสำหรับ AUC
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_auc_comparison.png
💾 บันทึก AUC Comparison Plot: LightGBM/Multi/results/plots\performance_auc_comparison.png
📁 ขนาดไฟล์: 141771 bytes
🔍 Debug: สร้างกราฟสำหรับ F1_Score
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_f1_score_comparison.png
💾 บันทึก F1_Score Comparison Plot: LightGBM/Multi/results/plots\performance_f1_score_comparison.png
📁 ขนาดไฟล์: 148205 bytes
🔍 Debug: สร้างกราฟสำหรับ Accuracy
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_accuracy_comparison.png
💾 บันทึก Accuracy Comparison Plot: LightGBM/Multi/results/plots\performance_accuracy_comparison.png
📁 ขนาดไฟล์: 148579 bytes
🔍 Debug: สร้างกราฟรวมทุก metrics
🔍 Debug: กำลังบันทึกกราฟรวมที่ LightGBM/Multi/results/plots\performance_combined_comparison.png
💾 บันทึก Combined Performance Comparison Plot: LightGBM/Multi/results/plots\performance_combined_comparison.png
📁 ขนาดไฟล์: 214646 bytes

🏗️ เปิดใช้งาน create final and training results
💾 บันทึก Final Results: LightGBM/Multi/results/trend_following\final_results.csv
💾 บันทึก Training Results: LightGBM/Multi/results/trend_following\training_results.csv

🏗️ เปิดใช้งาน create group feature importance comparison
💾 บันทึก Feature Importance Comparison: LightGBM/Multi/results/trend_following\M60_GOLD_feature_importance_comparison.csv

🏗️ เปิดใช้งาน create feature importance comparison plot
💾 บันทึก Feature Importance Comparison Plot: LightGBM/Multi/results/trend_following\M60_GOLD_feature_importance_comparison.png
💾 บันทึก Final Results: LightGBM/Multi/results/counter_trend\final_results.csv
💾 บันทึก Training Results: LightGBM/Multi/results/counter_trend\training_results.csv

🏗️ เปิดใช้งาน create group feature importance comparison
💾 บันทึก Feature Importance Comparison: LightGBM/Multi/results/counter_trend\M60_GOLD_feature_importance_comparison.csv

🏗️ เปิดใช้งาน create feature importance comparison plot
💾 บันทึก Feature Importance Comparison Plot: LightGBM/Multi/results/counter_trend\M60_GOLD_feature_importance_comparison.png

🏗️ เริ่มบันทึกผลลัพธ์ลงระบบสรุป Multi-Model
🔍 Debug: จำนวน results = 4
🔍 Debug: ตรวจสอบ trend_following
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 13210
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ trend_following
   📊 Test stats: {'buy': {'count': 36, 'win_rate': 69.44, 'expectancy': 4.05}, 'sell': {'count': 13174, 'win_rate': 63.37, 'expectancy': 5.01}, 'buy_sell': {'count': 13210, 'win_rate': 63.39, 'expectancy': 5.0}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 20, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9756245268735806, 'auc': 0.7498156383961624, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=trend_following
🔍 Debug: train_val_stats={'buy': {'count': 36, 'win_rate': 69.44, 'expectancy': 4.05}, 'sell': {'count': 13174, 'win_rate': 63.37, 'expectancy': 5.01}, 'buy_sell': {'count': 13210, 'win_rate': 63.39, 'expectancy': 5.0}}
🔍 Debug: test_stats={'buy': {'count': 36, 'win_rate': 69.44, 'expectancy': 4.05}, 'sell': {'count': 13174, 'win_rate': 63.37, 'expectancy': 5.01}, 'buy_sell': {'count': 13210, 'win_rate': 63.39, 'expectancy': 5.0}}
🔍 Debug: model_metrics={'accuracy': 0.9756245268735806, 'auc': 0.7498156383961624, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 20, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 82.4/100
📈 Win Rate เฉลี่ย: 63.4%, Expectancy เฉลี่ย: 5.00
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 82.4)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 82.37
✅ บันทึกสรุปสำหรับ trend_following เรียบร้อย
🔍 Debug: ตรวจสอบ counter_trend
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 1156
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ counter_trend
   📊 Test stats: {'buy': {'count': 3, 'win_rate': 66.67, 'expectancy': 8.66}, 'sell': {'count': 1153, 'win_rate': 62.36, 'expectancy': 4.62}, 'buy_sell': {'count': 1156, 'win_rate': 62.37, 'expectancy': 4.63}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 20, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9870242214532872, 'auc': 0.758657521474995, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=counter_trend
🔍 Debug: train_val_stats={'buy': {'count': 3, 'win_rate': 66.67, 'expectancy': 8.66}, 'sell': {'count': 1153, 'win_rate': 62.36, 'expectancy': 4.62}, 'buy_sell': {'count': 1156, 'win_rate': 62.37, 'expectancy': 4.63}}
🔍 Debug: test_stats={'buy': {'count': 3, 'win_rate': 66.67, 'expectancy': 8.66}, 'sell': {'count': 1153, 'win_rate': 62.36, 'expectancy': 4.62}, 'buy_sell': {'count': 1156, 'win_rate': 62.37, 'expectancy': 4.63}}
🔍 Debug: model_metrics={'accuracy': 0.9870242214532872, 'auc': 0.758657521474995, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 20, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 82.5/100
📈 Win Rate เฉลี่ย: 62.4%, Expectancy เฉลี่ย: 4.63
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 82.5)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 82.50
✅ บันทึกสรุปสำหรับ counter_trend เรียบร้อย
🔍 Debug: ตรวจสอบ trend_following_Sell
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 13209
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ trend_following_Sell
   📊 Test stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 13209, 'win_rate': 63.52, 'expectancy': 5.22}, 'buy_sell': {'count': 13209, 'win_rate': 63.52, 'expectancy': 5.22}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 20, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9974259974259975, 'auc': 0.8734191594093147, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=trend_following_Sell
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 13209, 'win_rate': 63.52, 'expectancy': 5.22}, 'buy_sell': {'count': 13209, 'win_rate': 63.52, 'expectancy': 5.22}}
🔍 Debug: test_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 13209, 'win_rate': 63.52, 'expectancy': 5.22}, 'buy_sell': {'count': 13209, 'win_rate': 63.52, 'expectancy': 5.22}}
🔍 Debug: model_metrics={'accuracy': 0.9974259974259975, 'auc': 0.8734191594093147, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 20, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 80.4/100
📈 Win Rate เฉลี่ย: 63.5%, Expectancy เฉลี่ย: 5.22
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 80.4)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 80.40
✅ บันทึกสรุปสำหรับ trend_following_Sell เรียบร้อย
🔍 Debug: ตรวจสอบ counter_trend_Sell
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 1156
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ counter_trend_Sell
   📊 Test stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1156, 'win_rate': 65.66, 'expectancy': 5.52}, 'buy_sell': {'count': 1156, 'win_rate': 65.66, 'expectancy': 5.52}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 20, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9991349480968859, 'auc': 0.5212121212121212, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=counter_trend_Sell
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1156, 'win_rate': 65.66, 'expectancy': 5.52}, 'buy_sell': {'count': 1156, 'win_rate': 65.66, 'expectancy': 5.52}}
🔍 Debug: test_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1156, 'win_rate': 65.66, 'expectancy': 5.52}, 'buy_sell': {'count': 1156, 'win_rate': 65.66, 'expectancy': 5.52}}
🔍 Debug: model_metrics={'accuracy': 0.9991349480968859, 'auc': 0.5212121212121212, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 20, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 1, คะแนนเฉลี่ย: 77.1/100
📈 Win Rate เฉลี่ย: 65.7%, Expectancy เฉลี่ย: 5.52
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 77.1)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 77.09
✅ บันทึกสรุปสำหรับ counter_trend_Sell เรียบร้อย
📊 บันทึกสรุปเสร็จสิ้น: 4/4 scenarios
✅ เทรนโมเดลสำเร็จ: 4 scenarios
🔍 Debug Multi-Model: จำนวน scenarios = 4
🔍 Debug Scenario 'trend_following': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'trend_following' metrics: {'accuracy': 0.9756245268735806, 'f1_score': 0.9858014885113722, 'auc': 0.7498156383961624, 'train_samples': 40157, 'test_samples': 13210, 'scenario': 'trend_following'}
🔍 Debug Scenario 'trend_following' cv_results: {'best_score': 0.8057651027292242, 'best_params': {'reg_lambda': 0.0, 'reg_alpha': 0.005, 'num_leaves': 22, 'min_data_in_leaf': 10, 'max_depth': 7, 'learning_rate': 0.035, 'feature_fraction': 0.84, 'bagging_freq': 1, 'bagging_fraction': 0.87}, 'scenario': 'trend_following'}
🔍 Debug Scenario 'counter_trend': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'counter_trend' metrics: {'accuracy': 0.9870242214532872, 'f1_score': 0.9883133257912758, 'auc': 0.758657521474995, 'train_samples': 10347, 'test_samples': 1156, 'scenario': 'counter_trend'}
🔍 Debug Scenario 'counter_trend' cv_results: {'best_score': 0.9626579992777176, 'best_params': {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 38, 'min_data_in_leaf': 8, 'max_depth': 8, 'learning_rate': 0.09, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.79}, 'scenario': 'counter_trend'}
🔍 Debug Scenario 'trend_following_Sell': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'trend_following_Sell' metrics: {'accuracy': 0.9974259974259975, 'f1_score': 0.9984089066108471, 'auc': 0.8734191594093147, 'train_samples': 79232, 'test_samples': 13209, 'scenario': 'trend_following_Sell'}
🔍 Debug Scenario 'trend_following_Sell' cv_results: {'best_score': 0.9995844460994799, 'best_params': {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 30, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 3, 'bagging_fraction': 0.9}, 'scenario': 'trend_following_Sell'}
🔍 Debug Scenario 'counter_trend_Sell': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'counter_trend_Sell' metrics: {'accuracy': 0.9991349480968859, 'f1_score': 0.9987026093049788, 'auc': 0.5212121212121212, 'train_samples': 3665, 'test_samples': 1156, 'scenario': 'counter_trend_Sell'}
🔍 Debug Scenario 'counter_trend_Sell' cv_results: {'best_score': 0.9993774118785894, 'best_params': {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}, 'scenario': 'counter_trend_Sell'}
🔍 Debug avg_metrics['accuracy'] = 0.9898 (จาก 4 scenarios)
🔍 Debug avg_metrics['f1_score'] = 0.9928 (จาก 4 scenarios)
🔍 Debug avg_metrics['auc'] = 0.7258 (จาก 4 scenarios)
🔍 Debug avg_metrics['train_samples'] = 33350.2500 (จาก 4 scenarios)
🔍 Debug avg_metrics['test_samples'] = 7182.7500 (จาก 4 scenarios)
⚠️ ไม่มีค่าตัวเลขสำหรับ scenario: ['trend_following', 'counter_trend', 'trend_following_Sell', 'counter_trend_Sell']
🔍 Debug avg_cv_results['best_score'] = 0.9418 (จาก 4 scenarios)
⚠️ ไม่มีค่าตัวเลขสำหรับ best_params: [{'reg_lambda': 0.0, 'reg_alpha': 0.005, 'num_leaves': 22, 'min_data_in_leaf': 10, 'max_depth': 7, 'learning_rate': 0.035, 'feature_fraction': 0.84, 'bagging_freq': 1, 'bagging_fraction': 0.87}, {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 38, 'min_data_in_leaf': 8, 'max_depth': 8, 'learning_rate': 0.09, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.79}, {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 30, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 3, 'bagging_fraction': 0.9}, {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}]
⚠️ ไม่มีค่าตัวเลขสำหรับ scenario: ['trend_following', 'counter_trend', 'trend_following_Sell', 'counter_trend_Sell']
🔍 Debug Final avg_metrics: {'accuracy': 0.9898024234624377, 'f1_score': 0.9928065825546185, 'auc': 0.7257761101231484, 'train_samples': 33350.25, 'test_samples': 7182.75}
🔍 Debug Final avg_cv_results: {'best_score': 0.9418462399962528}

==================================================
🎯 เริ่มทดสอบ Optimal Parameters
==================================================
🔍 Debug validation data:
   - X_val shape: (25, 20)
   - X_val index range: 69 - 141
   - combined_df shape: (71828, 342)
   - combined_df index range: 0 - 71827
✅ พบ indices ที่ตรงกัน: 25 จาก 25
📊 ข้อมูล validation สำหรับ optimization:
   - จำนวน samples: 25
   - จำนวน features: 20
   - Index range: 69 - 141
✅ ข้อมูล validation เพียงพอสำหรับการทดสอบ (25 samples)

💾 การบันทึก Artifacts สำหรับการทดสอบแบบ Offline...
✅ บันทึก Models Artifact สำเร็จที่: LightGBM/Data_Trained\M60_GOLD_scenario_results.pkl
✅ บันทึก Validation Set Artifact สำเร็จที่: LightGBM/Data_Trained\M60_GOLD_validation_set.csv
📋 จะทดสอบ 6 scenarios: ['trend_following', 'counter_trend', 'trend_following_Buy', 'counter_trend_Buy', 'trend_following_Sell', 'counter_trend_Sell']

📌 Running scenario: trend_following
🏗️ เปิดใช้งาน find optimal threshold multi model (Enhanced)

======================================================================
🎯 เริ่มการหา Optimal Threshold
📊 วิธีการ: YOUDEN
======================================================================
📊 จำนวนข้อมูล: 25 samples
📊 Positive samples: 0.0 (0.0%)
🎯 Min threshold: 0.3

⚠️ ไม่มี positive samples ใน validation set!
🔧 ใช้ fallback strategy เพื่อหา threshold ที่เหมาะสม

📈 ผลลัพธ์ Fallback Strategy:
   🎯 Fallback Threshold: 0.6500
   📊 Accuracy: 1.0000
   📊 Precision: 0.0000
   📊 Recall: 0.0000
   📊 F1 Score: 0.0000
   📊 ROC AUC: 0.5000
   🔧 Method: fallback (no positive samples)
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': 0.5}
💾 Saved: LightGBM/Multi/thresholds/M60_GOLD_trend_following_optimal_threshold.pkl

📌 Running scenario: counter_trend
🏗️ เปิดใช้งาน find optimal threshold multi model (Enhanced)

======================================================================
🎯 เริ่มการหา Optimal Threshold
📊 วิธีการ: YOUDEN
======================================================================
📊 จำนวนข้อมูล: 25 samples
📊 Positive samples: 0.0 (0.0%)
🎯 Min threshold: 0.3

⚠️ ไม่มี positive samples ใน validation set!
🔧 ใช้ fallback strategy เพื่อหา threshold ที่เหมาะสม

📈 ผลลัพธ์ Fallback Strategy:
   🎯 Fallback Threshold: 0.6500
   📊 Accuracy: 1.0000
   📊 Precision: 0.0000
   📊 Recall: 0.0000
   📊 F1 Score: 0.0000
   📊 ROC AUC: 0.5000
   🔧 Method: fallback (no positive samples)
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': 0.5}
💾 Saved: LightGBM/Multi/thresholds/M60_GOLD_counter_trend_optimal_threshold.pkl

📌 Running scenario: trend_following_Buy
🏗️ เปิดใช้งาน find optimal threshold multi model (Enhanced)

======================================================================
🎯 เริ่มการหา Optimal Threshold
📊 วิธีการ: YOUDEN
======================================================================
📊 จำนวนข้อมูล: 25 samples
📊 Positive samples: 0.0 (0.0%)
🎯 Min threshold: 0.3

⚠️ ไม่มี positive samples ใน validation set!
🔧 ใช้ fallback strategy เพื่อหา threshold ที่เหมาะสม

📈 ผลลัพธ์ Fallback Strategy:
   🎯 Fallback Threshold: 0.6500
   📊 Accuracy: 1.0000
   📊 Precision: 0.0000
   📊 Recall: 0.0000
   📊 F1 Score: 0.0000
   📊 ROC AUC: 0.5000
   🔧 Method: fallback (no positive samples)
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': 0.5}
💾 Saved: LightGBM/Multi/thresholds/M60_GOLD_trend_following_Buy_optimal_threshold.pkl

📌 Running scenario: counter_trend_Buy
🏗️ เปิดใช้งาน find optimal threshold multi model (Enhanced)

======================================================================
🎯 เริ่มการหา Optimal Threshold
📊 วิธีการ: YOUDEN
======================================================================
📊 จำนวนข้อมูล: 25 samples
📊 Positive samples: 0.0 (0.0%)
🎯 Min threshold: 0.3

⚠️ ไม่มี positive samples ใน validation set!
🔧 ใช้ fallback strategy เพื่อหา threshold ที่เหมาะสม

📈 ผลลัพธ์ Fallback Strategy:
   🎯 Fallback Threshold: 0.6500
   📊 Accuracy: 0.9600
   📊 Precision: 0.0000
   📊 Recall: 0.0000
   📊 F1 Score: 0.0000
   📊 ROC AUC: 0.5000
   🔧 Method: fallback (no positive samples)
Best threshold: 0.65
Metrics: {'Accuracy': 0.96, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': 0.5}
💾 Saved: LightGBM/Multi/thresholds/M60_GOLD_counter_trend_Buy_optimal_threshold.pkl

📌 Running scenario: trend_following_Sell
🏗️ เปิดใช้งาน find optimal threshold multi model (Enhanced)

======================================================================
🎯 เริ่มการหา Optimal Threshold
📊 วิธีการ: YOUDEN
======================================================================
📊 จำนวนข้อมูล: 25 samples
📊 Positive samples: 0.0 (0.0%)
🎯 Min threshold: 0.3

⚠️ ไม่มี positive samples ใน validation set!
🔧 ใช้ fallback strategy เพื่อหา threshold ที่เหมาะสม

📈 ผลลัพธ์ Fallback Strategy:
   🎯 Fallback Threshold: 0.6500
   📊 Accuracy: 1.0000
   📊 Precision: 0.0000
   📊 Recall: 0.0000
   📊 F1 Score: 0.0000
   📊 ROC AUC: 0.5000
   🔧 Method: fallback (no positive samples)
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': 0.5}
💾 Saved: LightGBM/Multi/thresholds/M60_GOLD_trend_following_Sell_optimal_threshold.pkl

📌 Running scenario: counter_trend_Sell
🏗️ เปิดใช้งาน find optimal threshold multi model (Enhanced)

======================================================================
🎯 เริ่มการหา Optimal Threshold
📊 วิธีการ: YOUDEN
======================================================================
📊 จำนวนข้อมูล: 25 samples
📊 Positive samples: 0.0 (0.0%)
🎯 Min threshold: 0.3

⚠️ ไม่มี positive samples ใน validation set!
🔧 ใช้ fallback strategy เพื่อหา threshold ที่เหมาะสม

📈 ผลลัพธ์ Fallback Strategy:
   🎯 Fallback Threshold: 0.6500
   📊 Accuracy: 1.0000
   📊 Precision: 0.0000
   📊 Recall: 0.0000
   📊 F1 Score: 0.0000
   📊 ROC AUC: 0.5000
   🔧 Method: fallback (no positive samples)
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': 0.5}
💾 Saved: LightGBM/Multi/thresholds/M60_GOLD_counter_trend_Sell_optimal_threshold.pkl

🔍 ทดสอบการเรียกใช้งาน threshold
⚠️ trend_data is None หรือไม่มี best_threshold - ใช้ค่า default
⚠️ counter_data is None หรือไม่มี best_threshold - ใช้ค่า default
Best threshold: 0.3
Metrics: None
⚠️ counter_all_th_df is None - ไม่มีข้อมูล threshold

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
⚠️ ไม่พบไฟล์ threshold สำหรับ GOLD MM60, ใช้ค่า default: 0.3

✅ symbol GOLD M60 : threshold 0.3 : trend 0.3000 counter 0.3000

🎯 ทดสอบ Optimal nBars SL...
🏗️ เปิดใช้งาน find optimal nbars sl multi model (Enhanced)

======================================================================
🎯 เริ่มการทดสอบ Optimal nBars_SL สำหรับ GOLD MM60
🚀 ใช้ Enhanced Optimal Parameter Finder
======================================================================
📊 ข้อมูล validation: 25 samples
🔧 จำนวน scenarios: 4
🎯 nBars_SL เริ่มต้น: 4

🔍 ทดสอบ Optimal nBars_SL สำหรับ trend_following
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 4
📋 nBars_SL เดิม: 4
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for trend_following...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 4
   📊 Best Score: 0.0000
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ trend_following:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.000    0.769                  
2        0.000    0.606                  
2        0.000    0.300                  
2        0.000    0.300                  
2        0.000    0.800                  
2        0.000    0.302                  
2        0.000    0.800                  
2        0.000    0.300                  
2        0.000    0.800                  
2        0.000    0.300                  
2        0.000    0.798                  
2        0.000    0.300                  
2        0.000    0.797                  
2        0.000    0.302                  
2        0.000    0.800                  
2        0.000    0.301                  
2        0.000    0.799                  
2        0.000    0.301                  
2        0.000    0.799                  
2        0.000    0.301                  
2        0.000    0.800                  
3        0.000    0.523                  
4        0.000    0.698        🏆 BEST    
6        0.000    0.530                  
9        0.000    0.312                  
10       0.000    0.690                  
10       0.000    0.371                  
10       0.000    0.796                  
11       0.000    0.328                  
15       0.000    0.800                  
15       0.000    0.300                  
15       0.000    0.800                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.300                  
15       0.000    0.799                  
15       0.000    0.302                  
15       0.000    0.800                  
15       0.000    0.302                  
15       0.000    0.800                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.301                  
15       0.000    0.800                  
15       0.000    0.303                  
15       0.000    0.797                  
15       0.000    0.301                  
15       0.000    0.799                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 4
   🔸 nBars_SL ใหม่: 4
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0000
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Trend Following: nBars_SL = 4 เหมาะสำหรับการติดตาม trend
   📊 SL ใกล้ - เหมาะกับ volatile market, ป้องกันเร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ trend_following: 4
📁 ไฟล์: M60_GOLD_trend_following_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ trend_following: 4

🔍 ทดสอบ Optimal nBars_SL สำหรับ counter_trend
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend: 4
📋 nBars_SL เดิม: 4
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for counter_trend...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 4
   📊 Best Score: 0.0000
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ counter_trend:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.000    0.769                  
2        0.000    0.606                  
2        0.000    0.300                  
2        0.000    0.300                  
2        0.000    0.800                  
2        0.000    0.302                  
2        0.000    0.800                  
2        0.000    0.300                  
2        0.000    0.800                  
2        0.000    0.300                  
2        0.000    0.798                  
2        0.000    0.300                  
2        0.000    0.797                  
2        0.000    0.302                  
2        0.000    0.800                  
2        0.000    0.301                  
2        0.000    0.799                  
2        0.000    0.301                  
2        0.000    0.799                  
2        0.000    0.301                  
2        0.000    0.800                  
3        0.000    0.523                  
4        0.000    0.698        🏆 BEST    
6        0.000    0.530                  
9        0.000    0.312                  
10       0.000    0.690                  
10       0.000    0.371                  
10       0.000    0.796                  
11       0.000    0.328                  
15       0.000    0.800                  
15       0.000    0.300                  
15       0.000    0.800                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.300                  
15       0.000    0.799                  
15       0.000    0.302                  
15       0.000    0.800                  
15       0.000    0.302                  
15       0.000    0.800                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.301                  
15       0.000    0.800                  
15       0.000    0.303                  
15       0.000    0.797                  
15       0.000    0.301                  
15       0.000    0.799                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 4
   🔸 nBars_SL ใหม่: 4
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0000
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 4 เหมาะสำหรับการเทรด reversal
   ⚡ SL ใกล้ - เหมาะกับ quick reversal, cut loss เร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ counter_trend: 4
📁 ไฟล์: M60_GOLD_counter_trend_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ counter_trend: 4

🔍 ทดสอบ Optimal nBars_SL สำหรับ trend_following_Sell
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following_Sell: 4
📋 nBars_SL เดิม: 4
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for trend_following_Sell...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 4
   📊 Best Score: 0.0000
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ trend_following_Sell:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.000    0.769                  
2        0.000    0.606                  
2        0.000    0.300                  
2        0.000    0.300                  
2        0.000    0.800                  
2        0.000    0.302                  
2        0.000    0.800                  
2        0.000    0.300                  
2        0.000    0.800                  
2        0.000    0.300                  
2        0.000    0.798                  
2        0.000    0.300                  
2        0.000    0.797                  
2        0.000    0.302                  
2        0.000    0.800                  
2        0.000    0.301                  
2        0.000    0.799                  
2        0.000    0.301                  
2        0.000    0.799                  
2        0.000    0.301                  
2        0.000    0.800                  
3        0.000    0.523                  
4        0.000    0.698        🏆 BEST    
6        0.000    0.530                  
9        0.000    0.312                  
10       0.000    0.690                  
10       0.000    0.371                  
10       0.000    0.796                  
11       0.000    0.328                  
15       0.000    0.800                  
15       0.000    0.300                  
15       0.000    0.800                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.300                  
15       0.000    0.799                  
15       0.000    0.302                  
15       0.000    0.800                  
15       0.000    0.302                  
15       0.000    0.800                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.301                  
15       0.000    0.800                  
15       0.000    0.303                  
15       0.000    0.797                  
15       0.000    0.301                  
15       0.000    0.799                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 4
   🔸 nBars_SL ใหม่: 4
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0000
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 4 เหมาะสำหรับการเทรด reversal
   ⚡ SL ใกล้ - เหมาะกับ quick reversal, cut loss เร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ trend_following_Sell: 4
📁 ไฟล์: M60_GOLD_trend_following_Sell_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ trend_following_Sell: 4

🔍 ทดสอบ Optimal nBars_SL สำหรับ counter_trend_Sell
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend_Sell: 4
📋 nBars_SL เดิม: 4
🚀 เริ่มใช้ Enhanced Parameter Optimization...

🔍 Enhanced Optimal Parameter Search for GOLD M60
📊 Method: BAYESIAN
🎯 Threshold Range: (0.3, 0.8)
📏 nBar Range: (2, 15)

🤖 Optimizing parameters for counter_trend_Sell...
⚠️ ไม่สามารถบันทึกผลลัพธ์ optimization: Can't pickle local object 'create_enhanced_optimal_parameter_finder.<locals>._bayesian_optimization.<locals>.objective'

🏆 ผลลัพธ์ Enhanced Optimization:
   🎯 Best nBars_SL: 4
   📊 Best Score: 0.0000
   🔍 Total Trials: N/A
   ⏱️ Optimization Time: N/A

📊 ผลการทดสอบ nBars_SL สำหรับ counter_trend_Sell:
nBars_SL Score    Threshold    Status    
──────────────────────────────────────────────────
2        0.000    0.769                  
2        0.000    0.606                  
2        0.000    0.300                  
2        0.000    0.300                  
2        0.000    0.800                  
2        0.000    0.302                  
2        0.000    0.800                  
2        0.000    0.300                  
2        0.000    0.800                  
2        0.000    0.300                  
2        0.000    0.798                  
2        0.000    0.300                  
2        0.000    0.797                  
2        0.000    0.302                  
2        0.000    0.800                  
2        0.000    0.301                  
2        0.000    0.799                  
2        0.000    0.301                  
2        0.000    0.799                  
2        0.000    0.301                  
2        0.000    0.800                  
3        0.000    0.523                  
4        0.000    0.698        🏆 BEST    
6        0.000    0.530                  
9        0.000    0.312                  
10       0.000    0.690                  
10       0.000    0.371                  
10       0.000    0.796                  
11       0.000    0.328                  
15       0.000    0.800                  
15       0.000    0.300                  
15       0.000    0.800                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.300                  
15       0.000    0.799                  
15       0.000    0.302                  
15       0.000    0.800                  
15       0.000    0.302                  
15       0.000    0.800                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.301                  
15       0.000    0.799                  
15       0.000    0.301                  
15       0.000    0.800                  
15       0.000    0.303                  
15       0.000    0.797                  
15       0.000    0.301                  
15       0.000    0.799                  

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 4
   🔸 nBars_SL ใหม่: 4
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.0000
   🔸 Optimization Method: Enhanced Bayesian
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 4 เหมาะสำหรับการเทรด reversal
   ⚡ SL ใกล้ - เหมาะกับ quick reversal, cut loss เร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ counter_trend_Sell: 4
📁 ไฟล์: M60_GOLD_counter_trend_Sell_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ counter_trend_Sell: 4

======================================================================
📋 สรุปผลการทดสอบ Optimal nBars_SL สำหรับ GOLD MM60
======================================================================
🔸 trend_following:
   nBars_SL: 4 → 4 (+0 bars)
   Best Score: 0.0000
🔸 counter_trend:
   nBars_SL: 4 → 4 (+0 bars)
   Best Score: 0.0000
🔸 trend_following_Sell:
   nBars_SL: 4 → 4 (+0 bars)
   Best Score: 0.0000
🔸 counter_trend_Sell:
   nBars_SL: 4 → 4 (+0 bars)
   Best Score: 0.0000

💡 คำแนะนำการใช้งาน:
   🔄 Trend Following (4 bars): เน้น protection, เหมาะกับ volatile market
   ⚡ Counter Trend (4 bars): เน้น quick exit, เหมาะกับ scalping
   ⚡ Counter Trend (4 bars): เน้น quick exit, เหมาะกับ scalping
   ⚡ Counter Trend (4 bars): เน้น quick exit, เหมาะกับ scalping

✅ การทดสอบ Optimal nBars_SL เสร็จสิ้น
💾 บันทึกผลลัพธ์แล้ว: 4 scenarios
✅ ผลการทดสอบ Optimal nBars SL:
   - trend_following: 4
   - counter_trend: 4
   - trend_following_Sell: 4
   - counter_trend_Sell: 4
✅ ทดสอบ Optimal Parameters เสร็จสิ้น

✅ ข้อมูล df และ trade_df หลังจาก train and evaluate
จำนวน columns ใน df: 332
จำนวน columns ใน trade_df: 338

[INFO] จำนวน Features หลัง train and evaluate (fallback): 20
🔍 Debug ไฟล์ CSV_Files_Fixed/GOLD_H1_FIXED.csv:
   metrics: {'accuracy': 0.9898024234624377, 'f1_score': 0.9928065825546185, 'auc': 0.7257761101231484, 'train_samples': 33350.25, 'test_samples': 7182.75}
   cv_results: {'best_score': 0.9418462399962528}

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.0000    | 0.9898 |
| AUC         | 0.5000    | 0.7258 |
| F1 Score    | 0.0000    | 0.0000 |

🔍 ตรวจสอบเงื่อนไข Performance Tracking:
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
   training_success: True
   เงื่อนไขรวม: True
✅ เข้าเงื่อนไข Performance Tracking - จะบันทึกไฟล์
🔍 Debug metrics from result_dict: {'accuracy': 0.9898024234624377, 'f1_score': 0.9928065825546185, 'auc': 0.7257761101231484, 'train_samples': 33350.25, 'test_samples': 7182.75}
🔍 Debug cv_results from result_dict: {'best_score': 0.9418462399962528}
🔍 Final model_metrics: {'avg_accuracy': 0.9898024234624377, 'avg_f1_score': 0.5, 'avg_auc': 0.7257761101231484, 'total_train_samples': 77, 'total_test_samples': 25}
⚠️ trend_data is None หรือไม่มี best_threshold - ใช้ค่า default
⚠️ counter_data is None หรือไม่มี best_threshold - ใช้ค่า default

🏗️ เปิดใช้งาน load time filters
กำลังโหลด time filters จาก: LightGBM/Multi/thresholds/M60_GOLD_time_filters.pkl
✅ โหลด time filters สำเร็จ (M60_GOLD)
🔍 Debug loaded time_filters: {'days': [], 'hours': [], 'detailed_stats': {'days': {'Monday': {'win_rate': 0.0, 'expectancy': 104.42857142857065, 'total_trades': 7.0, 'day_index': 0, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Tuesday': {'win_rate': 0.0, 'expectancy': 114.6666666666647, 'total_trades': 6.0, 'day_index': 1, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Wednesday': {'win_rate': 0.0, 'expectancy': 271.00000000000364, 'total_trades': 2.0, 'day_index': 2, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Thursday': {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 8.0, 'day_index': 3, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Friday': {'win_rate': 0.0, 'expectancy': 158.0000000000041, 'total_trades': 2.0, 'day_index': 4, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}}, 'hours': {3: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 2.0, 'strong_buy_rate': 0.0}, 4: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 3.0, 'strong_buy_rate': 0.0}, 9: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 10: {'win_rate': 0.0, 'expectancy': 542.0000000000073, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 12: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 14: {'win_rate': 0.0, 'expectancy': 158.0000000000041, 'total_trades': 2.0, 'strong_buy_rate': 0.0}, 15: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 4.0, 'strong_buy_rate': 0.0}, 16: {'win_rate': 0.0, 'expectancy': 243.66666666666484, 'total_trades': 3.0, 'strong_buy_rate': 0.0}, 17: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 5.0, 'strong_buy_rate': 0.0}, 18: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 19: {'win_rate': 0.0, 'expectancy': 343.9999999999941, 'total_trades': 2.0, 'strong_buy_rate': 0.0}}}, 'adaptive_settings': {'min_win_rate': 0.3, 'min_expectancy': 0.0, 'adaptive_threshold': True, 'confidence_level': 0.8}, 'time_blocks': [], 'performance_comparison': {'filtered_vs_all': {'error': 'No trades match the filter criteria'}, 'best_day': {'win_rate': 0.0, 'total': 7.0, 'expectancy': 104.42857142857065, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'best_hour': {'win_rate': 0.0, 'total': 2.0, 'expectancy': 0.0, 'strong_buy_rate': 0.0}}}
   ⚠️ time_filters ว่าง - สร้าง default
   ✅ สร้าง default time_filters: {'days': [0, 1, 2, 3, 4], 'hours': [8, 9, 10, 11, 12, 13, 14, 15, 16, 17], 'source': 'default'}

🎯 กำลังเรียกใช้ record_model_performance...

🏗️ เปิดใช้งาน record model performance
   Symbol: GOLD, Timeframe: M60
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
🔍 Debug trade_df:
   Shape: (172, 338)
   Columns: ['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
   ใช้ Trade Type แทน Signal
   Trade Type counts: {'Sell': 167, 'Buy': 5}
🔍 calculate_trade_metrics: input shape = (5, 338)
   📊 Total trades: 5
   💰 Winning trades: 0/5
   💰 Total profit: -223.00
   ✅ Calculated metrics: {'count': 5, 'win_rate': 0.0, 'expectancy': -44.600000000000364, 'trade_accuracy': 0.0, 'trade_f1_score': 0, 'trade_auc': 0.5}
🔍 calculate_trade_metrics: input shape = (167, 338)
   📊 Total trades: 167
   💰 Winning trades: 23/167
   💰 Total profit: -4094.00
   ✅ Calculated metrics: {'count': 167, 'win_rate': 13.77245508982036, 'expectancy': -24.514970059880817, 'trade_accuracy': 0.1377245508982036, 'trade_f1_score': 0.16526946107784432, 'trade_auc': 0.5550898203592814}

🏗️ เปิดใช้งาน format time filters display
🔍 Debug _compare_with_previous:
   Key: GOLD_M60
   Current F1: 0.5
   Current AUC: 0.7257761101231484
   Previous F1: 0.5
   Previous AUC: 0.7257761101231484
   Summary length: 3
⚠️ ⚠️ โมเดลไม่ดีขึ้น! F1 เปลี่ยน 0.0000, AUC เปลี่ยน 0.0000
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ (Multi-Model)
   - Symbol: GOLD, Timeframe: M60
   - Buy_sell stats (calculated): {'win_rate': 0.13372093023255813, 'expectancy': -25.098837209302896, 'profit_factor': 0.7087830545062019, 'count': 172, 'max_drawdown': inf}
   - Performance data prepared: {'symbol': 'GOLD', 'timeframe': 'M60', 'entry_config': 'config_1_enhanced_signal', 'entry_config_name': 'Enhanced MACD Signal', 'entry_config_description': 'ใช้ macd_signal พร้อมเงื่อนไข volume และ pullback เพิ่มเติม', 'win_rate': 0.13372093023255813, 'expectancy': -25.098837209302896, 'profit_factor': 0.7087830545062019, 'num_trades': 172, 'max_drawdown': inf, 'model_accuracy': 0.9898024234624377, 'model_auc': 0.7257761101231484, 'model_f1': 0, 'training_date': '2025-09-26T21:48:22.889285', 'training_type': 'multi_model', 'num_features': 20}

🏗️ เปิดใช้งาน save entry config performance

🏗️ เปิดใช้งาน get entry config results folder

🏗️ เปิดใช้งาน get entry config folder name
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models\M60_GOLD
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results\M60_GOLD
path บันทึกไฟล์ performance_summary.json LightGBM/Entry_config_1_enhanced_signal\results\M60_GOLD
✅ บันทึกผลการประเมิน config_1_enhanced_signal สำหรับ GOLD MM60 ที่: LightGBM/Entry_config_1_enhanced_signal\results\M60_GOLD\performance_summary.json
   📁 ขนาดไฟล์: 758 bytes
✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น (Multi-Model)

✅ เสร็จสิ้นการประมวลผลกลุ่ม M60
📊 ประมวลผล: 1 ไฟล์
📈 ผลลัพธ์: 1 รายการ

================================================================================
🔍 การวิเคราะห์เกณฑ์คุณภาพโมเดล (จาก main function)
================================================================================

================================================================================
📊 การวิเคราะห์เกณฑ์คุณภาพโมเดล (MODEL_QUALITY_THRESHOLDS)
================================================================================
Metric          Current    Recommended  New        Change     Data Points 
--------------------------------------------------------------------------------
Accuracy        0.450      0.987        0.987      +119.3%    4           
AUC             0.480      0.642        0.642      +33.8%     4           
F1 Score        0.080      0.990        0.990      +1137.6%   4           
Precision       0.080      0.995        0.995      +1144.0%   4           
Recall          0.300      0.987        0.987      +229.0%    4           
Win Rate        35.0%      0.7%         0.7%       -98.1%     4           
Expectancy      10.000     49.279       49.279     +392.8%    4           
Min Trades      8.000    N/A          8.000    N/A        0           

💡 คำแนะนำ:
   - เกณฑ์ใหม่อิงจาก percentile 25% ของข้อมูลจริง
   - ค่าที่แนะนำจะไม่เข้มงวดเกินไปและสมจริงกว่า
   - สามารถปรับ THRESHOLD_PERCENTILE เพื่อเปลี่ยนความเข้มงวด
================================================================================
💾 บันทึกผลการวิเคราะห์เกณฑ์ที่: LightGBM/Multi/threshold_analysis.json
✅ การวิเคราะห์เกณฑ์เสร็จสิ้น

📊 ไม่มีการปรับ threshold ในรอบนี้
🔍 Debug: round_results = <class 'dict'>
✅ กลุ่ม M60 สำเร็จ
📊 ผลลัพธ์กลุ่ม M60: สำเร็จ 1, ผิดพลาด 0
⏱️ เวลาที่ใช้: 726.6 วินาที (12.1 นาที)
📈 เฉลี่ยต่อไฟล์: 726.6 วินาที/ไฟล์

────────────────────────────────────────────────────────────
📋 สรุปรอบที่ 1:
   ⏱️ เวลาที่ใช้: 726.6 วินาที (12.1 นาที)
   ✅ ไฟล์สำเร็จ: 1
   ❌ ไฟล์ผิดพลาด: 0
   ⏰ เวลาสิ้นสุด: 21:48:23
   📊 M60: 726.6s (726.6s/ไฟล์)

============================================================
⏭️ ข้ามการวิเคราะห์ Feature Importance ข้าม Assets
============================================================
💡 หมายเหตุ: การวิเคราะห์จะทำงานเฉพาะเมื่อเทรนโมเดลใหม่และมีผลลัพธ์
============================================================

================================================================================
🎉 สรุปการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-26 21:36:16
⏰ เวลาสิ้นสุด: 2025-09-26 21:48:23
⏱️ เวลาที่ใช้ทั้งหมด: 726.6 วินาที (12.1 นาที)

🚀 ประสิทธิภาพการทำงาน:
   📈 ประมวลผลได้: 5 ไฟล์/ชั่วโมง
================================================================================
💾 บันทึกผลการทดสอบลงใน 'LightGBM/Multi_Time\time_test.txt' เรียบร้อยแล้ว

================================================================================
💰 เริ่มการวิเคราะห์ทางการเงินรวม
================================================================================

================================================================================
🚀 เริ่มการวิเคราะห์ทางการเงินทั้งหมด
================================================================================
🚀 เริ่มการวิเคราะห์ทางการเงินแบบสมบูรณ์
📊 พบข้อมูลการเทรด: 172 รายการ
📊 บันทึกกราฟที่: Financial_Analysis_Results/trading_performance_analysis.png
💾 บันทึกการวิเคราะห์สมบูรณ์:
   📄 Summary: Financial_Analysis_Results/complete_financial_analysis.json
   📊 Risk Table: Financial_Analysis_Results/risk_management_table.csv
   📝 Report: Financial_Analysis_Results/financial_analysis_report.txt

============================================================
📈 FINANCIAL ANALYSIS SUMMARY
============================================================
💰 Account Balance: $1,000.00
📊 Total Trades: 172
💵 Total Profit (1.0 lot): $-4,317.00
📉 Max Drawdown (1.0 lot): $7,737.00
🎯 Recommended Lot Size: 0.0026
⚠️ Max Risk: 2.00%
============================================================

🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
📁 ผลลัพธ์บันทึกที่: Financial_Analysis_Results

📈 สรุปผลการวิเคราะห์:
   💰 ยอดเงินในบัญชี: $1,000.00
   📊 จำนวนการเทรดทั้งหมด: 172
   💵 กำไรรวม (1.0 lot): $-4,317.00
   📉 Drawdown สูงสุด (1.0 lot): $7,737.00
   🎯 ขนาดล็อตที่แนะนำ: 0.0026
   ⚠️ ความเสี่ยงสูงสุด: 2.00%
🎉 การวิเคราะห์ทางการเงินเสร็จสมบูรณ์!
