#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบ analysis_summary และ prepare_analysis_summary function
"""

import sys
import os

# เพิ่ม path ปัจจุบันเข้าไปใน sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_analysis_summary():
    """ทดสอบ prepare_analysis_summary function"""
    
    print("🔍 ทดสอบ prepare_analysis_summary function")
    
    # Import functions
    try:
        from WebRequest_Server_03_Target import prepare_analysis_summary
        print("✅ Import functions สำเร็จ")
    except ImportError as e:
        print(f"❌ ไม่สามารถ import functions: {e}")
        return
    
    # สร้างข้อมูลทดสอบ analysis_results
    print(f"\n🔍 สร้างข้อมูลทดสอบ analysis_results")
    
    analysis_results = {
        'trend_following': {
            'buy': {
                'should_trade': False,
                'confidence': 0.0029,
                'details': 'TF_BUY_Prob:0.0029'
            },
            'sell': {
                'should_trade': True,
                'confidence': 0.9895,
                'details': 'TF_SELL_Prob:0.9895'
            }
        },
        'counter_trend': {
            'buy': {
                'should_trade': False,
                'confidence': 0.0547,
                'details': 'CT_BUY_Prob:0.0547'
            },
            'sell': {
                'should_trade': True,
                'confidence': 0.8999,
                'details': 'CT_SELL_Prob:0.8999'
            }
        },
        'enhanced': {
            'buy': {
                'should_trade': False,
                'confidence': 0.0301,
                'details': 'Main:False, Confirm:True, Quality:False'
            },
            'sell': {
                'should_trade': False,
                'confidence': 0.9627,
                'details': 'Main:True, Confirm:True, Quality:False'
            }
        }
    }
    
    print(f"📊 Original analysis_results:")
    for scenario, data in analysis_results.items():
        print(f"   {scenario}:")
        for action, result in data.items():
            print(f"      {action}: confidence={result['confidence']:.4f}, should_trade={result['should_trade']}")
    
    # ทดสอบ prepare_analysis_summary
    print(f"\n🔄 ทดสอบ prepare_analysis_summary")
    
    try:
        analysis_summary = prepare_analysis_summary(analysis_results)
        
        print(f"📊 Prepared analysis_summary:")
        print(f"   Keys: {list(analysis_summary.keys())}")
        
        for scenario, data in analysis_summary.items():
            print(f"   {scenario}:")
            for action, result in data.items():
                print(f"      {action}: {result}")
        
        # ทดสอบการดึงข้อมูล confidence
        print(f"\n📤 ทดสอบการดึงข้อมูล confidence:")
        
        # Trend Following confidence
        if 'trend_following' in analysis_summary:
            tf_data = analysis_summary['trend_following']
            if 'buy' in tf_data:
                tf_buy_conf = float(tf_data['buy'].get('confidence', 0.0))
                print(f"   trend_following_buy_confidence: {tf_buy_conf:.4f}")
            if 'sell' in tf_data:
                tf_sell_conf = float(tf_data['sell'].get('confidence', 0.0))
                print(f"   trend_following_sell_confidence: {tf_sell_conf:.4f}")

        # Counter Trend confidence
        if 'counter_trend' in analysis_summary:
            ct_data = analysis_summary['counter_trend']
            if 'buy' in ct_data:
                ct_buy_conf = float(ct_data['buy'].get('confidence', 0.0))
                print(f"   counter_trend_buy_confidence: {ct_buy_conf:.4f}")
            if 'sell' in ct_data:
                ct_sell_conf = float(ct_data['sell'].get('confidence', 0.0))
                print(f"   counter_trend_sell_confidence: {ct_sell_conf:.4f}")
        
        # ตรวจสอบว่าค่าไม่เป็น 0.0 ทั้งหมด
        total_confidence = tf_buy_conf + tf_sell_conf + ct_buy_conf + ct_sell_conf
        if total_confidence > 0:
            print(f"✅ การแปลง analysis_summary สำเร็จ! (Total: {total_confidence:.4f})")
        else:
            print(f"⚠️ ค่า confidence ทั้งหมดเป็น 0.0 - มีปัญหาในการแปลง")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ prepare_analysis_summary: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_analysis_summary()
