#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
sys.path.append('.')

# โหลดข้อมูลทดสอบ
trade_df = pd.read_csv('test_trades.csv')
print(f'📊 โหลดข้อมูล: {len(trade_df)} trades')

# ทดสอบฟังก์ชันรายงานครอบคลุม
from LightGBM_10_Test import create_comprehensive_trading_report

try:
    report_data = create_comprehensive_trading_report(
        trades_df=trade_df,
        symbol='GOLD',
        timeframe='M60',
        scenario='test_scenario',
        output_folder='test_reports'
    )
    
    if 'error' not in report_data:
        print('✅ สร้างรายงานครอบคลุมสำเร็จ')
        print(f'📁 บันทึกที่: {report_data["report_info"]["report_folder"]}')
        print(f'📄 ไฟล์ที่สร้าง: {len(report_data["report_info"]["files_created"])} ไฟล์')
        for file in report_data['report_info']['files_created']:
            print(f'   - {file}')
    else:
        print(f'❌ เกิดข้อผิดพลาด: {report_data["error"]}')
        
except Exception as e:
    print(f'❌ เกิดข้อผิดพลาด: {e}')
    import traceback
    traceback.print_exc()