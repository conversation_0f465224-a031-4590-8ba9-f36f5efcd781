"""
🔧 Quick Fix Model Saving
=========================

แก้ไขปัญหาการไม่บันทึกโมเดลแบบรวดเร็ว
"""

def quick_fix():
    """แก้ไขปัญหาแบบรวดเร็ว"""
    
    print("🔧 Quick Fix: แก้ไขปัญหาการไม่บันทึกโมเดล")
    print("="*60)
    
    # อ่านไฟล์ LightGBM_10_4.py
    try:
        with open('LightGBM_10_4.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ อ่านไฟล์ LightGBM_10_4.py สำเร็จ")
    except Exception as e:
        print(f"❌ ไม่สามารถอ่านไฟล์ได้: {e}")
        return False
    
    # วิธีที่ 1: เปิดใช้งาน DEVELOPMENT_MODE
    changes_made = []
    
    if 'DEVELOPMENT_MODE = False' in content:
        content = content.replace('DEVELOPMENT_MODE = False', 'DEVELOPMENT_MODE = True')
        changes_made.append("เปลี่ยน DEVELOPMENT_MODE เป็น True")
    
    # วิธีที่ 2: เพิ่ม DEVELOPMENT_MODE ถ้าไม่มี
    if 'DEVELOPMENT_MODE' not in content:
        # หาตำแหน่งที่เหมาะสม
        if '# การตั้งค่าหลัก' in content:
            old_line = '# การตั้งค่าหลัก'
            new_line = '''# การตั้งค่าหลัก
# โหมด Development - เปลี่ยนเป็น False เมื่อใช้งานจริง
DEVELOPMENT_MODE = True  # True = บันทึกทุกโมเดล, False = ใช้ระบบป้องกัน'''
            content = content.replace(old_line, new_line)
            changes_made.append("เพิ่ม DEVELOPMENT_MODE = True")
    
    # วิธีที่ 3: แก้ไขการเรียกใช้ Model Protection
    old_protection_check = '''if MODEL_PROTECTION_AVAILABLE and protection_system:
                    print(f"🛡️ เรียกใช้ Model Protection System...")'''
    
    new_protection_check = '''# ตรวจสอบโหมด Development ก่อน
                DEVELOPMENT_MODE = True  # เปลี่ยนเป็น False เมื่อใช้งานจริง
                
                if DEVELOPMENT_MODE:
                    print(f"🔧 Development Mode: บันทึกโมเดลทุกครั้งเพื่อการทดสอบ")
                    should_save_by_protection = True
                    protection_reason = "development_mode"
                elif MODEL_PROTECTION_AVAILABLE and protection_system:
                    print(f"🛡️ เรียกใช้ Model Protection System...")'''
    
    if old_protection_check in content:
        content = content.replace(old_protection_check, new_protection_check)
        changes_made.append("แก้ไขการเรียกใช้ Model Protection")
    
    # วิธีที่ 4: บังคับให้บันทึกโมเดลในโหมด Development
    old_save_check = "if evaluation_result['should_save']:"
    new_save_check = '''# บังคับบันทึกในโหมด Development
            DEVELOPMENT_MODE = True  # เปลี่ยนเป็น False เมื่อใช้งานจริง
            
            if DEVELOPMENT_MODE or evaluation_result['should_save']:'''
    
    if old_save_check in content and new_save_check not in content:
        content = content.replace(old_save_check, new_save_check)
        changes_made.append("บังคับบันทึกโมเดลในโหมด Development")
    
    # บันทึกไฟล์
    try:
        with open('LightGBM_10_4.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ บันทึกไฟล์ LightGBM_10_4.py สำเร็จ")
    except Exception as e:
        print(f"❌ ไม่สามารถบันทึกไฟล์ได้: {e}")
        return False
    
    # แสดงการเปลี่ยนแปลง
    if changes_made:
        print("\n📊 การเปลี่ยนแปลงที่ทำ:")
        for i, change in enumerate(changes_made, 1):
            print(f"   {i}. {change}")
    else:
        print("\n⚠️ ไม่พบการเปลี่ยนแปลงที่ต้องทำ")
    
    return len(changes_made) > 0

def create_backup():
    """สร้าง backup ไฟล์เดิม"""
    
    print("\n💾 สร้าง backup ไฟล์เดิม")
    print("="*30)
    
    try:
        import shutil
        shutil.copy2('LightGBM_10_4.py', 'LightGBM_10_4_backup.py')
        print("✅ สร้าง backup: LightGBM_10_4_backup.py")
        return True
    except Exception as e:
        print(f"❌ ไม่สามารถสร้าง backup ได้: {e}")
        return False

def test_fix():
    """ทดสอบการแก้ไข"""
    
    print("\n🧪 ทดสอบการแก้ไข")
    print("="*30)
    
    try:
        with open('LightGBM_10_4.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        tests = []
        
        # ทดสอบ 1: มี DEVELOPMENT_MODE หรือไม่
        if 'DEVELOPMENT_MODE = True' in content:
            tests.append(("DEVELOPMENT_MODE = True", True))
        else:
            tests.append(("DEVELOPMENT_MODE = True", False))
        
        # ทดสอบ 2: มีการตรวจสอบ Development Mode หรือไม่
        if 'if DEVELOPMENT_MODE:' in content:
            tests.append(("Development Mode Check", True))
        else:
            tests.append(("Development Mode Check", False))
        
        # ทดสอบ 3: มีการบังคับบันทึกหรือไม่
        if 'DEVELOPMENT_MODE or evaluation_result' in content:
            tests.append(("Force Save in Dev Mode", True))
        else:
            tests.append(("Force Save in Dev Mode", False))
        
        # แสดงผลการทดสอบ
        passed = 0
        for test_name, result in tests:
            status = "✅ ผ่าน" if result else "❌ ล้มเหลว"
            print(f"   {status} {test_name}")
            if result:
                passed += 1
        
        total = len(tests)
        score = passed / total * 100
        
        print(f"\n🎯 คะแนนการทดสอบ: {passed}/{total} ({score:.1f}%)")
        
        return score >= 66  # ผ่านอย่างน้อย 2 ใน 3 ข้อ
        
    except Exception as e:
        print(f"❌ ไม่สามารถทดสอบได้: {e}")
        return False

def main():
    """ฟังก์ชันหลัก"""
    
    print("🚀 เริ่มแก้ไขปัญหาการไม่บันทึกโมเดล")
    print("="*60)
    
    # สร้าง backup
    backup_success = create_backup()
    
    # แก้ไขปัญหา
    fix_success = quick_fix()
    
    # ทดสอบการแก้ไข
    test_success = test_fix()
    
    # สรุปผล
    print("\n📊 สรุปผลการแก้ไข")
    print("="*60)
    
    print(f"💾 Backup: {'✅ สำเร็จ' if backup_success else '❌ ล้มเหลว'}")
    print(f"🔧 Fix: {'✅ สำเร็จ' if fix_success else '❌ ล้มเหลว'}")
    print(f"🧪 Test: {'✅ ผ่าน' if test_success else '❌ ล้มเหลว'}")
    
    if fix_success and test_success:
        print("\n🎉 การแก้ไขสำเร็จ!")
        print("💡 ตอนนี้โมเดลจะถูกบันทึกในโหมด Development")
        print("🚀 สามารถรัน LightGBM_10_4.py ได้แล้ว")
        
        print("\n📋 คำแนะนำ:")
        print("1. รัน: python LightGBM_10_4.py")
        print("2. ตรวจสอบโฟลเดอร์ LightGBM/Multi/models/")
        print("3. ควรเห็นไฟล์โมเดลถูกบันทึก")
        print("4. เมื่อใช้งานจริง เปลี่ยน DEVELOPMENT_MODE = False")
        
    else:
        print("\n❌ การแก้ไขไม่สำเร็จ")
        print("💡 ลองใช้วิธีแก้ไขแบบ manual")
        
        print("\n📋 วิธีแก้ไขแบบ Manual:")
        print("1. เปิดไฟล์ LightGBM_10_4.py")
        print("2. ค้นหา 'MODEL_PROTECTION_AVAILABLE'")
        print("3. เพิ่มบรรทัด: DEVELOPMENT_MODE = True")
        print("4. แก้ไข if condition ให้บันทึกโมเดลเสมอ")

if __name__ == "__main__":
    main()
