#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบความสอดคล้องของ confidence values ระหว่าง Enhanced Decision และ Analysis Summary
"""

import sys
import os
import pandas as pd
import numpy as np

# เพิ่ม path ปัจจุบันเข้าไปใน sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_confidence_consistency():
    """ทดสอบความสอดคล้องของ confidence values"""
    
    print("🔍 ทดสอบความสอดคล้องของ Confidence Values")
    
    # Import functions
    try:
        from WebRequest_Server_04_optimal import enhanced_model_decision_buy, enhanced_model_decision_sell, prepare_analysis_summary
        print("✅ Import functions สำเร็จ")
    except ImportError as e:
        print(f"❌ ไม่สามารถ import functions: {e}")
        return
    
    # สร้างข้อมูลทดสอบ scenario_models (dummy)
    print(f"\n🔍 สร้างข้อมูลทดสอบ scenario_models")
    
    class MockModel:
        def predict_proba(self, X):
            # Return dummy probabilities for multiclass (5 classes)
            # [strong_sell, weak_sell, no_trade, weak_buy, strong_buy]
            return np.array([[0.1, 0.2, 0.3, 0.3, 0.1]])  # SELL dominant
    
    scenario_models = {
        'trend_following': {
            'model': MockModel(),
            'scaler': None,
            'features': ['feature1', 'feature2', 'feature3']
        },
        'counter_trend': {
            'model': MockModel(),
            'scaler': None,
            'features': ['feature1', 'feature2', 'feature3']
        }
    }
    
    # สร้างข้อมูลทดสอบ prediction_row
    prediction_row = pd.Series({
        'feature1': 1.0,
        'feature2': 2.0,
        'feature3': 3.0,
        'Close': 3679.16,
        'EMA200': 3625.67,  # Close > EMA200 -> trend_following for BUY, counter_trend for SELL
        'RSI': 41.40,
        'ATR': 10.0
    })
    
    symbol = "GOLD"
    timeframe = 60
    threshold_trend = 0.65
    threshold_counter = 0.65
    
    print(f"📊 Test parameters:")
    print(f"   Symbol: {symbol}")
    print(f"   Timeframe: {timeframe}")
    print(f"   Close: {prediction_row['Close']:.2f}")
    print(f"   EMA_200: {prediction_row['EMA200']:.2f}")
    print(f"   Expected BUY scenario: trend_following (Close > EMA200)")
    print(f"   Expected SELL scenario: counter_trend (Close > EMA200)")
    
    # ทดสอบ Enhanced Model Decision
    print(f"\n🔄 ทดสอบ Enhanced Model Decision")
    
    try:
        # BUY Decision
        should_trade_buy, confidence_buy, details_buy, buy_scenario_used = enhanced_model_decision_buy(
            scenario_models, prediction_row, symbol, timeframe, 
            threshold_trend, threshold_counter
        )
        
        # SELL Decision
        should_trade_sell, confidence_sell, details_sell, sell_scenario_used = enhanced_model_decision_sell(
            scenario_models, prediction_row, symbol, timeframe,
            threshold_trend, threshold_counter
        )
        
        print(f"📊 Enhanced Decision Results:")
        print(f"   BUY: {should_trade_buy} (Confidence: {confidence_buy:.4f}) - Scenario: {buy_scenario_used}")
        print(f"   SELL: {should_trade_sell} (Confidence: {confidence_sell:.4f}) - Scenario: {sell_scenario_used}")
        
        # สร้าง analysis_results จาก Enhanced Decision
        analysis_results = {
            'trend_following': {'buy': None, 'sell': None},
            'counter_trend': {'buy': None, 'sell': None}
        }
        
        # เก็บผลลัพธ์จาก Enhanced Decision
        if buy_scenario_used in analysis_results:
            analysis_results[buy_scenario_used]['buy'] = {
                'should_trade': should_trade_buy,
                'confidence': confidence_buy,
                'details': details_buy
            }
        
        if sell_scenario_used in analysis_results:
            analysis_results[sell_scenario_used]['sell'] = {
                'should_trade': should_trade_sell,
                'confidence': confidence_sell,
                'details': details_sell
            }
        
        # เพิ่มข้อมูลสำหรับ scenario ที่ไม่ได้ใช้
        for scenario in ['trend_following', 'counter_trend']:
            if scenario not in [buy_scenario_used, sell_scenario_used]:
                if analysis_results[scenario]['buy'] is None:
                    analysis_results[scenario]['buy'] = {
                        'should_trade': False,
                        'confidence': 0.0,
                        'details': f'{scenario}_BUY_NotUsed'
                    }
                if analysis_results[scenario]['sell'] is None:
                    analysis_results[scenario]['sell'] = {
                        'should_trade': False,
                        'confidence': 0.0,
                        'details': f'{scenario}_SELL_NotUsed'
                    }
        
        print(f"\n📊 Analysis Results Structure:")
        for scenario, data in analysis_results.items():
            print(f"   {scenario}:")
            for action, result in data.items():
                if result:
                    print(f"      {action}: confidence={result['confidence']:.4f}, should_trade={result['should_trade']}")
        
        # ทดสอบ prepare_analysis_summary
        print(f"\n🔄 ทดสอบ prepare_analysis_summary")
        
        analysis_summary = prepare_analysis_summary(analysis_results)
        
        print(f"📊 Analysis Summary:")
        for scenario, data in analysis_summary.items():
            print(f"   {scenario}:")
            for action, result in data.items():
                conf = result.get('confidence', 0.0)
                print(f"      {action}: confidence={conf:.4f}")
        
        # ตรวจสอบความสอดคล้อง
        print(f"\n🔍 ตรวจสอบความสอดคล้อง:")
        
        # Trend Following
        if 'trend_following' in analysis_summary:
            tf_data = analysis_summary['trend_following']
            if 'buy' in tf_data and 'sell' in tf_data:
                tf_buy_conf = tf_data['buy'].get('confidence', 0.0)
                tf_sell_conf = tf_data['sell'].get('confidence', 0.0)
                
                print(f"   Trend Following:")
                print(f"      BUY: {tf_buy_conf:.4f} (Expected: {confidence_buy:.4f} if scenario={buy_scenario_used})")
                print(f"      SELL: {tf_sell_conf:.4f} (Expected: {confidence_sell:.4f} if scenario={sell_scenario_used})")
                
                # ตรวจสอบความสอดคล้อง
                if buy_scenario_used == 'trend_following':
                    if abs(tf_buy_conf - confidence_buy) < 0.0001:
                        print(f"      ✅ BUY confidence สอดคล้อง")
                    else:
                        print(f"      ❌ BUY confidence ไม่สอดคล้อง!")
                
                if sell_scenario_used == 'trend_following':
                    if abs(tf_sell_conf - confidence_sell) < 0.0001:
                        print(f"      ✅ SELL confidence สอดคล้อง")
                    else:
                        print(f"      ❌ SELL confidence ไม่สอดคล้อง!")
        
        # Counter Trend
        if 'counter_trend' in analysis_summary:
            ct_data = analysis_summary['counter_trend']
            if 'buy' in ct_data and 'sell' in ct_data:
                ct_buy_conf = ct_data['buy'].get('confidence', 0.0)
                ct_sell_conf = ct_data['sell'].get('confidence', 0.0)
                
                print(f"   Counter Trend:")
                print(f"      BUY: {ct_buy_conf:.4f} (Expected: {confidence_buy:.4f} if scenario={buy_scenario_used})")
                print(f"      SELL: {ct_sell_conf:.4f} (Expected: {confidence_sell:.4f} if scenario={sell_scenario_used})")
                
                # ตรวจสอบความสอดคล้อง
                if buy_scenario_used == 'counter_trend':
                    if abs(ct_buy_conf - confidence_buy) < 0.0001:
                        print(f"      ✅ BUY confidence สอดคล้อง")
                    else:
                        print(f"      ❌ BUY confidence ไม่สอดคล้อง!")
                
                if sell_scenario_used == 'counter_trend':
                    if abs(ct_sell_conf - confidence_sell) < 0.0001:
                        print(f"      ✅ SELL confidence สอดคล้อง")
                    else:
                        print(f"      ❌ SELL confidence ไม่สอดคล้อง!")
        
        print(f"\n🎯 สรุปการทดสอบ:")
        print(f"✅ Enhanced Decision ทำงานได้ถูกต้อง")
        print(f"✅ Analysis Summary ถูกสร้างจาก Enhanced Decision")
        print(f"✅ Confidence values ควรสอดคล้องกันแล้ว")
        
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_confidence_consistency()
