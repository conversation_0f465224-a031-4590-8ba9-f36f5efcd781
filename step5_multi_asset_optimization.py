"""
🚀 Step 5: Multi-Asset Parameter Optimization
============================================

ทดสอบพารามิเตอร์กับ TEST_GROUPS ทั้งหมด เพื่อหาการตั้งค่าที่ดีที่สุดสำหรับแต่ละสินทรัพย์
"""

from Parameter_Testing_Integration import ParameterTester
import json
import os
from datetime import datetime
import pandas as pd

# กำหนด TEST_GROUPS
TEST_GROUPS = {
    "M30": [
        "CSV_Files_Fixed/AUDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_M30_FIXED.csv", 
        "CSV_Files_Fixed/GBPUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/GOLD_M30_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_M30_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_M30_FIXED.csv"
    ],
    "M60": [
        "CSV_Files_Fixed/AUDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/EURUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/GBPUSD_H1_FIXED.csv", 
        "CSV_Files_Fixed/GOLD_H1_FIXED.csv",
        "CSV_Files_Fixed/NZDUSD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDCAD_H1_FIXED.csv",
        "CSV_Files_Fixed/USDJPY_H1_FIXED.csv"
    ]
}

def extract_symbol_from_path(file_path):
    """แยกชื่อสัญลักษณ์จาก file path"""
    filename = os.path.basename(file_path)
    symbol = filename.split('_')[0]
    return symbol

def create_optimization_config():
    """สร้าง configuration สำหรับการทดสอบ"""
    return {
        'parameters_to_test': {
            'input_stop_loss_atr': {
                'min': 1.0,
                'max': 2.5,
                'step': 0.5
            },
            'input_take_profit': {
                'min': 1.5,
                'max': 2.5,
                'step': 0.5
            },
            'input_rsi_level_in': {
                'min': 25,
                'max': 40,
                'step': 5
            },
            'input_volume_spike': {
                'min': 1.0,
                'max': 1.5,
                'step': 0.25
            }
        },
        'max_combinations': 20  # จำกัดเพื่อความเร็ว
    }

def test_single_asset(tester, symbol, timeframe, file_path, config):
    """ทดสอบสินทรัพย์เดียว"""
    print(f"\n🔍 ทดสอบ {symbol}_{timeframe}")
    print(f"   ไฟล์: {file_path}")
    
    try:
        # ตรวจสอบว่าไฟล์มีอยู่จริง
        if not os.path.exists(file_path):
            print(f"   ❌ ไม่พบไฟล์: {file_path}")
            return None
        
        # รันการทดสอบ
        results = tester.run_batch_test(
            symbol=symbol,
            timeframe=timeframe,
            test_config=config,
            data_file=file_path
        )
        
        if results and len(results) > 0:
            best_result = results[0]
            print(f"   ✅ สำเร็จ - คะแนนสูงสุด: {best_result['performance_score']:.2f}")
            print(f"   📊 Win Rate: {best_result['results'].get('win_rate', 0):.1f}%")
            print(f"   💰 Profit: ${best_result['results'].get('total_profit', 0):.0f}")
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'file_path': file_path,
                'best_result': best_result,
                'all_results': results[:5],  # เก็บ top 5
                'test_date': datetime.now().isoformat()
            }
        else:
            print(f"   ❌ ไม่ได้ผลลัพธ์")
            return None
            
    except Exception as e:
        print(f"   ❌ เกิดข้อผิดพลาด: {e}")
        return None

def save_results_to_txt(all_results, summary_stats):
    """บันทึกผลลัพธ์เป็นไฟล์ .txt"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"multi_asset_optimization_results_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write("🚀 MULTI-ASSET PARAMETER OPTIMIZATION RESULTS\n")
        f.write("=" * 60 + "\n")
        f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Assets Tested: {len(all_results)}\n\n")
        
        # สรุปภาพรวม
        f.write("📊 SUMMARY STATISTICS\n")
        f.write("-" * 30 + "\n")
        f.write(f"Average Score: {summary_stats['avg_score']:.2f}\n")
        f.write(f"Best Score: {summary_stats['best_score']:.2f} ({summary_stats['best_asset']})\n")
        f.write(f"Worst Score: {summary_stats['worst_score']:.2f} ({summary_stats['worst_asset']})\n")
        f.write(f"Average Win Rate: {summary_stats['avg_win_rate']:.1f}%\n")
        f.write(f"Assets with Win Rate > 50%: {summary_stats['good_win_rate_count']}\n\n")
        
        # รายละเอียดแต่ละสินทรัพย์
        f.write("📋 DETAILED RESULTS BY ASSET\n")
        f.write("=" * 60 + "\n")
        
        for result in all_results:
            if result:
                best = result['best_result']
                params = best['parameters']
                metrics = best['results']
                
                f.write(f"\n🔸 {result['symbol']}_{result['timeframe']}\n")
                f.write(f"   Score: {best['performance_score']:.2f}\n")
                f.write(f"   Win Rate: {metrics.get('win_rate', 0):.1f}%\n")
                f.write(f"   Total Profit: ${metrics.get('total_profit', 0):.0f}\n")
                f.write(f"   Total Trades: {metrics.get('total_trades', 0)}\n")
                f.write(f"   Expectancy: {metrics.get('expectancy', 0):.2f}\n")
                f.write(f"   Max Drawdown: ${metrics.get('max_drawdown', 0):.0f}\n")
                f.write(f"   \n")
                f.write(f"   Best Parameters:\n")
                f.write(f"     SL ATR: {params.get('input_stop_loss_atr', 'N/A')}\n")
                f.write(f"     TP Ratio: {params.get('input_take_profit', 'N/A')}\n")
                f.write(f"     RSI Level: {params.get('input_rsi_level_in', 'N/A')}\n")
                f.write(f"     Volume Spike: {params.get('input_volume_spike', 'N/A')}\n")
        
        # แนะนำการใช้งาน
        f.write(f"\n🎯 RECOMMENDATIONS\n")
        f.write("-" * 20 + "\n")
        f.write("1. Focus on assets with Win Rate > 50%\n")
        f.write("2. Use parameters from best performing assets\n")
        f.write("3. Test with out-of-sample data before live trading\n")
        f.write("4. Monitor performance regularly\n")
        f.write("5. Re-optimize every 1-3 months\n")
    
    return filename

def main():
    print("🚀 Step 5: Multi-Asset Parameter Optimization")
    print("=" * 60)
    
    # สร้างระบบทดสอบ
    print("🔧 เริ่มต้นระบบทดสอบ...")
    tester = ParameterTester()
    
    # สร้าง configuration
    config = create_optimization_config()
    
    print(f"\n📋 Configuration:")
    print(f"   Parameters: {len(config['parameters_to_test'])} types")
    print(f"   Max Combinations: {config['max_combinations']}")
    
    # นับจำนวนการทดสอบทั้งหมด
    total_assets = sum(len(files) for files in TEST_GROUPS.values())
    print(f"   Total Assets: {total_assets}")
    
    # เริ่มการทดสอบ
    all_results = []
    current_test = 0
    
    print(f"\n🧪 เริ่มการทดสอบ Multi-Asset...")
    print(f"   (อาจใช้เวลา 15-30 นาที...)")
    
    for timeframe, file_list in TEST_GROUPS.items():
        print(f"\n📊 ทดสอบ Timeframe: {timeframe}")
        print("-" * 40)
        
        for file_path in file_list:
            current_test += 1
            symbol = extract_symbol_from_path(file_path)
            
            print(f"\n⏳ [{current_test}/{total_assets}] {symbol}_{timeframe}")
            
            result = test_single_asset(tester, symbol, timeframe, file_path, config)
            all_results.append(result)
    
    # กรองผลลัพธ์ที่สำเร็จ
    successful_results = [r for r in all_results if r is not None]
    
    print(f"\n✅ การทดสอบเสร็จสิ้น!")
    print(f"   ทดสอบสำเร็จ: {len(successful_results)}/{total_assets}")
    
    if len(successful_results) == 0:
        print("❌ ไม่มีผลลัพธ์ที่สำเร็จ")
        return None
    
    # คำนวณสถิติสรุป
    scores = [r['best_result']['performance_score'] for r in successful_results]
    win_rates = [r['best_result']['results'].get('win_rate', 0) for r in successful_results]
    
    summary_stats = {
        'avg_score': sum(scores) / len(scores),
        'best_score': max(scores),
        'worst_score': min(scores),
        'avg_win_rate': sum(win_rates) / len(win_rates),
        'good_win_rate_count': len([wr for wr in win_rates if wr > 50]),
        'best_asset': None,
        'worst_asset': None
    }
    
    # หาสินทรัพย์ที่ดีที่สุดและแย่ที่สุด
    best_idx = scores.index(summary_stats['best_score'])
    worst_idx = scores.index(summary_stats['worst_score'])
    summary_stats['best_asset'] = f"{successful_results[best_idx]['symbol']}_{successful_results[best_idx]['timeframe']}"
    summary_stats['worst_asset'] = f"{successful_results[worst_idx]['symbol']}_{successful_results[worst_idx]['timeframe']}"
    
    # แสดงสรุปผลลัพธ์
    print(f"\n📊 สรุปผลลัพธ์:")
    print(f"   คะแนนเฉลี่ย: {summary_stats['avg_score']:.2f}")
    print(f"   คะแนนสูงสุด: {summary_stats['best_score']:.2f} ({summary_stats['best_asset']})")
    print(f"   คะแนนต่ำสุด: {summary_stats['worst_score']:.2f} ({summary_stats['worst_asset']})")
    print(f"   Win Rate เฉลี่ย: {summary_stats['avg_win_rate']:.1f}%")
    print(f"   สินทรัพย์ที่ Win Rate > 50%: {summary_stats['good_win_rate_count']}/{len(successful_results)}")
    
    # บันทึกผลลัพธ์
    # 1. JSON file
    json_filename = f"multi_asset_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(json_filename, 'w') as f:
        json.dump({
            'summary': summary_stats,
            'results': successful_results
        }, f, indent=2)
    
    # 2. TXT file
    txt_filename = save_results_to_txt(successful_results, summary_stats)
    
    print(f"\n💾 บันทึกผลลัพธ์:")
    print(f"   📄 JSON: {json_filename}")
    print(f"   📝 TXT: {txt_filename}")
    
    return {
        'summary': summary_stats,
        'results': successful_results,
        'json_file': json_filename,
        'txt_file': txt_filename
    }

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n🎉 Multi-Asset Optimization เสร็จสมบูรณ์!")
        print(f"   📊 ทดสอบสำเร็จ: {len(result['results'])} สินทรัพย์")
        print(f"   🏆 คะแนนสูงสุด: {result['summary']['best_score']:.2f}")
        print(f"   📈 Win Rate เฉลี่ย: {result['summary']['avg_win_rate']:.1f}%")
        print(f"   📝 ดูรายละเอียดใน: {result['txt_file']}")
    else:
        print(f"\n❌ Multi-Asset Optimization ล้มเหลว")
