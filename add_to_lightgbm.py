"""
🔧 Code to Add to LightGBM_10_3.py
==================================

โค้ดสำหรับเพิ่มเข้า LightGBM_10_3.py เพื่อโหลดพารามิเตอร์อัตโนมัติ
"""

# ==============================================
# 1. เพิ่มที่ส่วน IMPORTS (บรรทัดประมาณ 50-60)
# ==============================================

# เพิ่มบรรทัดนี้หลัง imports อื่นๆ
try:
    from auto_parameter_loader import auto_load_parameters_for_training, apply_parameters_to_globals
    AUTO_PARAMETER_LOADING_AVAILABLE = True
    print("✅ Auto Parameter Loading System พร้อมใช้งาน")
except ImportError:
    AUTO_PARAMETER_LOADING_AVAILABLE = False
    print("⚠️ ไม่พบ Auto Parameter Loading System - ใช้พารามิเตอร์ default")

# ==============================================
# 2. เพิ่มฟังก์ชันใหม่ (หลัง imports, ก่อน main())
# ==============================================

def load_optimized_parameters_for_current_training():
    """
    โหลดพารามิเตอร์ที่ปรับปรุงแล้วสำหรับการเทรนปัจจุบัน
    """
    if not AUTO_PARAMETER_LOADING_AVAILABLE:
        print("⚠️ Auto Parameter Loading ไม่พร้อมใช้งาน - ใช้พารามิเตอร์ default")
        return False
    
    try:
        # ดึงค่า symbol และ timeframe จาก global variables
        current_symbol = globals().get('symbol', 'GOLD')
        current_timeframe = globals().get('timeframe', 30)
        
        print(f"\n🎯 กำลังโหลดพารามิเตอร์ที่ปรับปรุงแล้วสำหรับ {current_symbol} M{current_timeframe}...")
        
        # โหลดพารามิเตอร์
        optimized_params = auto_load_parameters_for_training(
            symbol=current_symbol, 
            timeframe=current_timeframe,
            show_details=True
        )
        
        # นำไปใช้กับ global variables
        changes_count = apply_parameters_to_globals(optimized_params, globals(), show_changes=True)
        
        if changes_count > 0:
            print(f"✅ อัปเดตพารามิเตอร์สำเร็จ: {changes_count} parameters")
            
            # แสดงพารามิเตอร์ที่จะใช้ในการเทรน
            print(f"\n📋 พารามิเตอร์ที่จะใช้ในการเทรน:")
            print(f"   Symbol: {current_symbol}")
            print(f"   Timeframe: M{current_timeframe}")
            print(f"   Volume Spike: {globals().get('input_volume_spike', 'N/A')}")
            print(f"   RSI Level In: {globals().get('input_rsi_level_in', 'N/A')}")
            print(f"   Stop Loss ATR: {globals().get('input_stop_loss_atr', 'N/A')}")
            print(f"   Take Profit: {globals().get('input_take_profit', 'N/A')}")
            print(f"   Initial nBar SL: {globals().get('input_initial_nbar_sl', 'N/A')}")
            
            return True
        else:
            print("ℹ️ ไม่มีพารามิเตอร์ที่ต้องอัปเดต")
            return False
        
    except Exception as e:
        print(f"⚠️ เกิดข้อผิดพลาดในการโหลดพารามิเตอร์: {e}")
        return False

def confirm_parameters_before_training():
    """
    ขอการยืนยันจากผู้ใช้ก่อนเริ่มการเทรน
    """
    print(f"\n" + "="*70)
    print("⚠️ กรุณาตรวจสอบพารามิเตอร์ข้างต้นก่อนเริ่มการเทรน")
    print("="*70)
    
    while True:
        confirm = input("ต้องการดำเนินการต่อด้วยพารามิเตอร์นี้? (y/n/s): ").strip().lower()
        
        if confirm == 'y':
            print("✅ เริ่มการเทรนด้วยพารามิเตอร์ที่ปรับปรุงแล้ว...")
            return True
        elif confirm == 'n':
            print("❌ ยกเลิกการเทรน")
            return False
        elif confirm == 's':
            print("⏭️ ข้ามการตรวจสอบ - ดำเนินการต่อ...")
            return True
        else:
            print("กรุณาใส่ y (ใช่), n (ไม่), หรือ s (ข้าม)")

# ==============================================
# 3. เพิ่มในฟังก์ชัน main() (ก่อนเริ่มการเทรน)
# ==============================================

def main():
    """ฟังก์ชันหลักของ LightGBM_10_3.py"""
    
    # ... โค้ดเดิมของ main() จนถึงก่อนเริ่มการเทรน ...
    
    # เพิ่มส่วนนี้ก่อนเริ่มการเทรน (หลังจากกำหนด symbol, timeframe)
    print("\n" + "="*70)
    print("🔧 AUTO PARAMETER OPTIMIZATION SYSTEM")
    print("="*70)
    
    # โหลดพารามิเตอร์ที่ปรับปรุงแล้ว
    parameter_loaded = load_optimized_parameters_for_current_training()
    
    if parameter_loaded:
        # ขอการยืนยันจากผู้ใช้
        if not confirm_parameters_before_training():
            print("🚪 ออกจากโปรแกรม")
            return
    else:
        print("ℹ️ ใช้พารามิเตอร์ default ที่กำหนดไว้ในโค้ด")
        print(f"📋 พารามิเตอร์ default:")
        print(f"   Volume Spike: {globals().get('input_volume_spike', 'N/A')}")
        print(f"   RSI Level In: {globals().get('input_rsi_level_in', 'N/A')}")
        print(f"   Stop Loss ATR: {globals().get('input_stop_loss_atr', 'N/A')}")
        print(f"   Take Profit: {globals().get('input_take_profit', 'N/A')}")
    
    print("="*70)
    
    # ... ส่วนที่เหลือของโค้ด main() เดิม ...

# ==============================================
# 4. ตัวอย่างการใช้งานแบบ Optional
# ==============================================

# เพิ่มที่ต้นฟังก์ชัน main() หากต้องการให้เป็น optional
ENABLE_AUTO_PARAMETER_LOADING = True  # เปลี่ยนเป็น False หากไม่ต้องการใช้

def main():
    """ฟังก์ชันหลักของ LightGBM_10_3.py"""
    
    # ... โค้ดเดิม ...
    
    # Auto Parameter Loading (Optional)
    if ENABLE_AUTO_PARAMETER_LOADING:
        print("\n🔧 Auto Parameter Loading เปิดใช้งาน")
        parameter_loaded = load_optimized_parameters_for_current_training()
        
        if parameter_loaded:
            if not confirm_parameters_before_training():
                return
    else:
        print("\n🔧 Auto Parameter Loading ปิดใช้งาน - ใช้พารามิเตอร์ default")
    
    # ... ส่วนที่เหลือของโค้ด ...

# ==============================================
# 5. ตัวอย่างการใช้งานแบบ Silent (ไม่ถามยืนยัน)
# ==============================================

def load_parameters_silently():
    """โหลดพารามิเตอร์แบบไม่ถามยืนยัน"""
    if AUTO_PARAMETER_LOADING_AVAILABLE:
        try:
            current_symbol = globals().get('symbol', 'GOLD')
            current_timeframe = globals().get('timeframe', 30)
            
            optimized_params = auto_load_parameters_for_training(
                symbol=current_symbol, 
                timeframe=current_timeframe,
                show_details=False  # ไม่แสดงรายละเอียด
            )
            
            apply_parameters_to_globals(optimized_params, globals(), show_changes=False)
            
            print(f"✅ โหลดพารามิเตอร์สำหรับ {current_symbol} M{current_timeframe} สำเร็จ")
            return True
        except:
            return False
    return False

# ==============================================
# 6. วิธีการติดตั้งและใช้งาน
# ==============================================

"""
📋 วิธีการติดตั้ง:

1. วางไฟล์ auto_parameter_loader.py ในโฟลเดอร์เดียวกับ LightGBM_10_3.py

2. เพิ่มโค้ดส่วนที่ 1 (IMPORTS) ที่ส่วนต้นของไฟล์

3. เพิ่มฟังก์ชันส่วนที่ 2 หลัง imports

4. เพิ่มโค้ดส่วนที่ 3 ในฟังก์ชัน main()

🎯 ผลลัพธ์ที่คาดหวัง:

เมื่อรัน LightGBM_10_3.py จะแสดง:
- รายละเอียดการทดสอบของสินทรัพย์ที่เลือก
- พารามิเตอร์ที่จะใช้ในการเทรน
- ขอการยืนยันก่อนเริ่มการเทรน
- อัปเดต global variables อัตโนมัติ

📊 ตัวอย่างผลลัพธ์:

🔸 GOLD_M30
   Score: 65.87
   Win Rate: 53.3%
   Total Profit: $121,493
   Total Trades: 15
   Expectancy: 8099.52
   Max Drawdown: $86,950
   
   Best Parameters:
     SL ATR: 1.0
     TP Ratio: 2.0
     RSI Level: 25
     Volume Spike: 1.0

📋 พารามิเตอร์ที่จะใช้ในการเทรน:
   Symbol: GOLD
   Timeframe: M30
   Volume Spike: 1.0
   RSI Level In: 25
   Stop Loss ATR: 1.0
   Take Profit: 2.0
   Initial nBar SL: 4

⚠️ กรุณาตรวจสอบพารามิเตอร์ข้างต้นก่อนเริ่มการเทรน
ต้องการดำเนินการต่อด้วยพารามิเตอร์นี้? (y/n/s):
"""
