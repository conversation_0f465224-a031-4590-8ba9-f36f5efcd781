#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ทดสอบ enhanced_model_decision functions ที่ส่งข้อมูล scenario กลับมา
"""

import sys
import os
import pandas as pd
import numpy as np

# เพิ่ม path ปัจจุบันเข้าไปใน sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_enhanced_decision_with_scenario():
    """ทดสอบ enhanced_model_decision functions ที่ส่ง scenario กลับมา"""
    
    print("🔍 ทดสอบ enhanced_model_decision functions with scenario return")
    
    # Import functions
    try:
        from WebRequest_Server_03_Target import enhanced_model_decision_buy, enhanced_model_decision_sell
        print("✅ Import functions สำเร็จ")
    except ImportError as e:
        print(f"❌ ไม่สามารถ import functions: {e}")
        return
    
    # สร้างข้อมูลทดสอบ scenario_models (dummy)
    print(f"\n🔍 สร้างข้อมูลทดสอบ scenario_models")
    
    class MockModel:
        def predict_proba(self, X):
            # Return dummy probabilities
            return np.array([[0.3, 0.7]])  # [prob_class_0, prob_class_1]
    
    scenario_models = {
        'trend_following': {
            'model': MockModel(),
            'scaler': None,
            'features': ['feature1', 'feature2', 'feature3']
        },
        'counter_trend': {
            'model': MockModel(),
            'scaler': None,
            'features': ['feature1', 'feature2', 'feature3']
        }
    }
    
    # สร้างข้อมูลทดสอบ prediction_row
    prediction_row = pd.Series({
        'feature1': 1.0,
        'feature2': 2.0,
        'feature3': 3.0,
        'Close': 2000.0,
        'EMA_200': 1950.0,  # Close > EMA_200 -> trend_following
        'RSI': 60.0,
        'ATR': 10.0
    })
    
    symbol = "GOLD"
    timeframe = 60
    threshold_trend = 0.6
    threshold_counter = 0.7
    
    print(f"📊 Test parameters:")
    print(f"   Symbol: {symbol}")
    print(f"   Timeframe: {timeframe}")
    print(f"   Close: {prediction_row['Close']:.2f}")
    print(f"   EMA_200: {prediction_row['EMA_200']:.2f}")
    print(f"   Expected scenario: trend_following (Close > EMA_200)")
    
    # ทดสอบ enhanced_model_decision_buy
    print(f"\n🔄 ทดสอบ enhanced_model_decision_buy")
    
    try:
        should_trade_buy, confidence_buy, details_buy, buy_scenario_used = enhanced_model_decision_buy(
            scenario_models, prediction_row, symbol, timeframe, 
            threshold_trend, threshold_counter
        )
        
        print(f"📊 BUY Results:")
        print(f"   should_trade: {should_trade_buy}")
        print(f"   confidence: {confidence_buy:.4f}")
        print(f"   details: {details_buy}")
        print(f"   scenario_used: {buy_scenario_used}")
        
        if buy_scenario_used == "trend_following":
            print(f"✅ BUY correctly used trend_following scenario")
        else:
            print(f"⚠️ BUY used unexpected scenario: {buy_scenario_used}")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ enhanced_model_decision_buy: {e}")
        import traceback
        traceback.print_exc()
    
    # ทดสอบ enhanced_model_decision_sell
    print(f"\n🔄 ทดสอบ enhanced_model_decision_sell")
    
    try:
        should_trade_sell, confidence_sell, details_sell, sell_scenario_used = enhanced_model_decision_sell(
            scenario_models, prediction_row, symbol, timeframe,
            threshold_trend, threshold_counter
        )
        
        print(f"📊 SELL Results:")
        print(f"   should_trade: {should_trade_sell}")
        print(f"   confidence: {confidence_sell:.4f}")
        print(f"   details: {details_sell}")
        print(f"   scenario_used: {sell_scenario_used}")
        
        if sell_scenario_used == "trend_following":
            print(f"✅ SELL correctly used trend_following scenario")
        else:
            print(f"⚠️ SELL used unexpected scenario: {sell_scenario_used}")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ enhanced_model_decision_sell: {e}")
        import traceback
        traceback.print_exc()
    
    # ทดสอบกรณี counter_trend
    print(f"\n🔄 ทดสอบกรณี counter_trend (Close < EMA_200)")
    
    prediction_row_counter = prediction_row.copy()
    prediction_row_counter['Close'] = 1900.0  # Close < EMA_200 -> counter_trend
    
    print(f"📊 Counter-trend test parameters:")
    print(f"   Close: {prediction_row_counter['Close']:.2f}")
    print(f"   EMA_200: {prediction_row_counter['EMA_200']:.2f}")
    print(f"   Expected scenario: counter_trend (Close < EMA_200)")
    
    try:
        should_trade_buy_ct, confidence_buy_ct, details_buy_ct, buy_scenario_used_ct = enhanced_model_decision_buy(
            scenario_models, prediction_row_counter, symbol, timeframe, 
            threshold_trend, threshold_counter
        )
        
        print(f"📊 Counter-trend BUY Results:")
        print(f"   scenario_used: {buy_scenario_used_ct}")
        
        if buy_scenario_used_ct == "counter_trend":
            print(f"✅ Counter-trend BUY correctly used counter_trend scenario")
        else:
            print(f"⚠️ Counter-trend BUY used unexpected scenario: {buy_scenario_used_ct}")
            
    except Exception as e:
        print(f"❌ เกิดข้อผิดพลาดในการทดสอบ counter-trend BUY: {e}")
    
    print(f"\n🎯 สรุปการทดสอบ:")
    print(f"✅ enhanced_model_decision functions ส่งข้อมูล scenario กลับมาได้แล้ว")
    print(f"✅ สามารถแยกแยะ trend_following และ counter_trend ได้ถูกต้อง")
    print(f"✅ พร้อมสำหรับการใช้งานในการกำหนด threshold และ nBars_SL")

if __name__ == "__main__":
    test_enhanced_decision_with_scenario()
