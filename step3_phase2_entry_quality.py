"""
🧪 Step 3: Phase 2 - Entry Quality Optimization
===============================================

ปรับปรุง Entry Conditions เพื่อเพิ่ม Win Rate ให้มากกว่า 50%
"""

from Parameter_Testing_Integration import ParameterTester
import json
from datetime import datetime

def main():
    print("🚀 Step 3: Phase 2 - Entry Quality Optimization")
    print("="*60)
    
    # โหลดผลลัพธ์ Phase 1
    try:
        with open('phase1_result.json', 'r') as f:
            phase1 = json.load(f)
        phase1_score = phase1['best_score']
        phase1_params = phase1['best_parameters']
        print(f"📊 Phase 1 Score: {phase1_score:.2f}")
        print(f"📋 Phase 1 Best Params: SL ATR {phase1_params['input_stop_loss_atr']}, TP {phase1_params['input_take_profit']}")
    except:
        print("❌ ไม่พบ phase1_result.json - กรุณารัน step2 ก่อน")
        return None
    
    # สร้างระบบทดสอบ
    print("\n🔧 เริ่มต้นระบบทดสอบ...")
    tester = ParameterTester()
    
    # ใช้พารามิเตอร์ที่ดีที่สุดจาก Phase 1 เป็นฐาน
    base_params = phase1_params.copy()
    
    # Phase 2 Configuration: ทดสอบ Entry Conditions
    print("\n📋 Phase 2 Test Configuration:")
    print("   🎯 เป้าหมาย: เพิ่ม Win Rate ให้มากกว่า 50%")
    print("   🔧 พารามิเตอร์ที่ทดสอบ: RSI Level และ Volume Spike")
    
    phase2_config = {
        'base_parameters': base_params,
        'parameters_to_test': {
            'input_rsi_level_in': {
                'min': 25,     # เข้มงวดขึ้นจาก 35
                'max': 45,     # ช่วงที่เหมาะสม
                'step': 5
            },
            'input_volume_spike': {
                'min': 1.0,    # ลดลงจาก 1.25
                'max': 2.0,    # เพิ่มขึ้นเพื่อทดสอบ
                'step': 0.25
            }
        },
        'max_combinations': 25  # จำกัดจำนวนการทดสอบ
    }
    
    print(f"\n📊 จำนวนการทดสอบ:")
    rsi_count = len([x for x in [25, 30, 35, 40, 45]])
    vol_count = len([x for x in [1.0, 1.25, 1.5, 1.75, 2.0]])
    total_tests = rsi_count * vol_count
    print(f"   RSI Level: {rsi_count} ค่า (25-45)")
    print(f"   Volume Spike: {vol_count} ค่า (1.0-2.0)")
    print(f"   รวม: {total_tests} combinations")
    
    # รัน Phase 2 Testing
    print(f"\n🧪 เริ่มทดสอบ Phase 2...")
    print("   (อาจใช้เวลา 5-8 นาที...)")
    
    try:
        phase2_results = tester.run_batch_test(
            symbol="GOLD",
            timeframe="H1",
            test_config=phase2_config
        )
        
        print(f"\n✅ ทดสอบเสร็จ - ได้ผลลัพธ์ {len(phase2_results)} รายการ")
        
        # แสดงผลลัพธ์ Top 5
        print(f"\n🏆 Top 5 ผลลัพธ์ Phase 2:")
        print(f"{'Rank':<4} {'Score':<8} {'Win%':<8} {'Profit':<12} {'RSI':<6} {'Vol':<6} {'Trades':<8}")
        print("="*70)
        
        for i, result in enumerate(phase2_results[:5]):
            rank = i + 1
            score = result['performance_score']
            win_rate = result['results'].get('win_rate', 0)
            profit = result['results'].get('total_profit', 0)
            rsi = result['parameters']['input_rsi_level_in']
            vol = result['parameters']['input_volume_spike']
            trades = result['results'].get('total_trades', 0)
            
            print(f"{rank:<4} {score:<8.2f} {win_rate:<8.1f} ${profit:<11.0f} {rsi:<6} {vol:<6.2f} {trades:<8}")
        
        # เลือกผลลัพธ์ที่ดีที่สุด
        best_result = phase2_results[0]
        best_params = best_result['parameters']
        best_score = best_result['performance_score']
        best_win_rate = best_result['results'].get('win_rate', 0)
        
        print(f"\n🎉 พารามิเตอร์ที่ดีที่สุดจาก Phase 2:")
        print(f"   RSI Level: {best_params['input_rsi_level_in']}")
        print(f"   Volume Spike: {best_params['input_volume_spike']}")
        print(f"   คะแนน: {best_score:.2f}")
        print(f"   Win Rate: {best_win_rate:.1f}%")
        
        # เปรียบเทียบกับ Phase 1
        improvement = best_score - phase1_score
        win_improvement = best_win_rate - 44.5  # Win rate จาก Phase 1
        
        print(f"\n📈 การเปรียบเทียบกับ Phase 1:")
        print(f"   Phase 1 Score: {phase1_score:.2f}")
        print(f"   Phase 2 Score: {best_score:.2f}")
        print(f"   การปรับปรุง: {improvement:+.2f} ({(improvement/phase1_score*100):+.1f}%)")
        print(f"   Win Rate: {best_win_rate:.1f}% ({win_improvement:+.1f}%)")
        
        # ตรวจสอบว่าบรรลุเป้าหมายหรือไม่
        print(f"\n🎯 การประเมินเป้าหมาย Phase 2:")
        
        if best_win_rate > 50:
            print(f"   🎉 Win Rate: {best_win_rate:.1f}% - บรรลุเป้าหมาย > 50%!")
        else:
            print(f"   ⚠️ Win Rate: {best_win_rate:.1f}% - ยังไม่บรรลุเป้าหมาย 50%")
            
        if improvement > 0:
            print(f"   ✅ Score เพิ่มขึ้น: {improvement:+.2f} คะแนน")
        else:
            print(f"   ⚠️ Score ไม่เพิ่มขึ้น: {improvement:+.2f} คะแนน")
        
        # สรุปพารามิเตอร์สุดท้าย
        print(f"\n📋 พารามิเตอร์สุดท้าย (Phase 1 + Phase 2):")
        for key, value in best_params.items():
            print(f"   {key}: {value}")
        
        # บันทึกผลลัพธ์
        phase2_result = {
            'phase': 2,
            'focus': 'Entry Quality',
            'best_parameters': best_params,
            'best_score': best_score,
            'best_win_rate': best_win_rate,
            'improvement_from_phase1': improvement,
            'all_results': phase2_results[:10],  # เก็บ top 10
            'test_date': datetime.now().isoformat()
        }
        
        with open('phase2_result.json', 'w') as f:
            json.dump(phase2_result, f, indent=2)
        
        print(f"\n💾 บันทึกผลลัพธ์: phase2_result.json")
        
        # แนะนำขั้นตอนต่อไป
        print(f"\n🎯 ขั้นตอนต่อไป:")
        if best_win_rate > 50 and improvement > 2:
            print("   👉 รัน step4_phase3_fine_tuning.py")
            print("      เพื่อ Fine-tuning สุดท้าย")
        elif best_win_rate > 50:
            print("   👉 รัน step4_final_comparison.py")
            print("      เพื่อเปรียบเทียบผลลัพธ์สุดท้าย")
        else:
            print("   👉 ลองปรับ configuration ใน Phase 2 อีกครั้ง")
            print("      หรือรัน step4_phase3_fine_tuning.py")
            
        return phase2_result
        
    except Exception as e:
        print(f"\n❌ เกิดข้อผิดพลาด: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n✅ Step 3 (Phase 2) เสร็จสมบูรณ์!")
        print(f"   Win Rate: {result['best_win_rate']:.1f}%")
        print(f"   การปรับปรุง: {result['improvement_from_phase1']:+.2f} คะแนน")
    else:
        print(f"\n❌ Step 3 (Phase 2) ล้มเหลว - กรุณาตรวจสอบข้อผิดพลาด")
